{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./dist/main", "rootDir": "./src/main", "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo-main"}, "include": ["src/main/**/*", "src/shared/**/*", "src/types/**/*"], "exclude": ["node_modules", "dist", "src/renderer", "**/*.test.ts", "**/*.spec.ts"]}