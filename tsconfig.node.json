{"compilerOptions": {"composite": true, "skipLibCheck": true, "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@core/*": ["src/core/*"], "@features/*": ["src/features/*"], "@shared/*": ["src/shared/*"], "@utils/*": ["src/utils/*"], "@services/*": ["src/services/*"], "@types/*": ["src/types/*"], "@constants/*": ["src/constants/*"], "@hooks/*": ["src/hooks/*"], "@store/*": ["src/store/*"], "@styles/*": ["src/styles/*"], "@assets/*": ["src/assets/*"], "@config/*": ["src/config/*"]}}, "include": ["vite.config.ts"]}