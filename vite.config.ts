import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import electron from 'vite-plugin-electron';
import { resolve } from 'path';

export default defineConfig({
  plugins: [
    react(),
    electron({
      main: {
        entry: 'src/main.ts',
      },
      preload: {
        input: 'src/preload.ts',
      },
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@hooks': resolve(__dirname, 'src/hooks'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@services': resolve(__dirname, 'src/services'),
      '@store': resolve(__dirname, 'src/store'),
      '@types': resolve(__dirname, 'src/types'),
      '@config': resolve(__dirname, 'src/config'),
      '@assets': resolve(__dirname, 'src/assets'),
      '@styles': resolve(__dirname, 'src/styles'),
      '@constants': resolve(__dirname, 'src/constants'),
      '@locales': resolve(__dirname, 'src/locales'),
      '@validation': resolve(__dirname, 'src/validation'),
      '@security': resolve(__dirname, 'src/security'),
      '@analytics': resolve(__dirname, 'src/analytics'),
      '@performance': resolve(__dirname, 'src/performance'),
      '@accessibility': resolve(__dirname, 'src/accessibility'),
      '@testing': resolve(__dirname, 'src/testing'),
      '@monitoring': resolve(__dirname, 'src/monitoring'),
      '@logging': resolve(__dirname, 'src/logging'),
      '@documentation': resolve(__dirname, 'src/documentation'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          redux: ['@reduxjs/toolkit', 'react-redux'],
          utils: ['axios', 'i18next', 'winston', 'zod'],
        },
      },
    },
  },
  server: {
    port: 3000,
    strictPort: true,
    host: true,
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@reduxjs/toolkit',
      'react-redux',
      'axios',
      'i18next',
      'winston',
      'zod',
    ],
  },
}); 