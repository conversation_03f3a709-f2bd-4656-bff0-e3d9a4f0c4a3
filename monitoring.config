# OpenTelemetry Configuration

exporters:
  jaeger:
    endpoint: "jaeger:14250"
    tls:
      insecure: true
  prometheus:
    endpoint: "0.0.0.0:9464"

resource:
  service.name: "validation-service"
  service.namespace: "security"
  attributes:
    deployment.environment: "production"

logging:
  level: "info"
  exporters:
    - console
    - file:/var/log/validation-service.log

metrics:
  processors:
    batch:
      timeout: 30s
      send_batch_size: 10000

sampling:
  probability: 0.75

autoinstrumentation:
  nodejs: true
  browser: false

tracing:
  max_export_batch_size: 512
  max_queue_size: 2048