# Internationalization Strategy Documentation

## Overview

This document outlines the comprehensive internationalization (i18n) strategy for the A14 Browser project. It covers all aspects of internationalization, from translation management to date/time formatting, number formatting, and RTL support.

## Translation Management

### 1. Translation Service

```typescript
// i18n/translation/TranslationService.ts
interface Translation {
  key: string;
  value: string;
  language: string;
  namespace: string;
}

class TranslationService {
  private static instance: TranslationService;
  private translations: Map<string, Map<string, Translation>>;

  private constructor() {
    this.translations = new Map();
  }

  public static getInstance(): TranslationService {
    if (!TranslationService.instance) {
      TranslationService.instance = new TranslationService();
    }
    return TranslationService.instance;
  }

  public addTranslation(translation: Translation): void {
    if (!this.translations.has(translation.language)) {
      this.translations.set(translation.language, new Map());
    }
    this.translations
      .get(translation.language)!
      .set(this.getTranslationKey(translation), translation);
  }

  public getTranslation(
    key: string,
    language: string,
    namespace: string = 'common'
  ): string {
    const translation =
      this.translations.get(language)?.get(this.getKey(key, namespace));
    return translation?.value || key;
  }

  private getTranslationKey(translation: Translation): string {
    return this.getKey(translation.key, translation.namespace);
  }

  private getKey(key: string, namespace: string): string {
    return `${namespace}:${key}`;
  }
}
```

### 2. Translation Hook

```typescript
// i18n/translation/useTranslation.ts
import { useState, useEffect } from 'react';

export function useTranslation(namespace: string = 'common') {
  const [language, setLanguage] = useState(
    localStorage.getItem('language') || 'en'
  );

  useEffect(() => {
    localStorage.setItem('language', language);
  }, [language]);

  const t = (key: string): string => {
    return TranslationService.getInstance().getTranslation(key, language, namespace);
  };

  const changeLanguage = (newLanguage: string): void => {
    setLanguage(newLanguage);
  };

  return { t, language, changeLanguage };
}
```

## Date and Time Formatting

### 1. Date Formatter

```typescript
// i18n/date/DateFormatter.ts
interface DateFormatOptions {
  locale: string;
  format: string;
  timeZone?: string;
}

class DateFormatter {
  private static instance: DateFormatter;

  private constructor() {}

  public static getInstance(): DateFormatter {
    if (!DateFormatter.instance) {
      DateFormatter.instance = new DateFormatter();
    }
    return DateFormatter.instance;
  }

  public formatDate(
    date: Date,
    options: DateFormatOptions
  ): string {
    return new Intl.DateTimeFormat(options.locale, {
      timeZone: options.timeZone,
      ...this.getFormatOptions(options.format),
    }).format(date);
  }

  public formatRelativeTime(
    date: Date,
    baseDate: Date = new Date(),
    locale: string = 'en'
  ): string {
    const formatter = new Intl.RelativeTimeFormat(locale, {
      numeric: 'auto',
    });

    const diff = date.getTime() - baseDate.getTime();
    const diffInSeconds = Math.round(diff / 1000);
    const diffInMinutes = Math.round(diffInSeconds / 60);
    const diffInHours = Math.round(diffInMinutes / 60);
    const diffInDays = Math.round(diffInHours / 24);

    if (Math.abs(diffInSeconds) < 60) {
      return formatter.format(diffInSeconds, 'second');
    }
    if (Math.abs(diffInMinutes) < 60) {
      return formatter.format(diffInMinutes, 'minute');
    }
    if (Math.abs(diffInHours) < 24) {
      return formatter.format(diffInHours, 'hour');
    }
    return formatter.format(diffInDays, 'day');
  }

  private getFormatOptions(format: string): Intl.DateTimeFormatOptions {
    switch (format) {
      case 'short':
        return {
          dateStyle: 'short',
          timeStyle: 'short',
        };
      case 'medium':
        return {
          dateStyle: 'medium',
          timeStyle: 'medium',
        };
      case 'long':
        return {
          dateStyle: 'long',
          timeStyle: 'long',
        };
      default:
        return {
          dateStyle: 'medium',
          timeStyle: 'medium',
        };
    }
  }
}
```

### 2. Time Zone Service

```typescript
// i18n/date/TimeZoneService.ts
class TimeZoneService {
  private static instance: TimeZoneService;

  private constructor() {}

  public static getInstance(): TimeZoneService {
    if (!TimeZoneService.instance) {
      TimeZoneService.instance = new TimeZoneService();
    }
    return TimeZoneService.instance;
  }

  public getTimeZones(): string[] {
    return Intl.supportedValuesOf('timeZone');
  }

  public getCurrentTimeZone(): string {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  }

  public convertTimeZone(
    date: Date,
    fromTimeZone: string,
    toTimeZone: string
  ): Date {
    const fromDate = new Date(
      date.toLocaleString('en-US', { timeZone: fromTimeZone })
    );
    const toDate = new Date(
      date.toLocaleString('en-US', { timeZone: toTimeZone })
    );
    const diff = toDate.getTime() - fromDate.getTime();
    return new Date(date.getTime() + diff);
  }
}
```

## Number Formatting

### 1. Number Formatter

```typescript
// i18n/number/NumberFormatter.ts
interface NumberFormatOptions {
  locale: string;
  style: 'decimal' | 'currency' | 'percent';
  currency?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
}

class NumberFormatter {
  private static instance: NumberFormatter;

  private constructor() {}

  public static getInstance(): NumberFormatter {
    if (!NumberFormatter.instance) {
      NumberFormatter.instance = new NumberFormatter();
    }
    return NumberFormatter.instance;
  }

  public formatNumber(
    number: number,
    options: NumberFormatOptions
  ): string {
    return new Intl.NumberFormat(options.locale, {
      style: options.style,
      currency: options.currency,
      minimumFractionDigits: options.minimumFractionDigits,
      maximumFractionDigits: options.maximumFractionDigits,
    }).format(number);
  }

  public formatCurrency(
    amount: number,
    locale: string = 'en',
    currency: string = 'USD'
  ): string {
    return this.formatNumber(amount, {
      locale,
      style: 'currency',
      currency,
    });
  }

  public formatPercent(
    value: number,
    locale: string = 'en',
    decimals: number = 2
  ): string {
    return this.formatNumber(value, {
      locale,
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    });
  }
}
```

### 2. Unit Formatter

```typescript
// i18n/number/UnitFormatter.ts
interface UnitFormatOptions {
  locale: string;
  unit: string;
  style: 'long' | 'short' | 'narrow';
}

class UnitFormatter {
  private static instance: UnitFormatter;

  private constructor() {}

  public static getInstance(): UnitFormatter {
    if (!UnitFormatter.instance) {
      UnitFormatter.instance = new UnitFormatter();
    }
    return UnitFormatter.instance;
  }

  public formatUnit(
    value: number,
    options: UnitFormatOptions
  ): string {
    return new Intl.NumberFormat(options.locale, {
      style: 'unit',
      unit: options.unit,
      unitDisplay: options.style,
    }).format(value);
  }

  public formatDistance(
    meters: number,
    locale: string = 'en',
    style: 'long' | 'short' | 'narrow' = 'long'
  ): string {
    return this.formatUnit(meters, {
      locale,
      unit: 'meter',
      style,
    });
  }

  public formatWeight(
    kilograms: number,
    locale: string = 'en',
    style: 'long' | 'short' | 'narrow' = 'long'
  ): string {
    return this.formatUnit(kilograms, {
      locale,
      unit: 'kilogram',
      style,
    });
  }
}
```

## RTL Support

### 1. RTL Service

```typescript
// i18n/rtl/RTLService.ts
class RTLService {
  private static instance: RTLService;
  private rtlLanguages: Set<string>;

  private constructor() {
    this.rtlLanguages = new Set(['ar', 'he', 'fa', 'ur']);
  }

  public static getInstance(): RTLService {
    if (!RTLService.instance) {
      RTLService.instance = new RTLService();
    }
    return RTLService.instance;
  }

  public isRTL(language: string): boolean {
    return this.rtlLanguages.has(language);
  }

  public setDirection(language: string): void {
    document.documentElement.dir = this.isRTL(language) ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
  }

  public getDirection(language: string): 'rtl' | 'ltr' {
    return this.isRTL(language) ? 'rtl' : 'ltr';
  }
}
```

### 2. RTL Components

```typescript
// i18n/rtl/RTLComponents.ts
class RTLComponents {
  private static instance: RTLComponents;

  private constructor() {}

  public static getInstance(): RTLComponents {
    if (!RTLComponents.instance) {
      RTLComponents.instance = new RTLComponents();
    }
    return RTLComponents.instance;
  }

  public createRTLContainer(
    children: React.ReactNode,
    language: string
  ): JSX.Element {
    return (
      <div
        dir={RTLService.getInstance().getDirection(language)}
        lang={language}
      >
        {children}
      </div>
    );
  }

  public createRTLText(
    text: string,
    language: string
  ): JSX.Element {
    return (
      <span
        dir={RTLService.getInstance().getDirection(language)}
        lang={language}
      >
        {text}
      </span>
    );
  }
}
```

## Best Practices

### 1. Translation Management

- Use translation keys
- Support namespaces
- Handle missing translations
- Support pluralization

### 2. Date and Time

- Use proper formats
- Handle time zones
- Support relative time
- Format durations

### 3. Number Formatting

- Use proper formats
- Handle currencies
- Support units
- Format percentages

### 4. RTL Support

- Handle text direction
- Support RTL layouts
- Test RTL rendering
- Handle mixed content

## Tools

### 1. i18n Tools

- i18next
- react-i18next
- date-fns
- Intl

### 2. Testing Tools

- jest-i18n
- i18n-tester
- rtl-tester
- locale-tester

## Contributing

1. Fork the repository
2. Create feature branch
3. Implement i18n
4. Add tests
5. Update documentation
6. Create pull request

## License

MIT License - see [LICENSE](../../LICENSE) for details. 