# useLocalStorage Hook

## Overview

The `useLocalStorage` hook provides a way to persist state in the browser's localStorage while maintaining reactivity in React components. It handles serialization/deserialization of data, error cases, and cross-tab synchronization.

## Features

- Automatic serialization/deserialization
- Type safety with TypeScript
- Cross-tab synchronization
- Error handling
- SSR compatibility
- Default value support
- Custom serialization
- Storage event handling
- Memory leak prevention
- Performance optimization

## Usage

```tsx
import { useLocalStorage } from '@/hooks/useLocalStorage';

// Basic usage
const [value, setValue] = useLocalStorage('key', 'default');

// With TypeScript
const [user, setUser] = useLocalStorage<User>('user', {
  id: 1,
  name: '<PERSON>',
});

// With custom serializer
const [data, setData] = useLocalStorage('data', initialValue, {
  serializer: customSerializer,
  deserializer: customDeserializer,
});
```

## API

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| key | `string` | Yes | Storage key |
| initialValue | `T` | Yes | Default value |
| options | `UseLocalStorageOptions` | No | Configuration options |

### Return Value

| Value | Type | Description |
|-------|------|-------------|
| value | `T` | Current value |
| setValue | `(value: T \| ((prev: T) => T)) => void` | Setter function |

### Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| serializer | `(value: T) => string` | `JSON.stringify` | Custom serializer |
| deserializer | `(value: string) => T` | `JSON.parse` | Custom deserializer |
| onError | `(error: Error) => void` | `console.error` | Error handler |
| sync | `boolean` | `true` | Enable cross-tab sync |

## Examples

### Basic Usage

```tsx
function Counter() {
  const [count, setCount] = useLocalStorage('count', 0);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>Increment</button>
    </div>
  );
}
```

### Complex Object

```tsx
interface User {
  id: number;
  name: string;
  preferences: {
    theme: 'light' | 'dark';
    notifications: boolean;
  };
}

function UserProfile() {
  const [user, setUser] = useLocalStorage<User>('user', {
    id: 1,
    name: 'John',
    preferences: {
      theme: 'light',
      notifications: true,
    },
  });

  const updateTheme = (theme: 'light' | 'dark') => {
    setUser(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        theme,
      },
    }));
  };

  return (
    <div>
      <h1>{user.name}</h1>
      <button onClick={() => updateTheme('dark')}>Dark Theme</button>
    </div>
  );
}
```

### Custom Serialization

```tsx
const dateSerializer = {
  serializer: (date: Date) => date.toISOString(),
  deserializer: (str: string) => new Date(str),
};

function DatePicker() {
  const [date, setDate] = useLocalStorage('selectedDate', new Date(), dateSerializer);
  
  return (
    <input
      type="date"
      value={date.toISOString().split('T')[0]}
      onChange={e => setDate(new Date(e.target.value))}
    />
  );
}
```

### Error Handling

```tsx
function ErrorHandling() {
  const [data, setData] = useLocalStorage('data', null, {
    onError: (error) => {
      // Custom error handling
      console.error('Storage error:', error);
      // Show user notification
      showNotification('Failed to save data');
    },
  });

  return <div>...</div>;
}
```

## Implementation Details

### Storage Event Handling

```typescript
useEffect(() => {
  const handleStorageChange = (e: StorageEvent) => {
    if (e.key === key && e.newValue !== null) {
      try {
        const newValue = deserializer(e.newValue);
        setValue(newValue);
      } catch (error) {
        onError(error);
      }
    }
  };

  window.addEventListener('storage', handleStorageChange);
  return () => window.removeEventListener('storage', handleStorageChange);
}, [key, deserializer, onError]);
```

### SSR Compatibility

```typescript
const getValue = () => {
  if (typeof window === 'undefined') {
    return initialValue;
  }

  try {
    const item = window.localStorage.getItem(key);
    return item ? deserializer(item) : initialValue;
  } catch (error) {
    onError(error);
    return initialValue;
  }
};
```

## Best Practices

1. **Key Naming**
   - Use consistent prefix
   - Avoid conflicts
   - Be descriptive

2. **Error Handling**
   - Always provide fallback
   - Handle serialization errors
   - Log issues appropriately

3. **Performance**
   - Avoid large objects
   - Use appropriate serialization
   - Monitor storage limits

4. **Type Safety**
   - Define proper types
   - Use TypeScript generics
   - Validate data structure

## Testing

### Unit Tests

```typescript
describe('useLocalStorage', () => {
  beforeEach(() => {
    window.localStorage.clear();
  });

  it('should initialize with default value', () => {
    const { result } = renderHook(() => useLocalStorage('test', 'default'));
    expect(result.current[0]).toBe('default');
  });

  it('should update value', () => {
    const { result } = renderHook(() => useLocalStorage('test', 'default'));
    act(() => {
      result.current[1]('new value');
    });
    expect(result.current[0]).toBe('new value');
  });

  it('should handle storage events', () => {
    const { result } = renderHook(() => useLocalStorage('test', 'default'));
    act(() => {
      window.dispatchEvent(
        new StorageEvent('storage', {
          key: 'test',
          newValue: JSON.stringify('updated'),
        })
      );
    });
    expect(result.current[0]).toBe('updated');
  });
});
```

## Performance Considerations

1. **Storage Limits**
   - Monitor usage
   - Implement cleanup
   - Handle quota exceeded

2. **Serialization**
   - Use efficient format
   - Minimize data size
   - Consider compression

3. **Event Handling**
   - Debounce updates
   - Clean up listeners
   - Handle errors

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Opera (latest)

## Related Hooks

- [useSessionStorage](./useSessionStorage.md)
- [useStorage](./useStorage.md)
- [usePersistedState](./usePersistedState.md)

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Update documentation
6. Create pull request

## License

MIT License - see [LICENSE](../../LICENSE) for details. 