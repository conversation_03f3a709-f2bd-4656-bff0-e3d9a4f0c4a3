# Button Component

## Overview

The Button component is a fundamental UI element that provides interactive functionality for user actions. It supports various styles, sizes, states, and features to accommodate different use cases.

## Features

- Multiple variants (primary, secondary, success, danger, warning, info, light, dark)
- Different sizes (small, medium, large)
- Icon support (left, right, or both)
- Loading state with spinner
- Disabled state
- Full width option
- Rounded corners
- Hover and focus effects
- Accessibility support
- Keyboard navigation
- Custom styling support

## Usage

```tsx
import { Button } from '@/components/common/Button';

// Basic usage
<Button>Click me</Button>

// With variant
<Button variant="primary">Primary Button</Button>

// With size
<Button size="large">Large Button</Button>

// With icon
<Button icon={<IconComponent />}>Button with Icon</Button>

// Loading state
<Button loading>Loading Button</Button>

// Disabled state
<Button disabled>Disabled Button</Button>

// Full width
<Button fullWidth>Full Width Button</Button>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| variant | `'primary' \| 'secondary' \| 'success' \| 'danger' \| 'warning' \| 'info' \| 'light' \| 'dark'` | `'primary'` | Button style variant |
| size | `'small' \| 'medium' \| 'large'` | `'medium'` | Button size |
| icon | `ReactNode` | `undefined` | Icon component to display |
| iconPosition | `'left' \| 'right'` | `'left'` | Position of the icon |
| loading | `boolean` | `false` | Shows loading spinner |
| disabled | `boolean` | `false` | Disables the button |
| fullWidth | `boolean` | `false` | Makes button full width |
| rounded | `boolean` | `false` | Adds rounded corners |
| className | `string` | `undefined` | Additional CSS classes |
| onClick | `(event: React.MouseEvent<HTMLButtonElement>) => void` | `undefined` | Click handler |
| type | `'button' \| 'submit' \| 'reset'` | `'button'` | Button type |
| children | `ReactNode` | `undefined` | Button content |

## Styling

### Default Styles

```css
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
}

.button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}
```

### Variants

| Variant | Background | Text Color | Border |
|---------|------------|------------|---------|
| primary | `#3182ce` | `#ffffff` | `none` |
| secondary | `#718096` | `#ffffff` | `none` |
| success | `#38a169` | `#ffffff` | `none` |
| danger | `#e53e3e` | `#ffffff` | `none` |
| warning | `#d69e2e` | `#ffffff` | `none` |
| info | `#4299e1` | `#ffffff` | `none` |
| light | `#f7fafc` | `#2d3748` | `1px solid #e2e8f0` |
| dark | `#2d3748` | `#ffffff` | `none` |

### Sizes

| Size | Padding | Font Size | Icon Size |
|------|---------|-----------|-----------|
| small | `0.375rem 0.75rem` | `0.875rem` | `1rem` |
| medium | `0.5rem 1rem` | `1rem` | `1.25rem` |
| large | `0.75rem 1.5rem` | `1.125rem` | `1.5rem` |

## Accessibility

### ARIA Attributes

- `role="button"` (default)
- `aria-disabled` when disabled
- `aria-busy` when loading
- `aria-label` for icon-only buttons

### Keyboard Navigation

- Focusable with Tab key
- Activatable with Enter/Space
- Focus ring visible on keyboard focus

## Best Practices

1. **Labeling**
   - Use clear, action-oriented labels
   - Avoid generic labels like "Click here"
   - Include icons for better visual hierarchy

2. **States**
   - Provide visual feedback for all states
   - Use loading state for async actions
   - Disable buttons during processing

3. **Placement**
   - Primary actions on the right
   - Secondary actions on the left
   - Destructive actions require confirmation

4. **Styling**
   - Maintain consistent spacing
   - Use appropriate contrast ratios
   - Follow platform conventions

## Examples

### Basic Button

```tsx
<Button>Click me</Button>
```

### Primary Button with Icon

```tsx
<Button variant="primary" icon={<IconComponent />}>
  Primary Action
</Button>
```

### Loading Button

```tsx
<Button loading>Processing...</Button>
```

### Disabled Button

```tsx
<Button disabled>Cannot Click</Button>
```

### Full Width Button

```tsx
<Button fullWidth>Full Width Action</Button>
```

### Button with Custom Styling

```tsx
<Button className="custom-button">
  Custom Styled
</Button>
```

## Testing

### Unit Tests

```tsx
describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Test</Button>);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalled();
  });

  it('shows loading state', () => {
    render(<Button loading>Loading</Button>);
    expect(screen.getByRole('button')).toHaveAttribute('aria-busy', 'true');
  });
});
```

### Integration Tests

```tsx
describe('Button Integration', () => {
  it('works with form submission', async () => {
    render(
      <form onSubmit={handleSubmit}>
        <Button type="submit">Submit</Button>
      </form>
    );
    fireEvent.click(screen.getByText('Submit'));
    await waitFor(() => {
      expect(handleSubmit).toHaveBeenCalled();
    });
  });
});
```

## Performance Considerations

1. **Bundle Size**
   - Component is tree-shakeable
   - Styles are co-located
   - Icons are optional

2. **Rendering**
   - Memoized when possible
   - Efficient state updates
   - Minimal re-renders

3. **Accessibility**
   - Semantic HTML
   - ARIA attributes
   - Keyboard support

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Opera (latest)

## Related Components

- [IconButton](./IconButton.md)
- [ButtonGroup](./ButtonGroup.md)
- [DropdownButton](./DropdownButton.md)

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Update documentation
6. Create pull request

## License

MIT License - see [LICENSE](../../LICENSE) for details. 