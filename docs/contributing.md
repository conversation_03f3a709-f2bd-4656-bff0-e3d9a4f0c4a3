> 🚀 **First Contribution**  
> Хочешь внести вклад прямо сейчас?  
> Вот лёгкие задачи для первого PR:  
> - Добавь unit-тест для любого slice (пример: src/store/slices/__tests__/notificationsSlice.test.ts)  
> - Улучши тест для компонента (например, ErrorBoundary, NotificationList)  
> - Исправь или дополни перевод в src/i18n/locales/ru.json  
> Советы и инструкции для старта — ниже по тексту!
# Contributing to NovaBrowser

Thank you for your interest in contributing to NovaBrowser! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [Development Setup](#development-setup)
4. [Contribution Workflow](#contribution-workflow)
5. [Code Style](#code-style)
6. [Testing](#testing)
7. [Documentation](#documentation)
8. [Review Process](#review-process)
9. [Release Process](#release-process)
10. [Community](#community)

## Code of Conduct

Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md). We are committed to providing a friendly, safe, and welcoming environment for all contributors.

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- Rust (latest stable)
- Git
- CMake (v3.10 or later)
- Python (v3.8 or later)
- Visual Studio (Windows) or Xcode (macOS)

### Required Tools

```bash
# Install Node.js dependencies
npm install -g yarn typescript @types/node

# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Install build tools
npm install -g electron-builder
```

## Development Setup

1. **Fork and Clone**

```bash
# Fork the repository on GitHub
# Clone your fork
git clone https://github.com/YOUR_USERNAME/novabrowser.git
cd novabrowser

# Add upstream remote
git remote add upstream https://github.com/novabrowser/novabrowser.git
```

2. **Install Dependencies**

```bash
# Install Node.js dependencies
yarn install

# Install Rust dependencies
cargo build
```

3. **Build the Project**

```bash
# Development build
yarn dev

# Production build
yarn build
```

## Contribution Workflow

1. **Create a Branch**

```bash
# Create a new branch
git checkout -b feature/your-feature-name

# Or for bug fixes
git checkout -b fix/your-bug-fix
```

2. **Make Changes**

- Follow the code style guidelines
- Write tests for new features
- Update documentation
- Add comments where necessary

3. **Commit Changes**

```bash
# Stage changes
git add .

# Commit with conventional commit message
git commit -m "feat: add new feature"
```

4. **Push Changes**

```bash
# Push to your fork
git push origin feature/your-feature-name
```

5. **Create Pull Request**

- Go to GitHub
- Create a new pull request
- Fill out the PR template
- Request review from maintainers

## Code Style

### TypeScript/JavaScript

```typescript
// Use TypeScript for type safety
interface User {
  id: string;
  name: string;
  email: string;
}

// Use async/await for asynchronous code
async function fetchUser(id: string): Promise<User> {
  const response = await fetch(`/api/users/${id}`);
  return response.json();
}

// Use meaningful variable names
const userCount = users.length;

// Add JSDoc comments for public APIs
/**
 * Fetches a user by ID
 * @param id - The user ID
 * @returns Promise<User>
 */
```

### Rust

```rust
// Use Rust idioms
#[derive(Debug, Clone)]
pub struct User {
    pub id: String,
    pub name: String,
    pub email: String,
}

// Use Result for error handling
pub fn fetch_user(id: &str) -> Result<User, Error> {
    // Implementation
}

// Add documentation comments
/// Fetches a user by ID
/// 
/// # Arguments
/// 
/// * `id` - The user ID
/// 
/// # Returns
/// 
/// Result<User, Error>
```

## Testing

### Unit Tests

```typescript
// Jest test example
describe('UserService', () => {
  it('should fetch user by id', async () => {
    const user = await userService.fetchUser('123');
    expect(user).toBeDefined();
    expect(user.id).toBe('123');
  });
});
```

### Integration Tests

```typescript
// Cypress test example
describe('User Interface', () => {
  it('should display user profile', () => {
    cy.visit('/profile');
    cy.get('[data-testid="user-name"]').should('be.visible');
  });
});
```

## Documentation

### Code Documentation

- Add JSDoc comments for TypeScript/JavaScript
- Add documentation comments for Rust
- Keep documentation up to date
- Include examples where helpful

### Project Documentation

- Update README.md for major changes
- Add new documentation files as needed
- Keep documentation organized
- Use clear and concise language

## Review Process

1. **Code Review**
   - Review code style
   - Check test coverage
   - Verify documentation
   - Ensure performance
   - Check security

2. **CI/CD Checks**
   - Run automated tests
   - Check code style
   - Verify build process
   - Run security scans

3. **Approval Process**
   - At least one maintainer approval
   - All CI checks passing
   - Documentation updated
   - Tests passing

## Release Process

1. **Version Management**
   - Follow semantic versioning
   - Update version numbers
   - Create release notes
   - Tag releases

2. **Release Steps**
   - Run full test suite
   - Build release artifacts
   - Create GitHub release
   - Deploy to distribution channels

## Community

### Communication

- GitHub Issues
- GitHub Discussions
- Discord Server
- Mailing List

### Events

- Weekly Development Calls
- Monthly Community Calls
- Quarterly Planning
- Annual Summit

### Recognition

- Contributor Hall of Fame
- Special Thanks
- Contributor Badges
- Swag Program

## Additional Resources

- [Development Guide](docs/development.md)
- [Testing Guide](docs/testing.md)
- [Security Guide](docs/security.md)
- [Release Guide](docs/release.md)

## Need Help?

- Check the [FAQ](docs/faq.md)
- Join our [Discord](https://discord.gg/novabrowser)
- Email the team
- Open an issue

Thank you for contributing to NovaBrowser! 