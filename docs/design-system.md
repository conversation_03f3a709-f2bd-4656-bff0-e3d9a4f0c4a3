# NovaBrowser Design System

## Overview

The NovaBrowser Design System provides a comprehensive set of guidelines, components, and tools to ensure consistency, accessibility, and excellence in user experience across the browser.

## Design Principles

### 1. User-Centered Design
- Prioritize user needs and goals
- Provide intuitive and efficient interactions
- Support both novice and power users

### 2. Accessibility First
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- High contrast support
- Reduced motion options

### 3. Performance
- Fast initial load times
- Smooth animations (60fps)
- Efficient resource usage
- Responsive interactions

### 4. Consistency
- Unified visual language
- Predictable interactions
- Consistent terminology
- Standardized patterns

## Color System

### Primary Colors
```css
:root {
  --primary-50: #E3F2FD;
  --primary-100: #BBDEFB;
  --primary-200: #90CAF9;
  --primary-300: #64B5F6;
  --primary-400: #42A5F5;
  --primary-500: #2196F3;
  --primary-600: #1E88E5;
  --primary-700: #1976D2;
  --primary-800: #1565C0;
  --primary-900: #0D47A1;
}
```

### Neutral Colors
```css
:root {
  --neutral-50: #FAFAFA;
  --neutral-100: #F5F5F5;
  --neutral-200: #EEEEEE;
  --neutral-300: #E0E0E0;
  --neutral-400: #BDBDBD;
  --neutral-500: #9E9E9E;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;
}
```

### Semantic Colors
```css
:root {
  --success: #4CAF50;
  --warning: #FFC107;
  --error: #F44336;
  --info: #2196F3;
}
```

## Typography

### Font Families
```css
:root {
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
}
```

### Font Sizes
```css
:root {
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
}
```

### Font Weights
```css
:root {
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

## Spacing

### Base Units
```css
:root {
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.25rem;  /* 20px */
  --space-6: 1.5rem;   /* 24px */
  --space-8: 2rem;     /* 32px */
  --space-10: 2.5rem;  /* 40px */
  --space-12: 3rem;    /* 48px */
  --space-16: 4rem;    /* 64px */
}
```

## Components

### Buttons

#### Primary Button
```css
.button-primary {
  background-color: var(--primary-500);
  color: white;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: background-color 0.2s;
}

.button-primary:hover {
  background-color: var(--primary-600);
}
```

#### Secondary Button
```css
.button-secondary {
  background-color: var(--neutral-100);
  color: var(--neutral-900);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: background-color 0.2s;
}

.button-secondary:hover {
  background-color: var(--neutral-200);
}
```

### Input Fields

#### Text Input
```css
.input {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: border-color 0.2s;
}

.input:focus {
  border-color: var(--primary-500);
  outline: none;
}
```

### Cards

#### Basic Card
```css
.card {
  background-color: white;
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
}
```

## Icons

### Icon System
- Use SVG icons for crisp rendering
- Maintain consistent stroke width
- Follow 24x24 grid system
- Support both light and dark themes

### Icon Sizes
```css
:root {
  --icon-xs: 16px;
  --icon-sm: 20px;
  --icon-md: 24px;
  --icon-lg: 32px;
  --icon-xl: 40px;
}
```

## Animations

### Timing Functions
```css
:root {
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}
```

### Durations
```css
:root {
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
}
```

## Shadows

### Elevation Levels
```css
:root {
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
}
```

## Dark Mode

### Dark Theme Colors
```css
[data-theme="dark"] {
  --bg-primary: var(--neutral-900);
  --bg-secondary: var(--neutral-800);
  --text-primary: var(--neutral-50);
  --text-secondary: var(--neutral-400);
}
```

## Responsive Design

### Breakpoints
```css
:root {
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}
```

## Grid System

### Container
```css
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}
```

## Best Practices

### Accessibility
- Use semantic HTML elements
- Provide alt text for images
- Ensure sufficient color contrast
- Support keyboard navigation
- Include ARIA labels where needed

### Performance
- Optimize images and assets
- Minimize CSS and JavaScript
- Use efficient animations
- Implement lazy loading
- Cache resources appropriately

### Code Quality
- Follow BEM naming convention
- Use CSS custom properties
- Maintain consistent formatting
- Document complex components
- Write reusable styles

## Resources

### Design Tools
- [Figma Design Kit](https://figma.com/novabrowser)
- [Icon Library](https://icons.novabrowser.com)
- [Color Palette](https://colors.novabrowser.com)

### Development Tools
- [Component Library](https://components.novabrowser.com)
- [Style Guide](https://style.novabrowser.com)
- [Design Tokens](https://tokens.novabrowser.com)

## Contributing

See our [Design System Contributing Guide](docs/design-system-contributing.md) for details on how to contribute to the design system.

## Version History

See our [Design System Changelog](docs/design-system-changelog.md) for a list of changes to the design system. 