# NovaBrowser API Documentation

This document provides detailed information about the NovaBrowser API.

## Table of Contents

1. [Core API](#core-api)
2. [Browser API](#browser-api)
3. [Tab API](#tab-api)
4. [Bookmark API](#bookmark-api)
5. [History API](#history-api)
6. [Download API](#download-api)
7. [Storage API](#storage-api)
8. [Security API](#security-api)
9. [Extension API](#extension-api)
10. [Event System](#event-system)

## Core API

### Browser

```typescript
interface Browser {
  // Browser instance
  version: string;
  name: string;
  platform: string;
  
  // Browser methods
  initialize(): Promise<void>;
  shutdown(): Promise<void>;
  restart(): Promise<void>;
  
  // Browser events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

### Window

```typescript
interface Window {
  // Window properties
  id: string;
  title: string;
  bounds: Bounds;
  isMaximized: boolean;
  isMinimized: boolean;
  isFullscreen: boolean;
  
  // Window methods
  focus(): void;
  minimize(): void;
  maximize(): void;
  close(): void;
  setBounds(bounds: Bounds): void;
  setTitle(title: string): void;
  
  // Window events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

## Browser API

### Navigation

```typescript
interface Navigation {
  // Navigation methods
  goBack(): void;
  goForward(): void;
  reload(): void;
  stop(): void;
  navigate(url: string): void;
  
  // Navigation properties
  canGoBack: boolean;
  canGoForward: boolean;
  isLoading: boolean;
  url: string;
  
  // Navigation events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

### Session

```typescript
interface Session {
  // Session methods
  clearCache(): Promise<void>;
  clearCookies(): Promise<void>;
  clearStorage(): Promise<void>;
  clearHistory(): Promise<void>;
  
  // Session properties
  cookies: Cookie[];
  cache: Cache;
  storage: Storage;
  
  // Session events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

## Tab API

### Tab

```typescript
interface Tab {
  // Tab properties
  id: string;
  title: string;
  url: string;
  favicon: string;
  isActive: boolean;
  isPinned: boolean;
  isMuted: boolean;
  
  // Tab methods
  activate(): void;
  close(): void;
  reload(): void;
  pin(): void;
  unpin(): void;
  mute(): void;
  unmute(): void;
  
  // Tab events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

### TabGroup

```typescript
interface TabGroup {
  // TabGroup properties
  id: string;
  name: string;
  tabs: Tab[];
  isCollapsed: boolean;
  
  // TabGroup methods
  addTab(tab: Tab): void;
  removeTab(tab: Tab): void;
  collapse(): void;
  expand(): void;
  
  // TabGroup events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

## Bookmark API

### Bookmark

```typescript
interface Bookmark {
  // Bookmark properties
  id: string;
  title: string;
  url: string;
  parentId: string;
  dateAdded: Date;
  
  // Bookmark methods
  update(properties: Partial<Bookmark>): void;
  remove(): void;
  move(parentId: string): void;
  
  // Bookmark events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

### BookmarkFolder

```typescript
interface BookmarkFolder {
  // BookmarkFolder properties
  id: string;
  title: string;
  parentId: string;
  children: (Bookmark | BookmarkFolder)[];
  
  // BookmarkFolder methods
  addBookmark(bookmark: Bookmark): void;
  addFolder(folder: BookmarkFolder): void;
  remove(item: Bookmark | BookmarkFolder): void;
  move(parentId: string): void;
  
  // BookmarkFolder events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

## History API

### HistoryItem

```typescript
interface HistoryItem {
  // HistoryItem properties
  id: string;
  url: string;
  title: string;
  visitCount: number;
  lastVisitTime: Date;
  
  // HistoryItem methods
  remove(): void;
  update(properties: Partial<HistoryItem>): void;
  
  // HistoryItem events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

### History

```typescript
interface History {
  // History methods
  search(query: string): Promise<HistoryItem[]>;
  getVisits(url: string): Promise<Visit[]>;
  removeRange(startTime: Date, endTime: Date): Promise<void>;
  removeAll(): Promise<void>;
  
  // History properties
  items: HistoryItem[];
  
  // History events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

## Download API

### Download

```typescript
interface Download {
  // Download properties
  id: string;
  url: string;
  filename: string;
  totalBytes: number;
  receivedBytes: number;
  state: DownloadState;
  startTime: Date;
  endTime: Date;
  
  // Download methods
  pause(): void;
  resume(): void;
  cancel(): void;
  remove(): void;
  
  // Download events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

### DownloadManager

```typescript
interface DownloadManager {
  // DownloadManager methods
  download(options: DownloadOptions): Promise<Download>;
  getDownloads(): Promise<Download[]>;
  clearDownloads(): Promise<void>;
  
  // DownloadManager properties
  downloads: Download[];
  
  // DownloadManager events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

## Storage API

### Storage

```typescript
interface Storage {
  // Storage methods
  get(key: string): Promise<any>;
  set(key: string, value: any): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
  
  // Storage properties
  quota: number;
  usage: number;
  
  // Storage events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

### Cache

```typescript
interface Cache {
  // Cache methods
  get(url: string): Promise<Response>;
  put(url: string, response: Response): Promise<void>;
  delete(url: string): Promise<void>;
  clear(): Promise<void>;
  
  // Cache properties
  size: number;
  
  // Cache events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

## Security API

### Security

```typescript
interface Security {
  // Security methods
  isSecure(url: string): Promise<boolean>;
  getCertificate(url: string): Promise<Certificate>;
  setPermission(url: string, permission: Permission): Promise<void>;
  
  // Security properties
  permissions: Permission[];
  
  // Security events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

### Permission

```typescript
interface Permission {
  // Permission properties
  name: string;
  state: PermissionState;
  
  // Permission methods
  request(): Promise<PermissionState>;
  query(): Promise<PermissionState>;
  
  // Permission events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

## Extension API

### Extension

```typescript
interface Extension {
  // Extension properties
  id: string;
  name: string;
  version: string;
  permissions: string[];
  
  // Extension methods
  enable(): void;
  disable(): void;
  uninstall(): void;
  update(): Promise<void>;
  
  // Extension events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

### ExtensionManager

```typescript
interface ExtensionManager {
  // ExtensionManager methods
  install(path: string): Promise<Extension>;
  getExtensions(): Promise<Extension[]>;
  updateAll(): Promise<void>;
  
  // ExtensionManager properties
  extensions: Extension[];
  
  // ExtensionManager events
  on(event: string, callback: Function): void;
  off(event: string, callback: Function): void;
}
```

## Event System

### Events

```typescript
interface Events {
  // Browser events
  'browser:ready': void;
  'browser:shutdown': void;
  'browser:error': Error;
  
  // Window events
  'window:created': Window;
  'window:closed': Window;
  'window:focused': Window;
  'window:blurred': Window;
  
  // Tab events
  'tab:created': Tab;
  'tab:closed': Tab;
  'tab:updated': Tab;
  'tab:activated': Tab;
  
  // Navigation events
  'navigation:started': string;
  'navigation:completed': string;
  'navigation:failed': Error;
  
  // Download events
  'download:started': Download;
  'download:progress': Download;
  'download:completed': Download;
  'download:failed': Error;
  
  // Extension events
  'extension:installed': Extension;
  'extension:uninstalled': Extension;
  'extension:updated': Extension;
  
  // Security events
  'security:certificate-error': Error;
  'security:permission-changed': Permission;
}
```

### EventEmitter

```typescript
interface EventEmitter {
  // EventEmitter methods
  on<T extends keyof Events>(event: T, callback: (data: Events[T]) => void): void;
  off<T extends keyof Events>(event: T, callback: (data: Events[T]) => void): void;
  emit<T extends keyof Events>(event: T, data: Events[T]): void;
  once<T extends keyof Events>(event: T, callback: (data: Events[T]) => void): void;
}
```

## Usage Examples

### Browser Initialization

```typescript
const browser = new Browser();

browser.on('browser:ready', () => {
  console.log('Browser is ready');
});

await browser.initialize();
```

### Tab Management

```typescript
const tab = await browser.tabs.create({
  url: 'https://example.com'
});

tab.on('tab:updated', () => {
  console.log('Tab updated');
});

await tab.activate();
```

### Bookmark Management

```typescript
const bookmark = await browser.bookmarks.create({
  title: 'Example',
  url: 'https://example.com'
});

bookmark.on('bookmark:updated', () => {
  console.log('Bookmark updated');
});

await bookmark.update({
  title: 'Updated Example'
});
```

### Download Management

```typescript
const download = await browser.downloads.download({
  url: 'https://example.com/file.pdf'
});

download.on('download:progress', () => {
  console.log(`Downloaded ${download.receivedBytes} of ${download.totalBytes} bytes`);
});

await download.resume();
```

### Extension Management

```typescript
const extension = await browser.extensions.install({
  path: '/path/to/extension'
});

extension.on('extension:updated', () => {
  console.log('Extension updated');
});

await extension.enable();
```

## Error Handling

```typescript
try {
  await browser.initialize();
} catch (error) {
  console.error('Failed to initialize browser:', error);
}

browser.on('browser:error', (error) => {
  console.error('Browser error:', error);
});
```

## Best Practices

1. Always handle errors appropriately
2. Clean up event listeners when no longer needed
3. Use TypeScript for type safety
4. Follow the event-driven architecture
5. Implement proper error boundaries
6. Use async/await for asynchronous operations
7. Implement proper security measures
8. Follow the principle of least privilege
9. Implement proper logging
10. Follow the documentation guidelines 