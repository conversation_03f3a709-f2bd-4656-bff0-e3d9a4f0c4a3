# Архитектура A14 Browser

## Обзор системы
```mermaid
flowchart TD
    A[Main Process] --> B[Extension System]
    A --> C[AdBlockerManager]
    A --> D[SessionManager]
    B --> E[Extension Sandbox]
    E --> F[Sandboxed Extensions]
    C --> G[Filter Lists]
    D --> H[Session Storage]
```

## Система расширений
### Основные компоненты:
- **ExtensionManager** - центральный контроллер расширений
- **ExtensionSandbox** - изолированное окружение (iframe/Worker)
- **IPC Communication** - безопасный обмен сообщениями

```mermaid
sequenceDiagram
    participant UI as UI Layer
    participant EM as ExtensionManager
    participant ES as ExtensionSandbox
    
    UI->>EM: loadExtension(id)
    EM->>ES: createSandbox(id)
    ES-->>EM: sandboxReady
    EM->>ES: injectBackgroundScript(script)
    ES->>EM: API Request
    EM->>ES: API Response
```

## Модель безопасности
1. Двойная изоляция (процесс + iframe)
2. Ограниченный API через IPC
3. Система разрешений
4. Песочница с политиками CSP

## Взаимодействие с AdBlocker
```mermaid
graph LR
    A[WebRequest] --> B[AdBlockerManager]
    B --> C{Match Filters?}
    C -->|Yes| D[Block Request]
    C -->|No| E[Allow Request]
```