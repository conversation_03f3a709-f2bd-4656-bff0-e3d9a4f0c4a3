const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const path = require('path');

module.exports = [
  new ForkTsCheckerWebpackPlugin({
    logger: 'webpack-infrastructure',
    typescript: {
      configFile: path.resolve(__dirname, 'tsconfig.json'),
    },
  }),
  new HtmlWebpackPlugin({
    template: './src/index.html',
  }),
]; 