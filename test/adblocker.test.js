const path = require('path');
const fs = require('fs');
const https = require('https');
const stream = require('stream');
const EventEmitter = require('events');

const mockApp = {
  getAppPath: jest.fn(() => '/mock/app/path'), // Using a fixed mock path for simplicity in tests
};

jest.mock('electron', () => ({
  ipcMain: {
    on: jest.fn(),
    handle: jest.fn(),
  },
  BrowserWindow: jest.fn(() => ({
    webContents: {
      send: jest.fn(),
    },
    loadFile: jest.fn(),
    show: jest.fn(),
    close: jest.fn(),
  })),
  app: mockApp,
}));

let mockStore = {};
jest.mock('electron-store', () => {
  return jest.fn().mockImplementation(() => {
    return {
      get: jest.fn((key, defaultValue) => mockStore[key] !== undefined ? mockStore[key] : defaultValue),
      set: jest.fn((key, value) => { mockStore[key] = value; }),
      // Mock other methods if needed, e.g., onDidChange, clear, etc.
    };
  });
});

const { compilePattern, isUrlBlocked, addUserRule, removeUserRule, updateMainBlocklistFromRemote, loadMainBlocklistFromFile, getUserRules, initializeAdblockerPaths } = require('../src/adblocker');

describe('Adblocker Functions', () => {
  let httpsGetSpy;

  beforeEach(() => {
    // Reset mockStore before each test
    mockStore = {
      userBlockRules: [],
      mainBlocklistUrl: 'https://easylist.to/easylist/easylist.txt',
      lastUpdated: null,
    };
    // Clear any previous compiled patterns cache
    jest.clearAllMocks();
    httpsGetSpy = jest.spyOn(https, 'get');
    // Mock fs.writeFileSync and store.set globally for relevant tests
    fs.writeFileSync = jest.fn();
    // Assuming 'store' is an instance of the mocked 'electron-store'
    // If 'store' is not globally available or needs specific mock per test, adjust accordingly
    // For now, let's assume it's implicitly available or created by the module under test
    // If 'store' is directly from require('electron-store')(), it's already mocked.
    // If it's a specific instance, it might need more targeted mocking.
  });

  afterEach(() => {
    if (httpsGetSpy) {
      httpsGetSpy.mockRestore();
    }
  });

  describe('compilePattern', () => {
    test('should compile a basic domain pattern', () => {
      const pattern = '||example.com^';
      const regex = compilePattern(pattern);
      expect(regex).toBeInstanceOf(RegExp);
      expect(regex.test('http://example.com/path')).toBe(true);
      expect(regex.test('https://sub.example.com/page')).toBe(true);
      expect(regex.test('http://anotherexample.com')).toBe(false);
    });

    test('should compile an exact address pattern', () => {
      const pattern = '|http://example.com/ad.gif^';
      const regex = compilePattern(pattern);
      expect(regex).toBeInstanceOf(RegExp);
      expect(regex.test('http://example.com/ad.gif')).toBe(true);
      expect(regex.test('https://example.com/ad.gif')).toBe(false);
      expect(regex.test('http://example.com/another.gif')).toBe(false);
    });

    test('should compile a general wildcard pattern', () => {
      const pattern = 'adserver.*.com/ads/*';
      const regex = compilePattern(pattern);
      expect(regex).toBeInstanceOf(RegExp);
      expect(regex.test('http://adserver.test.com/ads/banner.gif')).toBe(true);
      expect(regex.test('https://adserver.another.com/ads/pop.js')).toBe(true);
      expect(regex.test('http://otherserver.com/ads/banner.gif')).toBe(false);
    });

    test('should return null for comment patterns', () => {
      expect(compilePattern('! This is a comment')).toBeNull();
      expect(compilePattern('# This is another comment')).toBeNull();
    });

    test('should handle patterns with ^ as separator', () => {
      const pattern = 'example.com^path';
      const regex = compilePattern(pattern);
      expect(regex).toBeInstanceOf(RegExp);
      expect(regex.test('http://example.com/path')).toBe(true);
      expect(regex.test('http://example.com:8080/path')).toBe(true);
      expect(regex.test('http://example.com/anotherpath')).toBe(false);
    });

    test('should handle patterns starting with ^', () => {
      const pattern = '^ad.js';
      const regex = compilePattern(pattern);
      expect(regex).toBeInstanceOf(RegExp);
      expect(regex.test('http://example.com/ad.js')).toBe(true);
      expect(regex.test('http://example.com/path/ad.js')).toBe(true);
      expect(regex.test('http://example.com/bad.js')).toBe(false);
    });

    test('should handle patterns ending with $', () => {
      const pattern = 'ad.js$';
      const regex = compilePattern(pattern);
      expect(regex).toBeInstanceOf(RegExp);
      expect(regex.test('http://example.com/ad.js')).toBe(true);
      expect(regex.test('http://example.com/ad.js?param=1')).toBe(false);
    });
  });

  describe('addUserRule and removeUserRule', () => {
    test('should add a user rule and recompile patterns', () => {
      addUserRule('||test.com^');
      expect(getUserRules()).toEqual(['||test.com^']);
      // Verify that isUrlBlocked now blocks the added rule
      expect(isUrlBlocked('http://test.com/page')).toBe(true);
    });

    test('should not add duplicate user rules', () => {
      addUserRule('||test.com^');
      addUserRule('||test.com^');
      expect(getUserRules()).toEqual(['||test.com^']);
    });

    test('should remove a user rule and recompile patterns', () => {
      addUserRule('||test.com^');
      expect(getUserRules()).toEqual(['||test.com^']);
      removeUserRule('||test.com^');
      expect(getUserRules()).toEqual([]);
      // Verify that isUrlBlocked no longer blocks the removed rule
      expect(isUrlBlocked('http://test.com/page')).toBe(false);
    });

    test('should not remove a non-existent user rule', () => {
      addUserRule('||test.com^');
      removeUserRule('||nonexistent.com^');
      expect(getUserRules()).toEqual(['||test.com^']);
    });
  });

  describe('loadMainBlocklistFromFile', () => {
    beforeEach(() => {
      initializeAdblockerPaths();
    });
    const mockFilePath = path.join(__dirname, 'mock_blocklist.txt');

    beforeAll(() => {
      // Create a mock blocklist file for testing
      fs.writeFileSync(mockFilePath, `! Comment\n||mockdomain.com^\nexample.org/ads/*`, 'utf-8');
    });

    afterAll(() => {
      // Clean up the mock blocklist file
      fs.unlinkSync(mockFilePath);
    });

    test('should load patterns from a local file', async () => {
      await loadMainBlocklistFromFile(mockFilePath);
      // Check if patterns from the mock file are loaded and compiled
      expect(isUrlBlocked('http://mockdomain.com/page')).toBe(true);
      expect(isUrlBlocked('http://example.org/ads/banner.gif')).toBe(true);
      expect(isUrlBlocked('http://nonexistent.com')).toBe(false);
    });

    test('should handle non-existent local file gracefully', async () => {
      const nonExistentPath = path.join(__dirname, 'non_existent_blocklist.txt');
      await loadMainBlocklistFromFile(nonExistentPath);
      // Should not block anything if file not found and no other rules exist
      expect(isUrlBlocked('http://mockdomain.com')).toBe(false);
    });
  });

  describe('updateMainBlocklistFromRemote', () => {
    beforeEach(() => {
      initializeAdblockerPaths();
    });



    test('should fetch and update blocklist from remote URL', async () => {
      const mockBlocklistContent = `! Remote Comment\n||remote.com^\nremote.net/track/*`;
      const mockResponse = new stream.Readable({ read() {} });
      mockResponse.statusCode = 200;
      mockResponse.push(mockBlocklistContent);
      mockResponse.push(null);

      const mockRequest = new EventEmitter();
      mockRequest.destroy = jest.fn();

      httpsGetSpy.mockImplementationOnce((url, callback) => {
        callback(mockResponse);
        return mockRequest;
      });

      // Mock fs.writeFileSync and store.set
      fs.writeFileSync = jest.fn();
      store.set = jest.fn();

      await updateMainBlocklistFromRemote();

      expect(httpsGetSpy).toHaveBeenCalledWith(
        'https://easylist.to/easylist/easylist.txt',
        expect.any(Function)
      );
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        expect.stringContaining('main_blocklist.txt'),
        mockBlocklistContent,
        'utf-8'
      );
      expect(store.set).toHaveBeenCalledWith('lastUpdated', expect.any(Number));

      // Verify that patterns from the remote file are loaded and compiled
      expect(isUrlBlocked('http://remote.com/page')).toBe(true);
      expect(isUrlBlocked('http://remote.net/track/image.gif')).toBe(true);
      expect(isUrlBlocked('http://nonexistent.com')).toBe(false);
    });

    test('should handle remote fetch errors gracefully', async () => {
      httpsGetSpy.mockImplementationOnce((url, callback) => {
        const req = new EventEmitter();
        callback(req);
        req.emit('error', new Error('Network error'));
        return req;
      });

      await updateMainBlocklistFromRemote();

      expect(httpsGetSpy).toHaveBeenCalledWith(
        'https://easylist.to/easylist/easylist.txt',
        expect.any(Function)
      );
      expect(fs.writeFileSync).not.toHaveBeenCalled();
      expect(store.set).not.toHaveBeenCalledWith('lastUpdated', expect.any(Number));
    });

    test('should handle remote fetch with 404 status gracefully', async () => {
      const mockResponse = new stream.Readable({ read() {} });
      mockResponse.statusCode = 404;
      mockResponse.push(null);

      httpsGetSpy.mockImplementationOnce((url, callback) => {
        callback(mockResponse);
        return new EventEmitter();
      });

      await updateMainBlocklistFromRemote();

      expect(httpsGetSpy).toHaveBeenCalledWith(
        'https://easylist.to/easylist/easylist.txt',
        expect.any(Function)
      );
      expect(fs.writeFileSync).not.toHaveBeenCalled();
      expect(store.set).not.toHaveBeenCalledWith('lastUpdated', expect.any(Number));
    });

    test('should fetch and update blocklist from remote URL', async () => {
      const mockBlocklistContent = `! Remote Comment\n||remote.com^\nremote.net/track/*`;
      const mockResponse = new stream.Readable({ read() {} });
      mockResponse.statusCode = 200;
      mockResponse.push(mockBlocklistContent);
      mockResponse.push(null);

      const mockRequest = new EventEmitter();
      mockRequest.destroy = jest.fn();

      httpsGetSpy.mockImplementationOnce((url, callback) => {
        callback(mockResponse);
        return mockRequest;
      });

      // Mock fs.writeFileSync and store.set
      fs.writeFileSync = jest.fn();
      store.set = jest.fn();

      await updateMainBlocklistFromRemote();

      expect(httpsGetSpy).toHaveBeenCalledWith(
        'https://easylist.to/easylist/easylist.txt',
        expect.any(Function)
      );
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        expect.stringContaining('main_blocklist.txt'),
        mockBlocklistContent,
        'utf-8'
      );
      expect(store.set).toHaveBeenCalledWith('lastUpdated', expect.any(Number));

      // Verify that patterns from the remote file are loaded and compiled
      expect(isUrlBlocked('http://remote.com/page')).toBe(true);
      expect(isUrlBlocked('http://remote.net/track/image.gif')).toBe(true);
      expect(isUrlBlocked('http://nonexistent.com')).toBe(false);
    });

    test('should handle empty remote response gracefully', async () => {
      const mockResponse = new stream.Readable({ read() {} });
      mockResponse.statusCode = 200;
      mockResponse.push(''); // Empty content
      mockResponse.push(null);

      httpsGetSpy.mockImplementationOnce((url, callback) => {
        callback(mockResponse);
        return new EventEmitter();
      });

      fs.writeFileSync = jest.fn();
      store.set = jest.fn();

      await updateMainBlocklistFromRemote();

      expect(httpsGetSpy).toHaveBeenCalled();
      expect(fs.writeFileSync).toHaveBeenCalledWith(
        expect.stringContaining('main_blocklist.txt'),
        '', // Should write empty content
        'utf-8'
      );
      expect(store.set).toHaveBeenCalledWith('lastUpdated', expect.any(Number));
      // Ensure no new rules are blocked if the response was empty
      expect(isUrlBlocked('http://some-random-domain.com')).toBe(false);
    });

    test('should update lastUpdated timestamp in store on successful fetch', async () => {
      const mockBlocklistContent = `! Remote Comment\n||timestamp.com^`;
      const mockResponse = new stream.Readable({ read() {} });
      mockResponse.statusCode = 200;
      mockResponse.push(mockBlocklistContent);
      mockResponse.push(null);

      httpsGetSpy.mockImplementationOnce((url, callback) => {
        callback(mockResponse);
        return new EventEmitter();
      });

      fs.writeFileSync = jest.fn();
      store.set = jest.fn();

      const initialLastUpdated = mockStore.lastUpdated;
      await updateMainBlocklistFromRemote();

      expect(store.set).toHaveBeenCalledWith('lastUpdated', expect.any(Number));
      expect(mockStore.lastUpdated).toBeGreaterThan(initialLastUpdated);
    });
  });

  describe('isUrlBlocked', () => {
    beforeEach(() => {
      initializeAdblockerPaths();
    });
    test('should block URL matching a main blocklist pattern', () => {
      // Assuming loadMainBlocklistFromFile was called in beforeEach or explicitly here
      // For this test, let's manually add a pattern to the compiled list
      // In a real scenario, you'd ensure the blocklist is loaded.
      // For simplicity, we'll rely on the mock setup or add a rule directly if possible.
      // Since compilePattern is exported, we can simulate adding a rule.
      const pattern = '||example.com^';
      const regex = compilePattern(pattern);
      // This is a simplification; in reality, isUrlBlocked uses an internal compiled list.
      // We need to ensure the internal list is populated for this test.
      // Let's assume `loadMainBlocklistFromFile` or `updateMainBlocklistFromRemote`
      // has been called and populated the internal `compiledMainBlocklistPatterns`.
      // For now, we'll test against a known pattern that should be in the default mock blocklist.
      // Or, we can add a rule via addUserRule for testing purposes.
      addUserRule('||blockeddomain.com^');
      expect(isUrlBlocked('http://blockeddomain.com/path')).toBe(true);
    });

    test('should not block URL not matching any pattern', () => {
      expect(isUrlBlocked('http://safedomain.com/path')).toBe(false);
    });

    test('should block URL matching a user rule', () => {
      addUserRule('||userblocked.com^');
      expect(isUrlBlocked('http://userblocked.com/page')).toBe(true);
    });

    test('should not block URL if it matches an exception rule (not implemented yet, but for future)', () => {
      // This test is a placeholder for when exception rules are implemented.
      // For example, @@||example.com^ might unblock a previously blocked domain.
      // expect(isUrlBlocked('http://example.com/unblocked')).toBe(false);
    });

    test('should be case-insensitive for domain matching', () => {
      addUserRule('||CASETEST.com^');
      expect(isUrlBlocked('http://casetest.com/path')).toBe(true);
    });
  });

  describe('initializeAdblockerPaths', () => {
    const { app } = require('electron');
    const adblockerDir = path.join(app.getAppPath(), 'adblocker');

    beforeEach(() => {
      // Ensure the directory does not exist before each test
      if (fs.existsSync(adblockerDir)) {
        fs.rmSync(adblockerDir, { recursive: true, force: true });
      }
      // Mock fs.mkdirSync to spy on its calls
      fs.mkdirSync = jest.fn();
      fs.existsSync = jest.fn(() => false); // Simulate directory not existing by default
    });

    test('should create adblocker directory if it does not exist', () => {
      initializeAdblockerPaths();
      initializeAdblockerPaths();
      expect(fs.mkdirSync).toHaveBeenCalledWith(adblockerDir, { recursive: true });
    });

    test('should not create adblocker directory if it already exists', () => {
      initializeAdblockerPaths();
      fs.existsSync.mockReturnValue(true); // Simulate directory existing
      initializeAdblockerPaths();
      expect(fs.mkdirSync).not.toHaveBeenCalled();
    });
  });
});