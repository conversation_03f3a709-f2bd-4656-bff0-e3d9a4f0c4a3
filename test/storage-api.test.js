import { StorageExtension } from '../examples/extensions/storage-api-example.js';
import { serializer } from '../src/serialization/protobuf-wrapper';
import { applySecurityPolicy } from '../src/security/csp-config';

jest.mock('../src/security/csp-config', () => ({
  applySecurityPolicy: jest.fn()
}));

jest.mock('../src/serialization/protobuf-wrapper', () => ({
  serializer: {
    serialize: jest.fn().mockImplementation(async (data) => JSON.stringify(data)),
    deserialize: jest.fn().mockImplementation(async (data) => JSON.parse(data))
  }
}));

describe('StorageExtension Integration Tests', () => {
  let storage;
  
  beforeEach(() => {
    storage = new StorageExtension();
    jest.clearAllMocks();
  });

  test('should apply security policy on initialization', () => {
    expect(applySecurityPolicy).toHaveBeenCalledWith('development');
  });

  test('should serialize data when setting item', async () => {
    const testData = { key: 'test', value: 'value' };
    await storage.setItem('test', 'value');
    
    expect(serializer.serialize).toHaveBeenCalledWith({
      id: 'storage-ext',
      payload: JSON.stringify(testData),
      metadata: expect.any(Object)
    });
  });

  test('should deserialize data when getting item', async () => {
    await storage.setItem('test', 'value');
    await storage.getItem('test');
    
    expect(serializer.deserialize).toHaveBeenCalledWith(expect.any(String));
  });

  test('should handle concurrent read/write operations', async () => {
    const promises = [
      storage.setItem('concurrent', '1'),
      storage.setItem('concurrent', '2'),
      storage.getItem('concurrent')
    ];
    
    const results = await Promise.all(promises);
    expect(results[2]).toEqual(expect.objectContaining({
      payload: JSON.stringify({ key: 'concurrent', value: '2' })
    }));
  });

  test('should cache serialization schemas', async () => {
    const testStructure = { id: 'test', metadata: { version: 1 } };
    
    await serializer.serialize(testStructure);
    await serializer.serialize(testStructure);
    
    expect(serializer.serialize.mock.calls.length).toBe(2);
    expect(serializer._compileSchema).toHaveBeenCalledTimes(1);
  });

  test('should invalidate cache correctly', async () => {
    const testStructure = { id: 'cache-test', metadata: { version: 2 } };
    
    await serializer.serialize(testStructure);
    serializer.invalidateCache(JSON.stringify(testStructure.structure));
    await serializer.serialize(testStructure);
    
    expect(serializer._compileSchema).toHaveBeenCalledTimes(2);
  });
});