const { strict } = require('assert');
const { SessionManager } = require('../src/main/session/sessionManager');
const { describe, it, beforeEach } = require('mocha');

describe('SessionManager Validation Tests', () => {
    let manager;
    
    beforeEach(() => {
        manager.encryptionKey = 'a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6';

        manager = new SessionManager({ store: { get: () => null, set: () => {} } });
    });

    describe('validateEncryptionKey()', () => {
        it('Должен выбрасывать ошибку при отсутствии ключа', () => {
            strict.throws(() => manager.validateEncryptionKey(), {
                message: 'Ключ шифрования не инициализирован'
            });
        });

        it('Должен выбрасывать ошибку при сохранении без ключа', async () => {
            manager.encryptionKey = undefined;
            await strict.rejects(
                () => manager.saveSession({ version: '1.0', tabs: [] }),
                /Ключ шифрования не инициализирован/
            );
        });

        it('Должен проверять наличие hex-символов в ключе', () => {
            manager.encryptionKey = 'g1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6';
            strict.throws(() => manager.validateEncryptionKey(), 
                /Некорректный формат ключа/
            );
        });

        it('Должен обрабатывать полностью пустую сессию', () => {
            strict.throws(() => manager.validateSessionStructure(null),
                /Структура сессии не соответствует формату/
            );
        });

        it('Должен проверять формат hex-строки 64 символа', () => {
            manager.encryptionKey = 'a1b2c3';
            strict.throws(() => manager.validateEncryptionKey(), 
                /Некорректный формат ключа/
            );
        });
    });

    describe('validateSessionStructure()', () => {
        const validSession = {
            version: '1.0',
            tabs: [{ url: 'https://example.com', title: 'Example' }]
        };

        it('Должен пропускать валидную сессию', () => {
            strict.doesNotThrow(() => manager.validateSessionStructure(validSession));
        });

        it('Должен проверять наличие версии', () => {
            const invalid = { ...validSession, version: undefined };
            strict.throws(() => manager.validateSessionStructure(invalid), 
                /Отсутствует поле version/
            );
        });

        it('Должен проверять тип массива вкладок', () => {
            const invalid = { ...validSession, tabs: {} };
            strict.throws(() => manager.validateSessionStructure(invalid),
                /Поле tabs должно быть массивом/
            );
        });

        it('Должен проверять структуру вкладок', () => {
            const invalid = { ...validSession, tabs: [{ url: null }] };
            strict.throws(() => manager.validateSessionStructure(invalid),
                /отсутствует или некорректен URL/
            );
        });
        });

    describe('Integration Tests', () => {
        it('Должен корректно шифровать и дешифровать сессию', async () => {
            const session = {
                version: '1.0',
                tabs: [{ url: 'https://secure.com', title: 'Secure' }]
            };
            
            const encrypted = await manager.saveSession(session);
            const decrypted = await manager.restoreSession(encrypted);
            
            strict.deepEqual(decrypted, session);
        });

        it('Должен выбрасывать ошибку при несовпадении ключа', async () => {
            const originalSession = { version: '1.0', tabs: [] };
            const encrypted = await manager.saveSession(originalSession);
            
            manager.encryptionKey = 'invalidKey'.repeat(6);
            
            await strict.rejects(
                () => manager.restoreSession(encrypted),
                /Ошибка дешифрования/
            );
        });
        it('Должен выбрасывать ошибку при повреждённых данных сессии', async () => {
            const session = { version: '1.0', tabs: [] };
            const encrypted = await manager.saveSession(session);
            const corrupted = encrypted.slice(0, -10) + '0000';
            
            await strict.rejects(
                () => manager.restoreSession(corrupted),
                /Ошибка дешифрования/
            );
        });

        it('Должен обрабатывать параллельные вызовы методов', async () => {
            const session = { version: '1.0', tabs: [] };
            
            await Promise.all([
                manager.saveSession(session),
                manager.restoreSession(await manager.saveSession(session)),
                manager.validateSessionStructure(session)
            ]);
        });

        it('Должен логировать ошибки при невалидных данных', async () => {
            const originalError = console.error;
            const errors = [];
            console.error = (msg) => errors.push(msg);
            
            try {
                await manager.restoreSession('invalid_data');
            } catch {} 
            
            strict.ok(errors.some(e => e.includes('Ошибка дешифрования')));
            console.error = originalError;
        });

        it('Должен обрабатывать устаревший формат сессии', async () => {
            const oldSession = { version: '0.9', pages: [{ url: 'http://old' }] };
            await strict.rejects(
                () => manager.saveSession(oldSession),
                /Структура сессии не соответствует формату/
            );
        });
        it('Должен восстанавливать сессию через новый экземпляр менеджера', async () => {
            const session = {
                version: '1.0',
                tabs: [{ url: 'https://restart.com', title: 'Restart Test' }]
            };
            
            const encrypted = await manager.saveSession(session);
            
            // Создаем новый менеджер с теми же настройками
            const newManager = new SessionManager({
                store: manager.store,
                encryptionKey: manager.encryptionKey
            });
            
            const restored = await newManager.restoreSession(encrypted);
            strict.deepEqual(restored, session);
        });
});