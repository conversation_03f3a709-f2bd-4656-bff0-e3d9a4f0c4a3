{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["id", "name", "version", "description", "author", "compatibility", "license"], "properties": {"id": {"type": "string", "pattern": "^[a-z0-9-]+$", "description": "Unique identifier for the extension"}, "name": {"type": "string", "minLength": 1, "maxLength": 50, "description": "Display name of the extension"}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Version number in semantic versioning format"}, "description": {"type": "string", "minLength": 10, "maxLength": 500, "description": "Detailed description of the extension"}, "author": {"type": "object", "required": ["name", "email"], "properties": {"name": {"type": "string", "minLength": 1}, "email": {"type": "string", "format": "email"}, "website": {"type": "string", "format": "uri"}}}, "compatibility": {"type": "object", "required": ["minVersion"], "properties": {"minVersion": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "maxVersion": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}}}, "license": {"type": "string", "enum": ["MIT", "Apache-2.0", "GPL-3.0", "BSD-3-<PERSON><PERSON>", "ISC", "UNLICENSED"]}, "permissions": {"type": "array", "items": {"type": "string", "enum": ["storage", "tabs", "bookmarks", "history", "downloads", "notifications", "webNavigation", "webRequest", "cookies", "geolocation", "clipboardRead", "clipboardWrite"]}}, "hostPermissions": {"type": "array", "items": {"type": "string", "format": "uri"}}, "background": {"type": "object", "properties": {"serviceWorker": {"type": "string"}, "type": {"type": "string", "enum": ["module", "classic"]}}}, "contentScripts": {"type": "array", "items": {"type": "object", "required": ["matches", "js"], "properties": {"matches": {"type": "array", "items": {"type": "string", "format": "uri"}}, "excludeMatches": {"type": "array", "items": {"type": "string", "format": "uri"}}, "js": {"type": "array", "items": {"type": "string"}}, "css": {"type": "array", "items": {"type": "string"}}, "runAt": {"type": "string", "enum": ["document_start", "document_end", "document_idle"]}}}}, "webAccessibleResources": {"type": "array", "items": {"type": "string"}}, "icons": {"type": "object", "required": ["16", "48", "128"], "properties": {"16": {"type": "string"}, "48": {"type": "string"}, "128": {"type": "string"}}}, "action": {"type": "object", "properties": {"defaultIcon": {"type": "object", "required": ["16", "48", "128"], "properties": {"16": {"type": "string"}, "48": {"type": "string"}, "128": {"type": "string"}}}, "defaultTitle": {"type": "string"}, "defaultPopup": {"type": "string"}}}, "optionsPage": {"type": "string"}, "minimumChromeVersion": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "updateUrl": {"type": "string", "format": "uri"}, "homepageUrl": {"type": "string", "format": "uri"}, "privacyPolicyUrl": {"type": "string", "format": "uri"}, "contentSecurityPolicy": {"type": "string"}}}