import { Middleware, AnyAction, Dispatch } from 'redux';
import { updateMetrics, addAlert } from '../slices/performanceSlice';
import { PerformanceMonitor } from '../../utils/performance/PerformanceMonitor';
import { PerformanceOptimizer } from '../../utils/performance/PerformanceOptimizer';

// Define RootState type here since we can't import it
interface RootState {
  performance: {
    settings: {
      monitoring: {
        enabled: boolean;
        interval: number;
      };
    };
  };
}

const monitor = PerformanceMonitor.getInstance();
const optimizer = PerformanceOptimizer.getInstance();

export const performanceMiddleware: Middleware<Dispatch, RootState> = (store) => {
  let monitoringInterval: NodeJS.Timeout | null = null;

  return (next) => (action: unknown) => {
    const result = next(action);

    if (typeof action === 'object' && action !== null && 'type' in action) {
      switch (action.type) {
        case 'performance/updateSettings': {
          const { monitoring } = store.getState().performance.settings;

          // Handle monitoring interval changes
          if (monitoringInterval) {
            clearInterval(monitoringInterval);
            monitoringInterval = null;
          }

          if (monitoring.enabled) {
            monitoringInterval = setInterval(() => {
              const rawMetrics = monitor.getMetrics();
              // Transform metrics to match our state structure
              const metrics = {
                fps: rawMetrics.renderer.fps,
                memory: {
                  used: rawMetrics.memory.heapUsed,
                  total: rawMetrics.memory.heapTotal,
                },
                cpu: {
                  usage: rawMetrics.cpu.user + rawMetrics.cpu.system,
                  temperature: 0, // Not available in raw metrics
                },
                network: {
                  download: rawMetrics.network.bytesReceived,
                  upload: rawMetrics.network.bytesSent,
                  latency: rawMetrics.network.latency,
                },
                render: {
                  timeToFirstByte: 0, // Not available in raw metrics
                  firstContentfulPaint: 0, // Not available in raw metrics
                  largestContentfulPaint: 0, // Not available in raw metrics
                  timeToInteractive: 0, // Not available in raw metrics
                },
                errors: {
                  count: 0, // Not available in raw metrics
                  lastError: null, // Not available in raw metrics
                },
              };
              
              store.dispatch(updateMetrics(metrics));

              // Check for performance issues
              if (metrics.fps < 30) {
                store.dispatch(addAlert({
                  type: 'warning',
                  message: 'Low FPS detected. Consider closing some tabs or applications.',
                }));
              }

              if (metrics.memory.used > metrics.memory.total * 0.9) {
                store.dispatch(addAlert({
                  type: 'error',
                  message: 'High memory usage detected. Running memory optimization...',
                }));
                // Use public method instead of private
                optimizer.optimize();
              }

              if (metrics.cpu.usage > 90) {
                store.dispatch(addAlert({
                  type: 'warning',
                  message: 'High CPU usage detected. Consider closing resource-intensive tabs.',
                }));
              }

              if (metrics.network.latency > 1000) {
                store.dispatch(addAlert({
                  type: 'warning',
                  message: 'High network latency detected. Check your internet connection.',
                }));
              }
            }, monitoring.interval);
          }
          break;
        }

        case 'performance/resetMetrics': {
          if (monitoringInterval) {
            clearInterval(monitoringInterval);
            monitoringInterval = null;
          }
          break;
        }
      }
    }

    return result;
  };
}; 