import { Middleware } from 'redux';
import { SecurityManager, SecurityConfig } from '../../security/SecurityManager';
import { updateConfig, updateStatus, addAlert } from '../slices/securitySlice';

export const securityMiddleware: Middleware = (store) => (next) => (action) => {
  // Type guard for action type
  if (typeof action !== 'object' || action === null || !('type' in action)) {
    return next(action);
  }

  const securityManager = SecurityManager.getInstance();

  // Handle security-related actions
  switch (action.type) {
    case 'security/updateConfig': {
      if ('payload' in action) {
        try {
          // Update security manager with new config
          securityManager.updateConfig(action.payload as Partial<SecurityConfig>);

          // Get security status (Map<string, boolean>) and convert to status object
          const statusMap = securityManager.getSecurityStatus();
          const statusObj: Record<string, 'secure' | 'warning' | 'critical'> = {};
          statusMap.forEach((value, key) => {
            statusObj[key] = value ? 'secure' : 'critical';
          });
          store.dispatch(updateStatus(statusObj));
        } catch (error) {
          console.error('Failed to update security config:', error);
          store.dispatch(addAlert({
            type: 'error',
            message: 'Failed to update security settings',
          }));
        }
      }
      break;
    }

    case 'security/resetConfig': {
      try {
        // Reset to default config by updating with an empty object
        securityManager.updateConfig({});
        const defaultConfig = securityManager.getConfig();
        store.dispatch(updateConfig(defaultConfig));
        const statusMap = securityManager.getSecurityStatus();
        const statusObj: Record<string, 'secure' | 'warning' | 'critical'> = {};
        statusMap.forEach((value, key) => {
          statusObj[key] = value ? 'secure' : 'critical';
        });
        store.dispatch(updateStatus(statusObj));
      } catch (error) {
        console.error('Failed to reset security config:', error);
        store.dispatch(addAlert({
          type: 'error',
          message: 'Failed to reset security settings',
        }));
      }
      break;
    }
  }

  return next(action);
}; 