import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../index';

interface ThemeSettings {
  mode: 'light' | 'dark' | 'system';
  primaryColor: string;
  secondaryColor: string;
  fontSize: number;
  fontFamily: string;
  animations: boolean;
  reducedMotion: boolean;
  highContrast: boolean;
}

interface PrivacySettings {
  doNotTrack: boolean;
  cookies: 'accept' | 'block' | 'ask';
  thirdPartyCookies: boolean;
  trackingProtection: boolean;
  adBlocker: boolean;
  fingerprintingProtection: boolean;
  webRTCProtection: boolean;
  locationServices: boolean;
  notifications: boolean;
  clipboardAccess: boolean;
  cameraAccess: boolean;
  microphoneAccess: boolean;
}

interface SecuritySettings {
  passwordManager: boolean;
  autoFill: boolean;
  safeBrowsing: boolean;
  phishingProtection: boolean;
  malwareProtection: boolean;
  httpsOnly: boolean;
  certificateValidation: boolean;
  contentBlocking: boolean;
  scriptBlocking: boolean;
  popupBlocking: boolean;
}

interface PerformanceSettings {
  hardwareAcceleration: boolean;
  processPerTab: boolean;
  backgroundThrottling: boolean;
  cacheSize: number;
  diskCache: boolean;
  memoryCache: boolean;
  preloadPages: boolean;
  predictiveLoading: boolean;
}

interface SyncSettings {
  enabled: boolean;
  bookmarks: boolean;
  history: boolean;
  passwords: boolean;
  extensions: boolean;
  settings: boolean;
  openTabs: boolean;
  lastSync: number | null;
}

interface SearchSettings {
  defaultEngine: string;
  customEngines: Array<{
    name: string;
    url: string;
    icon?: string;
  }>;
  searchSuggestions: boolean;
  instantSearch: boolean;
  searchHistory: boolean;
}

interface DownloadSettings {
  defaultLocation: string;
  askForLocation: boolean;
  autoOpen: boolean;
  autoClose: boolean;
  showNotifications: boolean;
  maxConcurrent: number;
  retryFailed: boolean;
}

interface AccessibilitySettings {
  screenReader: boolean;
  zoomLevel: number;
  textSize: number;
  lineSpacing: number;
  letterSpacing: number;
  wordSpacing: number;
  colorBlindMode: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
  dyslexiaFont: boolean;
  cursorSize: number;
  cursorBlink: boolean;
  highlightLinks: boolean;
  highlightFocus: boolean;
}

interface SettingsState {
  theme: ThemeSettings;
  privacy: PrivacySettings;
  security: SecuritySettings;
  performance: PerformanceSettings;
  sync: SyncSettings;
  search: SearchSettings;
  downloads: DownloadSettings;
  accessibility: AccessibilitySettings;
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  measurementSystem: 'metric' | 'imperial';
  startupBehavior: 'restore' | 'new' | 'blank';
  defaultBrowser: boolean;
  updates: {
    autoCheck: boolean;
    autoDownload: boolean;
    autoInstall: boolean;
    channel: 'stable' | 'beta' | 'dev';
  };
  enableJavaScript: boolean;
  enableCookies: boolean;
  enableNotifications: boolean;
  enableLocation: boolean;
  enableCamera: boolean;
  enableMicrophone: boolean;
  enablePopups: boolean;
  enableImages: boolean;
  enablePlugins: boolean;
  enableWebGL: boolean;
  enableWebRTC: boolean;
  enableWebAssembly: boolean;
  enableWebSecurity: boolean;
  enableFileSystem: boolean;
}

const initialState: SettingsState = {
  theme: {
    mode: 'system',
    primaryColor: '#2196f3',
    secondaryColor: '#f50057',
    fontSize: 16,
    fontFamily: 'system-ui',
    animations: true,
    reducedMotion: false,
    highContrast: false,
  },
  privacy: {
    doNotTrack: true,
    cookies: 'accept',
    thirdPartyCookies: false,
    trackingProtection: true,
    adBlocker: true,
    fingerprintingProtection: true,
    webRTCProtection: true,
    locationServices: false,
    notifications: false,
    clipboardAccess: false,
    cameraAccess: false,
    microphoneAccess: false,
  },
  security: {
    passwordManager: true,
    autoFill: true,
    safeBrowsing: true,
    phishingProtection: true,
    malwareProtection: true,
    httpsOnly: true,
    certificateValidation: true,
    contentBlocking: true,
    scriptBlocking: false,
    popupBlocking: true,
  },
  performance: {
    hardwareAcceleration: true,
    processPerTab: true,
    backgroundThrottling: true,
    cacheSize: 1024,
    diskCache: true,
    memoryCache: true,
    preloadPages: true,
    predictiveLoading: true,
  },
  sync: {
    enabled: false,
    bookmarks: true,
    history: true,
    passwords: true,
    extensions: true,
    settings: true,
    openTabs: true,
    lastSync: null,
  },
  search: {
    defaultEngine: 'google',
    customEngines: [],
    searchSuggestions: true,
    instantSearch: true,
    searchHistory: true,
  },
  downloads: {
    defaultLocation: '',
    askForLocation: true,
    autoOpen: false,
    autoClose: false,
    showNotifications: true,
    maxConcurrent: 3,
    retryFailed: true,
  },
  accessibility: {
    screenReader: false,
    zoomLevel: 100,
    textSize: 16,
    lineSpacing: 1.5,
    letterSpacing: 0,
    wordSpacing: 0,
    colorBlindMode: 'none',
    dyslexiaFont: false,
    cursorSize: 1,
    cursorBlink: true,
    highlightLinks: true,
    highlightFocus: true,
  },
  language: 'en',
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  dateFormat: 'MM/DD/YYYY',
  timeFormat: '12h',
  measurementSystem: 'metric',
  startupBehavior: 'restore',
  defaultBrowser: false,
  updates: {
    autoCheck: true,
    autoDownload: true,
    autoInstall: false,
    channel: 'stable',
  },
  enableJavaScript: true,
  enableCookies: true,
  enableNotifications: false,
  enableLocation: false,
  enableCamera: false,
  enableMicrophone: false,
  enablePopups: false,
  enableImages: true,
  enablePlugins: true,
  enableWebGL: true,
  enableWebRTC: true,
  enableWebAssembly: true,
  enableWebSecurity: true,
  enableFileSystem: false,
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<Partial<ThemeSettings>>) => {
      state.theme = { ...state.theme, ...action.payload };
    },
    setPrivacy: (state, action: PayloadAction<Partial<PrivacySettings>>) => {
      state.privacy = { ...state.privacy, ...action.payload };
    },
    setSecurity: (state, action: PayloadAction<Partial<SecuritySettings>>) => {
      state.security = { ...state.security, ...action.payload };
    },
    setPerformance: (state, action: PayloadAction<Partial<PerformanceSettings>>) => {
      state.performance = { ...state.performance, ...action.payload };
    },
    setSync: (state, action: PayloadAction<Partial<SyncSettings>>) => {
      state.sync = { ...state.sync, ...action.payload };
    },
    setSearch: (state, action: PayloadAction<Partial<SearchSettings>>) => {
      state.search = { ...state.search, ...action.payload };
    },
    setDownloads: (state, action: PayloadAction<Partial<DownloadSettings>>) => {
      state.downloads = { ...state.downloads, ...action.payload };
    },
    setAccessibility: (state, action: PayloadAction<Partial<AccessibilitySettings>>) => {
      state.accessibility = { ...state.accessibility, ...action.payload };
    },
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
    },
    setTimezone: (state, action: PayloadAction<string>) => {
      state.timezone = action.payload;
    },
    setDateFormat: (state, action: PayloadAction<string>) => {
      state.dateFormat = action.payload;
    },
    setTimeFormat: (state, action: PayloadAction<string>) => {
      state.timeFormat = action.payload;
    },
    setMeasurementSystem: (state, action: PayloadAction<'metric' | 'imperial'>) => {
      state.measurementSystem = action.payload;
    },
    setStartupBehavior: (state, action: PayloadAction<'restore' | 'new' | 'blank'>) => {
      state.startupBehavior = action.payload;
    },
    setDefaultBrowser: (state, action: PayloadAction<boolean>) => {
      state.defaultBrowser = action.payload;
    },
    setUpdates: (state, action: PayloadAction<Partial<SettingsState['updates']>>) => {
      state.updates = { ...state.updates, ...action.payload };
    },
    updateSettings: (state, action: PayloadAction<Partial<SettingsState>>) => {
      return { ...state, ...action.payload };
    },
    resetSettings: () => initialState,
  },
});

// Selectors
export const selectSettings = (state: RootState) => state.settings;
export const selectTheme = (state: RootState) => state.settings.theme;
export const selectPrivacy = (state: RootState) => state.settings.privacy;
export const selectSecurity = (state: RootState) => state.settings.security;
export const selectPerformance = (state: RootState) => state.settings.performance;
export const selectSync = (state: RootState) => state.settings.sync;
export const selectSearch = (state: RootState) => state.settings.search;
export const selectDownloads = (state: RootState) => state.settings.downloads;
export const selectAccessibility = (state: RootState) => state.settings.accessibility;
export const selectLanguage = (state: RootState) => state.settings.language;
export const selectTimezone = (state: RootState) => state.settings.timezone;
export const selectDateFormat = (state: RootState) => state.settings.dateFormat;
export const selectTimeFormat = (state: RootState) => state.settings.timeFormat;
export const selectMeasurementSystem = (state: RootState) => state.settings.measurementSystem;
export const selectStartupBehavior = (state: RootState) => state.settings.startupBehavior;
export const selectDefaultBrowser = (state: RootState) => state.settings.defaultBrowser;
export const selectUpdates = (state: RootState) => state.settings.updates;

// Actions
export const {
  setTheme,
  setPrivacy,
  setSecurity,
  setPerformance,
  setSync,
  setSearch,
  setDownloads,
  setAccessibility,
  setLanguage,
  setTimezone,
  setDateFormat,
  setTimeFormat,
  setMeasurementSystem,
  setStartupBehavior,
  setDefaultBrowser,
  setUpdates,
  updateSettings,
  resetSettings,
} = settingsSlice.actions;

export default settingsSlice.reducer; 