import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../index';

export interface HistoryEntry {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  visitCount: number;
  lastVisitTime: number;
  firstVisitTime: number;
  typedCount: number;
  isTyped: boolean;
  isBookmarked: boolean;
  referrer?: string;
  transitionType: 'link' | 'typed' | 'auto_bookmark' | 'auto_subframe' | 'manual_subframe' | 'generated' | 'start_page' | 'form_submit' | 'reload' | 'keyword' | 'keyword_generated';
  deviceType: 'desktop' | 'mobile' | 'tablet';
  browserVersion: string;
  osVersion: string;
  tags: string[];
  notes?: string;
}

interface HistoryState {
  entries: HistoryEntry[];
  searchHistory: string[];
  lastSync: number | null;
  isSyncing: boolean;
  syncError: string | null;
  filters: {
    dateRange: {
      start: number | null;
      end: number | null;
    };
    searchTerm: string;
    tags: string[];
    deviceTypes: string[];
    transitionTypes: string[];
  };
  view: {
    groupBy: 'day' | 'week' | 'month' | 'none';
    sortBy: 'lastVisitTime' | 'visitCount' | 'title';
    sortOrder: 'asc' | 'desc';
    page: number;
    pageSize: number;
  };
  isSearching: boolean;
  searchQuery: string;
}

const initialState: HistoryState = {
  entries: [],
  searchHistory: [],
  lastSync: null,
  isSyncing: false,
  syncError: null,
  filters: {
    dateRange: {
      start: null,
      end: null,
    },
    searchTerm: '',
    tags: [],
    deviceTypes: [],
    transitionTypes: [],
  },
  view: {
    groupBy: 'day',
    sortBy: 'lastVisitTime',
    sortOrder: 'desc',
    page: 1,
    pageSize: 50,
  },
  isSearching: false,
  searchQuery: '',
};

const historySlice = createSlice({
  name: 'history',
  initialState,
  reducers: {
    addEntry: (state, action: PayloadAction<Omit<HistoryEntry, 'id' | 'visitCount' | 'firstVisitTime' | 'lastVisitTime'>>) => {
      const existingEntry = state.entries.find(entry => entry.url === action.payload.url);
      const now = Date.now();

      if (existingEntry) {
        existingEntry.visitCount += 1;
        existingEntry.lastVisitTime = now;
        existingEntry.title = action.payload.title;
        existingEntry.favicon = action.payload.favicon;
      } else {
        const newEntry: HistoryEntry = {
          ...action.payload,
          id: crypto.randomUUID(),
          visitCount: 1,
          firstVisitTime: now,
          lastVisitTime: now,
        };
        state.entries.push(newEntry);
      }
    },
    removeEntry: (state, action: PayloadAction<string>) => {
      state.entries = state.entries.filter(entry => entry.id !== action.payload);
    },
    clearHistory: (state) => {
      state.entries = [];
    },
    updateEntry: (state, action: PayloadAction<{ id: string; updates: Partial<HistoryEntry> }>) => {
      const entry = state.entries.find(entry => entry.id === action.payload.id);
      if (entry) {
        Object.assign(entry, action.payload.updates);
      }
    },
    setSearching: (state, action: PayloadAction<boolean>) => {
      state.isSearching = action.payload;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    addSearchHistory: (state, action: PayloadAction<string>) => {
      state.searchHistory.unshift(action.payload);
      if (state.searchHistory.length > 50) {
        state.searchHistory.pop();
      }
    },
    clearSearchHistory: (state) => {
      state.searchHistory = [];
    },
    setFilters: (state, action: PayloadAction<Partial<HistoryState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
      state.view.page = 1; // Reset to first page when filters change
    },
    setView: (state, action: PayloadAction<Partial<HistoryState['view']>>) => {
      state.view = { ...state.view, ...action.payload };
    },
    setSyncStatus: (state, action: PayloadAction<{ isSyncing: boolean; error?: string }>) => {
      state.isSyncing = action.payload.isSyncing;
      state.syncError = action.payload.error || null;
      if (!action.payload.isSyncing && !action.payload.error) {
        state.lastSync = Date.now();
      }
    },
    importHistory: (state, action: PayloadAction<HistoryEntry[]>) => {
      state.entries = [...action.payload, ...state.entries];
    },
    exportHistory: (state) => {
      // This is handled by a selector
    },
  },
});

// Selectors
export const selectHistory = (state: RootState) => state.history.entries;
export const selectSearchHistory = (state: RootState) => state.history.searchHistory;
export const selectFilters = (state: RootState) => state.history.filters;
export const selectView = (state: RootState) => state.history.view;
export const selectSyncStatus = (state: RootState) => ({
  isSyncing: state.history.isSyncing,
  lastSync: state.history.lastSync,
  error: state.history.syncError,
});

// Helper selectors
export const selectFilteredHistory = (state: RootState) => {
  let filtered = [...state.history.entries];
  const { filters } = state.history;

  // Apply date range filter
  if (filters.dateRange.start) {
    filtered = filtered.filter(entry => entry.lastVisitTime >= filters.dateRange.start!);
  }
  if (filters.dateRange.end) {
    filtered = filtered.filter(entry => entry.lastVisitTime <= filters.dateRange.end!);
  }

  // Apply search term filter
  if (filters.searchTerm) {
    const searchLower = filters.searchTerm.toLowerCase();
    filtered = filtered.filter(entry =>
      entry.url.toLowerCase().includes(searchLower) ||
      entry.title.toLowerCase().includes(searchLower)
    );
  }

  // Apply tags filter
  if (filters.tags.length > 0) {
    filtered = filtered.filter(entry =>
      filters.tags.every((tag: string) => entry.tags.includes(tag))
    );
  }

  // Apply device types filter
  if (filters.deviceTypes.length > 0) {
    filtered = filtered.filter(entry =>
      filters.deviceTypes.includes(entry.deviceType)
    );
  }

  // Apply transition types filter
  if (filters.transitionTypes.length > 0) {
    filtered = filtered.filter(entry =>
      filters.transitionTypes.includes(entry.transitionType)
    );
  }

  return filtered;
};

export const selectGroupedHistory = (state: RootState) => {
  const filtered = selectFilteredHistory(state);
  const { groupBy, sortBy, sortOrder } = state.history.view;

  // Sort entries
  filtered.sort((a, b) => {
    let comparison = 0;
    switch (sortBy) {
      case 'title':
        comparison = a.title.localeCompare(b.title);
        break;
      case 'lastVisitTime':
      case 'visitCount':
        comparison = (a[sortBy] as number) - (b[sortBy] as number);
        break;
      default:
        comparison = 0;
    }
    return sortOrder === 'asc' ? comparison : -comparison;
  });

  // Group entries
  if (groupBy === 'none') {
    return { 'All': filtered };
  }

  const groups: Record<string, HistoryEntry[]> = {};
  filtered.forEach(entry => {
    const date = new Date(entry.lastVisitTime);
    let groupKey: string;

    switch (groupBy) {
      case 'day':
        groupKey = date.toLocaleDateString();
        break;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        groupKey = weekStart.toLocaleDateString();
        break;
      case 'month':
        groupKey = `${date.getFullYear()}-${date.getMonth() + 1}`;
        break;
      default:
        groupKey = 'All';
    }

    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(entry);
  });

  return groups;
};

// Actions
export const {
  addEntry,
  removeEntry,
  clearHistory,
  updateEntry,
  setSearching,
  setSearchQuery,
  addSearchHistory,
  clearSearchHistory,
  setFilters,
  setView,
  setSyncStatus,
  importHistory,
  exportHistory,
} = historySlice.actions;

export default historySlice.reducer; 