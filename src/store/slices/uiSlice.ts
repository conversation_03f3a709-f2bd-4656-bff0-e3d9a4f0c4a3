import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UiState {
  sidebarOpen: boolean;
  theme: 'light' | 'dark' | 'system';
  isFullscreen: boolean;
  zoomLevel: number;
  showBookmarksBar: boolean;
  showStatusBar: boolean;
  showTabBar: boolean;
  showAddressBar: boolean;
  showToolbar: boolean;
}

const initialState: UiState = {
  sidebarOpen: true,
  theme: 'system',
  isFullscreen: false,
  zoomLevel: 100,
  showBookmarksBar: true,
  showStatusBar: true,
  showTabBar: true,
  showAddressBar: true,
  showToolbar: true,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload;
    },
    toggleFullscreen: (state) => {
      state.isFullscreen = !state.isFullscreen;
    },
    setZoomLevel: (state, action: PayloadAction<number>) => {
      state.zoomLevel = action.payload;
    },
    toggleBookmarksBar: (state) => {
      state.showBookmarksBar = !state.showBookmarksBar;
    },
    toggleStatusBar: (state) => {
      state.showStatusBar = !state.showStatusBar;
    },
    toggleTabBar: (state) => {
      state.showTabBar = !state.showTabBar;
    },
    toggleAddressBar: (state) => {
      state.showAddressBar = !state.showAddressBar;
    },
    toggleToolbar: (state) => {
      state.showToolbar = !state.showToolbar;
    },
  },
});

export const {
  toggleSidebar,
  setTheme,
  toggleFullscreen,
  setZoomLevel,
  toggleBookmarksBar,
  toggleStatusBar,
  toggleTabBar,
  toggleAddressBar,
  toggleToolbar,
} = uiSlice.actions;

export default uiSlice.reducer; 