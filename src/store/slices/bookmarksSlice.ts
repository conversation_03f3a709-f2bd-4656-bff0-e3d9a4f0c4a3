import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../index';

export interface Bookmark {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  description?: string;
  tags: string[];
  folderId: string | null;
  createdAt: number;
  updatedAt: number;
  lastAccessed: number;
  visitCount: number;
  isStarred: boolean;
  isPinned: boolean;
  customIcon?: string;
  notes?: string;
  color?: string;
  parentId: string | null;
}

export interface BookmarkFolder {
  id: string;
  name: string;
  parentId: string | null;
  description?: string;
  color?: string;
  icon?: string;
  createdAt: number;
  updatedAt: number;
  order: number;
  isExpanded: boolean;
  isSystem: boolean;
}

interface BookmarksState {
  bookmarks: Bookmark[];
  folders: { [key: string]: string }; // id -> name mapping
  tags: string[];
  searchHistory: string[];
  lastSync: number | null;
  isSyncing: boolean;
  syncError: string | null;
}

const defaultFolders: BookmarkFolder[] = [
  {
    id: 'root',
    name: 'Root',
    parentId: null,
    description: 'Root folder',
    createdAt: Date.now(),
    updatedAt: Date.now(),
    order: 0,
    isExpanded: true,
    isSystem: true,
  },
  {
    id: 'bookmarks-bar',
    name: 'Bookmarks Bar',
    parentId: 'root',
    description: 'Bookmarks shown in the bookmarks bar',
    createdAt: Date.now(),
    updatedAt: Date.now(),
    order: 1,
    isExpanded: true,
    isSystem: true,
  },
  {
    id: 'other',
    name: 'Other Bookmarks',
    parentId: 'root',
    description: 'Bookmarks not shown in the bookmarks bar',
    createdAt: Date.now(),
    updatedAt: Date.now(),
    order: 2,
    isExpanded: true,
    isSystem: true,
  },
];

const initialState: BookmarksState = {
  bookmarks: [],
  folders: {
    root: 'Bookmarks',
  },
  tags: [],
  searchHistory: [],
  lastSync: null,
  isSyncing: false,
  syncError: null,
};

const bookmarksSlice = createSlice({
  name: 'bookmarks',
  initialState,
  reducers: {
    addBookmark: (state, action: PayloadAction<Omit<Bookmark, 'id' | 'createdAt' | 'lastAccessed'>>) => {
      const newBookmark: Bookmark = {
        ...action.payload,
        id: crypto.randomUUID(),
        createdAt: Date.now(),
        lastAccessed: Date.now(),
      };
      state.bookmarks.push(newBookmark);
    },
    removeBookmark: (state, action: PayloadAction<string>) => {
      state.bookmarks = state.bookmarks.filter(bookmark => bookmark.id !== action.payload);
    },
    updateBookmark: (state, action: PayloadAction<{ id: string; updates: Partial<Bookmark> }>) => {
      const bookmark = state.bookmarks.find(bookmark => bookmark.id === action.payload.id);
      if (bookmark) {
        Object.assign(bookmark, action.payload.updates);
        bookmark.lastAccessed = Date.now();
      }
    },
    moveBookmark: (state, action: PayloadAction<{ id: string; newParentId: string | null }>) => {
      const bookmark = state.bookmarks.find(bookmark => bookmark.id === action.payload.id);
      if (bookmark) {
        bookmark.parentId = action.payload.newParentId;
        bookmark.lastAccessed = Date.now();
      }
    },
    addFolder: (state, action: PayloadAction<{ id: string; name: string }>) => {
      state.folders[action.payload.id] = action.payload.name;
    },
    removeFolder: (state, action: PayloadAction<string>) => {
      delete state.folders[action.payload];
      // Move bookmarks from deleted folder to root
        state.bookmarks.forEach(bookmark => {
        if (bookmark.parentId === action.payload) {
          bookmark.parentId = 'root';
          }
        });
    },
    updateFolder: (state, action: PayloadAction<{ id: string; name: string }>) => {
      if (state.folders[action.payload.id]) {
        state.folders[action.payload.id] = action.payload.name;
      }
    },
    addTag: (state, action: PayloadAction<string>) => {
      if (!state.tags.includes(action.payload)) {
        state.tags.push(action.payload);
      }
    },
    removeTag: (state, action: PayloadAction<string>) => {
      state.tags = state.tags.filter(tag => tag !== action.payload);
      // Remove tag from all bookmarks
      state.bookmarks.forEach(bookmark => {
        bookmark.tags = bookmark.tags.filter(tag => tag !== action.payload);
      });
    },
    addSearchHistory: (state, action: PayloadAction<string>) => {
      state.searchHistory.unshift(action.payload);
      if (state.searchHistory.length > 50) {
        state.searchHistory.pop();
      }
    },
    clearSearchHistory: (state) => {
      state.searchHistory = [];
    },
    setSyncStatus: (state, action: PayloadAction<{ isSyncing: boolean; error?: string }>) => {
      state.isSyncing = action.payload.isSyncing;
      state.syncError = action.payload.error || null;
      if (!action.payload.isSyncing && !action.payload.error) {
        state.lastSync = Date.now();
      }
    },
    importBookmarks: (state, action: PayloadAction<{ bookmarks: Bookmark[]; folders: BookmarkFolder[] }>) => {
      state.bookmarks = action.payload.bookmarks;
      state.folders = { ...state.folders, ...action.payload.folders.reduce((acc, folder) => ({ ...acc, [folder.id]: folder.name }), {}) };
    },
    exportBookmarks: (state) => {
      // This is handled by a selector
    },
  },
});

// Selectors
export const selectBookmarks = (state: RootState) => state.bookmarks.bookmarks;
export const selectFolders = (state: RootState) => state.bookmarks.folders;
export const selectTags = (state: RootState) => state.bookmarks.tags;
export const selectSearchHistory = (state: RootState) => state.bookmarks.searchHistory;
export const selectSyncStatus = (state: RootState) => ({
  isSyncing: state.bookmarks.isSyncing,
  lastSync: state.bookmarks.lastSync,
  error: state.bookmarks.syncError,
});

// Helper selectors
export const selectBookmarksByFolder = (state: RootState, folderId: string | null) =>
  state.bookmarks.bookmarks.filter(b => b.folderId === folderId);

export const selectBookmarksByTag = (state: RootState, tag: string) =>
  state.bookmarks.bookmarks.filter(b => b.tags.includes(tag));

export const selectFolderPath = (state: RootState, folderId: string) => {
  const path: BookmarkFolder[] = [];
  let currentId = folderId;
  
  while (currentId) {
    const folder = state.bookmarks.folders.find(f => f.id === currentId);
    if (folder) {
      path.unshift(folder);
      currentId = folder.parentId;
    } else {
      break;
    }
  }
  
  return path;
};

// Actions
export const {
  addBookmark,
  removeBookmark,
  updateBookmark,
  moveBookmark,
  addFolder,
  removeFolder,
  updateFolder,
  addTag,
  removeTag,
  addSearchHistory,
  clearSearchHistory,
  setSyncStatus,
  importBookmarks,
  exportBookmarks,
} = bookmarksSlice.actions;

export default bookmarksSlice.reducer; 
export default bookmarksSlice.reducer; 