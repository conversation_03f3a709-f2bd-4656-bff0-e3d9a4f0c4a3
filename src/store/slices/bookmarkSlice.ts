import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Bookmark {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  folder?: string;
  createdAt: number;
  updatedAt: number;
}

interface BookmarkState {
  bookmarks: Bookmark[];
  folders: string[];
}

const initialState: BookmarkState = {
  bookmarks: [],
  folders: ['Bookmarks Bar', 'Other Bookmarks'],
};

const bookmarkSlice = createSlice({
  name: 'bookmarks',
  initialState,
  reducers: {
    addBookmark: (state, action: PayloadAction<Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt'>>) => {
      const now = Date.now();
      const newBookmark: Bookmark = {
        ...action.payload,
        id: Date.now().toString(),
        createdAt: now,
        updatedAt: now,
      };
      state.bookmarks.push(newBookmark);
    },
    removeBookmark: (state, action: PayloadAction<string>) => {
      state.bookmarks = state.bookmarks.filter((bookmark: Bookmark) => bookmark.id !== action.payload);
    },
    updateBookmark: (state, action: PayloadAction<{ id: string; updates: Partial<Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt'>> }>) => {
      const { id, updates } = action.payload;
      const bookmark = state.bookmarks.find((b: Bookmark) => b.id === id);
      if (bookmark) {
        Object.assign(bookmark, { ...updates, updatedAt: Date.now() });
      }
    },
    addFolder: (state, action: PayloadAction<string>) => {
      if (!state.folders.includes(action.payload)) {
        state.folders.push(action.payload);
      }
    },
    removeFolder: (state, action: PayloadAction<string>) => {
      state.folders = state.folders.filter((folder: string) => folder !== action.payload);
      // Move bookmarks from removed folder to 'Other Bookmarks'
      state.bookmarks.forEach((bookmark: Bookmark) => {
        if (bookmark.folder === action.payload) {
          bookmark.folder = 'Other Bookmarks';
        }
      });
    },
    moveBookmark: (state, action: PayloadAction<{ id: string; folder: string }>) => {
      const { id, folder } = action.payload;
      const bookmark = state.bookmarks.find((b: Bookmark) => b.id === id);
      if (bookmark) {
        bookmark.folder = folder;
        bookmark.updatedAt = Date.now();
      }
    },
  },
});

export const {
  addBookmark,
  removeBookmark,
  updateBookmark,
  addFolder,
  removeFolder,
  moveBookmark,
} = bookmarkSlice.actions;

export default bookmarkSlice.reducer; 