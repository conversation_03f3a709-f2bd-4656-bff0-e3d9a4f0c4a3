import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Extension, ExtensionState } from '../../renderer/extensions/types';

const initialState: ExtensionState = {
  extensions: [],
  enabledExtensions: [],
  loading: false,
  error: null,
};

const extensionSlice = createSlice({
  name: 'extensions',
  initialState,
  reducers: {
    setExtensions: (state, action: PayloadAction<Extension[]>) => {
      state.extensions = action.payload;
    },
    addExtension: (state, action: PayloadAction<Extension>) => {
      state.extensions.push(action.payload);
    },
    removeExtension: (state, action: PayloadAction<string>) => {
      state.extensions = state.extensions.filter((ext: Extension) => ext.id !== action.payload);
      state.enabledExtensions = state.enabledExtensions.filter((id: string) => id !== action.payload);
    },
    updateExtension: (state, action: PayloadAction<Extension>) => {
      const index = state.extensions.findIndex((ext: Extension) => ext.id === action.payload.id);
      if (index !== -1) {
        state.extensions[index] = action.payload;
      }
    },
    enableExtension: (state, action: PayloadAction<string>) => {
      if (!state.enabledExtensions.includes(action.payload)) {
        state.enabledExtensions.push(action.payload);
      }
    },
    disableExtension: (state, action: PayloadAction<string>) => {
      state.enabledExtensions = state.enabledExtensions.filter((id: string) => id !== action.payload);
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setExtensions,
  addExtension,
  removeExtension,
  updateExtension,
  enableExtension,
  disableExtension,
  setLoading,
  setError,
} = extensionSlice.actions;

export default extensionSlice.reducer; 