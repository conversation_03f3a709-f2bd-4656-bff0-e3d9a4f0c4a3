import { createSlice, PayloadAction, Draft } from '@reduxjs/toolkit';
import { notificationManager } from '../../notifications/NotificationManager';
import type {
  Notification,
  NotificationType,
  NotificationPriority,
  NotificationPosition,
  NotificationTheme,
  NotificationAnimation,
  NotificationSound,
  NotificationBehavior,
  NotificationLayout,
  NotificationAccessibility
} from '../../types/notifications';

interface NotificationState {
  enabled: boolean;
  sound: boolean;
  vibration: boolean;
  position: NotificationPosition;
  duration: number;
  maxVisible: number;
  grouping: boolean;
  priority: NotificationPriority;
  actions: boolean;
  history: boolean;
  historyLimit: number;
  notifications: Draft<Notification>[];
}

const initialState: NotificationState = {
  enabled: true,
  sound: true,
  vibration: true,
  position: 'top-right',
  duration: 5000,
  maxVisible: 5,
  grouping: true,
  priority: 'normal',
  actions: true,
  history: true,
  historyLimit: 100,
  notifications: [],
};

const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    setEnabled: (state, action: PayloadAction<boolean>) => {
      state.enabled = action.payload;
      notificationManager.updateConfig({ enabled: action.payload });
    },
    setSound: (state, action: PayloadAction<boolean>) => {
      state.sound = action.payload;
      notificationManager.updateConfig({ sound: action.payload });
    },
    setVibration: (state, action: PayloadAction<boolean>) => {
      state.vibration = action.payload;
      notificationManager.updateConfig({ vibration: action.payload });
    },
    setPosition: (state, action: PayloadAction<NotificationPosition>) => {
      state.position = action.payload;
      notificationManager.updateConfig({ position: action.payload });
    },
    setDuration: (state, action: PayloadAction<number>) => {
      state.duration = action.payload;
      notificationManager.updateConfig({ duration: action.payload });
    },
    setMaxVisible: (state, action: PayloadAction<number>) => {
      state.maxVisible = action.payload;
      notificationManager.updateConfig({ maxVisible: action.payload });
    },
    setGrouping: (state, action: PayloadAction<boolean>) => {
      state.grouping = action.payload;
      notificationManager.updateConfig({ grouping: action.payload });
    },
    setPriority: (state, action: PayloadAction<NotificationPriority>) => {
      state.priority = action.payload;
      notificationManager.updateConfig({ priority: action.payload });
    },
    setActions: (state, action: PayloadAction<boolean>) => {
      state.actions = action.payload;
      notificationManager.updateConfig({ actions: action.payload });
    },
    setHistory: (state, action: PayloadAction<boolean>) => {
      state.history = action.payload;
      notificationManager.updateConfig({ history: action.payload });
    },
    setHistoryLimit: (state, action: PayloadAction<number>) => {
      state.historyLimit = action.payload;
      notificationManager.updateConfig({ historyLimit: action.payload });
    },
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'read'>>) => {
      const notification = {
        ...action.payload,
        id: crypto.randomUUID(),
        read: false,
      } as Draft<Notification>;
      state.notifications.push(notification);
      
      // Show system notification
      notificationManager.show({
        title: notification.title,
        body: notification.message || '',
        urgency: notification.priority === 'urgent' ? 'critical' : 'normal',
        actions: notification.actions?.map(action => ({
          type: 'button',
          label: action.label,
          callback: action.onClick
        })),
        tag: notification.id,
      });
    },
    markAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },
    markAllAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
      notificationManager.closeAll();
    },
  },
});

export const {
  setEnabled,
  setSound,
  setVibration,
  setPosition,
  setDuration,
  setMaxVisible,
  setGrouping,
  setPriority,
  setActions,
  setHistory,
  setHistoryLimit,
  addNotification,
  markAsRead,
  markAllAsRead,
  removeNotification,
  clearNotifications,
} = notificationSlice.actions;

export default notificationSlice.reducer; 