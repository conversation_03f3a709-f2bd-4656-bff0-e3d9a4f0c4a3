import authSlice, { loginSuccess, logout } from '../authSlice';

describe('authSlice', () => {
  const initialState = {
    user: null,
    isAuthenticated: false,
  };

  it('логин корректно обновляет state', () => {
    const user = { id: 'user-123', name: 'Тестовый пользователь' };
    const action = loginSuccess(user);
    const state = authSlice(initialState, action);
    expect(state.isAuthenticated).toBe(true);
    expect(state.user).toEqual(user);
  });

  it('logout очищает user и isAuthenticated', () => {
    const loggedIn = {
      user: { id: 'user-123', name: 'Тестовый пользователь' },
      isAuthenticated: true,
    };
    const action = logout();
    const state = authSlice(loggedIn, action);
    expect(state.isAuthenticated).toBe(false);
    expect(state.user).toBeNull();
  });
});