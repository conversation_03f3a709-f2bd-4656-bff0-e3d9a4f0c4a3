import notificationsSlice, { addNotification, removeNotification } from '../notificationsSlice';

describe('notificationsSlice', () => {
  const initialState = {
    notifications: [],
  };

  it('добавляет уведомление', () => {
    const action = addNotification({ id: '1', message: 'Привет!', type: 'info' });
    const state = notificationsSlice(initialState, action);
    expect(state.notifications).toHaveLength(1);
    expect(state.notifications[0].message).toBe('Привет!');
  });

  it('удаляет уведомление', () => {
    const populatedState = {
      notifications: [{ id: '1', message: 'Привет!', type: 'info' }],
    };
    const action = removeNotification('1');
    const state = notificationsSlice(populatedState, action);
    expect(state.notifications).toHaveLength(0);
  });
});