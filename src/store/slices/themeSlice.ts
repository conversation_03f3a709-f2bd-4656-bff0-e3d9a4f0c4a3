import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type ThemeMode = 'light' | 'dark' | 'system';
export type ColorScheme = 'default' | 'custom';

export interface CustomColors {
  primary: string;
  secondary: string;
  error: string;
  warning: string;
  info: string;
  success: string;
}

export interface TypographySettings {
  fontFamily: string;
  fontSize: number;
  fontWeightLight: number;
  fontWeightRegular: number;
  fontWeightMedium: number;
  fontWeightBold: number;
}

export interface AnimationSettings {
  enabled: boolean;
  duration: number;
  easing: string;
}

export interface ThemeSettings {
  mode: ThemeMode;
  colorScheme: ColorScheme;
  customColors: CustomColors;
  typography: TypographySettings;
  animation: AnimationSettings;
  borderRadius: number;
  spacing: number;
  shadows: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  transitions: {
    duration: number;
    easing: string;
  };
}

export interface ThemePreset {
  name: string;
  settings: ThemeSettings;
}

export interface ThemeState {
  settings: ThemeSettings;
  presets: {
    [key: string]: ThemePreset;
  };
}

const defaultTheme: ThemeSettings = {
  mode: 'light',
  colorScheme: 'default',
  customColors: {
    primary: '#1976d2',
    secondary: '#dc004e',
    error: '#f44336',
    warning: '#ff9800',
    info: '#2196f3',
    success: '#4caf50',
  },
  typography: {
    fontFamily: 'Roboto',
    fontSize: 14,
    fontWeightLight: 300,
    fontWeightRegular: 400,
    fontWeightMedium: 500,
    fontWeightBold: 700,
  },
  animation: {
    enabled: true,
    duration: 300,
    easing: 'ease-in-out',
  },
  borderRadius: 4,
  spacing: 8,
  shadows: {
    xs: '0 2px 4px rgba(0,0,0,0.1)',
    sm: '0 4px 8px rgba(0,0,0,0.1)',
    md: '0 8px 16px rgba(0,0,0,0.1)',
    lg: '0 16px 24px rgba(0,0,0,0.1)',
    xl: '0 24px 32px rgba(0,0,0,0.1)',
  },
  transitions: {
    duration: 300,
    easing: 'ease-in-out',
  },
};

const initialState: ThemeState = {
  settings: defaultTheme,
  presets: {
    default: {
      name: 'Default',
      settings: defaultTheme,
    },
  },
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setThemeMode: (state, action: PayloadAction<ThemeMode>) => {
      state.settings.mode = action.payload;
    },
    setColorScheme: (state, action: PayloadAction<ColorScheme>) => {
      state.settings.colorScheme = action.payload;
    },
    setCustomColors: (state, action: PayloadAction<Partial<CustomColors>>) => {
      state.settings.customColors = {
        ...state.settings.customColors,
        ...action.payload,
      };
    },
    setTypography: (state, action: PayloadAction<Partial<TypographySettings>>) => {
      state.settings.typography = {
        ...state.settings.typography,
        ...action.payload,
      };
    },
    setAnimationSettings: (state, action: PayloadAction<Partial<AnimationSettings>>) => {
      state.settings.animation = {
        ...state.settings.animation,
        ...action.payload,
      };
    },
    savePreset: (state, action: PayloadAction<ThemePreset>) => {
      const key = action.payload.name.toLowerCase().replace(/\s+/g, '-');
      state.presets[key] = action.payload;
    },
    deletePreset: (state, action: PayloadAction<string>) => {
      delete state.presets[action.payload];
    },
    loadPreset: (state, action: PayloadAction<string>) => {
      const preset = state.presets[action.payload];
      if (preset) {
        state.settings = preset.settings;
      }
    },
  },
});

export const {
  setThemeMode,
  setColorScheme,
  setCustomColors,
  setTypography,
  setAnimationSettings,
  savePreset,
  deletePreset,
  loadPreset,
} = themeSlice.actions;

export default themeSlice.reducer; 