import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SyncState {
  status: 'idle' | 'syncing' | 'error' | 'success';
  lastSync: number | null;
  error: string | null;
  syncItems: {
    bookmarks: boolean;
    history: boolean;
    settings: boolean;
    extensions: boolean;
    passwords: boolean;
    autofill: boolean;
    openTabs: boolean;
  };
  syncFrequency: 'manual' | 'hourly' | 'daily' | 'weekly';
  encryptionEnabled: boolean;
  syncProgress: {
    total: number;
    completed: number;
    currentItem: string;
  };
  conflicts: Array<{
    id: string;
    type: string;
    localVersion: any;
    remoteVersion: any;
    resolved: boolean;
  }>;
}

const initialState: SyncState = {
  status: 'idle',
  lastSync: null,
  error: null,
  syncItems: {
    bookmarks: true,
    history: true,
    settings: true,
    extensions: true,
    passwords: true,
    autofill: true,
    openTabs: true,
  },
  syncFrequency: 'daily',
  encryptionEnabled: true,
  syncProgress: {
    total: 0,
    completed: 0,
    currentItem: '',
  },
  conflicts: [],
};

const syncSlice = createSlice({
  name: 'sync',
  initialState,
  reducers: {
    startSync: (state) => {
      state.status = 'syncing';
      state.error = null;
    },
    completeSync: (state) => {
      state.status = 'success';
      state.lastSync = Date.now();
      state.syncProgress = {
        total: 0,
        completed: 0,
        currentItem: '',
      };
    },
    setSyncError: (state, action: PayloadAction<string>) => {
      state.status = 'error';
      state.error = action.payload;
    },
    updateSyncItems: (state, action: PayloadAction<Partial<SyncState['syncItems']>>) => {
      state.syncItems = { ...state.syncItems, ...action.payload };
    },
    setSyncFrequency: (state, action: PayloadAction<SyncState['syncFrequency']>) => {
      state.syncFrequency = action.payload;
    },
    toggleEncryption: (state) => {
      state.encryptionEnabled = !state.encryptionEnabled;
    },
    updateSyncProgress: (state, action: PayloadAction<Partial<SyncState['syncProgress']>>) => {
      state.syncProgress = { ...state.syncProgress, ...action.payload };
    },
    addConflict: (state, action: PayloadAction<Omit<SyncState['conflicts'][0], 'id' | 'resolved'>>) => {
      state.conflicts.push({
        ...action.payload,
        id: Date.now().toString(),
        resolved: false,
      });
    },
    resolveConflict: (state, action: PayloadAction<string>) => {
      const conflict = state.conflicts.find(c => c.id === action.payload);
      if (conflict) {
        conflict.resolved = true;
      }
    },
    clearConflicts: (state) => {
      state.conflicts = [];
    },
  },
});

export const {
  startSync,
  completeSync,
  setSyncError,
  updateSyncItems,
  setSyncFrequency,
  toggleEncryption,
  updateSyncProgress,
  addConflict,
  resolveConflict,
  clearConflicts,
} = syncSlice.actions;

export default syncSlice.reducer; 