import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../index';

export interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  homepage?: string;
  icon?: string;
  permissions: string[];
  contentScripts: {
    matches: string[];
    js?: string[];
    css?: string[];
  }[];
  background?: {
    serviceWorker?: string;
    persistent?: boolean;
  };
  options?: {
    page?: string;
    openInTab?: boolean;
  };
  settings: Record<string, any>;
  enabled: boolean;
  installedAt: number;
  updatedAt: number;
  size: number;
  rating: number;
  downloads: number;
  reviews: number;
  compatibility: {
    minVersion: string;
    maxVersion?: string;
  };
  dependencies?: string[];
  conflicts?: string[];
  categories: string[];
  tags: string[];
  languages: string[];
  license: string;
  privacyPolicy?: string;
  supportEmail?: string;
  repository?: string;
  changelog?: string;
  isVerified: boolean;
  isOfficial: boolean;
  isExperimental: boolean;
  isDeprecated: boolean;
  isBlocked: boolean;
  blockReason?: string;
  lastCheck: number;
  updateAvailable: boolean;
  updateInfo?: {
    version: string;
    size: number;
    releaseNotes: string;
    releaseDate: number;
  };
}

interface ExtensionsState {
  installed: Record<string, Extension>;
  available: Record<string, Extension>;
  settings: {
    autoUpdate: boolean;
    allowExperimental: boolean;
    allowDeprecated: boolean;
    allowBlocked: boolean;
    installFromWeb: boolean;
    installFromFile: boolean;
    installFromStore: boolean;
    maxConcurrentDownloads: number;
    downloadPath: string;
    updateCheckInterval: number;
    permissions: {
      autoGrant: boolean;
      rememberDecisions: boolean;
      defaultPolicy: 'ask' | 'allow' | 'deny';
    };
    notifications: {
      updates: boolean;
      installations: boolean;
      errors: boolean;
      permissions: boolean;
    };
    security: {
      verifySignatures: boolean;
      scanBeforeInstall: boolean;
      quarantineNew: boolean;
      maxFileSize: number;
      allowedSources: string[];
      blockedSources: string[];
    };
    performance: {
      maxBackgroundProcesses: number;
      maxMemoryUsage: number;
      maxCpuUsage: number;
      maxDiskUsage: number;
      autoDisableInactive: boolean;
      inactiveTimeout: number;
    };
  };
  status: {
    isChecking: boolean;
    isUpdating: boolean;
    isInstalling: boolean;
    isUninstalling: boolean;
    lastCheck: number;
    lastUpdate: number;
    errors: Array<{
      id: string;
      code: string;
      message: string;
      timestamp: number;
    }>;
  };
  categories: string[];
  tags: string[];
  languages: string[];
  permissions: string[];
  sources: {
    id: string;
    name: string;
    url: string;
    type: 'store' | 'repository' | 'custom';
    enabled: boolean;
    lastSync: number;
  }[];
}

const initialState: ExtensionsState = {
  installed: {},
  available: {},
  settings: {
    autoUpdate: true,
    allowExperimental: false,
    allowDeprecated: false,
    allowBlocked: false,
    installFromWeb: true,
    installFromFile: true,
    installFromStore: true,
    maxConcurrentDownloads: 3,
    downloadPath: '',
    updateCheckInterval: 3600000, // 1 hour
    permissions: {
      autoGrant: false,
      rememberDecisions: true,
      defaultPolicy: 'ask',
    },
    notifications: {
      updates: true,
      installations: true,
      errors: true,
      permissions: true,
    },
    security: {
      verifySignatures: true,
      scanBeforeInstall: true,
      quarantineNew: true,
      maxFileSize: 100 * 1024 * 1024, // 100MB
      allowedSources: ['https://extensions.a14browser.com'],
      blockedSources: [],
    },
    performance: {
      maxBackgroundProcesses: 5,
      maxMemoryUsage: 512 * 1024 * 1024, // 512MB
      maxCpuUsage: 50, // 50%
      maxDiskUsage: 1024 * 1024 * 1024, // 1GB
      autoDisableInactive: true,
      inactiveTimeout: 3600000, // 1 hour
    },
  },
  status: {
    isChecking: false,
    isUpdating: false,
    isInstalling: false,
    isUninstalling: false,
    lastCheck: 0,
    lastUpdate: 0,
    errors: [],
  },
  categories: [
    'Productivity',
    'Social',
    'Shopping',
    'Entertainment',
    'News',
    'Sports',
    'Weather',
    'Travel',
    'Education',
    'Developer Tools',
    'Accessibility',
    'Security',
    'Privacy',
    'Themes',
    'Other',
  ],
  tags: [],
  languages: ['en', 'ru', 'es', 'fr', 'de', 'zh', 'ja', 'ko'],
  permissions: [
    'tabs',
    'bookmarks',
    'history',
    'downloads',
    'storage',
    'cookies',
    'webRequest',
    'webNavigation',
    'notifications',
    'geolocation',
    'clipboardRead',
    'clipboardWrite',
    'system.display',
    'system.memory',
    'system.cpu',
    'system.storage',
  ],
  sources: [
    {
      id: 'official',
      name: 'A14 Browser Extensions',
      url: 'https://extensions.a14browser.com',
      type: 'store',
      enabled: true,
      lastSync: 0,
    },
  ],
};

const extensionsSlice = createSlice({
  name: 'extensions',
  initialState,
  reducers: {
    installExtension: (state, action: PayloadAction<Extension>) => {
      state.installed[action.payload.id] = {
        ...action.payload,
        installedAt: Date.now(),
        updatedAt: Date.now(),
      };
    },
    uninstallExtension: (state, action: PayloadAction<string>) => {
      delete state.installed[action.payload];
    },
    updateExtension: (state, action: PayloadAction<{ id: string; changes: Partial<Extension> }>) => {
      if (state.installed[action.payload.id]) {
        state.installed[action.payload.id] = {
          ...state.installed[action.payload.id],
          ...action.payload.changes,
          updatedAt: Date.now(),
        };
      }
    },
    setExtensionEnabled: (state, action: PayloadAction<{ id: string; enabled: boolean }>) => {
      if (state.installed[action.payload.id]) {
        state.installed[action.payload.id].enabled = action.payload.enabled;
      }
    },
    updateExtensionSettings: (state, action: PayloadAction<{ id: string; settings: Record<string, any> }>) => {
      if (state.installed[action.payload.id]) {
        state.installed[action.payload.id].settings = {
          ...state.installed[action.payload.id].settings,
          ...action.payload.settings,
        };
      }
    },
    setAvailableExtensions: (state, action: PayloadAction<Record<string, Extension>>) => {
      state.available = action.payload;
    },
    updateExtensionStatus: (state, action: PayloadAction<Partial<ExtensionsState['status']>>) => {
      state.status = { ...state.status, ...action.payload };
    },
    addExtensionError: (state, action: PayloadAction<{ id: string; code: string; message: string }>) => {
      state.status.errors.push({
        ...action.payload,
        timestamp: Date.now(),
      });
    },
    clearExtensionErrors: (state) => {
      state.status.errors = [];
    },
    updateExtensionSettings: (state, action: PayloadAction<Partial<ExtensionsState['settings']>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    addExtensionSource: (state, action: PayloadAction<ExtensionsState['sources'][0]>) => {
      state.sources.push(action.payload);
    },
    removeExtensionSource: (state, action: PayloadAction<string>) => {
      state.sources = state.sources.filter(source => source.id !== action.payload);
    },
    updateExtensionSource: (state, action: PayloadAction<{ id: string; changes: Partial<ExtensionsState['sources'][0]> }>) => {
      const source = state.sources.find(s => s.id === action.payload.id);
      if (source) {
        Object.assign(source, action.payload.changes);
      }
    },
  },
});

// Selectors
export const selectInstalledExtensions = (state: RootState) => state.extensions.installed;
export const selectAvailableExtensions = (state: RootState) => state.extensions.available;
export const selectExtensionSettings = (state: RootState) => state.extensions.settings;
export const selectExtensionStatus = (state: RootState) => state.extensions.status;
export const selectExtensionCategories = (state: RootState) => state.extensions.categories;
export const selectExtensionTags = (state: RootState) => state.extensions.tags;
export const selectExtensionLanguages = (state: RootState) => state.extensions.languages;
export const selectExtensionPermissions = (state: RootState) => state.extensions.permissions;
export const selectExtensionSources = (state: RootState) => state.extensions.sources;

// Helper selectors
export const selectEnabledExtensions = (state: RootState) =>
  Object.values(state.extensions.installed).filter(ext => ext.enabled);

export const selectExtensionById = (state: RootState, id: string) =>
  state.extensions.installed[id] || state.extensions.available[id];

export const selectExtensionsByCategory = (state: RootState, category: string) =>
  Object.values(state.extensions.installed).filter(ext => ext.categories.includes(category));

export const selectExtensionsByTag = (state: RootState, tag: string) =>
  Object.values(state.extensions.installed).filter(ext => ext.tags.includes(tag));

export const selectExtensionsByLanguage = (state: RootState, language: string) =>
  Object.values(state.extensions.installed).filter(ext => ext.languages.includes(language));

export const selectExtensionsWithUpdates = (state: RootState) =>
  Object.values(state.extensions.installed).filter(ext => ext.updateAvailable);

export const selectExtensionsByPermission = (state: RootState, permission: string) =>
  Object.values(state.extensions.installed).filter(ext => ext.permissions.includes(permission));

// Actions
export const {
  installExtension,
  uninstallExtension,
  updateExtension,
  setExtensionEnabled,
  updateExtensionSettings,
  setAvailableExtensions,
  updateExtensionStatus,
  addExtensionError,
  clearExtensionErrors,
  addExtensionSource,
  removeExtensionSource,
  updateExtensionSource,
} = extensionsSlice.actions;

export default extensionsSlice.reducer; 