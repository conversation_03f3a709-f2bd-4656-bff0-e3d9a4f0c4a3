import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface PageView {
  url: string;
  title: string;
  timestamp: number;
  duration: number;
  referrer?: string;
}

export interface UserAction {
  type: string;
  payload?: any;
  timestamp: number;
  userId?: string;
  sessionId: string;
}

export interface PerformanceMetric {
  type: 'navigation' | 'resource' | 'paint' | 'layout' | 'script';
  name: string;
  value: number;
  timestamp: number;
}

export interface ErrorReport {
  type: 'error' | 'warning' | 'info';
  message: string;
  stack?: string;
  timestamp: number;
  url: string;
  userId?: string;
  sessionId: string;
}

export interface SearchQuery {
  query: string;
  timestamp: number;
  results: number;
  filters?: Record<string, any>;
  userId?: string;
  sessionId: string;
}

interface AnalyticsState {
  pageViews: PageView[];
  userActions: UserAction[];
  performanceMetrics: PerformanceMetric[];
  errorReports: ErrorReport[];
  searchQueries: SearchQuery[];
  sessionId: string;
  isTrackingEnabled: boolean;
  lastSync: number | null;
}

const initialState: AnalyticsState = {
  pageViews: [],
  userActions: [],
  performanceMetrics: [],
  errorReports: [],
  searchQueries: [],
  sessionId: Date.now().toString(),
  isTrackingEnabled: true,
  lastSync: null,
};

const analyticsSlice = createSlice({
  name: 'analytics',
  initialState,
  reducers: {
    addPageView: (state, action: PayloadAction<PageView>) => {
      state.pageViews.push(action.payload);
    },
    addUserAction: (state, action: PayloadAction<UserAction>) => {
      state.userActions.push({
        ...action.payload,
        sessionId: state.sessionId,
      });
    },
    addPerformanceMetric: (state, action: PayloadAction<PerformanceMetric>) => {
      state.performanceMetrics.push(action.payload);
    },
    addErrorReport: (state, action: PayloadAction<ErrorReport>) => {
      state.errorReports.push({
        ...action.payload,
        sessionId: state.sessionId,
      });
    },
    addSearchQuery: (state, action: PayloadAction<SearchQuery>) => {
      state.searchQueries.push({
        ...action.payload,
        sessionId: state.sessionId,
      });
    },
    setTrackingEnabled: (state, action: PayloadAction<boolean>) => {
      state.isTrackingEnabled = action.payload;
    },
    clearAnalytics: (state) => {
      state.pageViews = [];
      state.userActions = [];
      state.performanceMetrics = [];
      state.errorReports = [];
      state.searchQueries = [];
    },
    setLastSync: (state, action: PayloadAction<number>) => {
      state.lastSync = action.payload;
    },
    updateSessionId: (state) => {
      state.sessionId = Date.now().toString();
    },
  },
});

export const {
  addPageView,
  addUserAction,
  addPerformanceMetric,
  addErrorReport,
  addSearchQuery,
  setTrackingEnabled,
  clearAnalytics,
  setLastSync,
  updateSessionId,
} = analyticsSlice.actions;

export default analyticsSlice.reducer; 