import { store } from '../index';
import { setThemeMode, setColorScheme } from '../slices/themeSlice';

describe('store', () => {
  it('should handle theme mode changes', () => {
    store.dispatch(setThemeMode('dark'));
    const state = store.getState();
    expect(state.theme.settings.mode).toBe('dark');
  });

  it('should handle color scheme changes', () => {
    store.dispatch(setColorScheme('custom'));
    const state = store.getState();
    expect(state.theme.settings.colorScheme).toBe('custom');
  });
}); 