import winston from 'winston';
import { format } from 'winston';
import { AppConfig } from '../types';

export class Logger {
  private static instance: Logger;
  private logger: winston.Logger;
  private config: AppConfig;

  private constructor(config: AppConfig) {
    this.config = config;
    this.initialize();
  }

  public static getInstance(config: AppConfig): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(config);
    }
    return Logger.instance;
  }

  private initialize(): void {
    const { combine, timestamp, printf, colorize } = format;

    const logFormat = printf(({ level, message, timestamp, ...metadata }) => {
      let msg = `${timestamp} [${level}]: ${message}`;
      if (Object.keys(metadata).length > 0) {
        msg += ` ${JSON.stringify(metadata)}`;
      }
      return msg;
    });

    this.logger = winston.createLogger({
      level: this.config.app.logLevel,
      format: combine(
        timestamp(),
        logFormat
      ),
      transports: [
        new winston.transports.Console({
          format: combine(
            colorize(),
            logFormat
          ),
        }),
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
        }),
        new winston.transports.File({
          filename: 'logs/combined.log',
        }),
      ],
    });

    if (this.config.app.environment !== 'production') {
      this.logger.add(new winston.transports.Console({
        format: combine(
          colorize(),
          logFormat
        ),
      }));
    }
  }

  public error(message: string, meta?: any): void {
    this.logger.error(message, meta);
  }

  public warn(message: string, meta?: any): void {
    this.logger.warn(message, meta);
  }

  public info(message: string, meta?: any): void {
    this.logger.info(message, meta);
  }

  public debug(message: string, meta?: any): void {
    this.logger.debug(message, meta);
  }

  public trace(message: string, meta?: any): void {
    this.logger.silly(message, meta);
  }

  public log(level: string, message: string, meta?: any): void {
    this.logger.log(level, message, meta);
  }

  public getLogger(): winston.Logger {
    return this.logger;
  }
}

export const logger = Logger.getInstance({
  app: {
    name: 'A14-Browser',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    debug: process.env.NODE_ENV !== 'production',
    logLevel: process.env.LOG_LEVEL || 'info',
  },
  // ... rest of the config
}); 