import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useSessionStore = create(
  persist(
    (set, get) => ({
      tabs: [],
      windows: [],
      activeTabId: null,
      activeWindowId: null,
      
      addTab: (tab) => set((state) => ({
        tabs: [...state.tabs, { ...tab, id: crypto.randomUUID() }],
      })),
      
      removeTab: (tabId) => set((state) => ({
        tabs: state.tabs.filter((tab) => tab.id !== tabId),
        activeTabId: state.activeTabId === tabId ? null : state.activeTabId,
      })),
      
      updateTab: (tabId, updates) => set((state) => ({
        tabs: state.tabs.map((tab) =>
          tab.id === tabId ? { ...tab, ...updates } : tab
        ),
      })),
      
      setActiveTab: (tabId) => set({ activeTabId: tabId }),
      
      addWindow: (window) => set((state) => ({
        windows: [...state.windows, { ...window, id: crypto.randomUUID() }],
      })),
      
      removeWindow: (windowId) => set((state) => ({
        windows: state.windows.filter((window) => window.id !== windowId),
        activeWindowId: state.activeWindowId === windowId ? null : state.activeWindowId,
      })),
      
      setActiveWindow: (windowId) => set({ activeWindowId: windowId }),
      
      saveSession: () => {
        const state = get();
        localStorage.setItem('browserSession', JSON.stringify({
          tabs: state.tabs,
          windows: state.windows,
          activeTabId: state.activeTabId,
          activeWindowId: state.activeWindowId,
        }));
      },
      
      restoreSession: () => {
        const savedSession = localStorage.getItem('browserSession');
        if (savedSession) {
          const session = JSON.parse(savedSession);
          set(session);
        }
      },
    }),
    {
      name: 'browser-session',
      partialize: (state) => ({
        tabs: state.tabs,
        windows: state.windows,
        activeTabId: state.activeTabId,
        activeWindowId: state.activeWindowId,
      }),
    }
  )
);

export default useSessionStore;
