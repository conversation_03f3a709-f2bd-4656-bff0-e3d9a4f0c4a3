.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.content {
  flex: 1;
  overflow: auto;
  padding: 16px;
  background-color: var(--background-color);
}

.urlForm {
  display: flex;
  flex: 1;
  margin: 0 8px;
}

.loadingIcon {
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

.title {
  margin-bottom: 16px;
  color: var(--text-color);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
} 