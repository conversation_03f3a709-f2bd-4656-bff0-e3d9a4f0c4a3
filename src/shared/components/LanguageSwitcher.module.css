.container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
}

.languageSwitcher {
  position: relative;
  display: inline-block;
  margin: 0.5rem;
}

.select {
  appearance: none;
  background-color: var(--background-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.5rem 2rem 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select:hover {
  border-color: var(--primary-color);
}

.select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color-light);
}

.select option {
  background-color: var(--background-color);
  color: var(--text-color);
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .select {
    background-color: var(--dark-background-color);
    color: var(--dark-text-color);
    border-color: var(--dark-border-color);
  }

  .select:hover {
    border-color: var(--dark-primary-color);
  }

  .select:focus {
    border-color: var(--dark-primary-color);
    box-shadow: 0 0 0 2px var(--dark-primary-color-light);
  }

  .select option {
    background-color: var(--dark-background-color);
    color: var(--dark-text-color);
  }
} 