import React from 'react';
import { useTranslation } from 'react-i18next';
import { languages, LanguageCode, setLanguage } from '../../i18n/config';
import styles from './LanguageSwitcher.module.css';

export const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language as LanguageCode;

  const handleLanguageChange = async (code: LanguageCode) => {
    if (code !== currentLanguage) {
      await setLanguage(code);
    }
  };

  return (
    <div className={styles.languageSwitcher}>
      <select
        value={currentLanguage}
        onChange={(e) => handleLanguageChange(e.target.value as LanguageCode)}
        className={styles.select}
        aria-label="Select language"
      >
        {Object.entries(languages).map(([code, { name, nativeName }]) => (
          <option key={code} value={code}>
            {nativeName} ({name})
          </option>
        ))}
      </select>
    </div>
  );
};

export default LanguageSwitcher; 