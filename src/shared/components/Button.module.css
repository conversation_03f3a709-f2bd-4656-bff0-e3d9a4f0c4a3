.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  gap: 0.5rem;
}

.button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

.button:disabled {
  cursor: not-allowed;
  opacity: 0.65;
}

/* Variants */
.primary {
  color: white;
  background-color: #3182ce;
  border-color: #3182ce;
}

.primary:hover:not(:disabled) {
  background-color: #2c5282;
  border-color: #2c5282;
}

.secondary {
  color: white;
  background-color: #718096;
  border-color: #718096;
}

.secondary:hover:not(:disabled) {
  background-color: #4a5568;
  border-color: #4a5568;
}

.outline {
  color: #3182ce;
  background-color: transparent;
  border-color: #3182ce;
}

.outline:hover:not(:disabled) {
  color: white;
  background-color: #3182ce;
}

.text {
  color: #3182ce;
  background-color: transparent;
  border-color: transparent;
  padding: 0.5rem;
}

.text:hover:not(:disabled) {
  background-color: rgba(49, 130, 206, 0.1);
}

/* Sizes */
.small {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.medium {
  padding: 0.5rem 1rem;
  font-size: 1rem;
}

.large {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
}

/* Loading state */
.loading {
  cursor: wait;
}

.spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 1.25rem;
  height: 1.25rem;
}

.spinnerSvg {
  animation: spin 1s linear infinite;
}

.spinnerCircle {
  stroke: currentColor;
  stroke-linecap: round;
  animation: spinner-circle 1.5s ease-in-out infinite;
}

/* Icons */
.startIcon {
  margin-right: 0.5rem;
}

.endIcon {
  margin-left: 0.5rem;
}

/* Full width */
.fullWidth {
  width: 100%;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spinner-circle {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
} 