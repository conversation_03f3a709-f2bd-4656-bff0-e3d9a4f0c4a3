import { StateManager } from '../StateManager';

describe('StateManager', () => {
  let stateManager: StateManager;
  let localStorageMock: { [key: string]: string };

  beforeEach(() => {
    // Mock localStorage
    localStorageMock = {};
    global.localStorage = {
      getItem: jest.fn((key: string) => localStorageMock[key]),
      setItem: jest.fn((key: string, value: string) => {
        localStorageMock[key] = value;
      }),
      removeItem: jest.fn((key: string) => {
        delete localStorageMock[key];
      }),
      clear: jest.fn(() => {
        localStorageMock = {};
      }),
      length: 0,
      key: jest.fn(),
    };

    stateManager = StateManager.getInstance();
  });

  afterEach(() => {
    stateManager.cleanup();
  });

  describe('Initialization', () => {
    it('should create a singleton instance', () => {
      const instance1 = StateManager.getInstance();
      const instance2 = StateManager.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should initialize with empty state', () => {
      expect(stateManager.getState()).toEqual({});
    });

    it('should load persisted state from localStorage', () => {
      const persistedState = { test: 'value' };
      localStorageMock['novabrowser_state'] = JSON.stringify(persistedState);

      const newStateManager = StateManager.getInstance();
      expect(newStateManager.getState()).toEqual(persistedState);
    });
  });

  describe('State Management', () => {
    it('should dispatch actions and update state', async () => {
      const action = {
        type: 'SET_STATE',
        payload: { test: 'value' },
      };

      await stateManager.dispatch(action);
      expect(stateManager.getState()).toEqual({ test: 'value' });
    });

    it('should not allow concurrent dispatches', async () => {
      const action1 = { type: 'SET_STATE', payload: { test: 'value1' } };
      const action2 = { type: 'SET_STATE', payload: { test: 'value2' } };

      const dispatch1 = stateManager.dispatch(action1);
      const dispatch2 = stateManager.dispatch(action2);

      await expect(dispatch2).rejects.toThrow('Cannot dispatch while another dispatch is in progress');
      await dispatch1;
    });

    it('should persist state to localStorage', async () => {
      const action = {
        type: 'SET_STATE',
        payload: { test: 'value' },
      };

      await stateManager.dispatch(action);
      expect(localStorage.setItem).toHaveBeenCalledWith(
        'novabrowser_state',
        JSON.stringify({ test: 'value' })
      );
    });
  });

  describe('Subscriptions', () => {
    it('should notify subscribers of state changes', async () => {
      const callback = jest.fn();
      const selector = (state: any) => state.test;

      stateManager.subscribe(selector, callback);

      await stateManager.dispatch({
        type: 'SET_STATE',
        payload: { test: 'value' },
      });

      expect(callback).toHaveBeenCalledWith('value', undefined);
    });

    it('should not notify subscribers if selected state has not changed', async () => {
      const callback = jest.fn();
      const selector = (state: any) => state.test;

      stateManager.subscribe(selector, callback);

      await stateManager.dispatch({
        type: 'SET_STATE',
        payload: { other: 'value' },
      });

      expect(callback).toHaveBeenCalledTimes(1); // Initial call only
    });

    it('should allow unsubscribing', async () => {
      const callback = jest.fn();
      const selector = (state: any) => state.test;

      const unsubscribe = stateManager.subscribe(selector, callback);
      unsubscribe();

      await stateManager.dispatch({
        type: 'SET_STATE',
        payload: { test: 'value' },
      });

      expect(callback).toHaveBeenCalledTimes(1); // Initial call only
    });
  });

  describe('Middleware', () => {
    it('should run middlewares before state update', async () => {
      const middleware = jest.fn();
      stateManager.addMiddleware(middleware);

      const action = {
        type: 'SET_STATE',
        payload: { test: 'value' },
      };

      await stateManager.dispatch(action);
      expect(middleware).toHaveBeenCalledWith(action, expect.any(Object));
    });

    it('should allow removing middleware', async () => {
      const middleware = jest.fn();
      stateManager.addMiddleware(middleware);
      stateManager.removeMiddleware(middleware);

      await stateManager.dispatch({
        type: 'SET_STATE',
        payload: { test: 'value' },
      });

      expect(middleware).not.toHaveBeenCalled();
    });
  });

  describe('History Management', () => {
    it('should maintain action history', async () => {
      const action1 = { type: 'SET_STATE', payload: { test: 'value1' } };
      const action2 = { type: 'SET_STATE', payload: { test: 'value2' } };

      await stateManager.dispatch(action1);
      await stateManager.dispatch(action2);

      const history = stateManager.getHistory();
      expect(history.past).toHaveLength(2);
      expect(history.past[0]).toEqual(action1);
      expect(history.past[1]).toEqual(action2);
    });

    it('should support undo/redo', async () => {
      const action1 = { type: 'SET_STATE', payload: { test: 'value1' } };
      const action2 = { type: 'SET_STATE', payload: { test: 'value2' } };

      await stateManager.dispatch(action1);
      await stateManager.dispatch(action2);

      expect(stateManager.getState()).toEqual({ test: 'value2' });

      stateManager.undo();
      expect(stateManager.getState()).toEqual({ test: 'value1' });

      stateManager.redo();
      expect(stateManager.getState()).toEqual({ test: 'value2' });
    });

    it('should respect max history size', async () => {
      stateManager.setMaxHistorySize(2);

      const action1 = { type: 'SET_STATE', payload: { test: 'value1' } };
      const action2 = { type: 'SET_STATE', payload: { test: 'value2' } };
      const action3 = { type: 'SET_STATE', payload: { test: 'value3' } };

      await stateManager.dispatch(action1);
      await stateManager.dispatch(action2);
      await stateManager.dispatch(action3);

      const history = stateManager.getHistory();
      expect(history.past).toHaveLength(2);
      expect(history.past[0]).toEqual(action2);
      expect(history.past[1]).toEqual(action3);
    });
  });

  describe('State Reset', () => {
    it('should reset state to initial state', async () => {
      await stateManager.dispatch({
        type: 'SET_STATE',
        payload: { test: 'value' },
      });

      stateManager.resetState();
      expect(stateManager.getState()).toEqual({});
    });

    it('should clear history on reset', async () => {
      await stateManager.dispatch({
        type: 'SET_STATE',
        payload: { test: 'value' },
      });

      stateManager.resetState();
      const history = stateManager.getHistory();
      expect(history.past).toHaveLength(0);
      expect(history.future).toHaveLength(0);
    });
  });

  describe('Cleanup', () => {
    it('should remove all subscriptions', async () => {
      const callback = jest.fn();
      const selector = (state: any) => state.test;

      stateManager.subscribe(selector, callback);
      stateManager.cleanup();

      await stateManager.dispatch({
        type: 'SET_STATE',
        payload: { test: 'value' },
      });

      expect(callback).toHaveBeenCalledTimes(1); // Initial call only
    });

    it('should remove all middlewares', async () => {
      const middleware = jest.fn();
      stateManager.addMiddleware(middleware);
      stateManager.cleanup();

      await stateManager.dispatch({
        type: 'SET_STATE',
        payload: { test: 'value' },
      });

      expect(middleware).not.toHaveBeenCalled();
    });

    it('should clear history', () => {
      stateManager.cleanup();
      const history = stateManager.getHistory();
      expect(history.past).toHaveLength(0);
      expect(history.future).toHaveLength(0);
    });
  });
}); 