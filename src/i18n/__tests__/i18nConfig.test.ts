import i18n from '../config';

describe('i18n настройка', () => {
  it('по умолчанию должен быть язык ru', () => {
    expect(i18n.language).toBe('ru');
  });

  it('может переключаться на en', async () => {
    await i18n.changeLanguage('en');
    expect(i18n.language).toBe('en');
  });

  it('ключ перевода присутствует для ru и en', () => {
    expect(i18n.t('app.title', { lng: 'ru' })).toBeDefined();
    expect(i18n.t('app.title', { lng: 'en' })).toBeDefined();
  });
});