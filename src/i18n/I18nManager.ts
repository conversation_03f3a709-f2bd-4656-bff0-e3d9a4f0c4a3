import i18n from 'i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';
import { initReactI18next } from 'react-i18next';
import { EventEmitter } from 'events';
import { logger } from '../utils/logger';

export interface I18nConfig {
  fallbackLng: string;
  supportedLanguages: string[];
  debug: boolean;
  interpolation: {
    escapeValue: boolean;
  };
  detection: {
    order: string[];
    caches: string[];
  };
  backend: {
    loadPath: string;
    addPath: string;
  };
  rtlLanguages: string[];
  pluralRules: Record<string, (n: number) => number>;
}

export interface TranslationNamespace {
  name: string;
  path: string;
  priority: number;
}

export interface LocaleInfo {
  code: string;
  name: string;
  nativeName: string;
  direction: 'ltr' | 'rtl';
  region: string;
  script: string;
  variants: string[];
  pluralRule: string;
  dateFormat: string;
  timeFormat: string;
  numberFormat: {
    decimal: string;
    thousands: string;
    currency: string;
  };
}

export class I18nManager extends EventEmitter {
  private static instance: I18nManager;
  private config: I18nConfig;
  private namespaces: Map<string, TranslationNamespace> = new Map();
  private locales: Map<string, LocaleInfo> = new Map();
  private currentLanguage: string = 'en';
  private isInitialized: boolean = false;

  private constructor() {
    super();
    this.config = {
      fallbackLng: 'en',
      supportedLanguages: [
        'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko',
        'ar', 'hi', 'tr', 'pl', 'nl', 'sv', 'da', 'no', 'fi', 'cs',
        'sk', 'hu', 'ro', 'bg', 'hr', 'sl', 'et', 'lv', 'lt', 'el',
        'he', 'th', 'vi', 'id', 'ms', 'tl', 'sw', 'am', 'bn', 'gu',
        'kn', 'ml', 'mr', 'pa', 'ta', 'te', 'ur', 'fa', 'uk', 'be'
      ],
      debug: process.env.NODE_ENV === 'development',
      interpolation: {
        escapeValue: false,
      },
      detection: {
        order: ['localStorage', 'navigator', 'htmlTag'],
        caches: ['localStorage'],
      },
      backend: {
        loadPath: '/locales/{{lng}}/{{ns}}.json',
        addPath: '/locales/add/{{lng}}/{{ns}}',
      },
      rtlLanguages: ['ar', 'he', 'fa', 'ur'],
      pluralRules: {
        en: (n: number) => n === 1 ? 0 : 1,
        ru: (n: number) => {
          if (n % 10 === 1 && n % 100 !== 11) return 0;
          if (n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20)) return 1;
          return 2;
        },
        ar: (n: number) => {
          if (n === 0) return 0;
          if (n === 1) return 1;
          if (n === 2) return 2;
          if (n % 100 >= 3 && n % 100 <= 10) return 3;
          if (n % 100 >= 11) return 4;
          return 5;
        },
      },
    };

    this.initializeLocales();
    this.initializeNamespaces();
  }

  public static getInstance(): I18nManager {
    if (!I18nManager.instance) {
      I18nManager.instance = new I18nManager();
    }
    return I18nManager.instance;
  }

  private initializeLocales(): void {
    const localeData: LocaleInfo[] = [
      {
        code: 'en',
        name: 'English',
        nativeName: 'English',
        direction: 'ltr',
        region: 'US',
        script: 'Latn',
        variants: ['en-US', 'en-GB', 'en-CA', 'en-AU'],
        pluralRule: 'en',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: 'h:mm A',
        numberFormat: { decimal: '.', thousands: ',', currency: '$' },
      },
      {
        code: 'es',
        name: 'Spanish',
        nativeName: 'Español',
        direction: 'ltr',
        region: 'ES',
        script: 'Latn',
        variants: ['es-ES', 'es-MX', 'es-AR', 'es-CO'],
        pluralRule: 'en',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: 'HH:mm',
        numberFormat: { decimal: ',', thousands: '.', currency: '€' },
      },
      {
        code: 'fr',
        name: 'French',
        nativeName: 'Français',
        direction: 'ltr',
        region: 'FR',
        script: 'Latn',
        variants: ['fr-FR', 'fr-CA', 'fr-BE', 'fr-CH'],
        pluralRule: 'en',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: 'HH:mm',
        numberFormat: { decimal: ',', thousands: ' ', currency: '€' },
      },
      {
        code: 'de',
        name: 'German',
        nativeName: 'Deutsch',
        direction: 'ltr',
        region: 'DE',
        script: 'Latn',
        variants: ['de-DE', 'de-AT', 'de-CH'],
        pluralRule: 'en',
        dateFormat: 'DD.MM.YYYY',
        timeFormat: 'HH:mm',
        numberFormat: { decimal: ',', thousands: '.', currency: '€' },
      },
      {
        code: 'ru',
        name: 'Russian',
        nativeName: 'Русский',
        direction: 'ltr',
        region: 'RU',
        script: 'Cyrl',
        variants: ['ru-RU'],
        pluralRule: 'ru',
        dateFormat: 'DD.MM.YYYY',
        timeFormat: 'HH:mm',
        numberFormat: { decimal: ',', thousands: ' ', currency: '₽' },
      },
      {
        code: 'ar',
        name: 'Arabic',
        nativeName: 'العربية',
        direction: 'rtl',
        region: 'SA',
        script: 'Arab',
        variants: ['ar-SA', 'ar-EG', 'ar-AE', 'ar-MA'],
        pluralRule: 'ar',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: 'HH:mm',
        numberFormat: { decimal: '.', thousands: ',', currency: 'ر.س' },
      },
      {
        code: 'zh',
        name: 'Chinese',
        nativeName: '中文',
        direction: 'ltr',
        region: 'CN',
        script: 'Hans',
        variants: ['zh-CN', 'zh-TW', 'zh-HK'],
        pluralRule: 'en',
        dateFormat: 'YYYY/MM/DD',
        timeFormat: 'HH:mm',
        numberFormat: { decimal: '.', thousands: ',', currency: '¥' },
      },
      {
        code: 'ja',
        name: 'Japanese',
        nativeName: '日本語',
        direction: 'ltr',
        region: 'JP',
        script: 'Jpan',
        variants: ['ja-JP'],
        pluralRule: 'en',
        dateFormat: 'YYYY/MM/DD',
        timeFormat: 'HH:mm',
        numberFormat: { decimal: '.', thousands: ',', currency: '¥' },
      },
    ];

    localeData.forEach(locale => {
      this.locales.set(locale.code, locale);
    });
  }

  private initializeNamespaces(): void {
    const namespaces: TranslationNamespace[] = [
      { name: 'common', path: '/locales/{{lng}}/common.json', priority: 1 },
      { name: 'navigation', path: '/locales/{{lng}}/navigation.json', priority: 2 },
      { name: 'settings', path: '/locales/{{lng}}/settings.json', priority: 3 },
      { name: 'security', path: '/locales/{{lng}}/security.json', priority: 4 },
      { name: 'extensions', path: '/locales/{{lng}}/extensions.json', priority: 5 },
      { name: 'bookmarks', path: '/locales/{{lng}}/bookmarks.json', priority: 6 },
      { name: 'history', path: '/locales/{{lng}}/history.json', priority: 7 },
      { name: 'downloads', path: '/locales/{{lng}}/downloads.json', priority: 8 },
      { name: 'tabs', path: '/locales/{{lng}}/tabs.json', priority: 9 },
      { name: 'errors', path: '/locales/{{lng}}/errors.json', priority: 10 },
    ];

    namespaces.forEach(ns => {
      this.namespaces.set(ns.name, ns);
    });
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await i18n
        .use(Backend)
        .use(LanguageDetector)
        .use(initReactI18next)
        .init({
          fallbackLng: this.config.fallbackLng,
          debug: this.config.debug,
          interpolation: this.config.interpolation,
          detection: this.config.detection,
          backend: this.config.backend,
          ns: Array.from(this.namespaces.keys()),
          defaultNS: 'common',
        });

      this.currentLanguage = i18n.language;
      this.isInitialized = true;
      
      // Set up language change listener
      i18n.on('languageChanged', (lng: string) => {
        this.currentLanguage = lng;
        this.emit('languageChanged', lng);
        this.updateDocumentDirection(lng);
      });

      this.updateDocumentDirection(this.currentLanguage);
      this.emit('initialized');
      
      logger.info('I18n initialized successfully', { language: this.currentLanguage });
    } catch (error) {
      logger.error('Failed to initialize i18n', error);
      throw error;
    }
  }

  private updateDocumentDirection(language: string): void {
    const isRTL = this.config.rtlLanguages.includes(language);
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
  }

  public async changeLanguage(language: string): Promise<void> {
    if (!this.config.supportedLanguages.includes(language)) {
      throw new Error(`Unsupported language: ${language}`);
    }

    try {
      await i18n.changeLanguage(language);
      this.currentLanguage = language;
      this.emit('languageChanged', language);
      logger.info('Language changed', { language });
    } catch (error) {
      logger.error('Failed to change language', { language, error });
      throw error;
    }
  }

  public getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  public getSupportedLanguages(): string[] {
    return [...this.config.supportedLanguages];
  }

  public getLocaleInfo(language: string): LocaleInfo | undefined {
    return this.locales.get(language);
  }

  public getAllLocales(): LocaleInfo[] {
    return Array.from(this.locales.values());
  }

  public isRTL(language?: string): boolean {
    const lang = language || this.currentLanguage;
    return this.config.rtlLanguages.includes(lang);
  }

  public translate(key: string, options?: any): string {
    return i18n.t(key, options);
  }

  public exists(key: string, options?: any): boolean {
    return i18n.exists(key, options);
  }

  public addNamespace(namespace: TranslationNamespace): void {
    this.namespaces.set(namespace.name, namespace);
    i18n.loadNamespaces(namespace.name);
  }

  public removeNamespace(name: string): void {
    this.namespaces.delete(name);
  }

  public getNamespaces(): TranslationNamespace[] {
    return Array.from(this.namespaces.values()).sort((a, b) => a.priority - b.priority);
  }

  public async loadTranslations(language: string, namespace: string, translations: Record<string, any>): Promise<void> {
    i18n.addResourceBundle(language, namespace, translations, true, true);
    this.emit('translationsLoaded', { language, namespace });
  }

  public getTranslations(language?: string, namespace?: string): Record<string, any> {
    const lang = language || this.currentLanguage;
    const ns = namespace || 'common';
    return i18n.getResourceBundle(lang, ns) || {};
  }

  public formatNumber(value: number, options?: Intl.NumberFormatOptions): string {
    return new Intl.NumberFormat(this.currentLanguage, options).format(value);
  }

  public formatDate(date: Date, options?: Intl.DateTimeFormatOptions): string {
    return new Intl.DateTimeFormat(this.currentLanguage, options).format(date);
  }

  public formatCurrency(value: number, currency: string): string {
    return new Intl.NumberFormat(this.currentLanguage, {
      style: 'currency',
      currency,
    }).format(value);
  }

  public formatRelativeTime(value: number, unit: Intl.RelativeTimeFormatUnit): string {
    return new Intl.RelativeTimeFormat(this.currentLanguage).format(value, unit);
  }

  public updateConfig(newConfig: Partial<I18nConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  public getConfig(): I18nConfig {
    return { ...this.config };
  }
}

export const i18nManager = I18nManager.getInstance();
