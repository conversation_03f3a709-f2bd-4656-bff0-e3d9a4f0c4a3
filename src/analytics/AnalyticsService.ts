import { AnalyticsEvent } from '../types';
import { logger } from '../logging/Logger';

export class AnalyticsService {
  private static instance: AnalyticsService;
  private events: AnalyticsEvent[] = [];
  private providers: Map<string, any> = new Map();
  private config: {
    enabled: boolean;
    providers: string[];
    events: string[];
  };

  private constructor(config: {
    enabled: boolean;
    providers: string[];
    events: string[];
  }) {
    this.config = config;
    this.initialize();
  }

  public static getInstance(config: {
    enabled: boolean;
    providers: string[];
    events: string[];
  }): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService(config);
    }
    return AnalyticsService.instance;
  }

  private initialize(): void {
    if (this.config.enabled) {
      this.initializeProviders();
    }
  }

  private initializeProviders(): void {
    this.config.providers.forEach(provider => {
      try {
        // Initialize each analytics provider
        switch (provider) {
          case 'google-analytics':
            this.initializeGoogleAnalytics();
            break;
          case 'mixpanel':
            this.initializeMixpanel();
            break;
          default:
            logger.warn(`Unknown analytics provider: ${provider}`);
        }
      } catch (error) {
        logger.error(`Failed to initialize analytics provider ${provider}:`, error);
      }
    });
  }

  private initializeGoogleAnalytics(): void {
    // Implement Google Analytics initialization
  }

  private initializeMixpanel(): void {
    // Implement Mixpanel initialization
  }

  public trackEvent(event: AnalyticsEvent): void {
    if (!this.config.enabled) {
      return;
    }

    try {
      this.events.push(event);
      this.sendToProviders(event);
      this.cleanupOldEvents();
    } catch (error) {
      logger.error('Failed to track event:', error);
    }
  }

  private sendToProviders(event: AnalyticsEvent): void {
    this.providers.forEach((provider, name) => {
      try {
        provider.track(event);
      } catch (error) {
        logger.error(`Failed to send event to provider ${name}:`, error);
      }
    });
  }

  private cleanupOldEvents(): void {
    const oneDayAgo = new Date(Date.now() - 86400000);
    this.events = this.events.filter(event => event.timestamp > oneDayAgo);
  }

  public getEvents(): AnalyticsEvent[] {
    return this.events;
  }

  public getEventsByType(type: string): AnalyticsEvent[] {
    return this.events.filter(event => event.name === type);
  }

  public getEventsByUser(userId: string): AnalyticsEvent[] {
    return this.events.filter(event => event.userId === userId);
  }

  public getEventsBySession(sessionId: string): AnalyticsEvent[] {
    return this.events.filter(event => event.sessionId === sessionId);
  }

  public clearEvents(): void {
    this.events = [];
  }
}

export const analyticsService = AnalyticsService.getInstance({
  enabled: true,
  providers: ['google-analytics', 'mixpanel'],
  events: ['page_view', 'user_action', 'error'],
}); 