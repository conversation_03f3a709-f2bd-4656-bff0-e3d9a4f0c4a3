import { app } from 'electron';
import { promises as fs } from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import { createHash } from 'crypto';
import { performance } from 'perf_hooks';
import { cpus, totalmem, freemem } from 'os';

interface AnalyticsEvent {
  id: string;
  type: string;
  category: string;
  timestamp: number;
  data: any;
  metadata: {
    sessionId: string;
    userId?: string;
    deviceId: string;
    version: string;
    platform: string;
  };
}

interface AnalyticsMetric {
  id: string;
  name: string;
  value: number;
  timestamp: number;
  category: string;
  tags: string[];
}

interface AnalyticsSettings {
  enabled: boolean;
  collection: {
    events: boolean;
    metrics: boolean;
    errors: boolean;
    performance: boolean;
    usage: boolean;
  };
  storage: {
    path: string;
    maxSize: number;
    retention: number;
  };
  privacy: {
    anonymize: boolean;
    maskIP: boolean;
    excludePII: boolean;
  };
  categories: {
    [key: string]: {
      enabled: boolean;
      priority: number;
    };
  };
  export: {
    format: 'json' | 'csv';
    schedule: string;
    destination: string;
  };
}

export class AnalyticsManager extends EventEmitter {
  private static instance: AnalyticsManager;
  private events: Map<string, AnalyticsEvent>;
  private metrics: Map<string, AnalyticsMetric>;
  private settings: AnalyticsSettings;
  private isInitialized: boolean = false;
  private sessionId: string;
  private deviceId: string;
  private collectionInterval?: NodeJS.Timeout;
  private exportInterval?: NodeJS.Timeout;

  private constructor() {
    super();
    this.events = new Map();
    this.metrics = new Map();
    this.settings = {
      enabled: true,
      collection: {
        events: true,
        metrics: true,
        errors: true,
        performance: true,
        usage: true
      },
      storage: {
        path: path.join(app.getPath('userData'), 'analytics'),
        maxSize: 100 * 1024 * 1024, // 100MB
        retention: 30 * 24 * 60 * 60 * 1000 // 30 days
      },
      privacy: {
        anonymize: true,
        maskIP: true,
        excludePII: true
      },
      categories: {
        'performance': { enabled: true, priority: 1 },
        'errors': { enabled: true, priority: 1 },
        'usage': { enabled: true, priority: 2 },
        'navigation': { enabled: true, priority: 2 },
        'interaction': { enabled: true, priority: 3 }
      },
      export: {
        format: 'json',
        schedule: '0 0 * * *', // Daily at midnight
        destination: path.join(app.getPath('userData'), 'analytics-export')
      }
    };
    this.sessionId = Math.random().toString(36).substr(2, 9);
    this.deviceId = this.generateDeviceId();
  }

  public static getInstance(): AnalyticsManager {
    if (!AnalyticsManager.instance) {
      AnalyticsManager.instance = new AnalyticsManager();
    }
    return AnalyticsManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadSettings();
      await this.setupStorage();
      this.startCollection();
      this.startExport();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize AnalyticsManager:', error);
      throw error;
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'analytics-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'analytics-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private async setupStorage(): Promise<void> {
    await fs.mkdir(this.settings.storage.path, { recursive: true });
    await fs.mkdir(this.settings.export.destination, { recursive: true });
  }

  private startCollection(): void {
    if (this.settings.enabled) {
      this.collectionInterval = setInterval(
        () => this.collectMetrics(),
        60000 // Every minute
      );
    }
  }

  private startExport(): void {
    if (this.settings.enabled) {
      // Parse cron schedule and set up export interval
      this.exportInterval = setInterval(
        () => this.exportData(),
        24 * 60 * 60 * 1000 // Daily
      );
    }
  }

  private generateDeviceId(): string {
    const systemInfo = {
      platform: process.platform,
      arch: process.arch,
      cpus: cpus().length,
      memory: totalmem()
    };
    const hash = createHash('sha256');
    hash.update(JSON.stringify(systemInfo));
    return hash.digest('hex');
  }

  public async trackEvent(type: string, category: string, data: any = {}): Promise<void> {
    if (!this.settings.enabled || !this.settings.collection.events) return;

    const event: AnalyticsEvent = {
      id: Math.random().toString(36).substr(2, 9),
      type,
      category,
      timestamp: Date.now(),
      data: this.anonymizeData(data),
      metadata: {
        sessionId: this.sessionId,
        deviceId: this.deviceId,
        version: app.getVersion(),
        platform: process.platform
      }
    };

    this.events.set(event.id, event);
    await this.saveEvent(event);
    this.emit('event-tracked', event);
  }

  public async trackMetric(name: string, value: number, category: string, tags: string[] = []): Promise<void> {
    if (!this.settings.enabled || !this.settings.collection.metrics) return;

    const metric: AnalyticsMetric = {
      id: Math.random().toString(36).substr(2, 9),
      name,
      value,
      timestamp: Date.now(),
      category,
      tags
    };

    this.metrics.set(metric.id, metric);
    await this.saveMetric(metric);
    this.emit('metric-tracked', metric);
  }

  private async collectMetrics(): Promise<void> {
    if (!this.settings.enabled) return;

    try {
      // System metrics
      if (this.settings.collection.performance) {
        const cpuUsage = process.cpuUsage();
        const memoryUsage = process.memoryUsage();
        const freeMemory = freemem();
        const totalMemory = totalmem();

        await this.trackMetric('cpu.usage', cpuUsage.user + cpuUsage.system, 'performance', ['system']);
        await this.trackMetric('memory.used', memoryUsage.heapUsed, 'performance', ['system']);
        await this.trackMetric('memory.free', freeMemory, 'performance', ['system']);
        await this.trackMetric('memory.total', totalMemory, 'performance', ['system']);
      }

      // Application metrics
      if (this.settings.collection.usage) {
        const uptime = process.uptime();
        const activeWindows = app.getWindows().length;

        await this.trackMetric('app.uptime', uptime, 'usage', ['application']);
        await this.trackMetric('app.windows', activeWindows, 'usage', ['application']);
      }
    } catch (error) {
      console.error('Failed to collect metrics:', error);
    }
  }

  private async saveEvent(event: AnalyticsEvent): Promise<void> {
    const eventPath = path.join(this.settings.storage.path, 'events', `${event.id}.json`);
    await fs.writeFile(eventPath, JSON.stringify(event, null, 2));
  }

  private async saveMetric(metric: AnalyticsMetric): Promise<void> {
    const metricPath = path.join(this.settings.storage.path, 'metrics', `${metric.id}.json`);
    await fs.writeFile(metricPath, JSON.stringify(metric, null, 2));
  }

  private async exportData(): Promise<void> {
    if (!this.settings.enabled) return;

    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const exportPath = path.join(
        this.settings.export.destination,
        `analytics-export-${timestamp}`
      );

      const data = {
        events: Array.from(this.events.values()),
        metrics: Array.from(this.metrics.values()),
        metadata: {
          exportTime: Date.now(),
          version: app.getVersion(),
          platform: process.platform
        }
      };

      if (this.settings.export.format === 'json') {
        await fs.writeFile(
          `${exportPath}.json`,
          JSON.stringify(data, null, 2)
        );
      } else {
        // Implement CSV export
      }

      this.emit('data-exported', exportPath);
    } catch (error) {
      console.error('Failed to export analytics data:', error);
    }
  }

  private anonymizeData(data: any): any {
    if (!this.settings.privacy.anonymize) return data;

    const anonymized = { ...data };

    if (this.settings.privacy.maskIP) {
      // Mask IP addresses
      Object.keys(anonymized).forEach(key => {
        if (typeof anonymized[key] === 'string' && this.isIPAddress(anonymized[key])) {
          anonymized[key] = this.maskIP(anonymized[key]);
        }
      });
    }

    if (this.settings.privacy.excludePII) {
      // Remove personally identifiable information
      const piiFields = ['email', 'phone', 'address', 'name', 'username'];
      piiFields.forEach(field => {
        delete anonymized[field];
      });
    }

    return anonymized;
  }

  private isIPAddress(str: string): boolean {
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(str) || ipv6Regex.test(str);
  }

  private maskIP(ip: string): string {
    if (ip.includes(':')) {
      // IPv6
      return ip.split(':').map((part, index) => 
        index < 4 ? part : 'xxxx'
      ).join(':');
    } else {
      // IPv4
      return ip.split('.').map((part, index) => 
        index < 2 ? part : 'xxx'
      ).join('.');
    }
  }

  public getEvents(options: {
    type?: string;
    category?: string;
    startTime?: number;
    endTime?: number;
  } = {}): AnalyticsEvent[] {
    return Array.from(this.events.values()).filter(event => {
      if (options.type && event.type !== options.type) return false;
      if (options.category && event.category !== options.category) return false;
      if (options.startTime && event.timestamp < options.startTime) return false;
      if (options.endTime && event.timestamp > options.endTime) return false;
      return true;
    });
  }

  public getMetrics(options: {
    name?: string;
    category?: string;
    startTime?: number;
    endTime?: number;
    tags?: string[];
  } = {}): AnalyticsMetric[] {
    return Array.from(this.metrics.values()).filter(metric => {
      if (options.name && metric.name !== options.name) return false;
      if (options.category && metric.category !== options.category) return false;
      if (options.startTime && metric.timestamp < options.startTime) return false;
      if (options.endTime && metric.timestamp > options.endTime) return false;
      if (options.tags && !options.tags.every(tag => metric.tags.includes(tag))) return false;
      return true;
    });
  }

  public getSettings(): AnalyticsSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<AnalyticsSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();
    await this.setupStorage();

    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
    }
    if (this.exportInterval) {
      clearInterval(this.exportInterval);
    }

    this.startCollection();
    this.startExport();
  }

  public cleanup(): void {
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
    }
    if (this.exportInterval) {
      clearInterval(this.exportInterval);
    }
  }
} 