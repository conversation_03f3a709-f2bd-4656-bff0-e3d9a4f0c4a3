/**
 * Менеджер синхронизации для A11 Browser
 * Отвечает за синхронизацию данных между устройствами с шифрованием и расширенными возможностями
 */

const { app, ipcMain } = require('electron');
const crypto = require('crypto');
const path = require('path');
const fs = require('fs');
const Store = require('electron-store');

class SyncManager {
  constructor() {
    this.store = new Store();
    this.syncEnabled = false;
    this.syncInterval = null;
    this.syncIntervalTime = 5 * 60 * 1000; // 5 минут по умолчанию
    this.encryptionKey = null;
    this.deviceId = this._generateOrGetDeviceId();
    this.syncQueue = [];
    this.lastSyncTime = 0;
    
    // Инициализация настроек синхронизации
    this._initSyncSettings();
    
    // Регистрация IPC обработчиков
    this._registerIpcHandlers();
  }

  /**
   * Загружает настройки синхронизации
   * @private
   */
  _initSyncSettings() {
    const settings = this.store.get('settings') || {};
    const syncSettings = settings.sync || {};
    
    this.syncEnabled = syncSettings.enabled === true;
    this.syncIntervalTime = syncSettings.interval || this.syncIntervalTime;
    
    // Если синхронизация включена, запускаем интервал
    if (this.syncEnabled) {
      this._startSyncInterval();
    }
  }

  /**
   * Генерирует или получает уникальный идентификатор устройства
   * @returns {string} - Идентификатор устройства
   * @private
   */
  _generateOrGetDeviceId() {
    const settings = this.store.get('settings') || {};
    const syncSettings = settings.sync || {};
    
    if (syncSettings.deviceId) {
      return syncSettings.deviceId;
    }
    
    // Генерируем новый ID устройства
    const deviceId = crypto.randomBytes(16).toString('hex');
    
    // Сохраняем ID устройства
    const updatedSettings = { ...settings };
    updatedSettings.sync = { ...syncSettings, deviceId };
    this.store.set('settings', updatedSettings);
    
    return deviceId;
  }

  /**
   * Запускает интервал синхронизации
   * @private
   */
  _startSyncInterval() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    this.syncInterval = setInterval(() => {
      this.syncData();
    }, this.syncIntervalTime);
    
    console.log(`Интервал синхронизации запущен (${this.syncIntervalTime / 1000} сек)`);
  }

  /**
   * Останавливает интервал синхронизации
   * @private
   */
  _stopSyncInterval() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('Интервал синхронизации остановлен');
    }
  }

  /**
   * Включает или отключает синхронизацию
   * @param {boolean} enabled - Статус включения
   * @param {string} [encryptionKey] - Ключ шифрования (при включении)
   * @returns {boolean} - Успешность операции
   */
  setSyncEnabled(enabled, encryptionKey) {
    try {
      const settings = this.store.get('settings') || {};
      const syncSettings = settings.sync || {};
      
      if (enabled && !encryptionKey) {
        throw new Error('Для включения синхронизации требуется ключ шифрования');
      }
      
      this.syncEnabled = enabled;
      
      if (enabled) {
        // Сохраняем ключ шифрования в памяти (не в хранилище!)
        this.encryptionKey = encryptionKey;
        this._startSyncInterval();
        
        // Выполняем начальную синхронизацию
        this.syncData();
      } else {
        this.encryptionKey = null;
        this._stopSyncInterval();
      }
      
      // Обновляем настройки
      const updatedSettings = { ...settings };
      updatedSettings.sync = { 
        ...syncSettings, 
        enabled, 
        interval: this.syncIntervalTime,
        lastSync: this.lastSyncTime
      };
      this.store.set('settings', updatedSettings);
      
      return true;
    } catch (error) {
      console.error('Ошибка при изменении статуса синхронизации:', error);
      return false;
    }
  }

  /**
   * Устанавливает интервал синхронизации
   * @param {number} intervalMs - Интервал в миллисекундах
   * @returns {boolean} - Успешность операции
   */
  setSyncInterval(intervalMs) {
    try {
      if (intervalMs < 60000) { // Минимум 1 минута
        intervalMs = 60000;
      }
      
      this.syncIntervalTime = intervalMs;
      
      // Перезапускаем интервал, если синхронизация включена
      if (this.syncEnabled) {
        this._startSyncInterval();
      }
      
      // Обновляем настройки
      const settings = this.store.get('settings') || {};
      const syncSettings = settings.sync || {};
      const updatedSettings = { ...settings };
      updatedSettings.sync = { ...syncSettings, interval: intervalMs };
      this.store.set('settings', updatedSettings);
      
      return true;
    } catch (error) {
      console.error('Ошибка при изменении интервала синхронизации:', error);
      return false;
    }
  }

  /**
   * Шифрует данные с использованием ключа пользователя
   * @param {Object} data - Данные для шифрования
   * @returns {string} - Зашифрованные данные в формате Base64
   * @private
   */
  _encryptData(data) {
    if (!this.encryptionKey) {
      throw new Error('Ключ шифрования не установлен');
    }
    
    try {
      // Преобразуем данные в строку JSON
      const jsonData = JSON.stringify(data);
      
      // Генерируем случайный вектор инициализации
      const iv = crypto.randomBytes(16);
      
      // Создаем ключ на основе пароля пользователя
      const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
      
      // Создаем шифр
      const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);
      
      // Шифруем данные
      let encrypted = cipher.update(jsonData, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      
      // Получаем тег аутентификации
      const authTag = cipher.getAuthTag();
      
      // Формируем результат: IV + AuthTag + EncryptedData
      const result = {
        iv: iv.toString('base64'),
        authTag: authTag.toString('base64'),
        data: encrypted,
        version: 1 // Версия формата шифрования
      };
      
      return JSON.stringify(result);
    } catch (error) {
      console.error('Ошибка при шифровании данных:', error);
      throw error;
    }
  }

  /**
   * Расшифровывает данные с использованием ключа пользователя
   * @param {string} encryptedData - Зашифрованные данные в формате Base64
   * @returns {Object} - Расшифрованные данные
   * @private
   */
  _decryptData(encryptedData) {
    if (!this.encryptionKey) {
      throw new Error('Ключ шифрования не установлен');
    }
    
    try {
      // Парсим зашифрованные данные
      const encryptedObj = JSON.parse(encryptedData);
      
      // Проверяем версию формата
      if (encryptedObj.version !== 1) {
        throw new Error(`Неподдерживаемая версия формата шифрования: ${encryptedObj.version}`);
      }
      
      // Получаем компоненты
      const iv = Buffer.from(encryptedObj.iv, 'base64');
      const authTag = Buffer.from(encryptedObj.authTag, 'base64');
      const encryptedText = encryptedObj.data;
      
      // Создаем ключ на основе пароля пользователя
      const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
      
      // Создаем дешифратор
      const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
      decipher.setAuthTag(authTag);
      
      // Расшифровываем данные
      let decrypted = decipher.update(encryptedText, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      
      // Преобразуем JSON обратно в объект
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Ошибка при расшифровке данных:', error);
      throw error;
    }
  }

  /**
   * Синхронизирует данные с сервером или локальным хранилищем
   * @returns {Promise<boolean>} - Успешность операции
   */
  async syncData() {
    if (!this.syncEnabled || !this.encryptionKey) {
      return false;
    }
    
    try {
      console.log('Начало синхронизации данных...');
      
      // Получаем данные для синхронизации
      const dataToSync = this._collectDataForSync();
      
      // Шифруем данные
      const encryptedData = this._encryptData(dataToSync);
      
      // В реальном приложении здесь был бы код для отправки данных на сервер
      // или сохранения в облачное хранилище. Для демонстрации сохраняем локально.
      const syncDir = path.join(app.getPath('userData'), 'sync');
      
      // Создаем директорию, если она не существует
      if (!fs.existsSync(syncDir)) {
        fs.mkdirSync(syncDir, { recursive: true });
      }
      
      // Сохраняем зашифрованные данные
      const syncFilePath = path.join(syncDir, `sync_${this.deviceId}.json`);
      fs.writeFileSync(syncFilePath, encryptedData);
      
      // Обновляем время последней синхронизации
      this.lastSyncTime = Date.now();
      
      // Обновляем настройки
      const settings = this.store.get('settings') || {};
      const syncSettings = settings.sync || {};
      const updatedSettings = { ...settings };
      updatedSettings.sync = { ...syncSettings, lastSync: this.lastSyncTime };
      this.store.set('settings', updatedSettings);
      
      console.log('Синхронизация данных завершена успешно');
      
      // Уведомляем все окна о завершении синхронизации
      this._notifyWindowsAboutSync(true);
      
      return true;
    } catch (error) {
      console.error('Ошибка при синхронизации данных:', error);
      
      // Уведомляем все окна об ошибке синхронизации
      this._notifyWindowsAboutSync(false, error.message);
      
      return false;
    }
  }

  /**
   * Собирает данные для синхронизации
   * @returns {Object} - Данные для синхронизации
   * @private
   */
  _collectDataForSync() {
    const settings = this.store.get('settings') || {};
    
    // Собираем данные, которые нужно синхронизировать
    return {
      deviceId: this.deviceId,
      timestamp: Date.now(),
      data: {
        bookmarks: settings.bookmarks || [],
        history: settings.history || [],
        preferences: {
          theme: settings.theme,
          homepage: settings.homepage,
          adBlockerEnabled: settings.adBlockerEnabled,
          // Не включаем конфиденциальные данные и настройки синхронизации
        },
        extensions: settings.extensions ? settings.extensions.map(ext => ({
          id: ext.id,
          enabled: ext.enabled
        })) : []
      }
    };
  }

  /**
   * Применяет синхронизированные данные
   * @param {Object} syncedData - Синхронизированные данные
   * @returns {boolean} - Успешность операции
   */
  applySync(syncedData) {
    try {
      if (!syncedData || !syncedData.data) {
        throw new Error('Некорректные данные синхронизации');
      }
      
      const settings = this.store.get('settings') || {};
      const updatedSettings = { ...settings };
      
      // Применяем синхронизированные данные
      if (syncedData.data.bookmarks) {
        updatedSettings.bookmarks = this._mergeArrays(
          settings.bookmarks || [], 
          syncedData.data.bookmarks,
          'url'
        );
      }
      
      if (syncedData.data.history) {
        updatedSettings.history = this._mergeArrays(
          settings.history || [], 
          syncedData.data.history,
          'url',
          1000 // Ограничиваем историю 1000 записями
        );
      }
      
      if (syncedData.data.preferences) {
        // Объединяем настройки, сохраняя локальные приоритетными
        updatedSettings.theme = settings.theme || syncedData.data.preferences.theme;
        updatedSettings.homepage = settings.homepage || syncedData.data.preferences.homepage;
        // Для настроек типа boolean используем локальное значение, если оно определено
        if (settings.adBlockerEnabled === undefined && syncedData.data.preferences.adBlockerEnabled !== undefined) {
          updatedSettings.adBlockerEnabled = syncedData.data.preferences.adBlockerEnabled;
        }
      }
      
      if (syncedData.data.extensions) {
        // Обновляем состояние расширений, сохраняя локальные настройки приоритетными
        const localExtensions = settings.extensions || [];
        const syncedExtensions = syncedData.data.extensions;
        
        // Создаем карту локальных расширений для быстрого поиска
        const localExtMap = new Map();
        localExtensions.forEach(ext => localExtMap.set(ext.id, ext));
        
        // Добавляем синхронизированные расширения, которых нет локально
        syncedExtensions.forEach(syncedExt => {
          if (!localExtMap.has(syncedExt.id)) {
            localExtensions.push(syncedExt);
          }
        });
        
        updatedSettings.extensions = localExtensions;
      }
      
      // Сохраняем обновленные настройки
      this.store.set('settings', updatedSettings);
      
      console.log('Синхронизированные данные успешно применены');
      return true;
    } catch (error) {
      console.error('Ошибка при применении синхронизированных данных:', error);
      return false;
    }
  }

  /**
   * Объединяет два массива объектов по ключу
   * @param {Array} localArray - Локальный массив
   * @param {Array} syncedArray - Синхронизированный массив
   * @param {string} keyField - Поле для идентификации уникальных элементов
   * @param {number} [limit] - Ограничение на количество элементов
   * @returns {Array} - Объединенный массив
   * @private
   */
  _mergeArrays(localArray, syncedArray, keyField, limit) {
    // Создаем карту для быстрого поиска по ключу
    const mergeMap = new Map();
    
    // Добавляем локальные элементы в карту
    localArray.forEach(item => {
      if (item[keyField]) {
        mergeMap.set(item[keyField], item);
      }
    });
    
    // Добавляем или обновляем элементы из синхронизированного массива
    syncedArray.forEach(item => {
      if (item[keyField]) {
        // Если элемент уже есть, выбираем более новый по timestamp
        const existingItem = mergeMap.get(item[keyField]);
        if (!existingItem || (item.timestamp && existingItem.timestamp && item.timestamp > existingItem.timestamp)) {
          mergeMap.set(item[keyField], item);
        }
      }
    });
    
    // Преобразуем карту обратно в массив и сортируем по timestamp (если есть)
    let result = Array.from(mergeMap.values());
    result.sort((a, b) => {
      if (a.timestamp && b.timestamp) {
        return b.timestamp - a.timestamp; // Сортировка по убыванию (новые сверху)
      }
      return 0;
    });
    
    // Ограничиваем количество элементов, если указан лимит
    if (limit && result.length > limit) {
      result = result.slice(0, limit);
    }
    
    return result;
  }

  /**
   * Уведомляет все окна о результате синхронизации
   * @param {boolean} success - Успешность синхронизации
   * @param {string} [errorMessage] - Сообщение об ошибке (если есть)
   * @private
   */
  _notifyWindowsAboutSync(success, errorMessage) {
    const { BrowserWindow } = require('electron');
    const windows = BrowserWindow.getAllWindows();
    
    for (const window of windows) {
      if (!window.isDestroyed()) {
        window.webContents.send('sync-status-update', {
          success,
          timestamp: Date.now(),
          error: errorMessage
        });
      }
    }
  }

  /**
   * Регистрирует IPC обработчики для взаимодействия с рендерером
   * @private
   */
  _registerIpcHandlers() {
    // Включение/отключение синхронизации
    ipcMain.handle('set-sync-enabled', (event, enabled, encryptionKey) => {
      return this.setSyncEnabled(enabled, encryptionKey);
    });
    
    // Установка интервала синхронизации
    ipcMain.handle('set-sync-interval', (event, intervalMs) => {
      return this.setSyncInterval(intervalMs);
    });
    
    // Запуск синхронизации вручную
    ipcMain.handle('sync-now', () => {
      return this.syncData();
    });
    
    // Получение статуса синхронизации
    ipcMain.handle('get-sync-status', () => {
      const settings = this.store.get('settings') || {};
      const syncSettings = settings.sync || {};
      
      return {
        enabled: this.syncEnabled,
        lastSync: this.lastSyncTime || syncSettings.lastSync || 0,
        interval: this.syncIntervalTime,
        deviceId: this.deviceId
      };
    });
  }
}

module.exports = SyncManager;