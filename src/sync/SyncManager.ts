import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';
import { configManager } from '../core/ConfigurationManager';
import { cryptographicService } from '../security/CryptographicService';

export interface SyncConfig {
  enableSync: boolean;
  syncServer: string;
  syncInterval: number;
  enableEncryption: boolean;
  enableConflictResolution: boolean;
  maxSyncRetries: number;
  syncTimeout: number;
  enableOfflineMode: boolean;
  enableDeltaSync: boolean;
  compressionEnabled: boolean;
  syncDataTypes: SyncDataType[];
}

export type SyncDataType = 'bookmarks' | 'history' | 'passwords' | 'settings' | 'extensions' | 'tabs' | 'downloads';

export interface SyncData {
  id: string;
  type: SyncDataType;
  data: any;
  timestamp: number;
  version: number;
  checksum: string;
  encrypted: boolean;
  deviceId: string;
  userId: string;
}

export interface SyncConflict {
  id: string;
  type: SyncDataType;
  localData: SyncData;
  remoteData: SyncData;
  conflictType: 'version' | 'timestamp' | 'content';
  resolution: 'local' | 'remote' | 'merge' | 'manual';
  resolved: boolean;
  resolvedAt?: number;
  resolvedBy?: string;
}

export interface SyncSession {
  id: string;
  startTime: number;
  endTime?: number;
  status: 'active' | 'completed' | 'failed' | 'cancelled';
  direction: 'upload' | 'download' | 'bidirectional';
  dataTypes: SyncDataType[];
  itemsProcessed: number;
  itemsTotal: number;
  bytesTransferred: number;
  conflicts: SyncConflict[];
  errors: SyncError[];
}

export interface SyncError {
  id: string;
  type: 'network' | 'authentication' | 'encryption' | 'validation' | 'conflict' | 'storage';
  message: string;
  timestamp: number;
  retryable: boolean;
  retryCount: number;
  data?: any;
}

export interface SyncDevice {
  id: string;
  name: string;
  type: 'desktop' | 'mobile' | 'tablet' | 'server';
  platform: string;
  version: string;
  lastSync: number;
  online: boolean;
  trusted: boolean;
  encryptionKey?: string;
}

export interface SyncStats {
  totalSessions: number;
  successfulSessions: number;
  failedSessions: number;
  totalDataSynced: number;
  totalConflicts: number;
  resolvedConflicts: number;
  averageSyncTime: number;
  lastSyncTime: number;
  syncFrequency: number;
}

export class SyncManager extends EventEmitter {
  private static instance: SyncManager;
  private config: SyncConfig;
  private currentSession: SyncSession | null = null;
  private syncData: Map<string, SyncData> = new Map();
  private conflicts: Map<string, SyncConflict> = new Map();
  private devices: Map<string, SyncDevice> = new Map();
  private syncTimer: NodeJS.Timeout | null = null;
  private offlineQueue: SyncData[] = [];
  private isOnline = true;
  private deviceId: string;
  private userId: string | null = null;

  private constructor() {
    super();
    this.config = {
      enableSync: false,
      syncServer: 'https://sync.a14browser.com',
      syncInterval: 300000, // 5 minutes
      enableEncryption: true,
      enableConflictResolution: true,
      maxSyncRetries: 3,
      syncTimeout: 60000, // 1 minute
      enableOfflineMode: true,
      enableDeltaSync: true,
      compressionEnabled: true,
      syncDataTypes: ['bookmarks', 'history', 'settings'],
    };

    this.deviceId = this.generateDeviceId();
    this.initializeSyncManager();
  }

  public static getInstance(): SyncManager {
    if (!SyncManager.instance) {
      SyncManager.instance = new SyncManager();
    }
    return SyncManager.instance;
  }

  private async initializeSyncManager(): Promise<void> {
    // Загрузка конфигурации
    const syncConfig = configManager.get('sync', {});
    this.config = { ...this.config, ...syncConfig };

    // Регистрация устройства
    await this.registerDevice();

    // Настройка мониторинга сети
    this.setupNetworkMonitoring();

    // Загрузка локальных данных синхронизации
    await this.loadLocalSyncData();

    // Запуск автоматической синхронизации
    if (this.config.enableSync) {
      this.startAutoSync();
    }

    logger.info('Sync manager initialized', {
      deviceId: this.deviceId,
      enableSync: this.config.enableSync,
      syncInterval: this.config.syncInterval,
      dataTypes: this.config.syncDataTypes,
    });
  }

  public async enableSync(userId: string, authToken: string): Promise<void> {
    try {
      this.userId = userId;

      // Аутентификация на сервере синхронизации
      const authResult = await this.authenticateWithSyncServer(userId, authToken);
      if (!authResult.success) {
        throw new Error('Authentication failed');
      }

      // Включение синхронизации
      this.config.enableSync = true;
      await this.updateConfig({ enableSync: true });

      // Первоначальная синхронизация
      await this.performInitialSync();

      // Запуск автоматической синхронизации
      this.startAutoSync();

      this.emit('sync_enabled', { userId, deviceId: this.deviceId });
      logger.info('Sync enabled', { userId, deviceId: this.deviceId });
    } catch (error) {
      logger.error('Failed to enable sync', error, { userId });
      throw error;
    }
  }

  public async disableSync(): Promise<void> {
    try {
      // Остановка автоматической синхронизации
      this.stopAutoSync();

      // Отключение синхронизации
      this.config.enableSync = false;
      await this.updateConfig({ enableSync: false });

      // Очистка данных синхронизации (опционально)
      // await this.clearSyncData();

      this.userId = null;

      this.emit('sync_disabled', { deviceId: this.deviceId });
      logger.info('Sync disabled', { deviceId: this.deviceId });
    } catch (error) {
      logger.error('Failed to disable sync', error);
      throw error;
    }
  }

  public async syncNow(dataTypes?: SyncDataType[]): Promise<SyncSession> {
    if (!this.config.enableSync) {
      throw new Error('Sync is not enabled');
    }

    if (this.currentSession && this.currentSession.status === 'active') {
      throw new Error('Sync session already in progress');
    }

    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const typesToSync = dataTypes || this.config.syncDataTypes;

    this.currentSession = {
      id: sessionId,
      startTime: Date.now(),
      status: 'active',
      direction: 'bidirectional',
      dataTypes: typesToSync,
      itemsProcessed: 0,
      itemsTotal: 0,
      bytesTransferred: 0,
      conflicts: [],
      errors: [],
    };

    try {
      this.emit('sync_started', this.currentSession);
      logger.info('Sync session started', {
        sessionId,
        dataTypes: typesToSync,
        direction: this.currentSession.direction,
      });

      // Подготовка данных для синхронизации
      const localData = await this.prepareLocalData(typesToSync);
      this.currentSession.itemsTotal = localData.length;

      // Получение удаленных данных
      const remoteData = await this.fetchRemoteData(typesToSync);

      // Обнаружение конфликтов
      const conflicts = this.detectConflicts(localData, remoteData);
      this.currentSession.conflicts = conflicts;

      // Разрешение конфликтов
      if (conflicts.length > 0 && this.config.enableConflictResolution) {
        await this.resolveConflicts(conflicts);
      }

      // Синхронизация данных
      await this.performDataSync(localData, remoteData);

      // Завершение сессии
      this.currentSession.status = 'completed';
      this.currentSession.endTime = Date.now();

      this.emit('sync_completed', this.currentSession);
      logger.info('Sync session completed', {
        sessionId,
        duration: this.currentSession.endTime - this.currentSession.startTime,
        itemsProcessed: this.currentSession.itemsProcessed,
        conflicts: this.currentSession.conflicts.length,
      });

      return this.currentSession;
    } catch (error) {
      this.currentSession.status = 'failed';
      this.currentSession.endTime = Date.now();

      const syncError: SyncError = {
        id: `error_${Date.now()}`,
        type: 'network',
        message: error instanceof Error ? error.message : String(error),
        timestamp: Date.now(),
        retryable: true,
        retryCount: 0,
      };

      this.currentSession.errors.push(syncError);

      this.emit('sync_failed', { session: this.currentSession, error });
      logger.error('Sync session failed', error, { sessionId });

      throw error;
    }
  }

  public async addSyncData(type: SyncDataType, data: any): Promise<void> {
    if (!this.config.syncDataTypes.includes(type)) {
      return;
    }

    const syncData: SyncData = {
      id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data: this.config.enableEncryption ? await this.encryptData(data) : data,
      timestamp: Date.now(),
      version: 1,
      checksum: await this.calculateChecksum(data),
      encrypted: this.config.enableEncryption,
      deviceId: this.deviceId,
      userId: this.userId || 'anonymous',
    };

    this.syncData.set(syncData.id, syncData);

    // Добавление в очередь офлайн-синхронизации
    if (!this.isOnline && this.config.enableOfflineMode) {
      this.offlineQueue.push(syncData);
    } else if (this.config.enableSync) {
      // Немедленная синхронизация для критических данных
      if (this.isCriticalDataType(type)) {
        await this.syncSingleItem(syncData);
      }
    }

    this.emit('sync_data_added', syncData);
    logger.debug('Sync data added', {
      id: syncData.id,
      type: syncData.type,
      encrypted: syncData.encrypted,
    });
  }

  public async updateSyncData(id: string, data: any): Promise<void> {
    const existingData = this.syncData.get(id);
    if (!existingData) {
      throw new Error(`Sync data ${id} not found`);
    }

    existingData.data = this.config.enableEncryption ? await this.encryptData(data) : data;
    existingData.timestamp = Date.now();
    existingData.version++;
    existingData.checksum = await this.calculateChecksum(data);

    // Добавление в очередь офлайн-синхронизации
    if (!this.isOnline && this.config.enableOfflineMode) {
      this.offlineQueue.push(existingData);
    } else if (this.config.enableSync) {
      // Немедленная синхронизация для критических данных
      if (this.isCriticalDataType(existingData.type)) {
        await this.syncSingleItem(existingData);
      }
    }

    this.emit('sync_data_updated', existingData);
    logger.debug('Sync data updated', {
      id: existingData.id,
      type: existingData.type,
      version: existingData.version,
    });
  }

  public async deleteSyncData(id: string): Promise<void> {
    const syncData = this.syncData.get(id);
    if (!syncData) {
      return;
    }

    this.syncData.delete(id);

    // Отправка команды удаления на сервер
    if (this.config.enableSync && this.isOnline) {
      await this.sendDeleteCommand(id);
    }

    this.emit('sync_data_deleted', { id, syncData });
    logger.debug('Sync data deleted', { id, type: syncData.type });
  }

  private async performInitialSync(): Promise<void> {
    logger.info('Performing initial sync');

    // Загрузка всех данных с сервера
    const remoteData = await this.fetchAllRemoteData();

    // Объединение с локальными данными
    for (const data of remoteData) {
      const existingData = Array.from(this.syncData.values())
        .find(local => local.type === data.type && this.isSameItem(local, data));

      if (existingData) {
        // Разрешение конфликта
        const resolvedData = await this.resolveDataConflict(existingData, data);
        this.syncData.set(resolvedData.id, resolvedData);
      } else {
        // Добавление нового элемента
        this.syncData.set(data.id, data);
      }
    }

    // Отправка локальных данных на сервер
    const localData = Array.from(this.syncData.values());
    await this.uploadDataToServer(localData);
  }

  private async prepareLocalData(dataTypes: SyncDataType[]): Promise<SyncData[]> {
    return Array.from(this.syncData.values())
      .filter(data => dataTypes.includes(data.type));
  }

  private async fetchRemoteData(dataTypes: SyncDataType[]): Promise<SyncData[]> {
    // В реальной реализации здесь будет запрос к серверу синхронизации
    return [];
  }

  private async fetchAllRemoteData(): Promise<SyncData[]> {
    // В реальной реализации здесь будет запрос всех данных с сервера
    return [];
  }

  private detectConflicts(localData: SyncData[], remoteData: SyncData[]): SyncConflict[] {
    const conflicts: SyncConflict[] = [];

    for (const local of localData) {
      const remote = remoteData.find(r => this.isSameItem(local, r));
      if (remote && this.hasConflict(local, remote)) {
        conflicts.push({
          id: `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: local.type,
          localData: local,
          remoteData: remote,
          conflictType: this.getConflictType(local, remote),
          resolution: 'manual',
          resolved: false,
        });
      }
    }

    return conflicts;
  }

  private async resolveConflicts(conflicts: SyncConflict[]): Promise<void> {
    for (const conflict of conflicts) {
      try {
        const resolution = await this.determineConflictResolution(conflict);
        conflict.resolution = resolution;

        switch (resolution) {
          case 'local':
            // Использовать локальную версию
            break;
          case 'remote':
            // Использовать удаленную версию
            this.syncData.set(conflict.remoteData.id, conflict.remoteData);
            break;
          case 'merge':
            // Объединить данные
            const mergedData = await this.mergeConflictData(conflict);
            this.syncData.set(mergedData.id, mergedData);
            break;
          case 'manual':
            // Требуется ручное разрешение
            this.conflicts.set(conflict.id, conflict);
            this.emit('sync_conflict_detected', conflict);
            continue;
        }

        conflict.resolved = true;
        conflict.resolvedAt = Date.now();
        conflict.resolvedBy = 'automatic';

        this.emit('sync_conflict_resolved', conflict);
      } catch (error) {
        logger.error('Failed to resolve conflict', error, { conflictId: conflict.id });
      }
    }
  }

  private async performDataSync(localData: SyncData[], remoteData: SyncData[]): Promise<void> {
    // Загрузка локальных изменений на сервер
    const localChanges = localData.filter(local => 
      !remoteData.find(remote => this.isSameItem(local, remote) && local.version <= remote.version)
    );

    if (localChanges.length > 0) {
      await this.uploadDataToServer(localChanges);
    }

    // Загрузка удаленных изменений
    const remoteChanges = remoteData.filter(remote => 
      !localData.find(local => this.isSameItem(local, remote) && remote.version <= local.version)
    );

    for (const remoteChange of remoteChanges) {
      this.syncData.set(remoteChange.id, remoteChange);
    }

    if (this.currentSession) {
      this.currentSession.itemsProcessed = localChanges.length + remoteChanges.length;
      this.currentSession.bytesTransferred = this.calculateDataSize(localChanges) + this.calculateDataSize(remoteChanges);
    }
  }

  private async syncSingleItem(syncData: SyncData): Promise<void> {
    try {
      await this.uploadDataToServer([syncData]);
      logger.debug('Single item synced', { id: syncData.id, type: syncData.type });
    } catch (error) {
      logger.error('Failed to sync single item', error, { id: syncData.id });
    }
  }

  private async uploadDataToServer(data: SyncData[]): Promise<void> {
    // В реальной реализации здесь будет отправка данных на сервер
    logger.debug('Uploading data to server', { count: data.length });
  }

  private async sendDeleteCommand(id: string): Promise<void> {
    // В реальной реализации здесь будет отправка команды удаления на сервер
    logger.debug('Sending delete command', { id });
  }

  private async authenticateWithSyncServer(userId: string, authToken: string): Promise<{ success: boolean }> {
    // В реальной реализации здесь будет аутентификация на сервере
    return { success: true };
  }

  private async encryptData(data: any): Promise<any> {
    if (!this.config.enableEncryption) {
      return data;
    }

    try {
      const encrypted = await cryptographicService.encrypt(JSON.stringify(data));
      return { encrypted: true, data: encrypted };
    } catch (error) {
      logger.error('Failed to encrypt sync data', error);
      return data;
    }
  }

  private async decryptData(data: any): Promise<any> {
    if (!data.encrypted) {
      return data;
    }

    try {
      const decrypted = await cryptographicService.decrypt(data.data);
      return JSON.parse(decrypted);
    } catch (error) {
      logger.error('Failed to decrypt sync data', error);
      return data;
    }
  }

  private async calculateChecksum(data: any): Promise<string> {
    const dataString = JSON.stringify(data);
    return await cryptographicService.hash(dataString);
  }

  private isSameItem(local: SyncData, remote: SyncData): boolean {
    return local.type === remote.type && 
           local.checksum === remote.checksum;
  }

  private hasConflict(local: SyncData, remote: SyncData): boolean {
    return local.version !== remote.version || 
           local.timestamp !== remote.timestamp ||
           local.checksum !== remote.checksum;
  }

  private getConflictType(local: SyncData, remote: SyncData): SyncConflict['conflictType'] {
    if (local.version !== remote.version) return 'version';
    if (local.timestamp !== remote.timestamp) return 'timestamp';
    return 'content';
  }

  private async determineConflictResolution(conflict: SyncConflict): Promise<SyncConflict['resolution']> {
    // Автоматическое разрешение конфликтов на основе правил
    switch (conflict.conflictType) {
      case 'timestamp':
        // Использовать более новую версию
        return conflict.localData.timestamp > conflict.remoteData.timestamp ? 'local' : 'remote';
      case 'version':
        // Использовать версию с большим номером
        return conflict.localData.version > conflict.remoteData.version ? 'local' : 'remote';
      default:
        // Требуется ручное разрешение
        return 'manual';
    }
  }

  private async mergeConflictData(conflict: SyncConflict): Promise<SyncData> {
    // Объединение конфликтующих данных
    const mergedData = { ...conflict.localData };
    mergedData.version = Math.max(conflict.localData.version, conflict.remoteData.version) + 1;
    mergedData.timestamp = Date.now();
    mergedData.checksum = await this.calculateChecksum(mergedData.data);

    return mergedData;
  }

  private async resolveDataConflict(local: SyncData, remote: SyncData): Promise<SyncData> {
    // Простое разрешение: использовать более новую версию
    return local.timestamp > remote.timestamp ? local : remote;
  }

  private isCriticalDataType(type: SyncDataType): boolean {
    return ['passwords', 'settings'].includes(type);
  }

  private calculateDataSize(data: SyncData[]): number {
    return data.reduce((size, item) => size + JSON.stringify(item).length, 0);
  }

  private generateDeviceId(): string {
    return `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async registerDevice(): Promise<void> {
    const device: SyncDevice = {
      id: this.deviceId,
      name: 'A14 Browser',
      type: 'desktop',
      platform: process.platform,
      version: '1.0.0',
      lastSync: 0,
      online: true,
      trusted: true,
    };

    this.devices.set(this.deviceId, device);
  }

  private setupNetworkMonitoring(): void {
    // Мониторинг состояния сети
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        this.isOnline = true;
        this.processOfflineQueue();
        this.emit('network_online');
      });

      window.addEventListener('offline', () => {
        this.isOnline = false;
        this.emit('network_offline');
      });
    }
  }

  private async processOfflineQueue(): Promise<void> {
    if (this.offlineQueue.length === 0) {
      return;
    }

    try {
      await this.uploadDataToServer(this.offlineQueue);
      this.offlineQueue = [];
      this.emit('offline_queue_processed');
      logger.info('Offline queue processed');
    } catch (error) {
      logger.error('Failed to process offline queue', error);
    }
  }

  private startAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(async () => {
      if (this.config.enableSync && this.isOnline) {
        try {
          await this.syncNow();
        } catch (error) {
          logger.warn('Auto sync failed', error);
        }
      }
    }, this.config.syncInterval);
  }

  private stopAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }

  private async loadLocalSyncData(): Promise<void> {
    // Загрузка локальных данных синхронизации
    logger.debug('Loading local sync data');
  }

  // Геттеры
  public getSyncData(): SyncData[] {
    return Array.from(this.syncData.values());
  }

  public getConflicts(): SyncConflict[] {
    return Array.from(this.conflicts.values());
  }

  public getCurrentSession(): SyncSession | null {
    return this.currentSession;
  }

  public getDevices(): SyncDevice[] {
    return Array.from(this.devices.values());
  }

  public getSyncStats(): SyncStats {
    // Расчет статистики синхронизации
    return {
      totalSessions: 0,
      successfulSessions: 0,
      failedSessions: 0,
      totalDataSynced: this.syncData.size,
      totalConflicts: this.conflicts.size,
      resolvedConflicts: Array.from(this.conflicts.values()).filter(c => c.resolved).length,
      averageSyncTime: 0,
      lastSyncTime: 0,
      syncFrequency: this.config.syncInterval,
    };
  }

  public isOnlineMode(): boolean {
    return this.isOnline;
  }

  public isSyncEnabled(): boolean {
    return this.config.enableSync;
  }

  public async updateConfig(config: Partial<SyncConfig>): Promise<void> {
    this.config = { ...this.config, ...config };
    configManager.set('sync', this.config);

    // Перезапуск автоматической синхронизации при изменении интервала
    if (config.syncInterval && this.config.enableSync) {
      this.startAutoSync();
    }

    this.emit('config_updated', this.config);
  }

  public getConfig(): SyncConfig {
    return { ...this.config };
  }

  public destroy(): void {
    this.stopAutoSync();
    this.removeAllListeners();
  }
}

// Экспорт синглтона
export const syncManager = SyncManager.getInstance();
