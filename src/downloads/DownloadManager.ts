import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';
import { configManager } from '../core/ConfigurationManager';
import { securityScanner } from '../security/SecurityScanner';

export interface Download {
  id: string;
  url: string;
  filename: string;
  filepath: string;
  mimeType: string;
  totalBytes: number;
  receivedBytes: number;
  status: 'pending' | 'downloading' | 'paused' | 'completed' | 'cancelled' | 'error';
  progress: number; // 0-100
  speed: number; // bytes per second
  timeRemaining: number; // seconds
  startTime: number;
  endTime?: number;
  error?: string;
  canResume: boolean;
  isSecure: boolean;
  referrer?: string;
  userAgent: string;
  headers: Record<string, string>;
  chunks: DownloadChunk[];
  virusScanResult?: VirusScanResult;
}

export interface DownloadChunk {
  id: string;
  start: number;
  end: number;
  size: number;
  downloaded: number;
  status: 'pending' | 'downloading' | 'completed' | 'error';
}

export interface VirusScanResult {
  scanned: boolean;
  clean: boolean;
  threats: string[];
  scanTime: number;
  engine: string;
}

export interface DownloadConfig {
  defaultDownloadPath: string;
  maxConcurrentDownloads: number;
  maxDownloadSpeed: number; // bytes per second, 0 = unlimited
  enableVirusScanning: boolean;
  enableResumeSupport: boolean;
  enableChunkedDownloads: boolean;
  chunkSize: number;
  maxChunks: number;
  autoScanDownloads: boolean;
  quarantineSuspiciousFiles: boolean;
  downloadTimeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export interface DownloadHistory {
  id: string;
  downloads: Download[];
  totalDownloads: number;
  totalBytes: number;
  averageSpeed: number;
  successRate: number;
  lastCleanup: number;
}

export class DownloadManager extends EventEmitter {
  private static instance: DownloadManager;
  private config: DownloadConfig;
  private downloads: Map<string, Download> = new Map();
  private activeDownloads: Set<string> = new Set();
  private downloadQueue: string[] = [];
  private speedLimiter: NodeJS.Timeout | null = null;
  private cleanupTimer: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    this.config = {
      defaultDownloadPath: './downloads',
      maxConcurrentDownloads: 3,
      maxDownloadSpeed: 0, // Unlimited
      enableVirusScanning: true,
      enableResumeSupport: true,
      enableChunkedDownloads: true,
      chunkSize: 1024 * 1024, // 1MB
      maxChunks: 8,
      autoScanDownloads: true,
      quarantineSuspiciousFiles: true,
      downloadTimeout: 300000, // 5 minutes
      retryAttempts: 3,
      retryDelay: 5000, // 5 seconds
    };

    this.initializeDownloadManager();
  }

  public static getInstance(): DownloadManager {
    if (!DownloadManager.instance) {
      DownloadManager.instance = new DownloadManager();
    }
    return DownloadManager.instance;
  }

  private async initializeDownloadManager(): Promise<void> {
    // Загрузка конфигурации
    const downloadConfig = configManager.get('downloads', {});
    this.config = { ...this.config, ...downloadConfig };

    // Восстановление незавершенных загрузок
    await this.restoreIncompleteDownloads();

    // Настройка ограничения скорости
    if (this.config.maxDownloadSpeed > 0) {
      this.setupSpeedLimiter();
    }

    // Настройка автоочистки
    this.setupAutoCleanup();

    logger.info('Download manager initialized', {
      maxConcurrentDownloads: this.config.maxConcurrentDownloads,
      enableVirusScanning: this.config.enableVirusScanning,
      enableResumeSupport: this.config.enableResumeSupport,
    });
  }

  public async startDownload(downloadData: {
    url: string;
    filename?: string;
    filepath?: string;
    headers?: Record<string, string>;
    referrer?: string;
  }): Promise<Download> {
    try {
      // Проверка URL
      if (!this.isValidUrl(downloadData.url)) {
        throw new Error('Invalid download URL');
      }

      // Проверка безопасности URL
      const securityCheck = await this.performSecurityCheck(downloadData.url);
      if (!securityCheck.safe) {
        throw new Error(`Security check failed: ${securityCheck.reason}`);
      }

      // Получение информации о файле
      const fileInfo = await this.getFileInfo(downloadData.url, downloadData.headers);
      
      const downloadId = `download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const filename = downloadData.filename || fileInfo.filename || this.extractFilenameFromUrl(downloadData.url);
      const filepath = downloadData.filepath || this.getDefaultFilePath(filename);

      const download: Download = {
        id: downloadId,
        url: downloadData.url,
        filename,
        filepath,
        mimeType: fileInfo.mimeType,
        totalBytes: fileInfo.size,
        receivedBytes: 0,
        status: 'pending',
        progress: 0,
        speed: 0,
        timeRemaining: 0,
        startTime: Date.now(),
        canResume: fileInfo.supportsResume,
        isSecure: downloadData.url.startsWith('https://'),
        referrer: downloadData.referrer,
        userAgent: 'A14Browser/1.0.0',
        headers: downloadData.headers || {},
        chunks: [],
      };

      // Создание чанков для многопоточной загрузки
      if (this.config.enableChunkedDownloads && fileInfo.supportsResume && fileInfo.size > this.config.chunkSize) {
        download.chunks = this.createDownloadChunks(fileInfo.size);
      }

      this.downloads.set(downloadId, download);

      // Добавление в очередь
      this.downloadQueue.push(downloadId);
      this.processDownloadQueue();

      this.emit('download_started', download);
      logger.info('Download started', {
        downloadId,
        url: download.url,
        filename: download.filename,
        totalBytes: download.totalBytes,
      });

      return download;
    } catch (error) {
      logger.error('Failed to start download', error, { url: downloadData.url });
      throw error;
    }
  }

  public async pauseDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (!download) {
      throw new Error(`Download ${downloadId} not found`);
    }

    if (download.status !== 'downloading') {
      throw new Error(`Cannot pause download with status: ${download.status}`);
    }

    download.status = 'paused';
    this.activeDownloads.delete(downloadId);

    this.emit('download_paused', download);
    logger.info('Download paused', { downloadId, filename: download.filename });

    // Обработка следующего в очереди
    this.processDownloadQueue();
  }

  public async resumeDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (!download) {
      throw new Error(`Download ${downloadId} not found`);
    }

    if (download.status !== 'paused') {
      throw new Error(`Cannot resume download with status: ${download.status}`);
    }

    if (!download.canResume) {
      throw new Error('Download does not support resume');
    }

    // Добавление обратно в очередь
    this.downloadQueue.unshift(downloadId);
    this.processDownloadQueue();

    this.emit('download_resumed', download);
    logger.info('Download resumed', { downloadId, filename: download.filename });
  }

  public async cancelDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (!download) {
      throw new Error(`Download ${downloadId} not found`);
    }

    download.status = 'cancelled';
    download.endTime = Date.now();
    this.activeDownloads.delete(downloadId);

    // Удаление из очереди
    const queueIndex = this.downloadQueue.indexOf(downloadId);
    if (queueIndex > -1) {
      this.downloadQueue.splice(queueIndex, 1);
    }

    // Удаление частично загруженного файла
    await this.cleanupPartialFile(download);

    this.emit('download_cancelled', download);
    logger.info('Download cancelled', { downloadId, filename: download.filename });

    // Обработка следующего в очереди
    this.processDownloadQueue();
  }

  public async retryDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (!download) {
      throw new Error(`Download ${downloadId} not found`);
    }

    if (download.status !== 'error') {
      throw new Error(`Cannot retry download with status: ${download.status}`);
    }

    // Сброс состояния
    download.status = 'pending';
    download.error = undefined;
    download.receivedBytes = 0;
    download.progress = 0;
    download.chunks.forEach(chunk => {
      chunk.status = 'pending';
      chunk.downloaded = 0;
    });

    // Добавление в очередь
    this.downloadQueue.push(downloadId);
    this.processDownloadQueue();

    this.emit('download_retried', download);
    logger.info('Download retried', { downloadId, filename: download.filename });
  }

  private async processDownloadQueue(): Promise<void> {
    // Проверка лимита одновременных загрузок
    if (this.activeDownloads.size >= this.config.maxConcurrentDownloads) {
      return;
    }

    // Получение следующей загрузки из очереди
    const downloadId = this.downloadQueue.shift();
    if (!downloadId) {
      return;
    }

    const download = this.downloads.get(downloadId);
    if (!download || download.status !== 'pending') {
      // Обработка следующей загрузки
      this.processDownloadQueue();
      return;
    }

    // Начало загрузки
    this.activeDownloads.add(downloadId);
    await this.performDownload(download);
  }

  private async performDownload(download: Download): Promise<void> {
    download.status = 'downloading';
    download.startTime = Date.now();

    try {
      if (download.chunks.length > 0) {
        // Многопоточная загрузка
        await this.performChunkedDownload(download);
      } else {
        // Обычная загрузка
        await this.performSingleDownload(download);
      }

      // Проверка целостности файла
      await this.verifyDownload(download);

      // Сканирование на вирусы
      if (this.config.enableVirusScanning && this.config.autoScanDownloads) {
        await this.scanDownloadForViruses(download);
      }

      download.status = 'completed';
      download.endTime = Date.now();
      download.progress = 100;

      this.emit('download_completed', download);
      logger.info('Download completed', {
        downloadId: download.id,
        filename: download.filename,
        totalBytes: download.totalBytes,
        duration: download.endTime - download.startTime,
      });

    } catch (error) {
      download.status = 'error';
      download.error = error instanceof Error ? error.message : String(error);
      download.endTime = Date.now();

      this.emit('download_error', { download, error });
      logger.error('Download failed', error, {
        downloadId: download.id,
        filename: download.filename,
      });

      // Автоматическая повторная попытка
      if (this.shouldRetryDownload(download)) {
        setTimeout(() => {
          this.retryDownload(download.id);
        }, this.config.retryDelay);
      }
    } finally {
      this.activeDownloads.delete(download.id);
      // Обработка следующей загрузки
      this.processDownloadQueue();
    }
  }

  private async performSingleDownload(download: Download): Promise<void> {
    // Имитация загрузки файла
    const totalSize = download.totalBytes;
    let downloadedSize = download.receivedBytes;

    while (downloadedSize < totalSize && download.status === 'downloading') {
      // Имитация загрузки чанка
      const chunkSize = Math.min(this.config.chunkSize, totalSize - downloadedSize);
      await new Promise(resolve => setTimeout(resolve, 100)); // Имитация времени загрузки

      downloadedSize += chunkSize;
      download.receivedBytes = downloadedSize;
      download.progress = (downloadedSize / totalSize) * 100;

      // Расчет скорости
      const elapsed = Date.now() - download.startTime;
      download.speed = downloadedSize / (elapsed / 1000);
      download.timeRemaining = (totalSize - downloadedSize) / download.speed;

      this.emit('download_progress', download);

      // Применение ограничения скорости
      if (this.config.maxDownloadSpeed > 0) {
        await this.applySpeedLimit(chunkSize);
      }
    }
  }

  private async performChunkedDownload(download: Download): Promise<void> {
    // Запуск загрузки чанков параллельно
    const activeChunks = new Set<string>();
    const maxConcurrentChunks = Math.min(this.config.maxChunks, download.chunks.length);

    while (download.chunks.some(chunk => chunk.status !== 'completed') && download.status === 'downloading') {
      // Запуск новых чанков
      while (activeChunks.size < maxConcurrentChunks) {
        const pendingChunk = download.chunks.find(chunk => chunk.status === 'pending');
        if (!pendingChunk) break;

        pendingChunk.status = 'downloading';
        activeChunks.add(pendingChunk.id);

        this.downloadChunk(download, pendingChunk).then(() => {
          activeChunks.delete(pendingChunk.id);
        });
      }

      // Обновление прогресса
      const totalDownloaded = download.chunks.reduce((sum, chunk) => sum + chunk.downloaded, 0);
      download.receivedBytes = totalDownloaded;
      download.progress = (totalDownloaded / download.totalBytes) * 100;

      // Расчет скорости
      const elapsed = Date.now() - download.startTime;
      download.speed = totalDownloaded / (elapsed / 1000);
      download.timeRemaining = (download.totalBytes - totalDownloaded) / download.speed;

      this.emit('download_progress', download);

      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  private async downloadChunk(download: Download, chunk: DownloadChunk): Promise<void> {
    try {
      // Имитация загрузки чанка
      while (chunk.downloaded < chunk.size && chunk.status === 'downloading') {
        const chunkPart = Math.min(8192, chunk.size - chunk.downloaded); // 8KB части
        await new Promise(resolve => setTimeout(resolve, 10));

        chunk.downloaded += chunkPart;

        // Применение ограничения скорости
        if (this.config.maxDownloadSpeed > 0) {
          await this.applySpeedLimit(chunkPart);
        }
      }

      chunk.status = 'completed';
    } catch (error) {
      chunk.status = 'error';
      throw error;
    }
  }

  private async getFileInfo(url: string, headers?: Record<string, string>): Promise<{
    filename: string;
    size: number;
    mimeType: string;
    supportsResume: boolean;
  }> {
    // В реальной реализации здесь будет HEAD запрос к серверу
    return {
      filename: this.extractFilenameFromUrl(url),
      size: Math.floor(Math.random() * 100 * 1024 * 1024) + 1024 * 1024, // 1-100MB
      mimeType: 'application/octet-stream',
      supportsResume: true,
    };
  }

  private async performSecurityCheck(url: string): Promise<{ safe: boolean; reason?: string }> {
    try {
      // Проверка URL на безопасность
      const scanResult = await securityScanner.scanUrl(url);
      
      if (scanResult.threats.length > 0) {
        return {
          safe: false,
          reason: `Security threats detected: ${scanResult.threats.join(', ')}`,
        };
      }

      return { safe: true };
    } catch (error) {
      logger.warn('Security check failed', { url, error });
      return { safe: true }; // Разрешаем загрузку при ошибке проверки
    }
  }

  private createDownloadChunks(totalSize: number): DownloadChunk[] {
    const chunks: DownloadChunk[] = [];
    const chunkCount = Math.min(this.config.maxChunks, Math.ceil(totalSize / this.config.chunkSize));
    const chunkSize = Math.floor(totalSize / chunkCount);

    for (let i = 0; i < chunkCount; i++) {
      const start = i * chunkSize;
      const end = i === chunkCount - 1 ? totalSize - 1 : start + chunkSize - 1;

      chunks.push({
        id: `chunk_${i}`,
        start,
        end,
        size: end - start + 1,
        downloaded: 0,
        status: 'pending',
      });
    }

    return chunks;
  }

  private async verifyDownload(download: Download): Promise<void> {
    // Проверка целостности загруженного файла
    if (download.receivedBytes !== download.totalBytes) {
      throw new Error('File size mismatch');
    }

    logger.debug('Download verification completed', {
      downloadId: download.id,
      filename: download.filename,
    });
  }

  private async scanDownloadForViruses(download: Download): Promise<void> {
    if (!this.config.enableVirusScanning) {
      return;
    }

    try {
      const scanResult = await securityScanner.scanFile(download.filepath);
      
      download.virusScanResult = {
        scanned: true,
        clean: scanResult.threats.length === 0,
        threats: scanResult.threats,
        scanTime: Date.now(),
        engine: 'A14Security',
      };

      if (!download.virusScanResult.clean) {
        if (this.config.quarantineSuspiciousFiles) {
          await this.quarantineFile(download);
        }

        this.emit('download_virus_detected', download);
        logger.warn('Virus detected in download', {
          downloadId: download.id,
          filename: download.filename,
          threats: download.virusScanResult.threats,
        });
      }
    } catch (error) {
      logger.error('Virus scan failed', error, {
        downloadId: download.id,
        filename: download.filename,
      });
    }
  }

  private async quarantineFile(download: Download): Promise<void> {
    // Перемещение файла в карантин
    logger.info('File quarantined', {
      downloadId: download.id,
      filename: download.filename,
    });
  }

  private shouldRetryDownload(download: Download): boolean {
    // Логика определения необходимости повторной попытки
    return download.status === 'error' && 
           !download.error?.includes('Security') &&
           !download.error?.includes('Cancelled');
  }

  private async applySpeedLimit(bytes: number): Promise<void> {
    if (this.config.maxDownloadSpeed <= 0) {
      return;
    }

    const delay = (bytes / this.config.maxDownloadSpeed) * 1000;
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  private setupSpeedLimiter(): void {
    // Настройка ограничителя скорости
    logger.debug('Speed limiter configured', {
      maxSpeed: this.config.maxDownloadSpeed,
    });
  }

  private setupAutoCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupOldDownloads();
    }, 24 * 60 * 60 * 1000); // Раз в день
  }

  private async cleanupOldDownloads(): Promise<void> {
    const cutoffTime = Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 дней
    const oldDownloads = Array.from(this.downloads.values())
      .filter(download => download.endTime && download.endTime < cutoffTime);

    for (const download of oldDownloads) {
      this.downloads.delete(download.id);
    }

    if (oldDownloads.length > 0) {
      logger.info('Old downloads cleaned up', { count: oldDownloads.length });
    }
  }

  private async restoreIncompleteDownloads(): Promise<void> {
    // Восстановление незавершенных загрузок после перезапуска
    logger.debug('Restoring incomplete downloads');
  }

  private async cleanupPartialFile(download: Download): Promise<void> {
    // Удаление частично загруженного файла
    logger.debug('Cleaning up partial file', {
      downloadId: download.id,
      filepath: download.filepath,
    });
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  private extractFilenameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop() || 'download';
      return filename.includes('.') ? filename : `${filename}.bin`;
    } catch {
      return 'download.bin';
    }
  }

  private getDefaultFilePath(filename: string): string {
    return `${this.config.defaultDownloadPath}/${filename}`;
  }

  // Геттеры
  public getDownloads(): Download[] {
    return Array.from(this.downloads.values());
  }

  public getDownload(downloadId: string): Download | null {
    return this.downloads.get(downloadId) || null;
  }

  public getActiveDownloads(): Download[] {
    return Array.from(this.downloads.values())
      .filter(download => this.activeDownloads.has(download.id));
  }

  public getDownloadHistory(): DownloadHistory {
    const downloads = Array.from(this.downloads.values());
    const completedDownloads = downloads.filter(d => d.status === 'completed');
    
    return {
      id: 'history',
      downloads,
      totalDownloads: downloads.length,
      totalBytes: downloads.reduce((sum, d) => sum + d.totalBytes, 0),
      averageSpeed: completedDownloads.length > 0 
        ? completedDownloads.reduce((sum, d) => sum + d.speed, 0) / completedDownloads.length 
        : 0,
      successRate: downloads.length > 0 
        ? (completedDownloads.length / downloads.length) * 100 
        : 0,
      lastCleanup: Date.now(),
    };
  }

  public updateConfig(config: Partial<DownloadConfig>): void {
    this.config = { ...this.config, ...config };
    configManager.set('downloads', this.config);
    this.emit('config_updated', this.config);
  }

  public getConfig(): DownloadConfig {
    return { ...this.config };
  }

  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.removeAllListeners();
  }
}

// Экспорт синглтона
export const downloadManager = DownloadManager.getInstance();
