import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  Chip,
  Rating,
  TextField,
  InputAdornment,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Slider,
  FormControlLabel,
  Switch,
  Pagination,
  CircularProgress,
  Alert,
  Snackbar,
  Tooltip,
  Badge,
  Avatar,
  Divider,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Sort as SortIcon,
  Star as StarIcon,
  Download as DownloadIcon,
  Verified as VerifiedIcon,
  TrendingUp as TrendingUpIcon,
  Category as CategoryIcon,
  LocalOffer as LocalOfferIcon,
  AttachMoney as AttachMoneyIcon,
  Security as SecurityIcon,
  Info as InfoIcon,
  Share as ShareIcon,
  Report as ReportIcon,
  RateReview as ReviewIcon,
  ShoppingCart as CartIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/types';
import { Extension, ExtensionStoreProps, ExtensionStoreState } from '../../types/extension';
import { InstallExtensionDialog } from './InstallExtensionDialog';
import { useTheme } from '@mui/material/styles';
import { styled } from '@mui/material/styles';
import { installExtension, uninstallExtension, updateExtension } from '../../store/slices/extensionsSlice';

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[4],
  },
}));

const FilterDrawer = styled(Drawer)(({ theme }) => ({
  width: 320,
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: 320,
    boxSizing: 'border-box',
    padding: theme.spacing(2),
  },
}));

const PriceRange = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  marginTop: theme.spacing(2),
}));

const ExtensionStore: React.FC<ExtensionStoreProps> = ({
  onInstall,
  onUninstall,
  onUpdate,
}): JSX.Element => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [selectedExtension, setSelectedExtension] = useState<Extension | null>(null);
  const [installDialogOpen, setInstallDialogOpen] = useState(false);
  const [filters, setFilters] = useState({
    category: [] as string[],
    tags: [] as string[],
    price: [0, 100] as [number, number],
    rating: 0,
    compatibility: true,
    verified: false,
    featured: false,
    promoted: false,
  });
  const [sort, setSort] = useState({
    field: 'rating' as keyof Extension,
    direction: 'desc' as 'asc' | 'desc',
  });
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);

  const extensions = useSelector((state: RootState) => state.extensions.store.extensions);
  const installedExtensions = useSelector((state: RootState) => state.extensions.installed);
  const loading = useSelector((state: RootState) => state.extensions.store.loading);
  const error = useSelector((state: RootState) => state.extensions.store.error);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setPage(1);
  }, []);

  const handleFilterChange = useCallback((filter: keyof typeof filters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filter]: value,
    }));
    setPage(1);
  }, []);

  const handleSortChange = useCallback((field: keyof Extension) => {
    setSort(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  }, []);

  const handlePageChange = useCallback((event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  }, []);

  const handleInstall = useCallback((extension: Extension) => {
    setSelectedExtension(extension);
    setInstallDialogOpen(true);
  }, []);

  const filteredExtensions = extensions.filter((extension: Extension) => {
    const matchesSearch = extension.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      extension.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = filters.category.length === 0 || filters.category.includes(extension.category);
    const matchesTags = filters.tags.length === 0 || filters.tags.every((tag: string) => extension.tags.includes(tag));
    const matchesPrice = extension.price >= filters.price[0] && extension.price <= filters.price[1];
    const matchesRating = extension.rating >= filters.rating;
    const matchesCompatibility = !filters.compatibility || extension.compatibility;
    const matchesVerified = !filters.verified || extension.verified;
    const matchesFeatured = !filters.featured || extension.featured;
    const matchesPromoted = !filters.promoted || extension.promoted;

    return matchesSearch && matchesCategory && matchesTags && matchesPrice &&
      matchesRating && matchesCompatibility && matchesVerified &&
      matchesFeatured && matchesPromoted;
  });

  const sortedExtensions = [...filteredExtensions].sort((a, b) => {
    const aValue = a[sort.field];
    const bValue = b[sort.field];
    const modifier = sort.direction === 'asc' ? 1 : -1;
    return aValue < bValue ? -1 * modifier : aValue > bValue ? 1 * modifier : 0;
  });

  const paginatedExtensions = sortedExtensions.slice(
    (page - 1) * pageSize,
    page * pageSize
  );

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">
          {t('extensions.store.title')}
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <TextField
            placeholder={t('extensions.store.search')}
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={() => setFilterDrawerOpen(true)}
          >
            {t('extensions.store.filters')}
          </Button>
          <Button
            variant="outlined"
            startIcon={<SortIcon />}
            onClick={() => handleSortChange('rating')}
          >
            {t('extensions.store.sort')}
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {paginatedExtensions.map(extension => (
            <Grid item xs={12} sm={6} md={4} key={extension.id}>
              <StyledCard>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      src={extension.developer.verified ? undefined : extension.icon}
                      sx={{ width: 48, height: 48, mr: 2 }}
                    >
                      {extension.name[0]}
                    </Avatar>
                    <Box>
                      <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
                        {extension.name}
                        {extension.verified && (
                          <Tooltip title={t('extensions.store.verified')}>
                            <VerifiedIcon color="primary" sx={{ ml: 1 }} />
                          </Tooltip>
                        )}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {extension.developer.name}
                      </Typography>
                    </Box>
                  </Box>

                  <Typography variant="body2" color="textSecondary" paragraph>
                    {extension.description}
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Rating value={extension.rating} readOnly size="small" />
                    <Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
                      ({extension.reviewCount})
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    <Chip
                      icon={<CategoryIcon />}
                      label={extension.category}
                      size="small"
                    />
                    {extension.tags.map(tag => (
                      <Chip
                        key={tag}
                        icon={<LocalOfferIcon />}
                        label={tag}
                        size="small"
                      />
                    ))}
                  </Box>

                  {extension.price > 0 && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <AttachMoneyIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="h6" color="primary">
                        {extension.price} {extension.currency}
                      </Typography>
                      {extension.trial && (
                        <Chip
                          label={t('extensions.store.trial', { days: extension.trialDays })}
                          color="secondary"
                          size="small"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Box>
                  )}

                  {extension.featured && (
                    <Chip
                      icon={<TrendingUpIcon />}
                      label={t('extensions.store.featured')}
                      color="secondary"
                      sx={{ mb: 2 }}
                    />
                  )}
                </CardContent>

                <CardActions>
                  {extension.id in installedExtensions ? (
                    <>
                      <Button
                        size="small"
                        color="primary"
                        onClick={() => onUpdate?.(extension)}
                      >
                        {t('extensions.store.actions.update')}
                      </Button>
                      <Button
                        size="small"
                        color="error"
                        onClick={() => onUninstall?.(extension)}
                      >
                        {t('extensions.store.actions.uninstall')}
                      </Button>
                    </>
                  ) : (
                    <Button
                      size="small"
                      color="primary"
                      variant="contained"
                      onClick={() => onInstall?.(extension)}
                      startIcon={<DownloadIcon />}
                    >
                      {extension.price > 0
                        ? t('extensions.store.actions.buy')
                        : t('extensions.store.actions.install')}
                    </Button>
                  )}
                  <Button
                    size="small"
                    onClick={() => setSelectedExtension(extension)}
                  >
                    {t('extensions.store.actions.details')}
                  </Button>
                </CardActions>
              </StyledCard>
            </Grid>
          ))}
        </Grid>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
        <Pagination
          count={Math.ceil(filteredExtensions.length / pageSize)}
          page={page}
          onChange={handlePageChange}
          color="primary"
        />
      </Box>

      <FilterDrawer
        anchor="right"
        open={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
      >
        <Typography variant="h6" gutterBottom>
          {t('extensions.store.filters')}
        </Typography>

        <List>
          <ListItem>
            <ListItemText
              primary={t('extensions.store.categories')}
              secondary={filters.category.join(', ') || t('extensions.store.all')}
            />
          </ListItem>

          <ListItem>
            <ListItemText
              primary={t('extensions.store.tags')}
              secondary={filters.tags.join(', ') || t('extensions.store.all')}
            />
          </ListItem>

          <ListItem>
            <ListItemText
              primary={t('extensions.store.priceRange')}
            />
            <PriceRange>
              <Slider
                value={filters.price}
                onChange={(_, value) => handleFilterChange('price', value)}
                valueLabelDisplay="auto"
                min={0}
                max={100}
              />
            </PriceRange>
          </ListItem>

          <ListItem>
            <ListItemText
              primary={t('extensions.store.minimumRating')}
            />
            <Rating
              value={filters.rating}
              onChange={(_, value) => handleFilterChange('rating', value || 0)}
            />
          </ListItem>

          <ListItem>
            <FormControlLabel
              control={
                <Switch
                  checked={filters.compatibility}
                  onChange={(e) => handleFilterChange('compatibility', e.target.checked)}
                />
              }
              label={t('extensions.store.compatibleOnly')}
            />
          </ListItem>

          <ListItem>
            <FormControlLabel
              control={
                <Switch
                  checked={filters.verified}
                  onChange={(e) => handleFilterChange('verified', e.target.checked)}
                />
              }
              label={t('extensions.store.verifiedOnly')}
            />
          </ListItem>

          <ListItem>
            <FormControlLabel
              control={
                <Switch
                  checked={filters.featured}
                  onChange={(e) => handleFilterChange('featured', e.target.checked)}
                />
              }
              label={t('extensions.store.featuredOnly')}
            />
          </ListItem>

          <ListItem>
            <FormControlLabel
              control={
                <Switch
                  checked={filters.promoted}
                  onChange={(e) => handleFilterChange('promoted', e.target.checked)}
                />
              }
              label={t('extensions.store.promotedOnly')}
            />
          </ListItem>
        </List>
      </FilterDrawer>

      {selectedExtension && (
        <InstallExtensionDialog
          open={installDialogOpen}
          onClose={() => setInstallDialogOpen(false)}
          manifest={selectedExtension}
          source="store"
        />
      )}
    </Box>
  );
}; 