import React, { useState, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Chip,
  Grid,
  Paper,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Storage as StorageIcon,
  Code as CodeIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { installExtension } from '../../store/slices/extensionsSlice';
import { ExtensionManifest } from '../../types/extension';

interface InstallExtensionDialogProps {
  open: boolean;
  onClose: () => void;
  manifest: ExtensionManifest;
  source: 'store' | 'file' | 'url';
}

export const InstallExtensionDialog: React.FC<InstallExtensionDialogProps> = ({
  open,
  onClose,
  manifest,
  source,
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInstall = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      await dispatch(installExtension({ manifest, source }));
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [dispatch, manifest, source, onClose]);

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case 'storage':
        return <StorageIcon />;
      case 'tabs':
        return <CodeIcon />;
      case 'notifications':
        return <InfoIcon />;
      default:
        return <SecurityIcon />;
    }
  };

  const getSourceLabel = () => {
    switch (source) {
      case 'store':
        return t('extensions.installFromStore');
      case 'file':
        return t('extensions.installFromFile');
      case 'url':
        return t('extensions.installFromUrl');
      default:
        return t('extensions.install');
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <img
            src={manifest.icons?.['128']}
            alt={manifest.name}
            style={{ width: 32, height: 32, marginRight: 16 }}
          />
          {t('extensions.installTitle', { name: manifest.name })}
        </Box>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              {manifest.name}
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              {manifest.description}
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                {t('extensions.details')}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    {t('extensions.version')}
                  </Typography>
                  <Typography variant="body1">
                    {manifest.version}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    {t('extensions.author')}
                  </Typography>
                  <Typography variant="body1">
                    {manifest.author.name}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    {t('extensions.license')}
                  </Typography>
                  <Typography variant="body1">
                    {manifest.license}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    {t('extensions.source')}
                  </Typography>
                  <Typography variant="body1">
                    {getSourceLabel()}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {manifest.permissions && manifest.permissions.length > 0 && (
            <Grid item xs={12}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  {t('extensions.permissionsRequired')}
                </Typography>
                <List>
                  {manifest.permissions.map((permission) => (
                    <ListItem key={permission}>
                      <ListItemIcon>
                        {getPermissionIcon(permission)}
                      </ListItemIcon>
                      <ListItemText
                        primary={t(`extensions.permissions.${permission}`)}
                        secondary={t(`extensions.permissions.${permission}Description`)}
                      />
                    </ListItem>
                  ))}
                </List>
              </Paper>
            </Grid>
          )}

          {manifest.contentSecurityPolicy && (
            <Grid item xs={12}>
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="subtitle2">
                  {t('extensions.contentSecurityPolicy')}
                </Typography>
                <Typography variant="body2">
                  {manifest.contentSecurityPolicy}
                </Typography>
              </Alert>
            </Grid>
          )}

          {error && (
            <Grid item xs={12}>
              <Alert severity="error">
                {error}
              </Alert>
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleInstall}
          color="primary"
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? t('extensions.installing') : t('extensions.install')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}; 