import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  Tooltip,
  Alert,
  Snackbar,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Collapse,
  Paper,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Security as SecurityIcon,
  Code as CodeIcon,
  Storage as StorageIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import {
  installExtension,
  uninstallExtension,
  updateExtension,
  setExtensionEnabled,
  updateExtensionSettings,
} from '../../store/slices/extensionsSlice';
import { Extension } from '../../store/slices/extensionsSlice';
import { useTheme } from '@mui/material/styles';
import { styled } from '@mui/material/styles';

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[4],
  },
}));

const StyledChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
}));

const PermissionsList = styled(List)(({ theme }) => ({
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
  marginTop: theme.spacing(1),
}));

interface ExtensionSettings {
  enabled: boolean;
  permissions: string[];
  autoUpdate: boolean;
  notifications: boolean;
  [key: string]: any;
}

export const ExtensionsManager: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const theme = useTheme();
  const extensions = useSelector((state: RootState) => state.extensions.extensions);
  const [selectedExtension, setSelectedExtension] = useState<Extension | null>(null);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [settings, setSettings] = useState<ExtensionSettings>({
    enabled: true,
    permissions: [],
    autoUpdate: true,
    notifications: true,
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning',
  });
  const [expandedPermissions, setExpandedPermissions] = useState<string[]>([]);

  useEffect(() => {
    if (selectedExtension) {
      setSettings({
        enabled: selectedExtension.enabled,
        permissions: selectedExtension.permissions,
        autoUpdate: selectedExtension.settings?.autoUpdate ?? true,
        notifications: selectedExtension.settings?.notifications ?? true,
        ...selectedExtension.settings,
      });
    }
  }, [selectedExtension]);

  const handleSettingsOpen = (extension: Extension) => {
    setSelectedExtension(extension);
    setSettingsOpen(true);
  };

  const handleSettingsClose = () => {
    setSettingsOpen(false);
    setSelectedExtension(null);
  };

  const handleSettingsSave = () => {
    if (selectedExtension) {
      dispatch(updateExtensionSettings({
        id: selectedExtension.id,
        settings: {
          ...settings,
          lastUpdated: new Date().toISOString(),
        },
      }));
      setSnackbar({
        open: true,
        message: t('extensions.settingsSaved'),
        severity: 'success',
      });
      handleSettingsClose();
    }
  };

  const handleExtensionToggle = (extension: Extension) => {
    dispatch(setExtensionEnabled({
      id: extension.id,
      enabled: !extension.enabled,
    }));
    setSnackbar({
      open: true,
      message: extension.enabled
        ? t('extensions.disabled')
        : t('extensions.enabled'),
      severity: 'info',
    });
  };

  const handleUninstall = (extension: Extension) => {
    dispatch(uninstallExtension(extension.id));
    setSnackbar({
      open: true,
      message: t('extensions.uninstalled'),
      severity: 'success',
    });
  };

  const handleUpdate = (extension: Extension) => {
    dispatch(updateExtension(extension.id));
    setSnackbar({
      open: true,
      message: t('extensions.updating'),
      severity: 'info',
    });
  };

  const handlePermissionToggle = (permission: string) => {
    setSettings(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission],
    }));
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case 'storage':
        return <StorageIcon />;
      case 'tabs':
        return <CodeIcon />;
      case 'notifications':
        return <InfoIcon />;
      default:
        return <SecurityIcon />;
    }
  };

  const getStatusColor = (extension: Extension) => {
    if (!extension.enabled) return theme.palette.grey[500];
    if (extension.error) return theme.palette.error.main;
    if (extension.updateAvailable) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {t('extensions.title')}
      </Typography>

      <Grid container spacing={3}>
        {extensions.map(extension => (
          <Grid item xs={12} sm={6} md={4} key={extension.id}>
            <StyledCard>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <img
                    src={extension.icon}
                    alt={extension.name}
                    style={{ width: 48, height: 48, marginRight: 16 }}
                  />
                  <Box>
                    <Typography variant="h6">{extension.name}</Typography>
                    <Typography variant="body2" color="textSecondary">
                      {extension.version}
                    </Typography>
                  </Box>
                </Box>

                <Typography variant="body2" color="textSecondary" paragraph>
                  {extension.description}
                </Typography>

                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {extension.permissions.map(permission => (
                    <StyledChip
                      key={permission}
                      icon={getPermissionIcon(permission)}
                      label={t(`extensions.permissions.${permission}`)}
                      size="small"
                    />
                  ))}
                </Box>

                {extension.error && (
                  <Alert severity="error" sx={{ mb: 2 }}>
                    {extension.error}
                  </Alert>
                )}

                {extension.updateAvailable && (
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    {t('extensions.updateAvailable')}
                  </Alert>
                )}
              </CardContent>

              <CardActions>
                <Button
                  size="small"
                  color={extension.enabled ? 'error' : 'success'}
                  onClick={() => handleExtensionToggle(extension)}
                >
                  {extension.enabled ? t('extensions.disable') : t('extensions.enable')}
                </Button>
                <Button
                  size="small"
                  onClick={() => handleSettingsOpen(extension)}
                  startIcon={<SettingsIcon />}
                >
                  {t('extensions.settings')}
                </Button>
                {extension.updateAvailable && (
                  <Button
                    size="small"
                    color="primary"
                    onClick={() => handleUpdate(extension)}
                    startIcon={<RefreshIcon />}
                  >
                    {t('extensions.update')}
                  </Button>
                )}
                <Button
                  size="small"
                  color="error"
                  onClick={() => handleUninstall(extension)}
                  startIcon={<DeleteIcon />}
                >
                  {t('extensions.uninstall')}
                </Button>
              </CardActions>
            </StyledCard>
          </Grid>
        ))}
      </Grid>

      <Dialog
        open={settingsOpen}
        onClose={handleSettingsClose}
        maxWidth="md"
        fullWidth
      >
        {selectedExtension && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <img
                  src={selectedExtension.icon}
                  alt={selectedExtension.name}
                  style={{ width: 32, height: 32, marginRight: 16 }}
                />
                {t('extensions.settingsFor', { name: selectedExtension.name })}
              </Box>
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.enabled}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          enabled: e.target.checked,
                        }))}
                      />
                    }
                    label={t('extensions.enabled')}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    {t('extensions.permissions')}
                  </Typography>
                  <PermissionsList>
                    {selectedExtension.permissions.map(permission => (
                      <ListItem key={permission}>
                        <ListItemText
                          primary={t(`extensions.permissions.${permission}`)}
                          secondary={t(`extensions.permissions.${permission}Description`)}
                        />
                        <ListItemSecondaryAction>
                          <Switch
                            edge="end"
                            checked={settings.permissions.includes(permission)}
                            onChange={() => handlePermissionToggle(permission)}
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </PermissionsList>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    {t('extensions.settings')}
                  </Typography>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.autoUpdate}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          autoUpdate: e.target.checked,
                        }))}
                      />
                    }
                    label={t('extensions.autoUpdate')}
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.notifications}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          notifications: e.target.checked,
                        }))}
                      />
                    }
                    label={t('extensions.notifications')}
                  />
                </Grid>

                {selectedExtension.error && (
                  <Grid item xs={12}>
                    <Alert severity="error">
                      {selectedExtension.error}
                    </Alert>
                  </Grid>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleSettingsClose}>
                {t('common.cancel')}
              </Button>
              <Button onClick={handleSettingsSave} color="primary" variant="contained">
                {t('common.save')}
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
      >
        <Alert
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}; 