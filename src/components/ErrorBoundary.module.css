.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 24px;
  text-align: center;
  background-color: var(--background-color);
}

.icon {
  font-size: 64px;
  color: var(--error-color);
  margin-bottom: 24px;
}

.title {
  margin-bottom: 16px;
  color: var(--text-color);
}

.message {
  margin-bottom: 24px;
  color: var(--text-secondary-color);
}

.details {
  width: 100%;
  max-width: 800px;
  margin: 24px 0;
  padding: 16px;
  background-color: var(--background-secondary-color);
  border-radius: 8px;
  overflow: auto;
}

.stack {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
  color: var(--text-secondary-color);
}

.button {
  margin-top: 24px;
} 