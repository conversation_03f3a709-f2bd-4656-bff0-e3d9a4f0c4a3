import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { Button, ButtonProps } from '../Button';
import { TestUtils } from '../../../testing/TestFramework';

describe('Button Component', () => {
  const defaultProps: ButtonProps = {
    children: 'Test Button',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders with default props', () => {
      render(<Button {...defaultProps} />);
      const button = screen.getByRole('button', { name: /test button/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass('bg-blue-600', 'text-white');
    });

    it('renders with custom className', () => {
      render(<Button {...defaultProps} className="custom-class" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class');
    });

    it('renders with different variants', () => {
      const variants: Array<ButtonProps['variant']> = [
        'primary', 'secondary', 'outline', 'ghost', 'danger', 'success', 'warning', 'info'
      ];

      variants.forEach((variant) => {
        const { unmount } = render(<Button variant={variant}>Test</Button>);
        const button = screen.getByRole('button');
        expect(button).toBeInTheDocument();
        unmount();
      });
    });

    it('renders with different sizes', () => {
      const sizes: Array<ButtonProps['size']> = ['xs', 'sm', 'md', 'lg', 'xl'];

      sizes.forEach((size) => {
        const { unmount } = render(<Button size={size}>Test</Button>);
        const button = screen.getByRole('button');
        expect(button).toBeInTheDocument();
        unmount();
      });
    });
  });

  describe('Interaction', () => {
    it('calls onClick when clicked', async () => {
      const handleClick = vi.fn();
      render(<Button {...defaultProps} onClick={handleClick} />);
      
      const button = screen.getByRole('button');
      await userEvent.click(button);
      
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('does not call onClick when disabled', async () => {
      const handleClick = vi.fn();
      render(<Button {...defaultProps} onClick={handleClick} disabled />);
      
      const button = screen.getByRole('button');
      await userEvent.click(button);
      
      expect(handleClick).not.toHaveBeenCalled();
    });

    it('does not call onClick when loading', async () => {
      const handleClick = vi.fn();
      render(<Button {...defaultProps} onClick={handleClick} isLoading />);
      
      const button = screen.getByRole('button');
      await userEvent.click(button);
      
      expect(handleClick).not.toHaveBeenCalled();
    });

    it('supports keyboard navigation', async () => {
      const handleClick = vi.fn();
      render(<Button {...defaultProps} onClick={handleClick} />);
      
      const button = screen.getByRole('button');
      button.focus();
      
      fireEvent.keyDown(button, { key: 'Enter' });
      fireEvent.keyDown(button, { key: ' ' });
      
      expect(button).toHaveFocus();
    });
  });

  describe('Loading State', () => {
    it('shows loading spinner when isLoading is true', () => {
      render(<Button {...defaultProps} isLoading />);
      
      const spinner = screen.getByRole('button').querySelector('svg');
      expect(spinner).toBeInTheDocument();
      expect(spinner).toHaveClass('animate-spin');
    });

    it('hides button text when loading', () => {
      render(<Button {...defaultProps} isLoading />);
      
      const button = screen.getByRole('button');
      const textSpan = button.querySelector('span:last-child');
      expect(textSpan).toHaveClass('opacity-0');
    });

    it('sets aria-busy when loading', () => {
      render(<Button {...defaultProps} isLoading />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-busy', 'true');
    });
  });

  describe('Icons', () => {
    const TestIcon = () => <span data-testid="test-icon">Icon</span>;

    it('renders left icon', () => {
      render(<Button {...defaultProps} leftIcon={<TestIcon />} />);
      
      expect(screen.getByTestId('test-icon')).toBeInTheDocument();
    });

    it('renders right icon', () => {
      render(<Button {...defaultProps} rightIcon={<TestIcon />} />);
      
      expect(screen.getByTestId('test-icon')).toBeInTheDocument();
    });

    it('hides left icon when loading', () => {
      render(<Button {...defaultProps} leftIcon={<TestIcon />} isLoading />);
      
      expect(screen.queryByTestId('test-icon')).not.toBeInTheDocument();
    });

    it('shows right icon when loading', () => {
      render(<Button {...defaultProps} rightIcon={<TestIcon />} isLoading />);
      
      expect(screen.getByTestId('test-icon')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(<Button {...defaultProps} disabled />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-disabled', 'true');
    });

    it('supports tooltip', () => {
      const tooltip = 'This is a tooltip';
      render(<Button {...defaultProps} tooltip={tooltip} />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('title', tooltip);
    });

    it('supports custom test id', () => {
      render(<Button {...defaultProps} data-testid="custom-button" />);
      
      expect(screen.getByTestId('custom-button')).toBeInTheDocument();
    });

    it('passes accessibility compliance check', async () => {
      render(<Button {...defaultProps} />);
      
      const button = screen.getByRole('button');
      const accessibilityResult = await TestUtils.checkAccessibilityCompliance(button);
      
      expect(accessibilityResult.score).toBeGreaterThan(90);
      expect(accessibilityResult.violations).toHaveLength(0);
    });
  });

  describe('Performance', () => {
    it('renders within performance budget', async () => {
      const performanceResult = await TestUtils.measureComponentPerformance(() => {
        render(<Button {...defaultProps} />);
      }, 10);
      
      expect(performanceResult.averageTime).toBeLessThan(50); // 50ms budget
    });

    it('handles rapid clicks efficiently', async () => {
      const handleClick = vi.fn();
      render(<Button {...defaultProps} onClick={handleClick} />);
      
      const button = screen.getByRole('button');
      
      // Simulate rapid clicking
      for (let i = 0; i < 10; i++) {
        await userEvent.click(button);
      }
      
      expect(handleClick).toHaveBeenCalledTimes(10);
    });
  });

  describe('Visual Regression', () => {
    it('maintains visual consistency', async () => {
      const { container } = render(<Button {...defaultProps} />);
      
      const snapshot = await TestUtils.captureVisualSnapshot(container, 'button-default');
      expect(snapshot.snapshot).toBeDefined();
      expect(snapshot.dimensions.width).toBeGreaterThan(0);
      expect(snapshot.dimensions.height).toBeGreaterThan(0);
    });

    it('maintains visual consistency across variants', async () => {
      const variants: Array<ButtonProps['variant']> = ['primary', 'secondary', 'danger'];
      
      for (const variant of variants) {
        const { container, unmount } = render(<Button variant={variant}>Test</Button>);
        
        const snapshot = await TestUtils.captureVisualSnapshot(container, `button-${variant}`);
        expect(snapshot.snapshot).toBeDefined();
        
        unmount();
      }
    });
  });

  describe('Edge Cases', () => {
    it('handles empty children gracefully', () => {
      render(<Button />);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('handles very long text', () => {
      const longText = 'This is a very long button text that might cause layout issues if not handled properly';
      render(<Button>{longText}</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveTextContent(longText);
    });

    it('handles special characters in text', () => {
      const specialText = 'Button with émojis 🚀 and spëcial chars!';
      render(<Button>{specialText}</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveTextContent(specialText);
    });
  });

  describe('Integration', () => {
    it('works with form submission', async () => {
      const handleSubmit = vi.fn((e) => e.preventDefault());
      
      render(
        <form onSubmit={handleSubmit}>
          <Button type="submit">Submit</Button>
        </form>
      );
      
      const button = screen.getByRole('button');
      await userEvent.click(button);
      
      expect(handleSubmit).toHaveBeenCalledTimes(1);
    });

    it('integrates with ref forwarding', () => {
      const ref = React.createRef<any>();
      render(<Button ref={ref}>Test</Button>);
      
      expect(ref.current).toBeDefined();
      expect(ref.current.focus).toBeInstanceOf(Function);
      expect(ref.current.click).toBeInstanceOf(Function);
    });
  });
});
