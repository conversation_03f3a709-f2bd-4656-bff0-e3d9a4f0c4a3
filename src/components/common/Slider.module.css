.sliderContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.slider {
  flex: 1;
  -webkit-appearance: none;
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: var(--background-secondary);
  outline: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.slider:hover::-webkit-slider-thumb {
  transform: scale(1.1);
}

.slider:hover::-moz-range-thumb {
  transform: scale(1.1);
}

.slider:focus {
  outline: none;
}

.slider:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 2px var(--accent-color-light);
}

.slider:focus::-moz-range-thumb {
  box-shadow: 0 0 0 2px var(--accent-color-light);
}

.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.disabled::-webkit-slider-thumb {
  cursor: not-allowed;
}

.disabled::-moz-range-thumb {
  cursor: not-allowed;
}

.value {
  min-width: 3rem;
  text-align: right;
  color: var(--text-secondary);
  font-size: 0.9rem;
} 