import React from 'react';
import { twMerge } from 'tailwind-merge';

export type BadgeVariant =
  | 'primary'
  | 'secondary'
  | 'success'
  | 'danger'
  | 'warning'
  | 'info'
  | 'light'
  | 'dark';

export interface BadgeProps {
  children: React.ReactNode;
  variant?: BadgeVariant;
  size?: 'sm' | 'md' | 'lg';
  rounded?: boolean;
  className?: string;
  dot?: boolean;
  count?: number;
  maxCount?: number;
  showZero?: boolean;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  rounded = false,
  className,
  dot = false,
  count,
  maxCount = 99,
  showZero = false,
}) => {
  const variants = {
    primary: 'bg-blue-100 text-blue-800',
    secondary: 'bg-gray-100 text-gray-800',
    success: 'bg-green-100 text-green-800',
    danger: 'bg-red-100 text-red-800',
    warning: 'bg-yellow-100 text-yellow-800',
    info: 'bg-cyan-100 text-cyan-800',
    light: 'bg-gray-50 text-gray-600',
    dark: 'bg-gray-800 text-white',
  };

  const sizes = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-2.5 py-0.5',
    lg: 'text-base px-3 py-1',
  };

  const renderCount = () => {
    if (count === undefined) return null;
    if (count === 0 && !showZero) return null;
    return (
      <span className="ml-1">
        {count > maxCount ? `${maxCount}+` : count}
      </span>
    );
  };

  const renderDot = () => {
    if (!dot) return null;
    return (
      <span
        className={twMerge(
          'inline-block w-2 h-2 rounded-full',
          variants[variant]
        )}
      />
    );
  };

  return (
    <span
      className={twMerge(
        'inline-flex items-center font-medium',
        variants[variant],
        sizes[size],
        rounded ? 'rounded-full' : 'rounded',
        className
      )}
    >
      {renderDot()}
      {children}
      {renderCount()}
    </span>
  );
}; 