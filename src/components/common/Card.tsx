import React from 'react';
import { twMerge } from 'tailwind-merge';

export interface CardProps {
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  header?: React.ReactNode;
  className?: string;
  onClick?: () => void;
  hoverable?: boolean;
  bordered?: boolean;
  shadow?: 'none' | 'sm' | 'md' | 'lg';
}

export const Card: React.FC<CardProps> = ({
  title,
  subtitle,
  children,
  footer,
  header,
  className,
  onClick,
  hoverable = false,
  bordered = true,
  shadow = 'md',
}) => {
  const shadows = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow',
    lg: 'shadow-lg',
  };

  return (
    <div
      className={twMerge(
        'bg-white rounded-lg overflow-hidden',
        bordered && 'border border-gray-200',
        shadows[shadow],
        hoverable && 'transition-shadow duration-200 hover:shadow-lg',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      {header && (
        <div className="px-4 py-3 border-b border-gray-200">{header}</div>
      )}
      {(title || subtitle) && (
        <div className="px-4 py-3 border-b border-gray-200">
          {title && (
            <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          )}
          {subtitle && (
            <p className="mt-1 text-sm text-gray-500">{subtitle}</p>
          )}
        </div>
      )}
      <div className="px-4 py-3">{children}</div>
      {footer && (
        <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
          {footer}
        </div>
      )}
    </div>
  );
}; 