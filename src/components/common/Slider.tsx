import React from 'react';
import styles from './Slider.module.css';

interface SliderProps {
  value: number;
  onChange: (value: number) => void;
  min: number;
  max: number;
  step?: number;
  disabled?: boolean;
}

export const Slider: React.FC<SliderProps> = ({
  value,
  onChange,
  min,
  max,
  step = 1,
  disabled = false,
}) => {
  return (
    <div className={styles.sliderContainer}>
      <input
        type="range"
        className={`${styles.slider} ${disabled ? styles.disabled : ''}`}
        value={value}
        onChange={(e) => onChange(Number(e.target.value))}
        min={min}
        max={max}
        step={step}
        disabled={disabled}
      />
      <span className={styles.value}>{value}</span>
    </div>
  );
}; 