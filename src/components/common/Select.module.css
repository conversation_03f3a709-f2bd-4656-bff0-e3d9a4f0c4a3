.select {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  outline: none;
  transition: all 0.2s ease;
}

.select:hover:not(:disabled) {
  border-color: var(--accent-color);
}

.select:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px var(--accent-color-light);
}

.select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--background-secondary);
}

.select option {
  background-color: var(--background-primary);
  color: var(--text-primary);
  padding: 0.5rem;
} 