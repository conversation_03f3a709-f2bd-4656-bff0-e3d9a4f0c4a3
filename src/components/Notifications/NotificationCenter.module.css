.container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1rem;
  background: var(--background-primary);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.settings {
  margin-bottom: 1.5rem;
}

.settingsTitle {
  margin-bottom: 1rem;
}

.settingsForm {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notifications {
  margin-top: 1.5rem;
}

.notificationsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.notificationsList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.notification {
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: var(--background-paper);
  box-shadow: var(--shadow-1);
  transition: opacity 0.2s ease-in-out;
}

.notification:hover {
  opacity: 1;
}

.notificationRead {
  opacity: 0.7;
}

.notificationHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.notificationTitle {
  margin-bottom: 0.5rem;
}

.notificationBody {
  color: var(--text-secondary);
}

.notificationActions {
  margin-top: 0.5rem;
  display: flex;
  gap: 0.5rem;
}

.notification.info {
  border-left-color: var(--info-color);
}

.notification.success {
  border-left-color: var(--success-color);
}

.notification.warning {
  border-left-color: var(--warning-color);
}

.notification.error {
  border-left-color: var(--error-color);
}

.timestamp {
  position: absolute;
  bottom: 0.5rem;
  right: 1rem;
  font-size: 0.8rem;
  color: var(--text-tertiary);
}

/* Animation classes */
.enter {
  opacity: 0;
  transform: translateY(20px);
}

.enterActive {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.exit {
  opacity: 1;
  transform: translateY(0);
}

.exitActive {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
} 