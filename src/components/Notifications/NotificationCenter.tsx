import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  addNotification,
  markAsRead,
  markAllAsRead,
  removeNotification,
  clearNotifications,
  setEnabled,
  setSound,
  setVibration,
  setPosition,
  setDuration,
  setMaxVisible,
  setGrouping,
  setPriority,
  setActions,
  setHistory,
  setHistoryLimit,
} from '@store/slices/notificationSlice';
import { RootState } from '@store';
import { Box, Card, Typography, Switch, Slider, Select, Alert, MenuItem, FormControl, InputLabel } from '@mui/material';
import { Icon } from '@shared/components/Icon';
import Button from '@shared/components/Button';
import styles from './NotificationCenter.module.css';
import type {
  Notification,
  NotificationAction,
  NotificationType,
  NotificationPriority,
  NotificationPosition,
  NotificationTheme,
  NotificationAnimation,
  NotificationSound,
  NotificationBehavior,
  NotificationLayout,
  NotificationAccessibility
} from '../../types/notifications';

export const NotificationCenter: React.FC = () => {
  const dispatch = useDispatch();
  const {
    enabled,
    sound,
    vibration,
    position,
    duration,
    maxVisible,
    grouping,
    priority,
    actions,
    history,
    historyLimit,
    notifications,
  } = useSelector((state: RootState) => state.notifications);

  const handleNotificationClick = (id: string) => {
    dispatch(markAsRead(id));
  };

  const handleNotificationClose = (id: string) => {
    dispatch(removeNotification(id));
  };

  const handleSettingsChange = (setting: string, value: any) => {
    switch (setting) {
      case 'enabled':
        dispatch(setEnabled(value));
        break;
      case 'sound':
        dispatch(setSound(value));
        break;
      case 'vibration':
        dispatch(setVibration(value));
        break;
      case 'position':
        dispatch(setPosition(value as NotificationPosition));
        break;
      case 'duration':
        dispatch(setDuration(value));
        break;
      case 'maxVisible':
        dispatch(setMaxVisible(value));
        break;
      case 'grouping':
        dispatch(setGrouping(value));
        break;
      case 'priority':
        dispatch(setPriority(value as NotificationPriority));
        break;
      case 'actions':
        dispatch(setActions(value));
        break;
      case 'history':
        dispatch(setHistory(value));
        break;
      case 'historyLimit':
        dispatch(setHistoryLimit(value));
        break;
    }
  };

  return (
    <Box className={styles.container}>
      <Box className={styles.settings}>
        <Typography variant="h6" className={styles.settingsTitle}>
          Notification Settings
        </Typography>
        <Box className={styles.settingsForm}>
          <FormControl>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography>Enable Notifications</Typography>
              <Switch
                checked={enabled}
                onChange={(e) => handleSettingsChange('enabled', e.target.checked)}
              />
            </Box>
          </FormControl>

          <FormControl>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography>Sound</Typography>
              <Switch
                checked={sound}
                onChange={(e) => handleSettingsChange('sound', e.target.checked)}
                disabled={!enabled}
              />
            </Box>
          </FormControl>

          <FormControl>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography>Vibration</Typography>
              <Switch
                checked={vibration}
                onChange={(e) => handleSettingsChange('vibration', e.target.checked)}
                disabled={!enabled}
              />
            </Box>
          </FormControl>

          <FormControl fullWidth>
            <InputLabel>Position</InputLabel>
            <Select
              value={position}
              onChange={(e) => handleSettingsChange('position', e.target.value)}
              disabled={!enabled}
            >
              <MenuItem value="top-right">Top Right</MenuItem>
              <MenuItem value="top-left">Top Left</MenuItem>
              <MenuItem value="bottom-right">Bottom Right</MenuItem>
              <MenuItem value="bottom-left">Bottom Left</MenuItem>
              <MenuItem value="top-center">Top Center</MenuItem>
              <MenuItem value="bottom-center">Bottom Center</MenuItem>
            </Select>
          </FormControl>

          <FormControl fullWidth>
            <Typography gutterBottom>Duration (ms)</Typography>
            <Slider
              value={duration}
              onChange={(_, value) => handleSettingsChange('duration', value)}
              min={1000}
              max={10000}
              step={500}
              disabled={!enabled}
            />
          </FormControl>

          <FormControl fullWidth>
            <Typography gutterBottom>Max Visible</Typography>
            <Slider
              value={maxVisible}
              onChange={(_, value) => handleSettingsChange('maxVisible', value)}
              min={1}
              max={10}
              step={1}
              disabled={!enabled}
            />
          </FormControl>

          <FormControl>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography>Group Notifications</Typography>
              <Switch
                checked={grouping}
                onChange={(e) => handleSettingsChange('grouping', e.target.checked)}
                disabled={!enabled}
              />
            </Box>
          </FormControl>

          <FormControl fullWidth>
            <InputLabel>Priority</InputLabel>
            <Select
              value={priority}
              onChange={(e) => handleSettingsChange('priority', e.target.value)}
              disabled={!enabled}
            >
              <MenuItem value="low">Low</MenuItem>
              <MenuItem value="normal">Normal</MenuItem>
              <MenuItem value="high">High</MenuItem>
              <MenuItem value="urgent">Urgent</MenuItem>
            </Select>
          </FormControl>

          <FormControl>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography>Show Actions</Typography>
              <Switch
                checked={actions}
                onChange={(e) => handleSettingsChange('actions', e.target.checked)}
                disabled={!enabled}
              />
            </Box>
          </FormControl>

          <FormControl>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography>Keep History</Typography>
              <Switch
                checked={history}
                onChange={(e) => handleSettingsChange('history', e.target.checked)}
                disabled={!enabled}
              />
            </Box>
          </FormControl>

          <FormControl fullWidth>
            <Typography gutterBottom>History Limit</Typography>
            <Slider
              value={historyLimit}
              onChange={(_, value) => handleSettingsChange('historyLimit', value)}
              min={10}
              max={1000}
              step={10}
              disabled={!enabled || !history}
            />
          </FormControl>
        </Box>
      </Box>

      <Box className={styles.notifications}>
        <Box className={styles.notificationsHeader}>
          <Typography variant="h6">Recent Notifications</Typography>
          <Button
            variant="primary"
            onClick={() => dispatch(clearNotifications())}
            disabled={notifications.length === 0}
          >
            Clear All
          </Button>
        </Box>
        {notifications.length === 0 ? (
          <Alert severity="info">No notifications</Alert>
        ) : (
          notifications.map((notification: Notification) => (
            <Card
              key={notification.id}
              className={`${styles.notification} ${notification.read ? styles.read : ''}`}
              onClick={() => handleNotificationClick(notification.id)}
            >
              <Box className={styles.notificationContent}>
                <Typography variant="subtitle1">{notification.title}</Typography>
                <Typography variant="body2">{notification.message}</Typography>
                {notification.actions && actions && (
                  <Box className={styles.notificationActions}>
                    {notification.actions.map((action: NotificationAction) => (
                      <Button
                        key={action.label}
                        variant="text"
                        onClick={(e) => {
                          e.stopPropagation();
                          action.onClick?.();
                        }}
                      >
                        {action.label}
                      </Button>
                    ))}
                  </Box>
                )}
              </Box>
              <Icon
                name="close"
                className={styles.closeIcon}
                onClick={(e) => {
                  e.stopPropagation();
                  handleNotificationClose(notification.id);
                }}
              />
            </Card>
          ))
        )}
      </Box>
    </Box>
  );
}; 