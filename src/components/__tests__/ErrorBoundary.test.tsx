import React from 'react';
import { render, screen } from '@testing-library/react';
import { ErrorBoundary } from '../ErrorBoundary';

function ThrowComponent() {
  throw new Error('Crash!');
}

describe('ErrorBoundary', () => {
  it('должен показывать fallback при ошибке', () => {
    render(
      <ErrorBoundary FallbackComponent={() => <div data-testid="error-fallback">Ошибка!</div>}>
        <ThrowComponent />
      </ErrorBoundary>
    );
    expect(screen.getByTestId('error-fallback')).toBeInTheDocument();
  });

  it('не должен показывать fallback без ошибки', () => {
    render(
      <ErrorBoundary FallbackComponent={() => <div>Ошибка!</div>}>
        <div data-testid="inside">Всё нормально</div>
      </ErrorBoundary>
    );
    expect(screen.getByTestId('inside')).toBeInTheDocument();
  });
});