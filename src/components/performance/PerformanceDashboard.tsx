import React from 'react';
import { usePerformance } from '../../hooks/usePerformance';
import {
  Box,
  Card,
  Typography,
  Switch,
  Slider,
  Select,
  Alert,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';

export const PerformanceDashboard: React.FC = () => {
  const { metrics, settings, alerts, updatePerformanceSettings } = usePerformance();

  const handleSettingChange = (section: keyof typeof settings, key: string, value: any) => {
    updatePerformanceSettings({
      [section]: {
        ...settings[section],
        [key]: value,
      },
    });
  };

  return (
    <>
      <Box sx={{ p: 3, display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 2 }}>
        <Card sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Typography color="text.secondary">FPS</Typography>
          <Typography variant="h3" color="primary.main" fontWeight="bold">
            {metrics.fps.toFixed(1)}
          </Typography>
        </Card>

        <Card sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Typography color="text.secondary">Memory Usage</Typography>
          <Typography variant="h3" color="primary.main" fontWeight="bold">
            {((metrics.memory.used / metrics.memory.total) * 100).toFixed(1)}%
          </Typography>
        </Card>

        <Card sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Typography color="text.secondary">CPU Usage</Typography>
          <Typography variant="h3" color="primary.main" fontWeight="bold">
            {metrics.cpu.usage.toFixed(1)}%
          </Typography>
        </Card>

        <Card sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Typography color="text.secondary">Network Latency</Typography>
          <Typography variant="h3" color="primary.main" fontWeight="bold">
            {metrics.network.latency.toFixed(0)}ms
          </Typography>
        </Card>

        <Box sx={{ gridColumn: '1 / -1', display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 2 }}>
          <Card sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Monitoring Settings
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <Switch
                checked={settings.monitoring.enabled}
                onChange={(e) => handleSettingChange('monitoring', 'enabled', e.target.checked)}
              />
              <Typography>Enable Monitoring</Typography>
            </Box>
            <Typography gutterBottom>Monitoring Interval (ms)</Typography>
            <Slider
              value={settings.monitoring.interval}
              min={100}
              max={5000}
              step={100}
              onChange={(_, value) => handleSettingChange('monitoring', 'interval', value)}
            />
          </Card>

          <Card sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Optimization Settings
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <Switch
                checked={settings.optimization.hardwareAcceleration}
                onChange={(e) =>
                  handleSettingChange('optimization', 'hardwareAcceleration', e.target.checked)
                }
              />
              <Typography>Hardware Acceleration</Typography>
            </Box>
            <FormControl fullWidth>
              <InputLabel>Process Priority</InputLabel>
              <Select
                value={settings.optimization.processPriority}
                label="Process Priority"
                onChange={(e) =>
                  handleSettingChange('optimization', 'processPriority', e.target.value)
                }
              >
                <MenuItem value="low">Low</MenuItem>
                <MenuItem value="normal">Normal</MenuItem>
                <MenuItem value="high">High</MenuItem>
              </Select>
            </FormControl>
          </Card>

          <Card sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Network Settings
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <Switch
                checked={settings.network.compression}
                onChange={(e) => handleSettingChange('network', 'compression', e.target.checked)}
              />
              <Typography>Compression</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Switch
                checked={settings.network.prefetching}
                onChange={(e) => handleSettingChange('network', 'prefetching', e.target.checked)}
              />
              <Typography>Prefetching</Typography>
            </Box>
          </Card>
        </Box>
      </Box>

      <Box
        sx={{
          position: 'fixed',
          bottom: 2,
          right: 2,
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          maxWidth: 400,
          zIndex: 1000,
        }}
      >
        {alerts.map((alert) => (
          <Alert
            key={alert.id}
            severity={alert.type === 'error' ? 'error' : 'warning'}
            sx={{ width: '100%' }}
          >
            {alert.message}
          </Alert>
        ))}
      </Box>
    </>
  );
}; 