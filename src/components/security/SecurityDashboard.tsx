import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { Button } from '../common/Button';
import { Switch } from '../common/Switch';
import { Icon } from '../common/Icon';
import { SecurityManager } from '../../security/SecurityManager';

const DashboardContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.lg};
  background: ${({ theme }) => theme.colors.background.primary};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.md};
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Title = styled.h2`
  margin: 0;
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: ${({ theme }) => theme.typography.fontSize.xl};
`;

const SecurityStatus = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const StatusCard = styled.div<{ status: 'secure' | 'warning' | 'critical' }>`
  padding: ${({ theme }) => theme.spacing.md};
  background: ${({ theme, status }) => {
    switch (status) {
      case 'secure':
        return theme.colors.success;
      case 'warning':
        return theme.colors.warning;
      case 'critical':
        return theme.colors.error;
      default:
        return theme.colors.background.secondary;
    }
  }};
  color: white;
  border-radius: ${({ theme }) => theme.borderRadius.md};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const StatusIcon = styled(Icon)`
  font-size: 1.5rem;
`;

const StatusText = styled.div`
  font-weight: ${({ theme }) => theme.typography.fontWeight.bold};
`;

const Section = styled.section`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const SectionTitle = styled.h3`
  margin: 0 0 ${({ theme }) => theme.spacing.md};
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: ${({ theme }) => theme.typography.fontSize.lg};
`;

const SettingItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing.sm} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const SettingLabel = styled.span`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: ${({ theme }) => theme.typography.fontSize.md};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-top: ${({ theme }) => theme.spacing.lg};
`;

export const SecurityDashboard: React.FC = () => {
  const [config, setConfig] = useState<any>(null);
  const securityManager = SecurityManager.getInstance();

  useEffect(() => {
    setConfig(securityManager.getConfig());
  }, []);

  const handleConfigChange = (key: string, value: any) => {
    const newConfig = { ...config };
    const keys = key.split('.');
    let current = newConfig;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    securityManager.setConfig(newConfig);
    setConfig(newConfig);
  };

  const handleReset = async () => {
    await securityManager.resetConfig();
    setConfig(securityManager.getConfig());
  };

  if (!config) {
    return <div>Loading...</div>;
  }

  return (
    <DashboardContainer>
      <Header>
        <Title>Security Dashboard</Title>
        <Button onClick={handleReset}>
          <Icon name="refresh" />
          Reset to Defaults
        </Button>
      </Header>

      <SecurityStatus>
        <StatusCard status={config.features.webSecurity ? 'secure' : 'critical'}>
          <StatusIcon name="shield" />
          <StatusText>Web Security</StatusText>
        </StatusCard>

        <StatusCard status={config.features.sandbox ? 'secure' : 'warning'}>
          <StatusIcon name="lock" />
          <StatusText>Sandbox</StatusText>
        </StatusCard>

        <StatusCard status={config.features.contextIsolation ? 'secure' : 'warning'}>
          <StatusIcon name="layers" />
          <StatusText>Context Isolation</StatusText>
        </StatusCard>

        <StatusCard status={config.csp.enabled ? 'secure' : 'warning'}>
          <StatusIcon name="policy" />
          <StatusText>Content Security</StatusText>
        </StatusCard>
      </SecurityStatus>

      <Section>
        <SectionTitle>Content Security Policy</SectionTitle>
        <SettingItem>
          <SettingLabel>Enable CSP</SettingLabel>
          <Switch
            checked={config.csp.enabled}
            onChange={(checked) => handleConfigChange('csp.enabled', checked)}
          />
        </SettingItem>
        <SettingItem>
          <SettingLabel>Report Only Mode</SettingLabel>
          <Switch
            checked={config.csp.reportOnly}
            onChange={(checked) => handleConfigChange('csp.reportOnly', checked)}
          />
        </SettingItem>
      </Section>

      <Section>
        <SectionTitle>Permissions</SectionTitle>
        <SettingItem>
          <SettingLabel>Clipboard Access</SettingLabel>
          <Switch
            checked={config.permissions.clipboard}
            onChange={(checked) => handleConfigChange('permissions.clipboard', checked)}
          />
        </SettingItem>
        <SettingItem>
          <SettingLabel>Notifications</SettingLabel>
          <Switch
            checked={config.permissions.notifications}
            onChange={(checked) => handleConfigChange('permissions.notifications', checked)}
          />
        </SettingItem>
        <SettingItem>
          <SettingLabel>Camera Access</SettingLabel>
          <Switch
            checked={config.permissions.camera}
            onChange={(checked) => handleConfigChange('permissions.camera', checked)}
          />
        </SettingItem>
        <SettingItem>
          <SettingLabel>Microphone Access</SettingLabel>
          <Switch
            checked={config.permissions.microphone}
            onChange={(checked) => handleConfigChange('permissions.microphone', checked)}
          />
        </SettingItem>
      </Section>

      <Section>
        <SectionTitle>Privacy Features</SectionTitle>
        <SettingItem>
          <SettingLabel>Do Not Track</SettingLabel>
          <Switch
            checked={config.privacy.doNotTrack}
            onChange={(checked) => handleConfigChange('privacy.doNotTrack', checked)}
          />
        </SettingItem>
        <SettingItem>
          <SettingLabel>Block Third-Party Cookies</SettingLabel>
          <Switch
            checked={!config.privacy.thirdPartyCookies}
            onChange={(checked) => handleConfigChange('privacy.thirdPartyCookies', !checked)}
          />
        </SettingItem>
        <SettingItem>
          <SettingLabel>Block WebRTC</SettingLabel>
          <Switch
            checked={!config.privacy.webRTC}
            onChange={(checked) => handleConfigChange('privacy.webRTC', !checked)}
          />
        </SettingItem>
        <SettingItem>
          <SettingLabel>Block Canvas Fingerprinting</SettingLabel>
          <Switch
            checked={config.privacy.canvasFingerprinting}
            onChange={(checked) => handleConfigChange('privacy.canvasFingerprinting', checked)}
          />
        </SettingItem>
      </Section>

      <Section>
        <SectionTitle>Network Security</SectionTitle>
        <SettingItem>
          <SettingLabel>Enable Proxy</SettingLabel>
          <Switch
            checked={config.network.proxy.enabled}
            onChange={(checked) => handleConfigChange('network.proxy.enabled', checked)}
          />
        </SettingItem>
        <SettingItem>
          <SettingLabel>Secure DNS</SettingLabel>
          <Switch
            checked={config.network.dns.secure}
            onChange={(checked) => handleConfigChange('network.dns.secure', checked)}
          />
        </SettingItem>
        <SettingItem>
          <SettingLabel>Verify Certificates</SettingLabel>
          <Switch
            checked={config.network.certificates.verify}
            onChange={(checked) => handleConfigChange('network.certificates.verify', checked)}
          />
        </SettingItem>
      </Section>

      <ActionButtons>
        <Button onClick={() => securityManager.initialize()}>
          <Icon name="refresh" />
          Apply Changes
        </Button>
        <Button onClick={() => securityManager.validateConfig()}>
          <Icon name="check" />
          Validate Configuration
        </Button>
      </ActionButtons>
    </DashboardContainer>
  );
}; 