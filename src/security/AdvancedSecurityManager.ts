/**
 * Продвинутая система безопасности с современными протоколами
 * аутентификации, авторизации и защиты данных
 */

import { EventEmitter } from 'events';
import CryptoJS from 'crypto-js';

export interface SecurityConfig {
  encryption: {
    algorithm: string;
    keySize: number;
    iterations: number;
  };
  authentication: {
    enableMFA: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    lockoutDuration: number;
  };
  authorization: {
    enableRBAC: boolean;
    enableABAC: boolean;
    defaultRole: string;
  };
  monitoring: {
    enableThreatDetection: boolean;
    enableAuditLogging: boolean;
    enableRealTimeAlerts: boolean;
  };
}

export interface User {
  id: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  mfaEnabled: boolean;
  lastLogin: Date;
  loginAttempts: number;
  lockedUntil?: Date;
  metadata: Record<string, any>;
}

export interface SecurityEvent {
  id: string;
  type: 'login' | 'logout' | 'failed_login' | 'permission_denied' | 'threat_detected' | 'data_access';
  userId?: string;
  ip: string;
  userAgent: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  details: Record<string, any>;
}

export interface ThreatSignature {
  id: string;
  name: string;
  pattern: RegExp;
  severity: 'low' | 'medium' | 'high' | 'critical';
  action: 'log' | 'block' | 'alert';
  description: string;
}

export interface AccessPolicy {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions: Array<{
    attribute: string;
    operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex';
    value: any;
  }>;
  effect: 'allow' | 'deny';
}

export class AdvancedSecurityManager extends EventEmitter {
  private config: SecurityConfig;
  private users = new Map<string, User>();
  private sessions = new Map<string, { userId: string; createdAt: Date; lastActivity: Date }>();
  private securityEvents: SecurityEvent[] = [];
  private threatSignatures: ThreatSignature[] = [];
  private accessPolicies: AccessPolicy[] = [];
  private encryptionKey: string;

  constructor(config: SecurityConfig, encryptionKey: string) {
    super();
    this.config = config;
    this.encryptionKey = encryptionKey;
    this.initializeDefaultThreatSignatures();
    this.startSecurityMonitoring();
  }

  /**
   * Аутентификация пользователя
   */
  async authenticate(username: string, password: string, ip: string, userAgent: string): Promise<{
    success: boolean;
    sessionId?: string;
    user?: User;
    requiresMFA?: boolean;
    error?: string;
  }> {
    const user = Array.from(this.users.values()).find(u => u.username === username);
    
    if (!user) {
      await this.logSecurityEvent('failed_login', undefined, ip, userAgent, 'medium', {
        reason: 'user_not_found',
        username
      });
      return { success: false, error: 'Invalid credentials' };
    }

    // Проверяем блокировку аккаунта
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      await this.logSecurityEvent('failed_login', user.id, ip, userAgent, 'high', {
        reason: 'account_locked',
        lockedUntil: user.lockedUntil
      });
      return { success: false, error: 'Account is locked' };
    }

    // Проверяем пароль (в реальном приложении используйте bcrypt)
    const hashedPassword = this.hashPassword(password);
    const storedPassword = user.metadata.hashedPassword;

    if (hashedPassword !== storedPassword) {
      user.loginAttempts++;
      
      if (user.loginAttempts >= this.config.authentication.maxLoginAttempts) {
        user.lockedUntil = new Date(Date.now() + this.config.authentication.lockoutDuration);
        await this.logSecurityEvent('failed_login', user.id, ip, userAgent, 'high', {
          reason: 'max_attempts_exceeded',
          attempts: user.loginAttempts
        });
      } else {
        await this.logSecurityEvent('failed_login', user.id, ip, userAgent, 'medium', {
          reason: 'invalid_password',
          attempts: user.loginAttempts
        });
      }
      
      return { success: false, error: 'Invalid credentials' };
    }

    // Сброс счетчика неудачных попыток
    user.loginAttempts = 0;
    user.lockedUntil = undefined;
    user.lastLogin = new Date();

    // Проверяем MFA
    if (user.mfaEnabled) {
      return { success: true, requiresMFA: true, user };
    }

    // Создаем сессию
    const sessionId = this.generateSessionId();
    this.sessions.set(sessionId, {
      userId: user.id,
      createdAt: new Date(),
      lastActivity: new Date()
    });

    await this.logSecurityEvent('login', user.id, ip, userAgent, 'low', {
      sessionId
    });

    return { success: true, sessionId, user };
  }

  /**
   * Верификация MFA
   */
  async verifyMFA(userId: string, code: string, ip: string, userAgent: string): Promise<{
    success: boolean;
    sessionId?: string;
    error?: string;
  }> {
    const user = this.users.get(userId);
    if (!user) {
      return { success: false, error: 'User not found' };
    }

    // В реальном приложении здесь будет проверка TOTP/SMS кода
    const isValidCode = this.verifyTOTPCode(code, user.metadata.mfaSecret);
    
    if (!isValidCode) {
      await this.logSecurityEvent('failed_login', userId, ip, userAgent, 'high', {
        reason: 'invalid_mfa_code'
      });
      return { success: false, error: 'Invalid MFA code' };
    }

    // Создаем сессию
    const sessionId = this.generateSessionId();
    this.sessions.set(sessionId, {
      userId,
      createdAt: new Date(),
      lastActivity: new Date()
    });

    await this.logSecurityEvent('login', userId, ip, userAgent, 'low', {
      sessionId,
      mfaVerified: true
    });

    return { success: true, sessionId };
  }

  /**
   * Авторизация доступа к ресурсу
   */
  async authorize(sessionId: string, resource: string, action: string, context: Record<string, any> = {}): Promise<{
    allowed: boolean;
    reason?: string;
  }> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return { allowed: false, reason: 'Invalid session' };
    }

    // Проверяем таймаут сессии
    const sessionAge = Date.now() - session.lastActivity.getTime();
    if (sessionAge > this.config.authentication.sessionTimeout) {
      this.sessions.delete(sessionId);
      return { allowed: false, reason: 'Session expired' };
    }

    // Обновляем активность сессии
    session.lastActivity = new Date();

    const user = this.users.get(session.userId);
    if (!user) {
      return { allowed: false, reason: 'User not found' };
    }

    // RBAC проверка
    if (this.config.authorization.enableRBAC) {
      const hasPermission = this.checkRBACPermission(user, resource, action);
      if (!hasPermission) {
        await this.logSecurityEvent('permission_denied', user.id, context.ip || '', context.userAgent || '', 'medium', {
          resource,
          action,
          reason: 'rbac_denied'
        });
        return { allowed: false, reason: 'Permission denied (RBAC)' };
      }
    }

    // ABAC проверка
    if (this.config.authorization.enableABAC) {
      const hasAccess = await this.checkABACAccess(user, resource, action, context);
      if (!hasAccess) {
        await this.logSecurityEvent('permission_denied', user.id, context.ip || '', context.userAgent || '', 'medium', {
          resource,
          action,
          reason: 'abac_denied'
        });
        return { allowed: false, reason: 'Permission denied (ABAC)' };
      }
    }

    await this.logSecurityEvent('data_access', user.id, context.ip || '', context.userAgent || '', 'low', {
      resource,
      action
    });

    return { allowed: true };
  }

  /**
   * Шифрование данных
   */
  encryptData(data: string): string {
    return CryptoJS.AES.encrypt(data, this.encryptionKey, {
      keySize: this.config.encryption.keySize / 32,
      iv: CryptoJS.lib.WordArray.random(16),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    }).toString();
  }

  /**
   * Расшифровка данных
   */
  decryptData(encryptedData: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  /**
   * Обнаружение угроз
   */
  async detectThreats(request: {
    ip: string;
    userAgent: string;
    url: string;
    headers: Record<string, string>;
    body?: string;
  }): Promise<{
    threats: Array<{ signature: ThreatSignature; matches: string[] }>;
    blocked: boolean;
  }> {
    const threats: Array<{ signature: ThreatSignature; matches: string[] }> = [];
    let blocked = false;

    for (const signature of this.threatSignatures) {
      const matches: string[] = [];
      
      // Проверяем URL
      if (signature.pattern.test(request.url)) {
        matches.push(`URL: ${request.url}`);
      }

      // Проверяем заголовки
      for (const [key, value] of Object.entries(request.headers)) {
        if (signature.pattern.test(`${key}: ${value}`)) {
          matches.push(`Header: ${key}: ${value}`);
        }
      }

      // Проверяем тело запроса
      if (request.body && signature.pattern.test(request.body)) {
        matches.push(`Body: ${request.body.substring(0, 100)}...`);
      }

      if (matches.length > 0) {
        threats.push({ signature, matches });
        
        if (signature.action === 'block') {
          blocked = true;
        }

        await this.logSecurityEvent('threat_detected', undefined, request.ip, request.userAgent, signature.severity, {
          signatureId: signature.id,
          signatureName: signature.name,
          matches
        });
      }
    }

    return { threats, blocked };
  }

  /**
   * Добавление пользователя
   */
  async addUser(userData: Omit<User, 'id' | 'loginAttempts' | 'lastLogin'> & { password: string }): Promise<User> {
    const user: User = {
      id: this.generateUserId(),
      username: userData.username,
      email: userData.email,
      roles: userData.roles,
      permissions: userData.permissions,
      mfaEnabled: userData.mfaEnabled,
      lastLogin: new Date(),
      loginAttempts: 0,
      metadata: {
        ...userData.metadata,
        hashedPassword: this.hashPassword(userData.password),
        mfaSecret: userData.mfaEnabled ? this.generateMFASecret() : undefined
      }
    };

    this.users.set(user.id, user);
    return user;
  }

  /**
   * Получение событий безопасности
   */
  getSecurityEvents(filter?: {
    type?: string;
    userId?: string;
    severity?: string;
    startDate?: Date;
    endDate?: Date;
  }): SecurityEvent[] {
    let events = this.securityEvents;

    if (filter) {
      events = events.filter(event => {
        if (filter.type && event.type !== filter.type) return false;
        if (filter.userId && event.userId !== filter.userId) return false;
        if (filter.severity && event.severity !== filter.severity) return false;
        if (filter.startDate && event.timestamp < filter.startDate) return false;
        if (filter.endDate && event.timestamp > filter.endDate) return false;
        return true;
      });
    }

    return events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  private async logSecurityEvent(
    type: SecurityEvent['type'],
    userId: string | undefined,
    ip: string,
    userAgent: string,
    severity: SecurityEvent['severity'],
    details: Record<string, any>
  ): Promise<void> {
    const event: SecurityEvent = {
      id: this.generateEventId(),
      type,
      userId,
      ip,
      userAgent,
      timestamp: new Date(),
      severity,
      details
    };

    this.securityEvents.push(event);
    
    // Ограничиваем количество событий в памяти
    if (this.securityEvents.length > 10000) {
      this.securityEvents = this.securityEvents.slice(-5000);
    }

    this.emit('securityEvent', event);

    // Отправляем критические события в реальном времени
    if (severity === 'critical' && this.config.monitoring.enableRealTimeAlerts) {
      this.emit('criticalThreat', event);
    }
  }

  private checkRBACPermission(user: User, resource: string, action: string): boolean {
    // Проверяем прямые разрешения
    const permission = `${resource}:${action}`;
    if (user.permissions.includes(permission) || user.permissions.includes('*')) {
      return true;
    }

    // Проверяем разрешения через роли
    for (const role of user.roles) {
      // В реальном приложении здесь будет загрузка разрешений роли из базы данных
      const rolePermissions = this.getRolePermissions(role);
      if (rolePermissions.includes(permission) || rolePermissions.includes('*')) {
        return true;
      }
    }

    return false;
  }

  private async checkABACAccess(user: User, resource: string, action: string, context: Record<string, any>): Promise<boolean> {
    for (const policy of this.accessPolicies) {
      if (policy.resource === resource && policy.action === action) {
        const conditionsMet = policy.conditions.every(condition => {
          const value = this.getAttributeValue(user, context, condition.attribute);
          return this.evaluateCondition(value, condition.operator, condition.value);
        });

        if (conditionsMet) {
          return policy.effect === 'allow';
        }
      }
    }

    return false;
  }

  private initializeDefaultThreatSignatures(): void {
    this.threatSignatures = [
      {
        id: 'sql-injection',
        name: 'SQL Injection',
        pattern: /(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript|onload|onerror)/i,
        severity: 'high',
        action: 'block',
        description: 'Detects potential SQL injection attempts'
      },
      {
        id: 'xss-attack',
        name: 'Cross-Site Scripting',
        pattern: /<script|javascript:|vbscript:|onload=|onerror=|onclick=/i,
        severity: 'high',
        action: 'block',
        description: 'Detects potential XSS attacks'
      },
      {
        id: 'path-traversal',
        name: 'Path Traversal',
        pattern: /\.\.[\/\\]|\.\.%2f|\.\.%5c/i,
        severity: 'medium',
        action: 'alert',
        description: 'Detects path traversal attempts'
      }
    ];
  }

  private startSecurityMonitoring(): void {
    if (!this.config.monitoring.enableThreatDetection) return;

    // Мониторинг подозрительной активности
    setInterval(() => {
      this.analyzeSecurityPatterns();
    }, 60000); // Каждую минуту
  }

  private analyzeSecurityPatterns(): void {
    const recentEvents = this.securityEvents.filter(
      event => Date.now() - event.timestamp.getTime() < 300000 // Последние 5 минут
    );

    // Анализ множественных неудачных попыток входа
    const failedLogins = recentEvents.filter(event => event.type === 'failed_login');
    const ipCounts = new Map<string, number>();
    
    failedLogins.forEach(event => {
      ipCounts.set(event.ip, (ipCounts.get(event.ip) || 0) + 1);
    });

    ipCounts.forEach((count, ip) => {
      if (count >= 10) { // 10 неудачных попыток за 5 минут
        this.logSecurityEvent('threat_detected', undefined, ip, '', 'high', {
          pattern: 'brute_force_attack',
          attempts: count,
          timeWindow: '5 minutes'
        });
      }
    });
  }

  private hashPassword(password: string): string {
    return CryptoJS.PBKDF2(password, this.encryptionKey, {
      keySize: 256/32,
      iterations: this.config.encryption.iterations
    }).toString();
  }

  private verifyTOTPCode(code: string, secret: string): boolean {
    // Упрощенная проверка TOTP (в реальном приложении используйте библиотеку speakeasy)
    const timeStep = Math.floor(Date.now() / 30000);
    const expectedCode = CryptoJS.HmacSHA1(timeStep.toString(), secret).toString().slice(-6);
    return code === expectedCode;
  }

  private generateSessionId(): string {
    return CryptoJS.lib.WordArray.random(32).toString();
  }

  private generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMFASecret(): string {
    return CryptoJS.lib.WordArray.random(20).toString();
  }

  private getRolePermissions(role: string): string[] {
    // В реальном приложении это будет загружаться из базы данных
    const rolePermissions: Record<string, string[]> = {
      admin: ['*'],
      user: ['read:profile', 'update:profile'],
      guest: ['read:public']
    };
    return rolePermissions[role] || [];
  }

  private getAttributeValue(user: User, context: Record<string, any>, attribute: string): any {
    if (attribute.startsWith('user.')) {
      const userAttr = attribute.substring(5);
      return (user as any)[userAttr];
    }
    if (attribute.startsWith('context.')) {
      const contextAttr = attribute.substring(8);
      return context[contextAttr];
    }
    return undefined;
  }

  private evaluateCondition(value: any, operator: string, expectedValue: any): boolean {
    switch (operator) {
      case 'equals':
        return value === expectedValue;
      case 'contains':
        return String(value).includes(String(expectedValue));
      case 'startsWith':
        return String(value).startsWith(String(expectedValue));
      case 'endsWith':
        return String(value).endsWith(String(expectedValue));
      case 'regex':
        return new RegExp(expectedValue).test(String(value));
      default:
        return false;
    }
  }
}

// Фабрика для создания Security Manager
export class SecurityManagerFactory {
  static create(encryptionKey?: string): AdvancedSecurityManager {
    const defaultConfig: SecurityConfig = {
      encryption: {
        algorithm: 'AES-256-CBC',
        keySize: 256,
        iterations: 10000
      },
      authentication: {
        enableMFA: true,
        sessionTimeout: 30 * 60 * 1000, // 30 minutes
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000 // 15 minutes
      },
      authorization: {
        enableRBAC: true,
        enableABAC: true,
        defaultRole: 'guest'
      },
      monitoring: {
        enableThreatDetection: true,
        enableAuditLogging: true,
        enableRealTimeAlerts: true
      }
    };

    const key = encryptionKey || process.env.SECURITY_ENCRYPTION_KEY || 'default-key-change-in-production';
    return new AdvancedSecurityManager(defaultConfig, key);
  }
}

// Глобальный экземпляр
export const securityManager = SecurityManagerFactory.create();
