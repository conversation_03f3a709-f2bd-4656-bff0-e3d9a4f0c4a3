import { SecurityManager } from '../SecurityManager';

describe('SecurityManager', () => {
  let securityManager: SecurityManager;

  beforeEach(() => {
    securityManager = SecurityManager.getInstance();
  });

  afterEach(() => {
    securityManager.cleanup();
  });

  describe('Password Management', () => {
    it('should hash and verify passwords correctly', async () => {
      const password = 'TestPassword123!';
      const hashedPassword = await securityManager.hashPassword(password);
      
      expect(hashedPassword).toBeDefined();
      expect(hashedPassword).not.toBe(password);
      
      const isValid = await securityManager.verifyPassword(password, hashedPassword);
      expect(isValid).toBe(true);
      
      const isInvalid = await securityManager.verifyPassword('WrongPassword123!', hashedPassword);
      expect(isInvalid).toBe(false);
    });

    it('should validate password requirements', () => {
      const result = securityManager.validatePassword('weak');
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);

      const strongResult = securityManager.validatePassword('StrongPassword123!');
      expect(strongResult.valid).toBe(true);
      expect(strongResult.errors.length).toBe(0);
    });
  });

  describe('Session Management', () => {
    const userId = 'test-user';
    const ip = '127.0.0.1';
    const userAgent = 'Mozilla/5.0';

    it('should create and validate sessions', () => {
      const session = securityManager.createSession(userId, ip, userAgent);
      
      expect(session).toBeDefined();
      expect(session.userId).toBe(userId);
      expect(session.ip).toBe(ip);
      expect(session.userAgent).toBe(userAgent);
      
      const isValid = securityManager.validateSession(session.id, ip);
      expect(isValid).toBe(true);
    });

    it('should invalidate sessions', () => {
      const session = securityManager.createSession(userId, ip, userAgent);
      securityManager.invalidateSession(session.id);
      
      const isValid = securityManager.validateSession(session.id, ip);
      expect(isValid).toBe(false);
    });

    it('should reject sessions with mismatched IP', () => {
      const session = securityManager.createSession(userId, ip, userAgent);
      const isValid = securityManager.validateSession(session.id, '***********');
      expect(isValid).toBe(false);
    });
  });

  describe('Login Security', () => {
    const ip = '127.0.0.1';

    it('should handle successful login attempts', () => {
      const result = securityManager.handleLoginAttempt(ip, true);
      expect(result).toBe(true);
    });

    it('should block IP after max failed attempts', () => {
      for (let i = 0; i < 5; i++) {
        securityManager.handleLoginAttempt(ip, false);
      }
      
      const result = securityManager.handleLoginAttempt(ip, true);
      expect(result).toBe(false);
    });

    it('should unblock IP after block duration', () => {
      // Block the IP
      for (let i = 0; i < 5; i++) {
        securityManager.handleLoginAttempt(ip, false);
      }
      
      // Fast-forward time
      jest.advanceTimersByTime(16 * 60 * 1000); // 16 minutes
      
      const result = securityManager.handleLoginAttempt(ip, true);
      expect(result).toBe(true);
    });
  });

  describe('Data Encryption', () => {
    it('should encrypt and decrypt data', () => {
      const originalData = 'Sensitive information';
      const encrypted = securityManager.encryptData(originalData);
      
      expect(encrypted).toBeDefined();
      expect(encrypted).not.toBe(originalData);
      
      const decrypted = securityManager.decryptData(encrypted);
      expect(decrypted).toBe(originalData);
    });

    it('should handle encryption errors', () => {
      expect(() => {
        securityManager.encryptData('');
      }).not.toThrow();
    });

    it('should handle decryption errors', () => {
      expect(() => {
        securityManager.decryptData('invalid:data:format');
      }).toThrow();
    });
  });

  describe('Security Logging', () => {
    it('should log security events', () => {
      const eventHandler = jest.fn();
      securityManager.on('securityEvent', eventHandler);
      
      securityManager.createSession('test-user', '127.0.0.1', 'Mozilla/5.0');
      
      expect(eventHandler).toHaveBeenCalled();
      expect(eventHandler.mock.calls[0][0].type).toBe('session_created');
    });

    it('should maintain security logs', () => {
      securityManager.createSession('test-user', '127.0.0.1', 'Mozilla/5.0');
      
      const logs = securityManager.getSecurityLogs();
      expect(logs.length).toBeGreaterThan(0);
      expect(logs[logs.length - 1].type).toBe('session_created');
    });
  });

  describe('Configuration Management', () => {
    it('should update configuration', () => {
      const newConfig = {
        sessionTimeout: 3600000, // 1 hour
        maxLoginAttempts: 3,
      };
      
      securityManager.updateConfig(newConfig);
      const config = securityManager.getConfig();
      
      expect(config.sessionTimeout).toBe(3600000);
      expect(config.maxLoginAttempts).toBe(3);
    });

    it('should maintain default values for unspecified config', () => {
      const newConfig = {
        sessionTimeout: 3600000,
      };
      
      securityManager.updateConfig(newConfig);
      const config = securityManager.getConfig();
      
      expect(config.sessionTimeout).toBe(3600000);
      expect(config.maxLoginAttempts).toBeDefined();
      expect(config.passwordMinLength).toBeDefined();
    });
  });

  describe('Cleanup', () => {
    it('should clean up expired sessions', () => {
      const session = securityManager.createSession('test-user', '127.0.0.1', 'Mozilla/5.0');
      
      // Fast-forward time
      jest.advanceTimersByTime(25 * 60 * 60 * 1000); // 25 hours
      
      securityManager.cleanup();
      const isValid = securityManager.validateSession(session.id, '127.0.0.1');
      expect(isValid).toBe(false);
    });

    it('should clean up old login attempts', () => {
      const ip = '127.0.0.1';
      
      // Create some login attempts
      for (let i = 0; i < 3; i++) {
        securityManager.handleLoginAttempt(ip, false);
      }
      
      // Fast-forward time
      jest.advanceTimersByTime(16 * 60 * 1000); // 16 minutes
      
      securityManager.cleanup();
      const result = securityManager.handleLoginAttempt(ip, true);
      expect(result).toBe(true);
    });
  });
}); 