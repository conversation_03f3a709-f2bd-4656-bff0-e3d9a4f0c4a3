import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';
import { configManager } from '../core/ConfigurationManager';

export interface SecurityVulnerability {
  id: string;
  type: 'xss' | 'csrf' | 'injection' | 'exposure' | 'weak_crypto' | 'insecure_transport' | 'auth_bypass' | 'dos';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  location: {
    file?: string;
    line?: number;
    column?: number;
    element?: Element;
    url?: string;
  };
  evidence: string;
  recommendation: string;
  cwe?: string; // Common Weakness Enumeration
  cvss?: number; // Common Vulnerability Scoring System
  references: string[];
  timestamp: number;
}

export interface SecurityScanResult {
  scanId: string;
  timestamp: number;
  duration: number;
  vulnerabilities: SecurityVulnerability[];
  summary: {
    total: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  score: number; // 0-100, higher is better
  recommendations: string[];
}

export interface SecurityScanConfig {
  enableStaticAnalysis: boolean;
  enableDynamicAnalysis: boolean;
  enableDependencyCheck: boolean;
  enableContentSecurityPolicy: boolean;
  enableInputValidation: boolean;
  enableAuthenticationCheck: boolean;
  enableEncryptionCheck: boolean;
  enableNetworkSecurity: boolean;
  maxScanTime: number;
  reportLevel: 'all' | 'medium+' | 'high+' | 'critical';
}

export class SecurityScanner extends EventEmitter {
  private static instance: SecurityScanner;
  private config: SecurityScanConfig;
  private scanHistory: SecurityScanResult[] = [];

  private constructor() {
    super();
    this.config = {
      enableStaticAnalysis: true,
      enableDynamicAnalysis: true,
      enableDependencyCheck: true,
      enableContentSecurityPolicy: true,
      enableInputValidation: true,
      enableAuthenticationCheck: true,
      enableEncryptionCheck: true,
      enableNetworkSecurity: true,
      maxScanTime: 300000, // 5 minutes
      reportLevel: 'medium+',
    };
  }

  public static getInstance(): SecurityScanner {
    if (!SecurityScanner.instance) {
      SecurityScanner.instance = new SecurityScanner();
    }
    return SecurityScanner.instance;
  }

  public async performComprehensiveScan(): Promise<SecurityScanResult> {
    const scanId = `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();
    
    logger.info('Starting comprehensive security scan', { scanId });
    this.emit('scan_started', { scanId });

    const vulnerabilities: SecurityVulnerability[] = [];

    try {
      // Static Analysis
      if (this.config.enableStaticAnalysis) {
        const staticVulns = await this.performStaticAnalysis();
        vulnerabilities.push(...staticVulns);
      }

      // Dynamic Analysis
      if (this.config.enableDynamicAnalysis) {
        const dynamicVulns = await this.performDynamicAnalysis();
        vulnerabilities.push(...dynamicVulns);
      }

      // Dependency Check
      if (this.config.enableDependencyCheck) {
        const depVulns = await this.checkDependencies();
        vulnerabilities.push(...depVulns);
      }

      // Content Security Policy
      if (this.config.enableContentSecurityPolicy) {
        const cspVulns = await this.checkContentSecurityPolicy();
        vulnerabilities.push(...cspVulns);
      }

      // Input Validation
      if (this.config.enableInputValidation) {
        const inputVulns = await this.checkInputValidation();
        vulnerabilities.push(...inputVulns);
      }

      // Authentication Check
      if (this.config.enableAuthenticationCheck) {
        const authVulns = await this.checkAuthentication();
        vulnerabilities.push(...authVulns);
      }

      // Encryption Check
      if (this.config.enableEncryptionCheck) {
        const cryptoVulns = await this.checkEncryption();
        vulnerabilities.push(...cryptoVulns);
      }

      // Network Security
      if (this.config.enableNetworkSecurity) {
        const networkVulns = await this.checkNetworkSecurity();
        vulnerabilities.push(...networkVulns);
      }

      const duration = Date.now() - startTime;
      const result = this.generateScanResult(scanId, vulnerabilities, duration);
      
      this.scanHistory.push(result);
      this.emit('scan_completed', result);
      
      logger.info('Security scan completed', {
        scanId,
        duration,
        vulnerabilityCount: vulnerabilities.length,
        score: result.score,
      });

      return result;
    } catch (error) {
      logger.error('Security scan failed', error, { scanId });
      throw error;
    }
  }

  private async performStaticAnalysis(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // Check for dangerous functions
    const dangerousFunctions = ['eval', 'innerHTML', 'outerHTML', 'document.write'];
    
    // Simulate static analysis
    dangerousFunctions.forEach(func => {
      if (this.codeContainsFunction(func)) {
        vulnerabilities.push({
          id: `static_${func}_${Date.now()}`,
          type: 'xss',
          severity: 'high',
          title: `Dangerous function usage: ${func}`,
          description: `Usage of ${func} can lead to XSS vulnerabilities`,
          location: { file: 'unknown' },
          evidence: `Function ${func} found in code`,
          recommendation: `Replace ${func} with safer alternatives`,
          cwe: 'CWE-79',
          cvss: 7.5,
          references: ['https://owasp.org/www-community/attacks/xss/'],
          timestamp: Date.now(),
        });
      }
    });

    // Check for hardcoded secrets
    const secretPatterns = [
      /password\s*=\s*["'][^"']+["']/i,
      /api[_-]?key\s*=\s*["'][^"']+["']/i,
      /secret\s*=\s*["'][^"']+["']/i,
    ];

    secretPatterns.forEach((pattern, index) => {
      if (this.codeMatchesPattern(pattern)) {
        vulnerabilities.push({
          id: `secret_${index}_${Date.now()}`,
          type: 'exposure',
          severity: 'critical',
          title: 'Hardcoded secret detected',
          description: 'Hardcoded secrets in source code pose security risks',
          location: { file: 'unknown' },
          evidence: 'Secret pattern found in code',
          recommendation: 'Use environment variables or secure key management',
          cwe: 'CWE-798',
          cvss: 9.0,
          references: ['https://owasp.org/www-top-10/A02_2021-Cryptographic_Failures/'],
          timestamp: Date.now(),
        });
      }
    });

    return vulnerabilities;
  }

  private async performDynamicAnalysis(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // Check for XSS vulnerabilities
    const xssPayloads = [
      '<script>alert("xss")</script>',
      'javascript:alert("xss")',
      '<img src=x onerror=alert("xss")>',
    ];

    for (const payload of xssPayloads) {
      if (await this.testXSSPayload(payload)) {
        vulnerabilities.push({
          id: `xss_${Date.now()}`,
          type: 'xss',
          severity: 'high',
          title: 'Cross-Site Scripting (XSS) vulnerability',
          description: 'Application is vulnerable to XSS attacks',
          location: { url: window.location.href },
          evidence: `XSS payload executed: ${payload}`,
          recommendation: 'Implement proper input validation and output encoding',
          cwe: 'CWE-79',
          cvss: 6.1,
          references: ['https://owasp.org/www-community/attacks/xss/'],
          timestamp: Date.now(),
        });
      }
    }

    // Check for CSRF vulnerabilities
    if (await this.testCSRF()) {
      vulnerabilities.push({
        id: `csrf_${Date.now()}`,
        type: 'csrf',
        severity: 'medium',
        title: 'Cross-Site Request Forgery (CSRF) vulnerability',
        description: 'Application lacks CSRF protection',
        location: { url: window.location.href },
        evidence: 'No CSRF token found in forms',
        recommendation: 'Implement CSRF tokens for state-changing operations',
        cwe: 'CWE-352',
        cvss: 4.3,
        references: ['https://owasp.org/www-community/attacks/csrf'],
        timestamp: Date.now(),
      });
    }

    return vulnerabilities;
  }

  private async checkDependencies(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // Simulate dependency vulnerability check
    const knownVulnerableDeps = [
      { name: 'lodash', version: '<4.17.21', cve: 'CVE-2021-23337' },
      { name: 'axios', version: '<0.21.2', cve: 'CVE-2021-3749' },
    ];

    knownVulnerableDeps.forEach(dep => {
      if (this.hasDependency(dep.name, dep.version)) {
        vulnerabilities.push({
          id: `dep_${dep.name}_${Date.now()}`,
          type: 'exposure',
          severity: 'medium',
          title: `Vulnerable dependency: ${dep.name}`,
          description: `Dependency ${dep.name} has known vulnerabilities`,
          location: { file: 'package.json' },
          evidence: `${dep.name} ${dep.version} - ${dep.cve}`,
          recommendation: `Update ${dep.name} to latest secure version`,
          cwe: 'CWE-1104',
          cvss: 5.0,
          references: [`https://cve.mitre.org/cgi-bin/cvename.cgi?name=${dep.cve}`],
          timestamp: Date.now(),
        });
      }
    });

    return vulnerabilities;
  }

  private async checkContentSecurityPolicy(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    const cspHeader = this.getCSPHeader();
    
    if (!cspHeader) {
      vulnerabilities.push({
        id: `csp_missing_${Date.now()}`,
        type: 'exposure',
        severity: 'medium',
        title: 'Missing Content Security Policy',
        description: 'No CSP header found',
        location: { url: window.location.href },
        evidence: 'Content-Security-Policy header not present',
        recommendation: 'Implement a strict Content Security Policy',
        cwe: 'CWE-693',
        cvss: 4.0,
        references: ['https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP'],
        timestamp: Date.now(),
      });
    } else {
      // Check for unsafe CSP directives
      const unsafeDirectives = ['unsafe-inline', 'unsafe-eval', '*'];
      
      unsafeDirectives.forEach(directive => {
        if (cspHeader.includes(directive)) {
          vulnerabilities.push({
            id: `csp_unsafe_${directive}_${Date.now()}`,
            type: 'exposure',
            severity: 'medium',
            title: `Unsafe CSP directive: ${directive}`,
            description: `CSP contains unsafe directive: ${directive}`,
            location: { url: window.location.href },
            evidence: `CSP header contains: ${directive}`,
            recommendation: `Remove or replace unsafe directive: ${directive}`,
            cwe: 'CWE-693',
            cvss: 3.5,
            references: ['https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP'],
            timestamp: Date.now(),
          });
        }
      });
    }

    return vulnerabilities;
  }

  private async checkInputValidation(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // Check all input elements for validation
    const inputs = document.querySelectorAll('input, textarea, select');
    
    inputs.forEach((input, index) => {
      const hasValidation = input.hasAttribute('pattern') ||
                           input.hasAttribute('required') ||
                           input.hasAttribute('minlength') ||
                           input.hasAttribute('maxlength') ||
                           input.hasAttribute('min') ||
                           input.hasAttribute('max');

      if (!hasValidation && input.getAttribute('type') !== 'hidden') {
        vulnerabilities.push({
          id: `input_validation_${index}_${Date.now()}`,
          type: 'injection',
          severity: 'medium',
          title: 'Missing input validation',
          description: 'Input field lacks validation attributes',
          location: { element: input },
          evidence: `Input element without validation: ${input.tagName}`,
          recommendation: 'Add appropriate validation attributes and server-side validation',
          cwe: 'CWE-20',
          cvss: 4.0,
          references: ['https://owasp.org/www-project-top-ten/2017/A1_2017-Injection'],
          timestamp: Date.now(),
        });
      }
    });

    return vulnerabilities;
  }

  private async checkAuthentication(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // Check for weak session management
    const sessionCookie = this.getSessionCookie();
    
    if (sessionCookie) {
      if (!sessionCookie.secure) {
        vulnerabilities.push({
          id: `session_insecure_${Date.now()}`,
          type: 'insecure_transport',
          severity: 'high',
          title: 'Insecure session cookie',
          description: 'Session cookie lacks Secure flag',
          location: { url: window.location.href },
          evidence: 'Session cookie without Secure flag',
          recommendation: 'Set Secure flag on session cookies',
          cwe: 'CWE-614',
          cvss: 6.0,
          references: ['https://owasp.org/www-community/controls/SecureCookieAttribute'],
          timestamp: Date.now(),
        });
      }

      if (!sessionCookie.httpOnly) {
        vulnerabilities.push({
          id: `session_httponly_${Date.now()}`,
          type: 'xss',
          severity: 'medium',
          title: 'Session cookie accessible via JavaScript',
          description: 'Session cookie lacks HttpOnly flag',
          location: { url: window.location.href },
          evidence: 'Session cookie without HttpOnly flag',
          recommendation: 'Set HttpOnly flag on session cookies',
          cwe: 'CWE-1004',
          cvss: 4.0,
          references: ['https://owasp.org/www-community/HttpOnly'],
          timestamp: Date.now(),
        });
      }
    }

    return vulnerabilities;
  }

  private async checkEncryption(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // Check for HTTPS usage
    if (window.location.protocol !== 'https:') {
      vulnerabilities.push({
        id: `https_missing_${Date.now()}`,
        type: 'insecure_transport',
        severity: 'high',
        title: 'Insecure transport protocol',
        description: 'Application not using HTTPS',
        location: { url: window.location.href },
        evidence: `Using ${window.location.protocol} instead of HTTPS`,
        recommendation: 'Implement HTTPS for all communications',
        cwe: 'CWE-319',
        cvss: 7.5,
        references: ['https://owasp.org/www-project-top-ten/2017/A3_2017-Sensitive_Data_Exposure'],
        timestamp: Date.now(),
      });
    }

    // Check for weak encryption algorithms
    const weakAlgorithms = ['MD5', 'SHA1', 'DES', 'RC4'];
    
    weakAlgorithms.forEach(algorithm => {
      if (this.codeContainsAlgorithm(algorithm)) {
        vulnerabilities.push({
          id: `weak_crypto_${algorithm}_${Date.now()}`,
          type: 'weak_crypto',
          severity: 'medium',
          title: `Weak cryptographic algorithm: ${algorithm}`,
          description: `Usage of weak algorithm: ${algorithm}`,
          location: { file: 'unknown' },
          evidence: `Weak algorithm ${algorithm} found in code`,
          recommendation: `Replace ${algorithm} with stronger alternatives like SHA-256 or AES`,
          cwe: 'CWE-327',
          cvss: 5.0,
          references: ['https://owasp.org/www-project-top-ten/2017/A3_2017-Sensitive_Data_Exposure'],
          timestamp: Date.now(),
        });
      }
    });

    return vulnerabilities;
  }

  private async checkNetworkSecurity(): Promise<SecurityVulnerability[]> {
    const vulnerabilities: SecurityVulnerability[] = [];

    // Check for mixed content
    const resources = document.querySelectorAll('script[src], link[href], img[src]');
    
    resources.forEach((resource, index) => {
      const src = resource.getAttribute('src') || resource.getAttribute('href');
      
      if (src && src.startsWith('http://') && window.location.protocol === 'https:') {
        vulnerabilities.push({
          id: `mixed_content_${index}_${Date.now()}`,
          type: 'insecure_transport',
          severity: 'medium',
          title: 'Mixed content detected',
          description: 'HTTPS page loading HTTP resources',
          location: { element: resource },
          evidence: `HTTP resource loaded: ${src}`,
          recommendation: 'Use HTTPS for all resources',
          cwe: 'CWE-319',
          cvss: 4.0,
          references: ['https://developer.mozilla.org/en-US/docs/Web/Security/Mixed_content'],
          timestamp: Date.now(),
        });
      }
    });

    return vulnerabilities;
  }

  private generateScanResult(
    scanId: string,
    vulnerabilities: SecurityVulnerability[],
    duration: number
  ): SecurityScanResult {
    const summary = {
      total: vulnerabilities.length,
      critical: vulnerabilities.filter(v => v.severity === 'critical').length,
      high: vulnerabilities.filter(v => v.severity === 'high').length,
      medium: vulnerabilities.filter(v => v.severity === 'medium').length,
      low: vulnerabilities.filter(v => v.severity === 'low').length,
    };

    // Calculate security score (0-100, higher is better)
    const maxPossibleScore = 100;
    const criticalPenalty = summary.critical * 25;
    const highPenalty = summary.high * 15;
    const mediumPenalty = summary.medium * 8;
    const lowPenalty = summary.low * 3;
    
    const score = Math.max(0, maxPossibleScore - criticalPenalty - highPenalty - mediumPenalty - lowPenalty);

    const recommendations = this.generateRecommendations(vulnerabilities);

    return {
      scanId,
      timestamp: Date.now(),
      duration,
      vulnerabilities,
      summary,
      score,
      recommendations,
    };
  }

  private generateRecommendations(vulnerabilities: SecurityVulnerability[]): string[] {
    const recommendations = new Set<string>();

    vulnerabilities.forEach(vuln => {
      recommendations.add(vuln.recommendation);
    });

    // Add general recommendations based on vulnerability types
    const types = new Set(vulnerabilities.map(v => v.type));
    
    if (types.has('xss')) {
      recommendations.add('Implement comprehensive input validation and output encoding');
    }
    
    if (types.has('csrf')) {
      recommendations.add('Implement CSRF protection for all state-changing operations');
    }
    
    if (types.has('insecure_transport')) {
      recommendations.add('Enforce HTTPS for all communications');
    }

    return Array.from(recommendations);
  }

  // Real implementation methods
  private async codeContainsFunction(func: string): Promise<boolean> {
    try {
      // Check in script tags
      const scripts = document.querySelectorAll('script');
      for (const script of scripts) {
        if (script.textContent && script.textContent.includes(func)) {
          return true;
        }

        // Check external scripts if accessible
        if (script.src && script.src.startsWith(window.location.origin)) {
          try {
            const response = await fetch(script.src);
            const content = await response.text();
            if (content.includes(func)) {
              return true;
            }
          } catch (e) {
            // External script not accessible, skip
          }
        }
      }

      // Check inline event handlers
      const elements = document.querySelectorAll('*');
      for (const element of elements) {
        for (const attr of element.attributes) {
          if (attr.name.startsWith('on') && attr.value.includes(func)) {
            return true;
          }
        }
      }

      return false;
    } catch (error) {
      logger.warn('Error checking for dangerous function', { func, error });
      return false;
    }
  }

  private async codeMatchesPattern(pattern: RegExp): Promise<boolean> {
    try {
      // Check all script content
      const scripts = document.querySelectorAll('script');
      for (const script of scripts) {
        if (script.textContent && pattern.test(script.textContent)) {
          return true;
        }
      }

      // Check HTML content
      if (pattern.test(document.documentElement.outerHTML)) {
        return true;
      }

      return false;
    } catch (error) {
      logger.warn('Error checking code pattern', { pattern: pattern.source, error });
      return false;
    }
  }

  private async testXSSPayload(payload: string): Promise<boolean> {
    try {
      // Test in a sandboxed iframe
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.sandbox = 'allow-scripts';
      document.body.appendChild(iframe);

      return new Promise((resolve) => {
        let executed = false;
        const timeout = setTimeout(() => {
          document.body.removeChild(iframe);
          resolve(executed);
        }, 1000);

        // Set up detection
        iframe.contentWindow!.addEventListener('error', () => {
          executed = true;
        });

        // Inject payload
        iframe.contentDocument!.write(`
          <html>
            <body>
              <div>${payload}</div>
              <script>
                try {
                  // If this executes, XSS is possible
                  parent.postMessage('xss-executed', '*');
                } catch(e) {}
              </script>
            </body>
          </html>
        `);

        window.addEventListener('message', (event) => {
          if (event.data === 'xss-executed') {
            executed = true;
            clearTimeout(timeout);
            document.body.removeChild(iframe);
            resolve(true);
          }
        }, { once: true });
      });
    } catch (error) {
      logger.warn('Error testing XSS payload', { payload, error });
      return false;
    }
  }

  private async testCSRF(): Promise<boolean> {
    const forms = document.querySelectorAll('form');
    let hasCSRFVulnerability = false;

    for (const form of forms) {
      // Check if form has CSRF protection
      const hasCSRFToken = form.querySelector('input[name*="csrf"], input[name*="token"], input[name*="_token"]');
      const hasCSRFHeader = form.querySelector('meta[name="csrf-token"]');

      if (!hasCSRFToken && !hasCSRFHeader) {
        // Check if form performs state-changing operations
        const method = form.method.toLowerCase();
        const action = form.action;

        if (method === 'post' || method === 'put' || method === 'delete' ||
            action.includes('delete') || action.includes('update') || action.includes('create')) {
          hasCSRFVulnerability = true;
          break;
        }
      }
    }

    return hasCSRFVulnerability;
  }

  private async hasDependency(name: string, version: string): Promise<boolean> {
    try {
      // Check if we can access package.json or similar
      const response = await fetch('/package.json');
      if (response.ok) {
        const packageData = await response.json();
        const dependencies = { ...packageData.dependencies, ...packageData.devDependencies };

        if (dependencies[name]) {
          const installedVersion = dependencies[name];
          return this.compareVersions(installedVersion, version);
        }
      }

      // Fallback: check if module is loaded
      if (typeof window !== 'undefined' && (window as any)[name]) {
        return true;
      }

      return false;
    } catch (error) {
      logger.warn('Error checking dependency', { name, version, error });
      return false;
    }
  }

  private compareVersions(installed: string, vulnerable: string): boolean {
    // Simple version comparison - in production, use a proper semver library
    const cleanInstalled = installed.replace(/[^0-9.]/g, '');
    const cleanVulnerable = vulnerable.replace(/[^0-9.]/g, '');

    const installedParts = cleanInstalled.split('.').map(Number);
    const vulnerableParts = cleanVulnerable.split('.').map(Number);

    for (let i = 0; i < Math.max(installedParts.length, vulnerableParts.length); i++) {
      const installedPart = installedParts[i] || 0;
      const vulnerablePart = vulnerableParts[i] || 0;

      if (installedPart < vulnerablePart) {
        return true; // Installed version is vulnerable
      } else if (installedPart > vulnerablePart) {
        return false; // Installed version is safe
      }
    }

    return false; // Versions are equal, assume safe
  }

  private getCSPHeader(): string | null {
    // Check meta tag first
    const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (cspMeta) {
      return cspMeta.getAttribute('content');
    }

    // Check if we can access response headers (limited in browser)
    try {
      const xhr = new XMLHttpRequest();
      xhr.open('HEAD', window.location.href, false);
      xhr.send();
      return xhr.getResponseHeader('Content-Security-Policy');
    } catch (error) {
      // Cannot access headers in browser context
      return null;
    }
  }

  private getSessionCookie(): { secure: boolean; httpOnly: boolean } | null {
    const cookies = document.cookie.split(';');

    for (const cookie of cookies) {
      const [name, ...valueParts] = cookie.trim().split('=');

      // Look for common session cookie names
      if (name.toLowerCase().includes('session') ||
          name.toLowerCase().includes('auth') ||
          name.toLowerCase().includes('token')) {

        // Check cookie attributes (limited in browser)
        // Note: HttpOnly cookies are not accessible via document.cookie
        const secure = window.location.protocol === 'https:';
        const httpOnly = false; // Cannot detect HttpOnly from client-side

        return { secure, httpOnly };
      }
    }

    return null;
  }

  private async codeContainsAlgorithm(algorithm: string): Promise<boolean> {
    try {
      const algorithmPatterns = {
        'MD5': /md5|MD5/g,
        'SHA1': /sha1|SHA1/g,
        'DES': /\bdes\b|\bDES\b/g,
        'RC4': /rc4|RC4/g,
      };

      const pattern = algorithmPatterns[algorithm as keyof typeof algorithmPatterns];
      if (!pattern) return false;

      return await this.codeMatchesPattern(pattern);
    } catch (error) {
      logger.warn('Error checking for weak algorithm', { algorithm, error });
      return false;
    }
  }

  public getScanHistory(): SecurityScanResult[] {
    return [...this.scanHistory];
  }

  public getLatestScan(): SecurityScanResult | null {
    return this.scanHistory.length > 0 ? this.scanHistory[this.scanHistory.length - 1] : null;
  }
}

// Export singleton instance
export const securityScanner = SecurityScanner.getInstance();
