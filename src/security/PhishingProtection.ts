import { URL } from 'url';
import * as https from 'https';
import { promises as fs } from 'fs';
import * as path from 'path';
import { app } from 'electron';

interface PhishingCheckResult {
  isPhishing: boolean;
  confidence: number;
  reason?: string;
}

interface PhishingDatabase {
  knownPhishingSites: Set<string>;
  suspiciousPatterns: RegExp[];
  lastUpdate: number;
}

export class PhishingProtection {
  private static instance: PhishingProtection;
  private database: PhishingDatabase;
  private databasePath: string;
  private updateInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.database = {
      knownPhishingSites: new Set(),
      suspiciousPatterns: [
        /(?:login|signin|account|secure|verify|confirm|update|validate)\.(?:[a-z0-9-]+\.)*[a-z0-9-]+\.[a-z]{2,}/i,
        /(?:bank|paypal|amazon|google|facebook|apple|microsoft)\.(?:[a-z0-9-]+\.)*[a-z0-9-]+\.[a-z]{2,}/i,
        /(?:password|security|verification|confirmation)\.(?:[a-z0-9-]+\.)*[a-z0-9-]+\.[a-z]{2,}/i
      ],
      lastUpdate: 0
    };
    this.databasePath = path.join(app.getPath('userData'), 'phishing-db.json');
  }

  public static getInstance(): PhishingProtection {
    if (!PhishingProtection.instance) {
      PhishingProtection.instance = new PhishingProtection();
    }
    return PhishingProtection.instance;
  }

  public async initialize(): Promise<void> {
    await this.loadDatabase();
    this.startUpdateInterval();
  }

  private async loadDatabase(): Promise<void> {
    try {
      const data = await fs.readFile(this.databasePath, 'utf-8');
      const parsed = JSON.parse(data);
      this.database = {
        knownPhishingSites: new Set(parsed.knownPhishingSites),
        suspiciousPatterns: parsed.suspiciousPatterns.map((p: string) => new RegExp(p)),
        lastUpdate: parsed.lastUpdate
      };
    } catch (error) {
      // If database doesn't exist, start with empty one
      await this.updateDatabase();
    }
  }

  private async saveDatabase(): Promise<void> {
    const data = {
      knownPhishingSites: Array.from(this.database.knownPhishingSites),
      suspiciousPatterns: this.database.suspiciousPatterns.map(p => p.toString()),
      lastUpdate: Date.now()
    };
    await fs.writeFile(this.databasePath, JSON.stringify(data, null, 2));
  }

  private startUpdateInterval(): void {
    // Update database every 24 hours
    this.updateInterval = setInterval(() => {
      this.updateDatabase();
    }, 24 * 60 * 60 * 1000);
  }

  private async updateDatabase(): Promise<void> {
    try {
      // Here you would implement the actual update logic
      // For example, fetching from a trusted source
      this.database.lastUpdate = Date.now();
      await this.saveDatabase();
    } catch (error) {
      console.error('Failed to update phishing database:', error);
    }
  }

  public async checkUrl(url: string): Promise<PhishingCheckResult> {
    const parsedUrl = new URL(url);
    const hostname = parsedUrl.hostname;

    // Check against known phishing sites
    if (this.database.knownPhishingSites.has(hostname)) {
      return {
        isPhishing: true,
        confidence: 1.0,
        reason: 'Known phishing site'
      };
    }

    // Check for suspicious patterns
    for (const pattern of this.database.suspiciousPatterns) {
      if (pattern.test(hostname)) {
        return {
          isPhishing: true,
          confidence: 0.8,
          reason: 'Matches suspicious pattern'
        };
      }
    }

    // Check for SSL certificate
    try {
      const hasValidSSL = await this.checkSSL(hostname);
      if (!hasValidSSL) {
        return {
          isPhishing: true,
          confidence: 0.9,
          reason: 'Invalid SSL certificate'
        };
      }
    } catch (error) {
      return {
        isPhishing: true,
        confidence: 0.7,
        reason: 'SSL check failed'
      };
    }

    return {
      isPhishing: false,
      confidence: 0.0
    };
  }

  private async checkSSL(hostname: string): Promise<boolean> {
    return new Promise((resolve) => {
      const req = https.request({
        host: hostname,
        port: 443,
        method: 'HEAD',
        rejectUnauthorized: true
      }, (res) => {
        resolve(res.statusCode === 200);
      });

      req.on('error', () => {
        resolve(false);
      });

      req.end();
    });
  }

  public cleanup(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
  }
} 