import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';

export interface CryptoConfig {
  algorithm: 'AES-GCM' | 'AES-CBC' | 'ChaCha20-Poly1305';
  keyLength: 128 | 192 | 256;
  ivLength: 12 | 16;
  tagLength: 16;
  iterations: number;
  saltLength: number;
  hashAlgorithm: 'SHA-256' | 'SHA-384' | 'SHA-512';
}

export interface EncryptionResult {
  ciphertext: ArrayBuffer;
  iv: ArrayBuffer;
  salt: ArrayBuffer;
  tag?: ArrayBuffer;
  algorithm: string;
  keyLength: number;
}

export interface KeyDerivationOptions {
  password: string;
  salt: ArrayBuffer;
  iterations: number;
  keyLength: number;
  hashAlgorithm: string;
}

export interface DigitalSignature {
  signature: ArrayBuffer;
  algorithm: string;
  publicKey: CryptoKey;
  timestamp: number;
}

export class CryptographicService extends EventEmitter {
  private static instance: CryptographicService;
  private config: CryptoConfig;
  private keyCache: Map<string, CryptoKey> = new Map();

  private constructor() {
    super();
    this.config = {
      algorithm: 'AES-GCM',
      keyLength: 256,
      ivLength: 12,
      tagLength: 16,
      iterations: 100000,
      saltLength: 32,
      hashAlgorithm: 'SHA-256',
    };
  }

  public static getInstance(): CryptographicService {
    if (!CryptographicService.instance) {
      CryptographicService.instance = new CryptographicService();
    }
    return CryptographicService.instance;
  }

  /**
   * Generate a cryptographically secure random key
   */
  public async generateKey(algorithm: string = this.config.algorithm): Promise<CryptoKey> {
    try {
      const key = await crypto.subtle.generateKey(
        {
          name: algorithm,
          length: this.config.keyLength,
        },
        true, // extractable
        ['encrypt', 'decrypt']
      );

      logger.debug('Cryptographic key generated', { algorithm, keyLength: this.config.keyLength });
      return key;
    } catch (error) {
      logger.error('Failed to generate cryptographic key', error);
      throw new Error('Key generation failed');
    }
  }

  /**
   * Derive a key from a password using PBKDF2
   */
  public async deriveKey(options: KeyDerivationOptions): Promise<CryptoKey> {
    try {
      const encoder = new TextEncoder();
      const passwordBuffer = encoder.encode(options.password);

      // Import password as key material
      const keyMaterial = await crypto.subtle.importKey(
        'raw',
        passwordBuffer,
        'PBKDF2',
        false,
        ['deriveKey']
      );

      // Derive the actual key
      const key = await crypto.subtle.deriveKey(
        {
          name: 'PBKDF2',
          salt: options.salt,
          iterations: options.iterations,
          hash: options.hashAlgorithm,
        },
        keyMaterial,
        {
          name: this.config.algorithm,
          length: options.keyLength,
        },
        false, // not extractable
        ['encrypt', 'decrypt']
      );

      logger.debug('Key derived from password', {
        iterations: options.iterations,
        keyLength: options.keyLength,
        hashAlgorithm: options.hashAlgorithm,
      });

      return key;
    } catch (error) {
      logger.error('Failed to derive key from password', error);
      throw new Error('Key derivation failed');
    }
  }

  /**
   * Encrypt data using AES-GCM
   */
  public async encrypt(data: ArrayBuffer, key: CryptoKey): Promise<EncryptionResult> {
    try {
      // Generate random IV
      const iv = crypto.getRandomValues(new Uint8Array(this.config.ivLength));
      
      // Generate random salt
      const salt = crypto.getRandomValues(new Uint8Array(this.config.saltLength));

      // Encrypt the data
      const ciphertext = await crypto.subtle.encrypt(
        {
          name: this.config.algorithm,
          iv: iv,
        },
        key,
        data
      );

      const result: EncryptionResult = {
        ciphertext,
        iv: iv.buffer,
        salt: salt.buffer,
        algorithm: this.config.algorithm,
        keyLength: this.config.keyLength,
      };

      logger.debug('Data encrypted successfully', {
        algorithm: this.config.algorithm,
        dataSize: data.byteLength,
        ciphertextSize: ciphertext.byteLength,
      });

      this.emit('data_encrypted', { size: data.byteLength });
      return result;
    } catch (error) {
      logger.error('Failed to encrypt data', error);
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypt data using AES-GCM
   */
  public async decrypt(encryptionResult: EncryptionResult, key: CryptoKey): Promise<ArrayBuffer> {
    try {
      const decryptedData = await crypto.subtle.decrypt(
        {
          name: encryptionResult.algorithm,
          iv: encryptionResult.iv,
        },
        key,
        encryptionResult.ciphertext
      );

      logger.debug('Data decrypted successfully', {
        algorithm: encryptionResult.algorithm,
        decryptedSize: decryptedData.byteLength,
      });

      this.emit('data_decrypted', { size: decryptedData.byteLength });
      return decryptedData;
    } catch (error) {
      logger.error('Failed to decrypt data', error);
      throw new Error('Decryption failed');
    }
  }

  /**
   * Generate a cryptographic hash
   */
  public async hash(data: ArrayBuffer, algorithm: string = this.config.hashAlgorithm): Promise<ArrayBuffer> {
    try {
      const hashBuffer = await crypto.subtle.digest(algorithm, data);
      
      logger.debug('Data hashed successfully', {
        algorithm,
        dataSize: data.byteLength,
        hashSize: hashBuffer.byteLength,
      });

      return hashBuffer;
    } catch (error) {
      logger.error('Failed to hash data', error);
      throw new Error('Hashing failed');
    }
  }

  /**
   * Generate HMAC for message authentication
   */
  public async generateHMAC(data: ArrayBuffer, key: CryptoKey): Promise<ArrayBuffer> {
    try {
      const signature = await crypto.subtle.sign('HMAC', key, data);
      
      logger.debug('HMAC generated successfully', {
        dataSize: data.byteLength,
        signatureSize: signature.byteLength,
      });

      return signature;
    } catch (error) {
      logger.error('Failed to generate HMAC', error);
      throw new Error('HMAC generation failed');
    }
  }

  /**
   * Verify HMAC
   */
  public async verifyHMAC(data: ArrayBuffer, signature: ArrayBuffer, key: CryptoKey): Promise<boolean> {
    try {
      const isValid = await crypto.subtle.verify('HMAC', key, signature, data);
      
      logger.debug('HMAC verification completed', { isValid });
      return isValid;
    } catch (error) {
      logger.error('Failed to verify HMAC', error);
      return false;
    }
  }

  /**
   * Generate a key pair for asymmetric cryptography
   */
  public async generateKeyPair(algorithm: 'RSA-PSS' | 'ECDSA' = 'RSA-PSS'): Promise<CryptoKeyPair> {
    try {
      let keyGenParams: any;

      if (algorithm === 'RSA-PSS') {
        keyGenParams = {
          name: 'RSA-PSS',
          modulusLength: 2048,
          publicExponent: new Uint8Array([1, 0, 1]),
          hash: this.config.hashAlgorithm,
        };
      } else {
        keyGenParams = {
          name: 'ECDSA',
          namedCurve: 'P-256',
        };
      }

      const keyPair = await crypto.subtle.generateKey(
        keyGenParams,
        true,
        ['sign', 'verify']
      );

      logger.debug('Key pair generated successfully', { algorithm });
      return keyPair;
    } catch (error) {
      logger.error('Failed to generate key pair', error);
      throw new Error('Key pair generation failed');
    }
  }

  /**
   * Create a digital signature
   */
  public async sign(data: ArrayBuffer, privateKey: CryptoKey): Promise<DigitalSignature> {
    try {
      const signature = await crypto.subtle.sign(
        {
          name: 'RSA-PSS',
          saltLength: 32,
        },
        privateKey,
        data
      );

      const result: DigitalSignature = {
        signature,
        algorithm: 'RSA-PSS',
        publicKey: privateKey, // In real implementation, extract public key
        timestamp: Date.now(),
      };

      logger.debug('Digital signature created', {
        algorithm: 'RSA-PSS',
        dataSize: data.byteLength,
        signatureSize: signature.byteLength,
      });

      return result;
    } catch (error) {
      logger.error('Failed to create digital signature', error);
      throw new Error('Digital signature creation failed');
    }
  }

  /**
   * Verify a digital signature
   */
  public async verifySignature(
    data: ArrayBuffer,
    signature: DigitalSignature,
    publicKey: CryptoKey
  ): Promise<boolean> {
    try {
      const isValid = await crypto.subtle.verify(
        {
          name: signature.algorithm,
          saltLength: 32,
        },
        publicKey,
        signature.signature,
        data
      );

      logger.debug('Digital signature verification completed', { isValid });
      return isValid;
    } catch (error) {
      logger.error('Failed to verify digital signature', error);
      return false;
    }
  }

  /**
   * Secure random number generation
   */
  public generateSecureRandom(length: number): Uint8Array {
    return crypto.getRandomValues(new Uint8Array(length));
  }

  /**
   * Generate a secure random string
   */
  public generateSecureRandomString(length: number, charset: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string {
    const randomBytes = this.generateSecureRandom(length);
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += charset[randomBytes[i] % charset.length];
    }
    
    return result;
  }

  /**
   * Constant-time string comparison to prevent timing attacks
   */
  public constantTimeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }

    return result === 0;
  }

  /**
   * Convert ArrayBuffer to hex string
   */
  public arrayBufferToHex(buffer: ArrayBuffer): string {
    const byteArray = new Uint8Array(buffer);
    const hexCodes = [...byteArray].map(value => value.toString(16).padStart(2, '0'));
    return hexCodes.join('');
  }

  /**
   * Convert hex string to ArrayBuffer
   */
  public hexToArrayBuffer(hex: string): ArrayBuffer {
    const bytes = new Uint8Array(hex.length / 2);
    for (let i = 0; i < hex.length; i += 2) {
      bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
    }
    return bytes.buffer;
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<CryptoConfig>): void {
    this.config = { ...this.config, ...config };
    logger.info('Cryptographic service configuration updated', { config: this.config });
  }

  /**
   * Get current configuration
   */
  public getConfig(): CryptoConfig {
    return { ...this.config };
  }

  /**
   * Clear sensitive data from memory
   */
  public clearSensitiveData(): void {
    this.keyCache.clear();
    logger.info('Sensitive cryptographic data cleared from memory');
  }
}

// Export singleton instance
export const cryptographicService = CryptographicService.getInstance();
