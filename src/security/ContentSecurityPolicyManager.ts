import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';
import { configManager } from '../core/ConfigurationManager';

export interface CSPDirective {
  name: string;
  values: string[];
  reportOnly?: boolean;
}

export interface CSPPolicy {
  id: string;
  name: string;
  directives: CSPDirective[];
  reportUri?: string;
  reportTo?: string;
  upgradeInsecureRequests?: boolean;
  blockAllMixedContent?: boolean;
  requireSriFor?: string[];
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
}

export interface CSPViolation {
  id: string;
  documentUri: string;
  referrer: string;
  violatedDirective: string;
  effectiveDirective: string;
  originalPolicy: string;
  blockedUri: string;
  lineNumber?: number;
  columnNumber?: number;
  sourceFile?: string;
  statusCode: number;
  timestamp: number;
  userAgent: string;
  disposition: 'enforce' | 'report';
}

export interface CSPAnalysisResult {
  policyId: string;
  score: number; // 0-100
  issues: Array<{
    severity: 'low' | 'medium' | 'high' | 'critical';
    type: string;
    directive: string;
    description: string;
    recommendation: string;
  }>;
  recommendations: string[];
  compliance: {
    owasp: boolean;
    nist: boolean;
    gdpr: boolean;
  };
}

export class ContentSecurityPolicyManager extends EventEmitter {
  private static instance: ContentSecurityPolicyManager;
  private policies: Map<string, CSPPolicy> = new Map();
  private violations: Map<string, CSPViolation> = new Map();
  private activePolicyId: string | null = null;

  private constructor() {
    super();
    this.initializeCSPManager();
  }

  public static getInstance(): ContentSecurityPolicyManager {
    if (!ContentSecurityPolicyManager.instance) {
      ContentSecurityPolicyManager.instance = new ContentSecurityPolicyManager();
    }
    return ContentSecurityPolicyManager.instance;
  }

  private async initializeCSPManager(): Promise<void> {
    // Create default policies
    await this.createDefaultPolicies();

    // Setup violation reporting
    this.setupViolationReporting();

    // Load saved policies
    await this.loadPolicies();

    logger.info('CSP Manager initialized', {
      policyCount: this.policies.size,
      activePolicyId: this.activePolicyId,
    });
  }

  private async createDefaultPolicies(): Promise<void> {
    // Strict policy
    const strictPolicy: CSPPolicy = {
      id: 'strict',
      name: 'Strict Security Policy',
      directives: [
        { name: 'default-src', values: ["'self'"] },
        { name: 'script-src', values: ["'self'", "'unsafe-inline'"] },
        { name: 'style-src', values: ["'self'", "'unsafe-inline'"] },
        { name: 'img-src', values: ["'self'", 'data:', 'https:'] },
        { name: 'font-src', values: ["'self'", 'https:'] },
        { name: 'connect-src', values: ["'self'", 'https:'] },
        { name: 'media-src', values: ["'self'"] },
        { name: 'object-src', values: ["'none'"] },
        { name: 'child-src', values: ["'self'"] },
        { name: 'frame-ancestors', values: ["'none'"] },
        { name: 'base-uri', values: ["'self'"] },
        { name: 'form-action', values: ["'self'"] },
      ],
      upgradeInsecureRequests: true,
      blockAllMixedContent: true,
      requireSriFor: ['script', 'style'],
      isActive: false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    // Balanced policy
    const balancedPolicy: CSPPolicy = {
      id: 'balanced',
      name: 'Balanced Security Policy',
      directives: [
        { name: 'default-src', values: ["'self'"] },
        { name: 'script-src', values: ["'self'", "'unsafe-inline'", "'unsafe-eval'"] },
        { name: 'style-src', values: ["'self'", "'unsafe-inline'", 'https:'] },
        { name: 'img-src', values: ["'self'", 'data:', 'https:', 'http:'] },
        { name: 'font-src', values: ["'self'", 'https:', 'data:'] },
        { name: 'connect-src', values: ["'self'", 'https:', 'wss:'] },
        { name: 'media-src', values: ["'self'", 'https:'] },
        { name: 'object-src', values: ["'self'"] },
        { name: 'child-src', values: ["'self'", 'https:'] },
        { name: 'frame-ancestors', values: ["'self'"] },
        { name: 'base-uri', values: ["'self'"] },
        { name: 'form-action', values: ["'self'", 'https:'] },
      ],
      upgradeInsecureRequests: true,
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    // Permissive policy
    const permissivePolicy: CSPPolicy = {
      id: 'permissive',
      name: 'Permissive Policy',
      directives: [
        { name: 'default-src', values: ['*'] },
        { name: 'script-src', values: ['*', "'unsafe-inline'", "'unsafe-eval'"] },
        { name: 'style-src', values: ['*', "'unsafe-inline'"] },
        { name: 'img-src', values: ['*', 'data:', 'blob:'] },
        { name: 'font-src', values: ['*', 'data:'] },
        { name: 'connect-src', values: ['*'] },
        { name: 'media-src', values: ['*'] },
        { name: 'object-src', values: ['*'] },
        { name: 'child-src', values: ['*'] },
      ],
      isActive: false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    this.policies.set(strictPolicy.id, strictPolicy);
    this.policies.set(balancedPolicy.id, balancedPolicy);
    this.policies.set(permissivePolicy.id, permissivePolicy);

    this.activePolicyId = balancedPolicy.id;
  }

  public async createPolicy(policyData: {
    name: string;
    directives: CSPDirective[];
    reportUri?: string;
    reportTo?: string;
    upgradeInsecureRequests?: boolean;
    blockAllMixedContent?: boolean;
    requireSriFor?: string[];
  }): Promise<CSPPolicy> {
    const policyId = `policy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const policy: CSPPolicy = {
      id: policyId,
      name: policyData.name,
      directives: policyData.directives,
      reportUri: policyData.reportUri,
      reportTo: policyData.reportTo,
      upgradeInsecureRequests: policyData.upgradeInsecureRequests,
      blockAllMixedContent: policyData.blockAllMixedContent,
      requireSriFor: policyData.requireSriFor,
      isActive: false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    this.policies.set(policyId, policy);
    await this.savePolicies();

    this.emit('policy_created', policy);
    logger.info('CSP policy created', { policyId, name: policy.name });

    return policy;
  }

  public async updatePolicy(policyId: string, updates: Partial<CSPPolicy>): Promise<CSPPolicy> {
    const policy = this.policies.get(policyId);
    if (!policy) {
      throw new Error(`CSP policy ${policyId} not found`);
    }

    const updatedPolicy = {
      ...policy,
      ...updates,
      id: policyId, // Ensure ID doesn't change
      updatedAt: Date.now(),
    };

    this.policies.set(policyId, updatedPolicy);
    await this.savePolicies();

    this.emit('policy_updated', updatedPolicy);
    logger.info('CSP policy updated', { policyId, updates });

    return updatedPolicy;
  }

  public async deletePolicy(policyId: string): Promise<void> {
    const policy = this.policies.get(policyId);
    if (!policy) {
      throw new Error(`CSP policy ${policyId} not found`);
    }

    // Don't allow deletion of active policy
    if (this.activePolicyId === policyId) {
      throw new Error('Cannot delete active CSP policy');
    }

    this.policies.delete(policyId);
    await this.savePolicies();

    this.emit('policy_deleted', { policyId, policy });
    logger.info('CSP policy deleted', { policyId, name: policy.name });
  }

  public async activatePolicy(policyId: string): Promise<void> {
    const policy = this.policies.get(policyId);
    if (!policy) {
      throw new Error(`CSP policy ${policyId} not found`);
    }

    // Deactivate current policy
    if (this.activePolicyId) {
      const currentPolicy = this.policies.get(this.activePolicyId);
      if (currentPolicy) {
        currentPolicy.isActive = false;
      }
    }

    // Activate new policy
    policy.isActive = true;
    this.activePolicyId = policyId;

    // Apply the policy
    await this.applyPolicy(policy);

    await this.savePolicies();

    this.emit('policy_activated', policy);
    logger.info('CSP policy activated', { policyId, name: policy.name });
  }

  private async applyPolicy(policy: CSPPolicy): Promise<void> {
    const cspHeader = this.generateCSPHeader(policy);
    
    // In a real implementation, this would set the CSP header for all requests
    logger.debug('Applying CSP policy', { policyId: policy.id, header: cspHeader });

    // For browser context, we can set a meta tag
    if (typeof document !== 'undefined') {
      let metaTag = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      
      if (!metaTag) {
        metaTag = document.createElement('meta');
        metaTag.setAttribute('http-equiv', 'Content-Security-Policy');
        document.head.appendChild(metaTag);
      }
      
      metaTag.setAttribute('content', cspHeader);
    }
  }

  public generateCSPHeader(policy: CSPPolicy): string {
    const directives = policy.directives.map(directive => {
      return `${directive.name} ${directive.values.join(' ')}`;
    });

    if (policy.upgradeInsecureRequests) {
      directives.push('upgrade-insecure-requests');
    }

    if (policy.blockAllMixedContent) {
      directives.push('block-all-mixed-content');
    }

    if (policy.requireSriFor && policy.requireSriFor.length > 0) {
      directives.push(`require-sri-for ${policy.requireSriFor.join(' ')}`);
    }

    if (policy.reportUri) {
      directives.push(`report-uri ${policy.reportUri}`);
    }

    if (policy.reportTo) {
      directives.push(`report-to ${policy.reportTo}`);
    }

    return directives.join('; ');
  }

  public async analyzePolicy(policyId: string): Promise<CSPAnalysisResult> {
    const policy = this.policies.get(policyId);
    if (!policy) {
      throw new Error(`CSP policy ${policyId} not found`);
    }

    const issues = [];
    const recommendations = [];
    let score = 100;

    // Check for unsafe directives
    for (const directive of policy.directives) {
      if (directive.values.includes("'unsafe-inline'")) {
        issues.push({
          severity: 'high' as const,
          type: 'unsafe_inline',
          directive: directive.name,
          description: `Directive ${directive.name} allows unsafe-inline`,
          recommendation: 'Use nonces or hashes instead of unsafe-inline',
        });
        score -= 15;
      }

      if (directive.values.includes("'unsafe-eval'")) {
        issues.push({
          severity: 'high' as const,
          type: 'unsafe_eval',
          directive: directive.name,
          description: `Directive ${directive.name} allows unsafe-eval`,
          recommendation: 'Remove unsafe-eval and refactor code to avoid eval()',
        });
        score -= 20;
      }

      if (directive.values.includes('*')) {
        issues.push({
          severity: 'medium' as const,
          type: 'wildcard',
          directive: directive.name,
          description: `Directive ${directive.name} uses wildcard`,
          recommendation: 'Specify explicit sources instead of wildcard',
        });
        score -= 10;
      }
    }

    // Check for missing important directives
    const importantDirectives = ['default-src', 'script-src', 'object-src', 'base-uri'];
    for (const directiveName of importantDirectives) {
      if (!policy.directives.find(d => d.name === directiveName)) {
        issues.push({
          severity: 'medium' as const,
          type: 'missing_directive',
          directive: directiveName,
          description: `Missing important directive: ${directiveName}`,
          recommendation: `Add ${directiveName} directive for better security`,
        });
        score -= 5;
      }
    }

    // Check for object-src 'none'
    const objectSrc = policy.directives.find(d => d.name === 'object-src');
    if (!objectSrc || !objectSrc.values.includes("'none'")) {
      recommendations.push('Set object-src to none to prevent plugin execution');
    }

    // Check for frame-ancestors
    const frameAncestors = policy.directives.find(d => d.name === 'frame-ancestors');
    if (!frameAncestors) {
      recommendations.push('Add frame-ancestors directive to prevent clickjacking');
    }

    // Compliance checks
    const compliance = {
      owasp: score >= 80 && !issues.some(i => i.severity === 'critical'),
      nist: score >= 85 && policy.upgradeInsecureRequests === true,
      gdpr: score >= 75, // Basic compliance
    };

    return {
      policyId,
      score: Math.max(0, score),
      issues,
      recommendations,
      compliance,
    };
  }

  public recordViolation(violationData: {
    documentUri: string;
    referrer: string;
    violatedDirective: string;
    effectiveDirective: string;
    originalPolicy: string;
    blockedUri: string;
    lineNumber?: number;
    columnNumber?: number;
    sourceFile?: string;
    statusCode: number;
    userAgent: string;
    disposition: 'enforce' | 'report';
  }): CSPViolation {
    const violationId = `violation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const violation: CSPViolation = {
      id: violationId,
      ...violationData,
      timestamp: Date.now(),
    };

    this.violations.set(violationId, violation);

    // Limit violation storage
    if (this.violations.size > 10000) {
      const oldestViolations = Array.from(this.violations.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp)
        .slice(0, 1000);

      oldestViolations.forEach(([id]) => {
        this.violations.delete(id);
      });
    }

    this.emit('violation_recorded', violation);
    logger.warn('CSP violation recorded', {
      violationId,
      violatedDirective: violation.violatedDirective,
      blockedUri: violation.blockedUri,
    });

    return violation;
  }

  private setupViolationReporting(): void {
    // Setup CSP violation reporting endpoint
    if (typeof window !== 'undefined') {
      document.addEventListener('securitypolicyviolation', (event) => {
        this.recordViolation({
          documentUri: event.documentURI,
          referrer: event.referrer,
          violatedDirective: event.violatedDirective,
          effectiveDirective: event.effectiveDirective,
          originalPolicy: event.originalPolicy,
          blockedUri: event.blockedURI,
          lineNumber: event.lineNumber,
          columnNumber: event.columnNumber,
          sourceFile: event.sourceFile,
          statusCode: event.statusCode,
          userAgent: navigator.userAgent,
          disposition: event.disposition,
        });
      });
    }
  }

  public getViolationStats(): {
    total: number;
    byDirective: Record<string, number>;
    byDomain: Record<string, number>;
    recent: CSPViolation[];
  } {
    const violations = Array.from(this.violations.values());
    const byDirective: Record<string, number> = {};
    const byDomain: Record<string, number> = {};

    violations.forEach(violation => {
      // Count by directive
      byDirective[violation.violatedDirective] = (byDirective[violation.violatedDirective] || 0) + 1;

      // Count by domain
      try {
        const domain = new URL(violation.blockedUri).hostname;
        byDomain[domain] = (byDomain[domain] || 0) + 1;
      } catch {
        // Invalid URL, skip
      }
    });

    const recent = violations
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10);

    return {
      total: violations.length,
      byDirective,
      byDomain,
      recent,
    };
  }

  public async generatePolicyFromViolations(): Promise<CSPPolicy> {
    const violations = Array.from(this.violations.values());
    const allowedSources: Record<string, Set<string>> = {};

    // Analyze violations to suggest allowed sources
    violations.forEach(violation => {
      const directive = violation.violatedDirective;
      if (!allowedSources[directive]) {
        allowedSources[directive] = new Set();
      }

      try {
        const url = new URL(violation.blockedUri);
        allowedSources[directive].add(url.origin);
      } catch {
        // Invalid URL, skip
      }
    });

    // Generate directives
    const directives: CSPDirective[] = [];
    
    Object.entries(allowedSources).forEach(([directiveName, sources]) => {
      directives.push({
        name: directiveName,
        values: ["'self'", ...Array.from(sources)],
      });
    });

    // Add default directives if not present
    const defaultDirectives = [
      { name: 'default-src', values: ["'self'"] },
      { name: 'object-src', values: ["'none'"] },
      { name: 'base-uri', values: ["'self'"] },
    ];

    defaultDirectives.forEach(defaultDir => {
      if (!directives.find(d => d.name === defaultDir.name)) {
        directives.push(defaultDir);
      }
    });

    return this.createPolicy({
      name: `Generated Policy ${new Date().toISOString()}`,
      directives,
      upgradeInsecureRequests: true,
    });
  }

  private async loadPolicies(): Promise<void> {
    try {
      // In a real implementation, load from persistent storage
      logger.debug('CSP policies loaded from storage');
    } catch (error) {
      logger.warn('Failed to load CSP policies', { error });
    }
  }

  private async savePolicies(): Promise<void> {
    try {
      // In a real implementation, save to persistent storage
      logger.debug('CSP policies saved to storage');
    } catch (error) {
      logger.warn('Failed to save CSP policies', { error });
    }
  }

  // Getters
  public getPolicies(): CSPPolicy[] {
    return Array.from(this.policies.values());
  }

  public getPolicy(policyId: string): CSPPolicy | null {
    return this.policies.get(policyId) || null;
  }

  public getActivePolicy(): CSPPolicy | null {
    return this.activePolicyId ? this.policies.get(this.activePolicyId) || null : null;
  }

  public getViolations(): CSPViolation[] {
    return Array.from(this.violations.values());
  }

  public clearViolations(): void {
    this.violations.clear();
    this.emit('violations_cleared');
    logger.info('CSP violations cleared');
  }
}

// Export singleton instance
export const cspManager = ContentSecurityPolicyManager.getInstance();
