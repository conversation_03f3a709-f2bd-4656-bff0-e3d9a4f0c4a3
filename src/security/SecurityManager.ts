import { app, BrowserWindow, session, net } from 'electron';
import { EventEmitter } from 'events';
import * as crypto from 'crypto';
import { promises as fs } from 'fs';
import path from 'path';
import { APP_CONFIG } from '../config/app.config';
import { promisify } from 'util';
import { logger } from '../utils/logger';
import { trackError } from '../utils/errorTracking';
import { performanceMonitor } from '../utils/performance/PerformanceMonitor';

type PermissionType = 'notifications' | 'media' | 'geolocation' | 'midi' | 'midi-sysex' | 'pointer-lock' | 'fullscreen' | 'open-external' | 'cookie';

export interface SecurityConfig {
  encryptionKey: string;
  saltRounds: number;
  sessionTimeout: number;
  maxLoginAttempts: number;
  blockDuration: number;
  passwordMinLength: number;
  passwordRequirements: {
    uppercase: boolean;
    lowercase: boolean;
    numbers: boolean;
    special: boolean;
  };
  csp: {
    enabled: boolean;
    reportOnly: boolean;
    directives: {
      defaultSrc: string[];
      scriptSrc: string[];
      styleSrc: string[];
      imgSrc: string[];
      connectSrc: string[];
      fontSrc: string[];
      objectSrc: string[];
      mediaSrc: string[];
      frameSrc: string[];
      sandbox: string[];
      reportUri: string;
    };
  };
  permissions: {
    clipboard: boolean;
    notifications: boolean;
    camera: boolean;
    microphone: boolean;
    geolocation: boolean;
    midi: boolean;
    usb: boolean;
    serial: boolean;
    bluetooth: boolean;
    sensors: boolean;
    fullscreen: boolean;
    pointerLock: boolean;
    openExternal: boolean;
  };
  features: {
    sandbox: boolean;
    contextIsolation: boolean;
    nodeIntegration: boolean;
    webSecurity: boolean;
    allowRunningInsecureContent: boolean;
    experimentalFeatures: boolean;
    webgl: boolean;
    plugins: boolean;
    spellcheck: boolean;
  };
  privacy: {
    doNotTrack: boolean;
    thirdPartyCookies: boolean;
    trackingProtection: boolean;
    fingerprintingProtection: boolean;
    cryptominingProtection: boolean;
    socialTrackingProtection: boolean;
    webRTC: boolean;
    canvasFingerprinting: boolean;
    audioFingerprinting: boolean;
    batteryStatus: boolean;
    deviceMemory: boolean;
    hardwareConcurrency: boolean;
  };
  network: {
    proxy: {
      enabled: boolean;
      mode: 'system' | 'direct' | 'pac_script' | 'fixed_servers';
      pacScript?: string;
      proxyRules?: string;
    };
    dns: {
      secure: boolean;
      customServers: string[];
    };
    certificates: {
      strict: boolean;
      custom: string[];
      verify: boolean;
      customCA: string[];
    };
  };
}

export interface SecurityEvent {
  type: string;
  timestamp: number;
  userId?: string;
  ipAddress: string;
  details: Record<string, any>;
}

export class SecurityManager extends EventEmitter {
  private static instance: SecurityManager;
  private config: SecurityConfig;
  private securityChecks: Map<string, boolean>;
  private blockedDomains: Set<string>;
  private trustedDomains: Set<string>;
  private sessions: Map<string, any>;
  private loginAttempts: Map<string, number>;
  private activeSessions: Map<string, Date>;
  private blockedIPs: Map<string, number>;
  private securityLog: SecurityEvent[];
  private readonly defaultConfig: SecurityConfig = {
    encryptionKey: process.env.ENCRYPTION_KEY || crypto.randomBytes(32).toString('hex'),
    saltRounds: 10,
    sessionTimeout: 3600000, // 1 hour
    maxLoginAttempts: 5,
    blockDuration: 900000, // 15 minutes
    passwordMinLength: 8,
    passwordRequirements: {
      uppercase: true,
      lowercase: true,
      numbers: true,
      special: true,
    },
    csp: {
      enabled: true,
      reportOnly: false,
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'", 'https:'],
        fontSrc: ["'self'", 'data:', 'https:'],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'", 'https:'],
        frameSrc: ["'self'"],
        sandbox: ['allow-forms', 'allow-same-origin', 'allow-scripts'],
        reportUri: '/csp-report',
      },
    },
    permissions: {
      clipboard: true,
      notifications: true,
      camera: false,
      microphone: false,
      geolocation: false,
      midi: false,
      usb: false,
      serial: false,
      bluetooth: false,
      sensors: false,
      fullscreen: true,
      pointerLock: true,
      openExternal: false,
    },
    features: {
      sandbox: true,
      contextIsolation: true,
      nodeIntegration: false,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      webgl: true,
      plugins: false,
      spellcheck: true,
    },
    privacy: {
      doNotTrack: true,
      thirdPartyCookies: false,
      trackingProtection: true,
      fingerprintingProtection: true,
      cryptominingProtection: true,
      socialTrackingProtection: true,
      webRTC: false,
      canvasFingerprinting: true,
      audioFingerprinting: true,
      batteryStatus: false,
      deviceMemory: false,
      hardwareConcurrency: false,
    },
    network: {
      proxy: {
        enabled: false,
        mode: 'system',
      },
      dns: {
        secure: true,
        customServers: [],
      },
      certificates: {
        strict: true,
        custom: [],
        verify: true,
        customCA: [],
      },
    },
  };

  private constructor() {
    super();
    this.config = { ...this.defaultConfig };
    this.securityChecks = new Map();
    this.blockedDomains = new Set();
    this.trustedDomains = new Set();
    this.sessions = new Map();
    this.loginAttempts = new Map();
    this.activeSessions = new Map();
    this.blockedIPs = new Map();
    this.securityLog = [];
    this.setupSecurityFeatures();
  }

  public static getInstance(): SecurityManager {
    if (!SecurityManager.instance) {
      SecurityManager.instance = new SecurityManager();
    }
    return SecurityManager.instance;
  }

  private setupSecurityFeatures(): void {
    try {
      // Set up CSP
      this.setupCSP();

      // Set up permissions
      this.setupPermissions();

      // Set up privacy features
      this.setupPrivacyFeatures();

      // Set up network security
      this.setupNetworkSecurity();

      // Set up certificate verification
      this.setupCertificateVerification();

      // Set up secure headers
      this.setupSecureHeaders();

      // Monitor security events
      this.monitorSecurityEvents();

      await this.loadBlockedDomains();
      await this.loadTrustedDomains();
      this.setupSecurityEvents();
    } catch (error) {
      trackError(error as Error, {
        context: { component: 'SecurityManager' },
      });
    }
  }

  private setupCSP(): void {
    session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      const cspHeader = this.generateCSPHeader();
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [this.config.csp.reportOnly ? `report-only ${cspHeader}` : cspHeader],
        },
      });
    });
  }

  private setupPermissions(): void {
    session.defaultSession.setPermissionRequestHandler(
      (webContents, permission, callback) => {
        const url = webContents.getURL();
        const permissionGranted = this.config.permissions[permission as keyof typeof this.config.permissions];
        
        if (permissionGranted) {
          logger.info(`Permission granted: ${permission} for ${url}`);
          callback(true);
        } else {
          logger.warn(`Permission denied: ${permission} for ${url}`);
          callback(false);
        }
      }
    );
  }

  private setupPrivacyFeatures(): void {
    // Implement privacy features
    if (this.config.privacy.doNotTrack) {
      session.defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
        details.requestHeaders['DNT'] = '1';
        callback({ requestHeaders: details.requestHeaders });
      });
    }

    if (this.config.privacy.trackingProtection) {
      // Implement tracking protection
    }

    if (this.config.privacy.fingerprintingProtection) {
      // Implement fingerprinting protection
    }
  }

  private setupNetworkSecurity(): void {
    if (this.config.network.proxy.enabled) {
      // Configure proxy settings
    }

    if (this.config.network.dns.secure) {
      // Configure secure DNS
    }
  }

  private setupCertificateVerification(): void {
    app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
      event.preventDefault();
      const errorMessage = `Certificate error for ${url}: ${error}`;
      logger.error('Certificate error', { 
        message: errorMessage,
        certificate 
      });
      callback(false);
    });
  }

  private setupSecureHeaders(): void {
    session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'X-Content-Type-Options': ['nosniff'],
          'X-Frame-Options': ['SAMEORIGIN'],
          'X-XSS-Protection': ['1; mode=block'],
          'Strict-Transport-Security': ['max-age=31536000; includeSubDomains'],
          'Referrer-Policy': ['strict-origin-when-cross-origin'],
          'Permissions-Policy': ['geolocation=(), microphone=(), camera=()'],
        },
      });
    });
  }

  private monitorSecurityEvents(): void {
    // Monitor for suspicious network activity
    session.defaultSession.webRequest.onBeforeRequest(
      { urls: ['*://*/*'] },
      (details, callback) => {
        const url = new URL(details.url);
        if (this.isSuspiciousURL(url)) {
          logger.warn('Suspicious URL detected', { url: details.url });
          callback({ cancel: true });
        } else {
          callback({});
        }
      }
    );

    // Monitor for suspicious headers
    session.defaultSession.webRequest.onHeadersReceived(
      { urls: ['*://*/*'] },
      (details, callback) => {
        if (details.responseHeaders && this.hasSuspiciousHeaders(details.responseHeaders)) {
          logger.warn('Suspicious headers detected', {
            url: details.url,
            headers: details.responseHeaders,
          });
        }
        callback({ responseHeaders: details.responseHeaders });
      }
    );
  }

  private isSuspiciousURL(url: URL): boolean {
    const suspiciousPatterns = [
      /\.exe$/i,
      /\.dll$/i,
      /\.bat$/i,
      /\.cmd$/i,
      /\.vbs$/i,
      /\.js$/i,
      /\.jar$/i,
      /\.msi$/i,
      /\.reg$/i,
      /\.scr$/i,
      /\.pif$/i,
      /\.lnk$/i,
    ];

    return suspiciousPatterns.some(pattern => pattern.test(url.pathname));
  }

  private hasSuspiciousHeaders(headers: Record<string, string[]>): boolean {
    const suspiciousHeaders = [
      'x-powered-by',
      'server',
      'x-aspnet-version',
      'x-aspnetmvc-version',
    ];

    return suspiciousHeaders.some(header => headers[header]);
  }

  private generateCSPHeader(): string {
    const { csp } = this.config;
    return Object.entries(csp.directives)
      .map(([key, value]) => {
        if (key === 'reportUri') return `report-uri ${value}`;
        return `${key.replace(/([A-Z])/g, '-$1').toLowerCase()} ${value.join(' ')}`;
      })
      .join('; ');
  }

  private setupSecurityEvents(): void {
    app.on('web-contents-created', (event, contents) => {
      contents.on('will-navigate', (event, navigationUrl) => {
        this.validateNavigation(event, navigationUrl);
      });

      contents.on('will-redirect', (event, redirectUrl) => {
        this.validateNavigation(event, redirectUrl);
      });
    });
  }

  private validateNavigation(event: Electron.Event, url: string): void {
    try {
      const parsedUrl = new URL(url);
      if (this.blockedDomains.has(parsedUrl.hostname)) {
        event.preventDefault();
        this.emit('navigation-blocked', { url, reason: 'domain-blocked' });
      }
    } catch (error) {
      event.preventDefault();
      this.emit('navigation-blocked', { url, reason: 'invalid-url' });
    }
  }

  public async addBlockedDomain(domain: string): Promise<void> {
    this.blockedDomains.add(domain);
    await this.saveBlockedDomains();
  }

  public async removeBlockedDomain(domain: string): Promise<void> {
    this.blockedDomains.delete(domain);
    await this.saveBlockedDomains();
  }

  public async addTrustedDomain(domain: string): Promise<void> {
    this.trustedDomains.add(domain);
    await this.saveTrustedDomains();
  }

  public async removeTrustedDomain(domain: string): Promise<void> {
    this.trustedDomains.delete(domain);
    await this.saveTrustedDomains();
  }

  private isTrustedDomain(domain: string): boolean {
    return this.trustedDomains.has(domain);
  }

  private async loadBlockedDomains(): Promise<void> {
    try {
      const data = await fs.readFile(
        path.join(app.getPath('userData'), 'blocked-domains.json'),
        'utf-8'
      );
      this.blockedDomains = new Set(JSON.parse(data));
    } catch (error) {
      this.blockedDomains = new Set();
    }
  }

  private async saveBlockedDomains(): Promise<void> {
    await fs.writeFile(
      path.join(app.getPath('userData'), 'blocked-domains.json'),
      JSON.stringify(Array.from(this.blockedDomains))
    );
  }

  private async loadTrustedDomains(): Promise<void> {
    try {
      const data = await fs.readFile(
        path.join(app.getPath('userData'), 'trusted-domains.json'),
        'utf-8'
      );
      this.trustedDomains = new Set(JSON.parse(data));
    } catch (error) {
      this.trustedDomains = new Set();
    }
  }

  private async saveTrustedDomains(): Promise<void> {
    await fs.writeFile(
      path.join(app.getPath('userData'), 'trusted-domains.json'),
      JSON.stringify(Array.from(this.trustedDomains))
    );
  }

  public getSecurityConfig(): SecurityConfig {
    return { ...this.config };
  }

  public updateSecurityConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.setupSecurityFeatures();
  }

  public async validateSecurity(): Promise<Map<string, boolean>> {
    const checks = new Map<string, boolean>();
    
    checks.set('csp-enabled', this.config.csp.enabled);
    checks.set('sandbox-enabled', this.config.features.sandbox);
    checks.set('context-isolation', this.config.features.contextIsolation);
    checks.set('node-integration-disabled', !this.config.features.nodeIntegration);
    checks.set('remote-module-disabled', !this.config.features.nodeIntegration);
    checks.set('web-security-enabled', this.config.features.webSecurity);
    
    this.securityChecks = checks;
    this.emit('security-validation-complete', checks);
    return checks;
  }

  public getSecurityStatus(): Map<string, boolean> {
    return new Map(this.securityChecks);
  }

  // Public methods for runtime security management
  public async blockDomain(domain: string): Promise<void> {
    const defaultSession = session.defaultSession;
    await defaultSession.webRequest.onBeforeRequest({
      urls: [`*://${domain}/*`],
    }, (details, callback) => {
      callback({ cancel: true });
    });
  }

  public async allowDomain(domain: string): Promise<void> {
    const defaultSession = session.defaultSession;
    await defaultSession.webRequest.onBeforeRequest({
      urls: [`*://${domain}/*`],
    }, (details, callback) => {
      callback({ cancel: false });
    });
  }

  public async clearBrowserData(): Promise<void> {
    const defaultSession = session.defaultSession;
    await defaultSession.clearStorageData({
      storages: ['cookies', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'serviceworkers', 'cachestorage'],
    });
  }

  public async setSecureCookies(secure: boolean): Promise<void> {
    const defaultSession = session.defaultSession;
    await defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
      if (permission === 'cookie') {
        callback(secure);
      } else {
        callback(true);
      }
    });
  }

  // Password Management
  public async hashPassword(password: string): Promise<string> {
    const salt = await crypto.randomBytes(16).toString('hex');
    const hash = await crypto.pbkdf2Sync(
      password,
      salt,
      this.config.saltRounds,
      64,
      'sha512'
    ).toString('hex');
    return `${salt}:${hash}`;
  }

  public async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    const [salt, hash] = hashedPassword.split(':');
    const verifyHash = await crypto.pbkdf2Sync(
      password,
      salt,
      this.config.saltRounds,
      64,
      'sha512'
    ).toString('hex');
    return hash === verifyHash;
  }

  public validatePassword(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < this.config.passwordMinLength) {
      errors.push(`Password must be at least ${this.config.passwordMinLength} characters long`);
    }

    if (this.config.passwordRequirements.uppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (this.config.passwordRequirements.lowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (this.config.passwordRequirements.numbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (this.config.passwordRequirements.special && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  // Session Management
  public createSession(userId: string, ipAddress: string): string {
    const sessionId = crypto.randomBytes(32).toString('hex');
    const session = {
      userId,
      ipAddress,
      createdAt: Date.now(),
      lastActivity: Date.now(),
    };

    this.sessions.set(sessionId, session);
    this.logSecurityEvent('session_created', userId, ipAddress, { sessionId });

    return sessionId;
  }

  public validateSession(sessionId: string, ipAddress: string): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    if (session.ipAddress !== ipAddress) {
      this.logSecurityEvent('session_invalid_ip', session.userId, ipAddress, { sessionId });
      return false;
    }

    if (Date.now() - session.lastActivity > this.config.sessionTimeout) {
      this.sessions.delete(sessionId);
      this.logSecurityEvent('session_expired', session.userId, ipAddress, { sessionId });
      return false;
    }

    session.lastActivity = Date.now();
    return true;
  }

  public endSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      this.logSecurityEvent('session_ended', session.userId, session.ipAddress, { sessionId });
      this.sessions.delete(sessionId);
    }
  }

  // Login Security
  public async handleLoginAttempt(userId: string, ipAddress: string): Promise<boolean> {
    if (this.isIPBlocked(ipAddress)) {
      this.logSecurityEvent('login_blocked', userId, ipAddress, { reason: 'IP blocked' });
      return false;
    }

    const attempts = this.loginAttempts.get(ipAddress) || 0;
    this.loginAttempts.set(ipAddress, attempts + 1);

    if (attempts + 1 >= this.config.maxLoginAttempts) {
      this.blockIP(ipAddress);
      this.logSecurityEvent('ip_blocked', userId, ipAddress, { attempts: attempts + 1 });
      return false;
    }

    return true;
  }

  public resetLoginAttempts(ipAddress: string): void {
    this.loginAttempts.delete(ipAddress);
  }

  private isIPBlocked(ipAddress: string): boolean {
    const blockTime = this.blockedIPs.get(ipAddress);
    if (!blockTime) return false;

    if (Date.now() - blockTime > this.config.blockDuration) {
      this.blockedIPs.delete(ipAddress);
      return false;
    }

    return true;
  }

  private blockIP(ipAddress: string): void {
    this.blockedIPs.set(ipAddress, Date.now());
  }

  // Data Encryption
  public encryptData(data: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-gcm', Buffer.from(this.config.encryptionKey, 'hex'), iv);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return `${iv.toString('hex')}:${encrypted}:${authTag.toString('hex')}`;
  }

  public decryptData(encryptedData: string): string {
    const [ivHex, encrypted, authTagHex] = encryptedData.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');
    
    const decipher = crypto.createDecipheriv('aes-256-gcm', Buffer.from(this.config.encryptionKey, 'hex'), iv);
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  // Security Logging
  private logSecurityEvent(
    type: string,
    userId: string | undefined,
    ipAddress: string,
    details: Record<string, any>
  ): void {
    const event: SecurityEvent = {
      type,
      timestamp: Date.now(),
      userId,
      ipAddress,
      details,
    };

    this.securityLog.push(event);
    this.emit('securityEvent', event);

    // Keep log size manageable
    if (this.securityLog.length > 1000) {
      this.securityLog = this.securityLog.slice(-1000);
    }
  }

  public getSecurityLog(): SecurityEvent[] {
    return [...this.securityLog];
  }

  // Security Checks
  public async performSecurityCheck(): Promise<{
    status: 'ok' | 'warning' | 'error';
    issues: string[];
  }> {
    const issues: string[] = [];

    // Check encryption key
    if (this.config.encryptionKey === process.env.ENCRYPTION_KEY) {
      issues.push('Using default encryption key');
    }

    // Check active sessions
    const now = Date.now();
    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastActivity > this.config.sessionTimeout) {
        issues.push(`Stale session found: ${sessionId}`);
      }
    }

    // Check blocked IPs
    for (const [ip, blockTime] of this.blockedIPs.entries()) {
      if (now - blockTime > this.config.blockDuration) {
        issues.push(`Expired IP block not cleared: ${ip}`);
      }
    }

    return {
      status: issues.length === 0 ? 'ok' : issues.length < 3 ? 'warning' : 'error',
      issues,
    };
  }

  // Configuration
  public updateConfig(config: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...config };
    this.setupSecurityFeatures();
  }

  public getConfig(): SecurityConfig {
    return { ...this.config };
  }

  // Enhanced Security Features
  public enableContentSecurityPolicy(window: BrowserWindow): void {
    const cspPolicy = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self' https:",
      "media-src 'self'",
      "object-src 'none'",
      "child-src 'self'",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; ');

    window.webContents.session.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [cspPolicy],
          'X-Content-Type-Options': ['nosniff'],
          'X-Frame-Options': ['DENY'],
          'X-XSS-Protection': ['1; mode=block'],
          'Strict-Transport-Security': ['max-age=31536000; includeSubDomains'],
          'Referrer-Policy': ['strict-origin-when-cross-origin']
        }
      });
    });

    logger.info('Content Security Policy enabled', { policy: cspPolicy });
  }

  public async scanForVulnerabilities(): Promise<{
    vulnerabilities: Array<{
      type: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      description: string;
      recommendation: string;
    }>;
    score: number;
  }> {
    const vulnerabilities = [];
    let score = 100;

    // Check for weak encryption
    if (this.config.encryptionKey.length < 32) {
      vulnerabilities.push({
        type: 'weak_encryption',
        severity: 'high' as const,
        description: 'Encryption key is too short',
        recommendation: 'Use at least 32 character encryption key'
      });
      score -= 20;
    }

    // Check for insecure session timeout
    if (this.config.sessionTimeout > 24 * 60 * 60 * 1000) {
      vulnerabilities.push({
        type: 'long_session_timeout',
        severity: 'medium' as const,
        description: 'Session timeout is too long',
        recommendation: 'Reduce session timeout to maximum 24 hours'
      });
      score -= 10;
    }

    // Check for weak password policy
    if (this.config.passwordMinLength < 8) {
      vulnerabilities.push({
        type: 'weak_password_policy',
        severity: 'medium' as const,
        description: 'Minimum password length is too short',
        recommendation: 'Require at least 8 characters for passwords'
      });
      score -= 10;
    }

    logger.info('Security vulnerability scan completed', {
      vulnerabilityCount: vulnerabilities.length,
      score
    });

    return { vulnerabilities, score };
  }

  public enableRealTimeProtection(): void {
    // Monitor for suspicious activities
    setInterval(() => {
      this.detectAnomalousActivity();
    }, 30000); // Check every 30 seconds

    logger.info('Real-time security protection enabled');
  }

  private detectAnomalousActivity(): void {
    const now = Date.now();
    const recentAttempts = Array.from(this.loginAttempts.values())
      .filter(attempts => attempts.some(attempt => now - attempt < 60000)); // Last minute

    if (recentAttempts.length > 10) {
      this.logSecurityEvent('anomalous_activity', 'high', {
        type: 'multiple_login_attempts',
        count: recentAttempts.length,
        timeWindow: '1 minute'
      });
    }
  }

  public generateSecurityReport(): {
    summary: {
      totalSessions: number;
      activeSessions: number;
      blockedIPs: number;
      securityEvents: number;
      lastSecurityScan?: Date;
    };
    recommendations: string[];
    recentEvents: Array<{
      type: string;
      severity: string;
      timestamp: Date;
      details: any;
    }>;
  } {
    const now = Date.now();
    const activeSessions = Array.from(this.sessions.values())
      .filter(session => now - session.lastActivity < this.config.sessionTimeout).length;

    const recentEvents = this.securityLog
      .slice(-10)
      .map(event => ({
        type: event.type,
        severity: event.severity,
        timestamp: new Date(event.timestamp),
        details: event.details
      }));

    const recommendations = [];
    if (this.config.encryptionKey.length < 32) {
      recommendations.push('Strengthen encryption key length');
    }
    if (this.config.sessionTimeout > 24 * 60 * 60 * 1000) {
      recommendations.push('Reduce session timeout duration');
    }
    if (this.blockedIPs.size > 100) {
      recommendations.push('Review and clean up blocked IP list');
    }

    return {
      summary: {
        totalSessions: this.sessions.size,
        activeSessions,
        blockedIPs: this.blockedIPs.size,
        securityEvents: this.securityLog.length,
      },
      recommendations,
      recentEvents
    };
  }

  // Cleanup
  public cleanup(): void {
    this.sessions.clear();
    this.loginAttempts.clear();
    this.blockedIPs.clear();
    this.securityLog = [];
    this.removeAllListeners();
    logger.info('Security manager cleanup completed');
  }
} 