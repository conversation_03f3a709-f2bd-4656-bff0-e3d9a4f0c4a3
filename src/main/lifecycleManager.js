const { app } = require('electron');
const path = require('path');
// const errorHandler = require('./utils/errorHandling'); // Now injected

/**
 * @class LifecycleManager
 * @description Manages the application's lifecycle events, such as quitting, window closing, and activation.
 * Ensures proper cleanup and state management during these events.
 */
class LifecycleManager {
    static dependencies = ['AppInitializer', 'ErrorHandler'];

    /**
     * Creates an instance of LifecycleManager.
     * @param {AppInitializer} appInitializer - The application initializer instance, used to interact with main window and other app-wide functionalities.
     */
    constructor(appInitializer, errorHandler) {
        /**
         * @property {AppInitializer} appInitializer - Reference to the AppInitializer instance.
         */
        this.appInitializer = appInitializer;
        this.errorHandler = errorHandler;
        /**
         * @property {boolean} isQuitting - A flag indicating whether the application is in the process of quitting.
         */
        this.isQuitting = false;

        // Bind event handlers to the current instance to maintain `this` context
        this.handleBeforeQuit = this.handleBeforeQuit.bind(this);
        this.handleWindowAllClosed = this.handleWindowAllClosed.bind(this);
        this.handleActivate = this.handleActivate.bind(this);

        this.registerListeners();
    }

    /**
     * Initializes the lifecycle manager by registering event listeners for Electron app events.
     * This method should be called once during application startup.
     * @returns {void}
     */
    initialize() {
        // For macOS: re-create window when the app is activated if no windows are open
        app.on('activate', this.errorHandler.wrapSync(this.handleActivate, 'lifecycle:handleActivate'));

        // Quit the app when all windows are closed, unless on macOS where the app remains active
        app.on('window-all-closed', this.errorHandler.wrapSync(this.handleWindowAllClosed, 'lifecycle:handleWindowAllClosed'));

        // Handle 'before-quit' event to perform cleanup and save session data
        app.on('before-quit', this.errorHandler.wrapSync(this.handleBeforeQuit, 'lifecycle:handleBeforeQuit'));
    }

    /**
     * Handles the 'before-quit' event, setting a flag and clearing all application data.
     * This method is asynchronous to allow for completion of cleanup tasks.
     * @async
     * @returns {Promise<void>}
     */
    async handleBeforeQuit() {
        this.isQuitting = true;
        // Call clearAllData in AppInitializer for cleanup
        try {
            if (this.appInitializer && typeof this.appInitializer.clearAllData === 'function') {
                await this.appInitializer.clearAllData();
            }
        } catch (error) {
            global.logger.error('Error during application cleanup on quit:', error);
        }
        global.logger.info('Application is quitting. Performing cleanup...');
    }

    /**
     * Handles the 'window-all-closed' event.
     * On macOS, the application typically remains active even after all windows are closed.
     * On other platforms, the application quits when all windows are closed.
     * @returns {void}
     */
    handleWindowAllClosed() {
        if (process.platform !== 'darwin') {
            app.quit();
        }
    }

    /**
     * Handles the 'activate' event, typically on macOS when the dock icon is clicked.
     * If no windows are open, it re-creates the main window.
     * @returns {void}
     */
    handleActivate() {
        if (this.appInitializer.getMainWindow() === null) {
            // Re-create the main window, ensuring all necessary paths are passed.
            // These paths should ideally be managed and provided by AppInitializer or a central configuration.
            // For now, assuming AppInitializer can provide these or they are globally accessible.
            this.appInitializer.createWindow(
                path.join(__dirname, '..', 'preload.js'),
                path.join(__dirname, '..', '..', 'renderer', 'index.html')
            );
        }
    }

    /**
     * Registers all necessary event listeners for the application lifecycle.
     * This method is called during the constructor to set up event handling.
     * @private
     * @returns {void}
     */
    registerListeners() {
        // Listeners are registered in the initialize method, this method can be removed or repurposed
        // if there are other listeners to register that are not part of the main app lifecycle.
        global.logger.debug('LifecycleManager listeners registered.');
    }
}

module.exports = LifecycleManager;