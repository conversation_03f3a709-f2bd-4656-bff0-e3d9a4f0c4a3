const { app } = require('electron');
const AppInitializer = require('./appInitializer');
const os = require('os');
const LifecycleManager = require('./lifecycleManager');
const DependencyInjector = require('./core/dependencyInjector');
const ErrorHandler = require('./utils/errorHandling');
const { AdBlockerManager } = require('./adblocker/adBlockerManager');
const { ConfigManager } = require('./config/configManager');
const { ExtensionLoader } = require('../extensions/extension-loader');

/**
 * @file main.js
 * @description This is the main entry point for the Electron application.
 * It handles the application's ready event, initializes the dependency injector,
 * and orchestrates the startup of core application components via AppInitializer.
 */

/**
 * @type {DependencyInjector}
 * @description The central dependency injection container for the application.
 */
const injector = new DependencyInjector();

/**
 * Event handler for the Electron 'ready' event.
 * This function is executed once Electron has finished initializing.
 * It registers core application components with the dependency injector and starts the application initialization process.
 * @async
 * @returns {Promise<void>}
 */
app.on('ready', async () => {
    // Register AppInitializer and LifecycleManager as dependencies.
    // AppInitializer is registered as a singleton by default.
    injector.register('AppInitializer', AppInitializer);
    injector.register('LifecycleManager', LifecycleManager);
    injector.register('ErrorHandler', new ErrorHandler(global.logger));
    injector.register('WindowManager', WindowManager);
    injector.register('AdBlockerManager', AdBlockerManager);
    injector.register('ConfigManager', ConfigManager, {
    name: 'a11-config',
    defaults: {
        theme: 'dark',
        homepage: 'https://www.google.com',
        privacy: {
            doNotTrack: true,
            clearOnExit: false
        },
        performance: {
            hardwareAcceleration: true
        },
        updates: {
            autoUpdate: true,
            checkInterval: 24 // hours
        }
    }
});
    injector.register('ExtensionLoader', ExtensionLoader, true);

    /**
     * @type {AppInitializer}
     * @description The instance responsible for initializing all major application modules.
     */
    const appInitializer = injector.resolve('AppInitializer');
    /**
     * @type {LifecycleManager}
     * @description The instance responsible for managing application lifecycle events.
     */
    const lifecycleManager = injector.resolve('LifecycleManager');

    global.logger.info(`Application starting on ${os.platform()} (${os.release()}).`);
    global.logger.info('App is ready. Initializing adblocker and creating window...');
    // Initialize the application components.
    await appInitializer.initialize();
});

// The 'before-quit' event handling is now managed by LifecycleManager
// and SessionManager, so no additional logic is required here.

// This module simply launches the Electron application.
// All initialization logic has been moved to AppInitializer;