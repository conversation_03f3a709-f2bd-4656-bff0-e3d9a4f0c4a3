import { app, <PERSON><PERSON>erWindow } from 'electron';
import { logger } from './logging';
import { trackError } from './error-tracking';

interface PerformanceMetric {
  name: string;
  value: number;
  tags?: Record<string, string>;
  timestamp: number;
}

interface PerformanceThreshold {
  name: string;
  threshold: number;
  severity: 'warning' | 'error' | 'critical';
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private thresholds: PerformanceThreshold[] = [];
  private readonly maxMetrics: number = 1000;
  private monitoringInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.setupDefaultThresholds();
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private setupDefaultThresholds(): void {
    this.thresholds = [
      {
        name: 'memory_usage',
        threshold: 1024 * 1024 * 1024, // 1GB
        severity: 'warning',
      },
      {
        name: 'cpu_usage',
        threshold: 80, // 80%
        severity: 'warning',
      },
      {
        name: 'response_time',
        threshold: 1000, // 1 second
        severity: 'error',
      },
    ];
  }

  public startMonitoring(interval: number = 30000): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, interval);
  }

  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  private collectMetrics(): void {
    try {
      // Memory metrics
      const memoryUsage = process.memoryUsage();
      this.recordMetric('memory_usage', memoryUsage.heapUsed, {
        type: 'heap_used',
      });
      this.recordMetric('memory_usage', memoryUsage.heapTotal, {
        type: 'heap_total',
      });
      this.recordMetric('memory_usage', memoryUsage.rss, {
        type: 'rss',
      });

      // CPU metrics
      const cpuUsage = process.cpuUsage();
      this.recordMetric('cpu_usage', (cpuUsage.user + cpuUsage.system) / 1000, {
        type: 'total',
      });

      // Process metrics
      this.recordMetric('process_uptime', process.uptime());
      this.recordMetric('process_pid', process.pid);

      // Window metrics
      BrowserWindow.getAllWindows().forEach((window, index) => {
        this.recordMetric('window_count', index + 1);
        this.recordMetric('window_memory', window.getBounds().width * window.getBounds().height);
      });

      // Check thresholds
      this.checkThresholds();
    } catch (error) {
      trackError(error as Error, {
        context: { component: 'PerformanceMonitor' },
      });
    }
  }

  public recordMetric(
    name: string,
    value: number,
    tags?: Record<string, string>
  ): void {
    const metric: PerformanceMetric = {
      name,
      value,
      tags,
      timestamp: Date.now(),
    };

    this.metrics.push(metric);

    if (this.metrics.length > this.maxMetrics) {
      this.metrics.shift();
    }

    this.checkThreshold(name, value);
  }

  private checkThreshold(name: string, value: number): void {
    const threshold = this.thresholds.find((t) => t.name === name);
    if (threshold && value > threshold.threshold) {
      const message = `Performance threshold exceeded: ${name} = ${value} (threshold: ${threshold.threshold})`;
      logger.warn(message, { metric: name, value, threshold: threshold.threshold });

      if (threshold.severity === 'error' || threshold.severity === 'critical') {
        trackError(new Error(message), {
          context: {
            metric: name,
            value,
            threshold: threshold.threshold,
            severity: threshold.severity,
          },
        });
      }
    }
  }

  private checkThresholds(): void {
    this.metrics.forEach((metric) => {
      this.checkThreshold(metric.name, metric.value);
    });
  }

  public getMetrics(
    name?: string,
    startTime?: number,
    endTime?: number
  ): PerformanceMetric[] {
    let filtered = this.metrics;

    if (name) {
      filtered = filtered.filter((m) => m.name === name);
    }

    if (startTime) {
      filtered = filtered.filter((m) => m.timestamp >= startTime);
    }

    if (endTime) {
      filtered = filtered.filter((m) => m.timestamp <= endTime);
    }

    return filtered;
  }

  public getMetricSummary(name: string): {
    min: number;
    max: number;
    avg: number;
    count: number;
  } {
    const metrics = this.getMetrics(name);
    if (metrics.length === 0) {
      return { min: 0, max: 0, avg: 0, count: 0 };
    }

    const values = metrics.map((m) => m.value);
    return {
      min: Math.min(...values),
      max: Math.max(...values),
      avg: values.reduce((a, b) => a + b, 0) / values.length,
      count: values.length,
    };
  }

  public setThreshold(
    name: string,
    threshold: number,
    severity: 'warning' | 'error' | 'critical'
  ): void {
    const existing = this.thresholds.findIndex((t) => t.name === name);
    const newThreshold: PerformanceThreshold = { name, threshold, severity };

    if (existing >= 0) {
      this.thresholds[existing] = newThreshold;
    } else {
      this.thresholds.push(newThreshold);
    }
  }

  public clearMetrics(): void {
    this.metrics = [];
  }

  public getThresholds(): PerformanceThreshold[] {
    return [...this.thresholds];
  }
}

export const performanceMonitor = PerformanceMonitor.getInstance();

export const startPerformanceMonitoring = (interval?: number): void => {
  performanceMonitor.startMonitoring(interval);
};

export const stopPerformanceMonitoring = (): void => {
  performanceMonitor.stopMonitoring();
};

export const recordPerformanceMetric = (
  name: string,
  value: number,
  tags?: Record<string, string>
): void => {
  performanceMonitor.recordMetric(name, value, tags);
};

export const getPerformanceMetrics = (
  name?: string,
  startTime?: number,
  endTime?: number
): PerformanceMetric[] => {
  return performanceMonitor.getMetrics(name, startTime, endTime);
};

export const getPerformanceMetricSummary = (name: string): {
  min: number;
  max: number;
  avg: number;
  count: number;
} => {
  return performanceMonitor.getMetricSummary(name);
};

export const setPerformanceThreshold = (
  name: string,
  threshold: number,
  severity: 'warning' | 'error' | 'critical'
): void => {
  performanceMonitor.setThreshold(name, threshold, severity);
};

export const clearPerformanceMetrics = (): void => {
  performanceMonitor.clearMetrics();
};

export const getPerformanceThresholds = (): PerformanceThreshold[] => {
  return performanceMonitor.getThresholds();
}; 