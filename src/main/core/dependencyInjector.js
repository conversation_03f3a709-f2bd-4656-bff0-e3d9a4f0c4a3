// src/main/core/dependencyInjector.js

/**
 * @class DependencyInjector
 * @description A simple dependency injection container for managing and resolving dependencies.
 */
class DependencyInjector {
    constructor() {
        /**
         * @private
         * @type {Map<string, {dependency: any, isSingleton: boolean, instance: any|null}>}
         * @description Stores registered dependencies along with their singleton status and instance.
         */
        this.dependencies = new Map();
    }

    /**
     * Registers a dependency with the container.
     * @param {string} name - The unique name of the dependency.
     * @param {any} dependency - The dependency to register. This can be a class, an object, a function, or any primitive value.
     * @param {boolean} [isSingleton=true] - Optional. If true, the dependency will be treated as a singleton, meaning only one instance will be created and reused. Defaults to true.
     * @returns {void}
     */
    register(name, dependency, isSingleton = true) {
        if (this.dependencies.has(name)) {
    
        }
        this.dependencies.set(name, { dependency, isSingleton, instance: null });
    }

    /**
     * Resolves a dependency from the container.
     * If the dependency is a class, it will be instantiated. If it's a singleton, the same instance will be returned on subsequent calls.
     * Dependencies of the resolved class (if specified in a static `dependencies` array) will also be resolved and injected into its constructor.
     * @param {string} name - The name of the dependency to resolve.
     * @returns {any} The resolved dependency instance or value.
     * @throws {Error} If the dependency is not found in the container.
     */
    resolve(name) {
        const dep = this.dependencies.get(name);
        if (!dep) {
            throw new Error(`Dependency '${name}' not found.`);
        }

        if (dep.isSingleton) {
            if (!dep.instance) {
                dep.instance = this._createInstance(dep.dependency);
            }
            return dep.instance;
        } else {
            return this._createInstance(dep.dependency);
        }
    }

    /**
     * Instantiates a class, injecting its dependencies if specified in a static 'dependencies' property.
     * If the dependency is not a class (e.g., an object, function, or primitive), it is returned as is.
     * @private
     * @param {any} dependency - The dependency to potentially instantiate. Can be a class constructor or any other value.
     * @returns {any} The instantiated object if `dependency` is a class, or `dependency` itself otherwise.
     */
    _createInstance(dependency) {
        if (typeof dependency === 'function' && dependency.prototype && dependency.prototype.constructor === dependency) {
            // It's a class, try to inject dependencies
            const constructorArgs = (dependency.dependencies || []).map(depName => this.resolve(depName));
            return new dependency(...constructorArgs);
        } else {
            // It's not a class (e.g., an object, function, or primitive), return as is
            return dependency;
        }
    }

    /**
     * Clears all registered dependencies and their instances from the container.
     * This effectively resets the injector to its initial state.
     * @returns {void}
     */
    clear() {
        this.dependencies.clear();
    }
}

module.exports = DependencyInjector;