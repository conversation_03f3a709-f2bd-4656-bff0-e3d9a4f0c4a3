import { app, BrowserWindow, dialog } from 'electron';
import { writeFileSync, appendFileSync } from 'fs';
import { join } from 'path';

interface ErrorLog {
  timestamp: string;
  type: string;
  message: string;
  stack?: string;
  additionalInfo?: Record<string, any>;
}

class ErrorHandler {
  private readonly logFile: string;
  private readonly maxLogSize: number = 5 * 1024 * 1024; // 5MB
  private readonly maxLogFiles: number = 5;

  constructor() {
    this.logFile = join(app.getPath('userData'), 'error.log');
    this.setupErrorHandlers();
  }

  private setupErrorHandlers() {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error: Error) => {
      this.handleError('uncaughtException', error);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason: any) => {
      this.handleError('unhandledRejection', reason instanceof Error ? reason : new Error(String(reason)));
    });

    // Handle window errors
    app.on('render-process-gone', (event, webContents, details) => {
      this.handleError('render-process-gone', new Error(`Render process gone: ${details.reason}`), {
        reason: details.reason,
        exitCode: details.exitCode,
      });
    });

    // Handle GPU process crashes
    app.on('gpu-process-crashed', (event, killed) => {
      this.handleError('gpu-process-crashed', new Error('GPU process crashed'), {
        killed,
      });
    });
  }

  private async handleError(type: string, error: Error, additionalInfo?: Record<string, any>) {
    const errorLog: ErrorLog = {
      timestamp: new Date().toISOString(),
      type,
      message: error.message,
      stack: error.stack,
      additionalInfo,
    };

    // Log to file
    this.logError(errorLog);

    // Show error dialog
    this.showErrorDialog(error);

    // Attempt recovery
    await this.attemptRecovery(type, error);
  }

  private logError(errorLog: ErrorLog) {
    try {
      // Check log file size
      const logSize = this.getLogFileSize();
      if (logSize > this.maxLogSize) {
        this.rotateLogFiles();
      }

      // Append error to log file
      appendFileSync(this.logFile, JSON.stringify(errorLog, null, 2) + '\n', 'utf8');
    } catch (err) {
      console.error('Failed to write to error log:', err);
    }
  }

  private getLogFileSize(): number {
    try {
      const stats = require('fs').statSync(this.logFile);
      return stats.size;
    } catch {
      return 0;
    }
  }

  private rotateLogFiles() {
    try {
      // Rotate existing log files
      for (let i = this.maxLogFiles - 1; i > 0; i--) {
        const oldPath = `${this.logFile}.${i}`;
        const newPath = `${this.logFile}.${i + 1}`;
        if (require('fs').existsSync(oldPath)) {
          require('fs').renameSync(oldPath, newPath);
        }
      }

      // Rename current log file
      if (require('fs').existsSync(this.logFile)) {
        require('fs').renameSync(this.logFile, `${this.logFile}.1`);
      }

      // Create new log file
      writeFileSync(this.logFile, '', 'utf8');
    } catch (err) {
      console.error('Failed to rotate log files:', err);
    }
  }

  private showErrorDialog(error: Error) {
    dialog.showErrorBox(
      'Application Error',
      `An error occurred: ${error.message}\n\nPlease check the error log for more details.`
    );
  }

  private async attemptRecovery(type: string, error: Error) {
    switch (type) {
      case 'render-process-gone':
        // Reload the main window
        const windows = BrowserWindow.getAllWindows();
        if (windows.length > 0) {
          windows[0].reload();
        }
        break;

      case 'gpu-process-crashed':
        // Disable GPU acceleration and restart
        app.disableHardwareAcceleration();
        app.relaunch();
        app.exit(0);
        break;

      case 'uncaughtException':
      case 'unhandledRejection':
        // Attempt to save any unsaved data
        // Then restart the application
        app.relaunch();
        app.exit(1);
        break;
    }
  }

  public getErrorLogs(): ErrorLog[] {
    try {
      const content = require('fs').readFileSync(this.logFile, 'utf8');
      return content
        .split('\n')
        .filter(Boolean)
        .map(line => JSON.parse(line));
    } catch {
      return [];
    }
  }

  public clearErrorLogs() {
    try {
      writeFileSync(this.logFile, '', 'utf8');
    } catch (err) {
      console.error('Failed to clear error logs:', err);
    }
  }
}

export const errorHandler = new ErrorHandler();

export const setupErrorHandling = (mainWindow: BrowserWindow) => {
  // Handle window-specific errors
  mainWindow.webContents.on('crashed', () => {
    errorHandler.handleError('window-crashed', new Error('Window crashed'));
  });

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    errorHandler.handleError('page-load-failed', new Error(`Failed to load page: ${errorDescription}`), {
      errorCode,
      errorDescription,
    });
  });

  // Handle IPC errors
  mainWindow.webContents.on('ipc-message', (event, channel, ...args) => {
    try {
      // Validate IPC messages
      if (typeof channel !== 'string') {
        throw new Error('Invalid IPC channel');
      }
    } catch (error) {
      errorHandler.handleError('ipc-error', error as Error, {
        channel,
        args,
      });
    }
  });
}; 