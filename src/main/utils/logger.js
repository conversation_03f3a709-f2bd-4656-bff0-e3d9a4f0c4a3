const log4js = require('log4js');
const path = require('path');

let logger;

function initializeLogger(app) {
  const logsDirectory = path.join(app.getPath('userData'), 'logs');

  log4js.configure({
    appenders: {
      file: {
        type: 'file',
        filename: path.join(logsDirectory, 'app.log'),
        maxLogSize: 10485760, // 10 MB
        backups: 3,
        compress: true,
      },
      console: {
        type: 'console',
      },
    },
    categories: {
      default: {
        appenders: ['file', 'console'],
        level: 'debug',
      },
    },
  });

  logger = log4js.getLogger('app');
  global.logger = logger;
  logger.info('Logger initialized.');
}

module.exports = { initializeLogger, logger };