const { BrowserWindow } = require('electron');
const path = require('path');
// const errorHandler = require('./utils/errorHandling'); // Now injected

/**
 * @class WindowManager
 * @description Manages the creation and lifecycle of the main Electron browser window.
 */
class WindowManager {
    static dependencies = ['ErrorHandler'];
    constructor(errorHandler) {
        /**
         * @property {BrowserWindow|null} mainWindow - The main Electron browser window instance.
         * @private
         */
        this.mainWindow = null;
        this.errorHandler = errorHandler;
    }

    /**
     * Creates and configures the main Electron browser window.
     * @param {string} preloadScriptPath - The absolute path to the preload script.
     * @param {string} htmlFilePath - The absolute path to the HTML file to load.
     * @returns {BrowserWindow} The created BrowserWindow instance.
     */
    createWindow(preloadScriptPath, htmlFilePath) {
        this.mainWindow = new BrowserWindow({
            // Set a custom title bar style for macOS to create a more modern look.
            titleBarStyle: 'hidden',
            // For other platforms, consider using a frameless window for full customization.
            // This might require implementing custom window controls in the renderer process.
            frame: process.platform === 'darwin' ? true : false,
            width: 1200,
            height: 800,
            minWidth: 800,
            minHeight: 600,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: preloadScriptPath,
                spellcheck: true,
                // Enable web security for enhanced protection against common web vulnerabilities.
                webSecurity: true
            }
        });

        this.mainWindow.loadFile(htmlFilePath);

        // Open DevTools if the '--inspect' argument is present
        if (process.argv.includes('--inspect')) {
            this.mainWindow.webContents.openDevTools();
        }

        // Dereference the window object when the window is closed
        this.mainWindow.on('closed', this.errorHandler.wrapSync(() => {
            this.mainWindow = null;
        }, 'MainWindowClosed'));

        return this.mainWindow;
    }

    /**
     * Retrieves the main Electron browser window instance.
     * @returns {BrowserWindow|null} The main window instance, or null if it hasn't been created or has been closed.
     */
    getMainWindow() {
        return this.mainWindow;
    }
}

module.exports = WindowManager;