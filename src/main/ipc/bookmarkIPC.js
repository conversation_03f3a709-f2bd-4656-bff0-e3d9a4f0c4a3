const { ipcMain } = require('electron');
const errorHandler = require('../utils/errorHandling');

let configManagerInstance = null;

/**
 * Initializes IPC handlers for bookmark-related operations.
 * @param {ConfigManager} configManager - The ConfigManager instance for managing bookmark data.
 */
function initializeBookmarkIPC(configManager) {
  configManagerInstance = configManager;

  /**
   * <PERSON><PERSON> requests to get all bookmarks.
   * @returns {Array<object>} - An array of bookmark objects.
   */
  ipcMain.handle('get-bookmarks', errorHandler.wrapSync(() => {
    const settings = configManagerInstance.getAll();
    return settings.bookmarks || [];
  }, 'IPC:get-bookmarks'));

  /**
   * <PERSON><PERSON> requests to add a new bookmark or update an existing one.
   * If a bookmark with the same URL already exists, it updates the existing entry.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {object} bookmark - The bookmark object to add or update.
   * @param {string} bookmark.url - The URL of the bookmark.
   * @param {string} bookmark.title - The title of the bookmark.
   * @returns {boolean} - True if the operation was successful.
   */
  ipcMain.handle('add-bookmark', errorHandler.wrapSync((event, bookmark) => {
    const settings = configManagerInstance.getAll();
    const bookmarks = settings.bookmarks || [];
    
    // Check if such an entry already exists
    const existingIndex = bookmarks.findIndex(b => b.url === bookmark.url);
    
    if (existingIndex !== -1) {
      // Update the existing bookmark
      bookmarks[existingIndex] = bookmark;
    } else {
      // Add a new bookmark
      bookmarks.push(bookmark);
    }
    
    // Save the updated bookmarks
    configManagerInstance.set('bookmarks', bookmarks);
    
    return true;
  }, 'IPC:add-bookmark'));

  /**
   * Handles requests to remove a bookmark by its URL.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {string} bookmarkUrl - The URL of the bookmark to remove.
   * @returns {boolean} - True if the operation was successful.
   */
  ipcMain.handle('remove-bookmark', errorHandler.wrapSync((event, bookmarkUrl) => {
    const settings = configManagerInstance.getAll();
    const bookmarks = settings.bookmarks || [];
    
    // Remove the bookmark by URL
    const updatedBookmarks = bookmarks.filter(bookmark => bookmark.url !== bookmarkUrl);
    
    // Save the updated bookmarks
    configManagerInstance.set('bookmarks', updatedBookmarks);
    
    return true;
  }, 'IPC:remove-bookmark'));
}

module.exports = { initializeBookmarkIPC };