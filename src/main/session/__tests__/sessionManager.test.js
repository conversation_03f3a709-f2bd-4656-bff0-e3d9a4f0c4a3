const SessionManager = require('../sessionManager');
const { safeStorage } = require('electron');

jest.mock('electron', () => ({
  safeStorage: {
    encryptString: jest.fn(),
    decryptString: jest.fn()
  }
}));

describe('SessionManager Validation', () => {
  let manager;
  
  beforeEach(() => {
    manager = new SessionManager({
      store: {
        get: jest.fn(),
        set: jest.fn(),
        delete: jest.fn()
      }
    });
  });

  test('Validates correct session structure', () => {
    const validSession = {
      version: '1.0',
      tabs: [{ url: 'https://example.com', title: 'Example' }]
    };
    expect(() => manager.validateSessionStructure(validSession)).not.toThrow();
  });

  test('Rejects invalid session version', () => {
    const invalidSession = {
      version: '2.0',
      tabs: [{ url: 'https://example.com', title: 'Example' }]
    };
    expect(() => manager.validateSessionStructure(invalidSession))
      .toThrow('Несовместимая версия формата сессии');
  });

  test('Rejects missing master password', async () => {
    await expect(manager.deriveKey(''))
      .rejects.toThrow('Минимальная длина мастер-пароля - 8 символов');
  });

  test('Generates valid encryption key', async () => {
    const key = await manager.deriveKey('securePass123');
    expect(key).toMatch(/^[0-9a-f]{64}$/);
  });
});