import { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectPerformanceMetrics,
  selectPerformanceSettings,
  selectPerformanceAlerts,
  updateSettings,
  clearAlerts,
} from '../store/slices/performanceSlice';
import type { PerformanceSettings } from '../store/slices/performanceSlice';

export const usePerformance = () => {
  const dispatch = useDispatch();
  const metrics = useSelector(selectPerformanceMetrics);
  const settings = useSelector(selectPerformanceSettings);
  const alerts = useSelector(selectPerformanceAlerts);

  const updatePerformanceSettings = useCallback(
    (newSettings: Partial<PerformanceSettings>) => {
      dispatch(updateSettings(newSettings));
    },
    [dispatch]
  );

  const clearPerformanceAlerts = useCallback(() => {
    dispatch(clearAlerts());
  }, [dispatch]);

  // Auto-clear alerts after 5 seconds
  useEffect(() => {
    if (alerts.length > 0) {
      const timer = setTimeout(() => {
        clearPerformanceAlerts();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [alerts, clearPerformanceAlerts]);

  return {
    metrics,
    settings,
    alerts,
    updatePerformanceSettings,
    clearPerformanceAlerts,
  };
}; 