import { useState, useCallback, useMemo } from 'react';

type ValidationSchema<T> = {
  [K in keyof T]: (value: T[K]) => string | boolean | Promise<string | boolean>;
};

interface UseFormValidationResult<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  isValid: boolean;
  handleChange: (field: keyof T) => (value: T[keyof T]) => Promise<void>;
  handleSubmit: (callback: (values: T) => void) => () => Promise<void>;
}

export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  schema: ValidationSchema<T>
): UseFormValidationResult<T> {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [isValid, setIsValid] = useState(false);

  const validateField = useCallback(
    async (field: keyof T, value: T[keyof T]) => {
      const validator = schema[field];
      if (!validator) return '';

      try {
        const result = await validator(value);
        return typeof result === 'string' ? result : '';
      } catch (error) {
        return 'Validation error';
      }
    },
    [schema]
  );

  const handleChange = useCallback(
    (field: keyof T) => async (value: T[keyof T]) => {
      setValues(prev => ({ ...prev, [field]: value }));
      const error = await validateField(field, value);
      setErrors(prev => ({ ...prev, [field]: error }));
    },
    [validateField]
  );

  const validateAll = useCallback(async () => {
    const newErrors: Partial<Record<keyof T, string>> = {};
    let isValid = true;

    await Promise.all(
      Object.keys(schema).map(async (field) => {
        const error = await validateField(field as keyof T, values[field as keyof T]);
        if (error) {
          newErrors[field as keyof T] = error;
          isValid = false;
        }
      })
    );

    setErrors(newErrors);
    setIsValid(isValid);
    return isValid;
  }, [schema, validateField, values]);

  const handleSubmit = useCallback(
    (callback: (values: T) => void) => async () => {
      const isValid = await validateAll();
      if (isValid) callback(values);
    },
    [validateAll, values]
  );

  const memoizedResult = useMemo(() => ({
    values,
    errors,
    isValid,
    handleChange,
    handleSubmit
  }), [values, errors, isValid, handleChange, handleSubmit]);

  return memoizedResult;
}