import { render, screen, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useFormValidation } from '../useFormValidation';

const TestFormComponent = () => {
  const { values, errors, handleChange, handleSubmit } = useFormValidation(
    { email: '', password: '' },
    {
      email: (v) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v) || 'Invalid email',
      password: (v) => v.length >= 8 || 'Password too short'
    }
  );

  return (
    <form onSubmit={handleSubmit(() => {})}>
      <input
        data-testid="email-input"
        value={values.email}
        onChange={(e) => handleChange('email')(e.target.value)}
      />
      {errors.email && <div data-testid="email-error">{errors.email}</div>}

      <input
        data-testid="password-input"
        type="password"
        value={values.password}
        onChange={(e) => handleChange('password')(e.target.value)}
      />
      {errors.password && <div data-testid="password-error">{errors.password}</div>}

      <button data-testid="submit-btn" type="submit">Submit</button>
    </form>
  );
};

describe('FormValidation Integration', () => {
  test('should display validation errors in UI', async () => {
    render(<TestFormComponent />);
    
    const emailInput = screen.getByTestId('email-input');
    const passwordInput = screen.getByTestId('password-input');

    await act(async () => {
      await userEvent.type(emailInput, 'invalid-email');
      await userEvent.type(passwordInput, 'short');
    });

    expect(screen.getByTestId('email-error')).toHaveTextContent('Invalid email');
    expect(screen.getByTestId('password-error')).toHaveTextContent('Password too short');
  });

  test('should clear errors after valid input', async () => {
    render(<TestFormComponent />);
    
    const emailInput = screen.getByTestId('email-input');
    const passwordInput = screen.getByTestId('password-input');

    await act(async () => {
      await userEvent.type(emailInput, 'invalid-email');
      await userEvent.type(passwordInput, 'short');
      await userEvent.clear(emailInput);
      await userEvent.type(emailInput, '<EMAIL>');
      await userEvent.clear(passwordInput);
      await userEvent.type(passwordInput, 'longenoughpassword');
    });

    expect(screen.queryByTestId('email-error')).toBeNull();
    expect(screen.queryByTestId('password-error')).toBeNull();
  });
});