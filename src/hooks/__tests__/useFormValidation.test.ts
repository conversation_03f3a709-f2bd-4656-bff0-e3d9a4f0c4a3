import { renderHook, act } from '@testing-library/react-hooks';
import { useFormValidation } from '../useFormValidation';

describe('useFormValidation', () => {
  const validationSchema = {
    email: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) || 'Invalid email',
    password: (value: string) => value.length >= 8 || 'Password too short'
  };

  test('should initialize with default values', () => {
    const { result } = renderHook(() =>
      useFormValidation({ email: '', password: '' }, validationSchema)
    );

    expect(result.current.values).toEqual({ email: '', password: '' });
    expect(result.current.errors).toEqual({});
    expect(result.current.isValid).toBe(false);
  });

  test('should handle field change and validation', async () => {
    const { result } = renderHook(() =>
      useFormValidation({ email: '', password: '' }, validationSchema)
    );

    await act(async () => {
      await result.current.handleChange('email')('<EMAIL>');
    });

    expect(result.current.values.email).toBe('<EMAIL>');
    expect(result.current.errors.email).toBeUndefined();

    await act(async () => {
      await result.current.handleChange('password')('1234');
    });

    expect(result.current.errors.password).toBe('Password too short');
    expect(result.current.isValid).toBe(false);
  });

  test('should validate all fields on submit', async () => {
    const mockSubmit = jest.fn();
    const { result } = renderHook(() =>
      useFormValidation({ email: '', password: '' }, validationSchema)
    );

    await act(async () => {
      await result.current.handleSubmit(mockSubmit)();
    });

    expect(result.current.errors).toEqual({
      email: 'Invalid email',
      password: 'Password too short'
    });
    expect(mockSubmit).not.toHaveBeenCalled();
  });

  test('should handle async validation', async () => {
    const asyncSchema = {
      username: async (value: string) => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return value.length >= 3 || 'Username too short';
      }
    };

    const { result } = renderHook(() =>
      useFormValidation({ username: '' }, asyncSchema)
    );

    await act(async () => {
      await result.current.handleChange('username')('ab');
    });

    expect(result.current.errors.username).toBe('Username too short');
  });

  test('should handle concurrent field updates', async () => {
    const { result, waitForNextUpdate } = renderHook(() =>
      useFormValidation({ email: '', password: '' }, validationSchema)
    );

    await act(async () => {
      result.current.handleChange('email')('test@');
      result.current.handleChange('password')('12345678');
      await waitForNextUpdate();
    });

    expect(result.current.errors.email).toBe('Invalid email');
    expect(result.current.errors.password).toBeUndefined();
  });

  test('should handle extremely long inputs', async () => {
    const longString = 'a'.repeat(10000);
    const { result } = renderHook(() =>
      useFormValidation({ password: '' }, validationSchema)
    );

    await act(async () => {
      await result.current.handleChange('password')(longString);
    });

    expect(result.current.errors.password).toBeUndefined();
    expect(result.current.isValid).toBe(true);
  });

  test('should handle special characters in email', async () => {
    const { result } = renderHook(() =>
      useFormValidation({ email: '' }, validationSchema)
    );

    await act(async () => {
      await result.current.handleChange('email')('тест@пример.рф');
    });

    expect(result.current.errors.email).toBeUndefined();
  });
});