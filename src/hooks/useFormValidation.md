# Документация useFormValidation

Кастомный хук для управления валидацией форм с поддержкой синхронных и асинхронных правил.

## API

```ts
interface ValidationRule<T> {
  (value: T): string | boolean | Promise<string | boolean>;
}

interface UseFormValidationResult<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  isValid: boolean;
  handleChange: (field: keyof T) => (value: T[keyof T]) => Promise<void>;
  handleSubmit: (callback: (values: T) => void) => () => Promise<void>;
}
```

## Пример использования
### Базовый сценарий
```tsx
const schema = {
  email: (v) => /^\S+@\S+$/.test(v) || 'Invalid email',
  password: useMemo(() => (v) => v.length >= 8 || 'Too short', [])
};

### Расширенный пример с асинхронной проверкой
```tsx
const registrationSchema = {
  username: async (v) => {
    if (!/^[a-z0-9_]{3,20}$/i.test(v)) return 'Invalid format';
    const res = await fetch(`/api/check-username/${encodeURIComponent(v)}`);
    return res.ok ? true : 'Username taken';
  },
  password: (v) => {
    const hasUpper = /[A-Z]/.test(v);
    const hasLower = /[a-z]/.test(v);
    const hasNumber = /\d/.test(v);
    return hasUpper && hasLower && hasNumber || 'Missing character types';
  }
};
```

## Безопасность

### Content Security Policy (CSP)
```html
<meta http-equiv="Content-Security-Policy"
  content="default-src 'none';
    script-src 'self' 'unsafe-inline';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data:;
    connect-src 'self' api.example.com;">
```

### Защита от CSRF:
```tsx
// Генерация CSRF-токена
const [csrfToken, setCsrfToken] = useState('');

useEffect(() => {
  fetch('/api/csrf-token')
    .then(res => res.json())
    .then(data => setCsrfToken(data.token));
}, []);

// Использование в запросах
fetch('/submit', {
  method: 'POST',
  headers: {
    'X-CSRF-Token': csrfToken,
    'Content-Type': 'application/json'
  }
});
```

### CORS Настройки для Express.js:
```javascript
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? 'https://your-domain.com' 
    : 'http://localhost:3000',
  methods: ['POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'X-CSRF-Token'],
  credentials: true
}));
```
1. **Санітізація вводу**: Всегда экранируйте пользовательский ввод перед отображением
2. **XSS Protection**: Используйте dangerouslySetInnerHTML только с санитизированным контентом
3. **CSP Политики**: Реализуйте Content Security Policy
```html
<meta http-equiv="Content-Security-Policy" 
  content="default-src 'self'; script-src 'self' 'unsafe-inline'; connect-src 'self' api.example.com;">
```
4. **CSRF Protection**: Добавляйте токены в асинхронные запросы
```tsx
fetch('/validate', {
  headers: {
    'X-CSRF-Token': document.cookie.match(/csrftoken=([^;]+)/)?.[1]
  }
});
```
5. **Rate Limiting**: Ограничивайте частоту валидационных запросов
6. **CORS**: Настройки для production:
```
Access-Control-Allow-Origin: https://your-domain.com
Access-Control-Allow-Methods: POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, X-CSRF-Token
```

```tsx
// Пример безопасной обработки ошибок
const safeError = (msg: string) => {
  return msg
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');
};
```

const { values, errors, handleChange } = useFormValidation(initialValues, schema);
```

## Доступность (Accessibility)

### Рекомендации для форм:
1. Добавляйте aria-live="polite" для динамических ошибок
2. Используйте aria-describedby для связки полей с ошибками
3. Реализуйте навигацию клавишей Tab
4. Поддерживайте контрастность текста ошибок

```tsx
// Пример доступного поля с ошибкой
<label htmlFor="email">Email</label>
<input 
  id="email"
  aria-invalid={!!errors.email}
  aria-describedby="email-error"
/>
{errors.email && 
  <div id="email-error" role="alert" aria-live="polite">
    {errors.email}
  </div>}
```

### Управление фокусом:
```tsx
// Автофокус на первую ошибку
useEffect(() => {
  if (Object.keys(errors).length > 0) {
    const firstError = document.querySelector('[aria-invalid="true"]');
    (firstError as HTMLElement)?.focus();
  }
}, [errors]);
```

## Производительность и оптимизация
1. Мемоизируйте сложные валидационные правила с помощью useMemo
2. Для асинхронной валидации возвращайте Promise
3. Используйте handleSubmit для обработки корректных данных
4. Оптимизируйте производительность с помощью разделения состояний

## Лучшие практики асинхронных валидаций
- Используйте debounce для сетевых запросов (300-500ms)
- Отменяйте предыдущие запросы при новом вводе
- Кешируйте успешные ответы сервера
- Обрабатывайте состояния загрузки в UI
- Используйте AbortController для прерывания fetch-запросов

```tsx
const schema = {
  username: async (v) => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    try {
      const res = await fetch(`/api/check-username?${v}`, {
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      return res.ok || 'Username already taken';
    } catch {
      return 'Validation service unavailable';
    }
  }
};
```

## Обработка ошибок
### Стратегии обработки:
1. **Сетевые ошибки** - повторный запрос с экспоненциальным отступом
2. **Таймауты** - автоматическая отмена долгих запросов
3. **Серверные ошибки** - возврат универсального сообщения
4. **Клиентские ошибки** - детализированная диагностика
5. **Retry-стратегии** - автоматический повтор с backoff алгоритмом
6. **Fallback-сценарии** - кеширование последних успешных результатов

### Реализация retry-логики:
```tsx
const withRetry = async (fn: () => Promise<any>, retries = 3) => {
  try {
    return await fn();
  } catch (error) {
    if (retries <= 0) throw error;
    await new Promise(r => setTimeout(r, 1000 * (4 - retries)));
    return withRetry(fn, retries - 1);
  }
};

// Использование в валидаторе
const schema = {
  email: async (v) => 
    withRetry(() => fetch(`/validate/email/${v}`))
};
```

### Примеры международных форматов:
```tsx
// Валидация телефонных номеров
const phoneValidators = {
  RU: (v) => /^\+7\d{10}$/.test(v) || 'Некорректный номер',
  US: (v) => /^\+1\d{10}$/.test(v) || 'Invalid format',
  EU: (v) => /^\+\d{1,3}\d{4,14}$/.test(v) || 'Invalid number'
};

// Валидация IBAN
const ibanValidator = (v) => {
  const cleaned = v.replace(/[^A-Z0-9]/gi, '');
  const regex = /^[A-Z]{2}\d{2}[A-Z0-9]{11,30}$/;
  return regex.test(cleaned) || 'Invalid IBAN';
};
```

### Диаграмма последовательности:
```mermaid
sequenceDiagram
  participant UI as Пользовательский интерфейс
  participant Hook as useFormValidation
  participant Server as API Сервер
  
  UI->>Hook: Ввод данных
  Hook->>Hook: Синхронная валидация
  alt Есть ошибка
    Hook-->>UI: Немедленный возврат ошибки
  else
    Hook->>Server: Асинхронный запрос
    alt Таймаут
      Hook-->>UI: "Превышено время ожидания"
    else Серверная ошибка
      Server-->>Hook: 5xx ошибка
      Hook-->>UI: "Ошибка сервера"
    else Успех
      Server-->>Hook: 200 OK
      Hook-->>UI: Успешная валидация
    end
  end
```

При возникновении исключения в валидаторе, ошибка будет записана в поле ошибок:
```ts
const schema = {
  asyncField: async (v) => {
    const res = await fetch('/validate');
    if (!res.ok) throw new Error('Validation failed');
    return true;
  }
};
```