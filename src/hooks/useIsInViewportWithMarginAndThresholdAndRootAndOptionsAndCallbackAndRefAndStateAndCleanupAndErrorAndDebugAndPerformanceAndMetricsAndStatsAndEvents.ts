import { useEffect, useRef, useState } from 'react';

interface UseIsInViewportWithMarginAndThresholdAndRootAndOptionsAndCallbackAndRefAndStateAndCleanupAndErrorAndDebugAndPerformanceAndMetricsAndStatsAndEventsProps {
  threshold?: number;
  root?: Element | null;
  rootMargin?: string;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  thresholdPercentage?: number;
  rootMargin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  options?: {
    trackVisibility?: boolean;
    delay?: number;
  };
  onIntersectionChange?: (isIntersecting: boolean, entry: IntersectionObserverEntry) => void;
  ref?: React.RefObject<HTMLElement>;
  initialState?: boolean;
  onCleanup?: () => void;
  onError?: (error: Error) => void;
  debug?: boolean;
  performance?: {
    measure?: boolean;
    mark?: boolean;
    log?: boolean;
  };
  metrics?: {
    track?: boolean;
    log?: boolean;
    onMetricsUpdate?: (metrics: {
      initTime: number;
      callbackTime: number;
      cleanupTime: number;
      totalTime: number;
      callbacks: number;
      errors: number;
    }) => void;
  };
  stats?: {
    track?: boolean;
    log?: boolean;
    onStatsUpdate?: (stats: {
      averageCallbackTime: number;
      averageInitTime: number;
      averageCleanupTime: number;
      averageTotalTime: number;
      errorRate: number;
      callbackRate: number;
    }) => void;
  };
  events?: {
    onInit?: () => void;
    onCallback?: () => void;
    onCleanup?: () => void;
    onError?: (error: Error) => void;
  };
}

export function useIsInViewportWithMarginAndThresholdAndRootAndOptionsAndCallbackAndRefAndStateAndCleanupAndErrorAndDebugAndPerformanceAndMetricsAndStatsAndEvents<T extends HTMLElement = HTMLElement>(
  props: UseIsInViewportWithMarginAndThresholdAndRootAndOptionsAndCallbackAndRefAndStateAndCleanupAndErrorAndDebugAndPerformanceAndMetricsAndStatsAndEventsProps = {}
): [React.RefObject<T>, boolean] {
  const {
    threshold = 0,
    root = null,
    rootMargin = '0px',
    margin = {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
    },
    thresholdPercentage = 0,
    rootMargin: rootMarginOptions = {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
    },
    options = {
      trackVisibility: false,
      delay: 0,
    },
    onIntersectionChange,
    ref: externalRef,
    initialState = false,
    onCleanup,
    onError,
    debug = false,
    performance: performanceOptions = {
      measure: false,
      mark: false,
      log: false,
    },
    metrics: metricsOptions = {
      track: false,
      log: false,
      onMetricsUpdate: undefined,
    },
    stats: statsOptions = {
      track: false,
      log: false,
      onStatsUpdate: undefined,
    },
    events = {
      onInit: undefined,
      onCallback: undefined,
      onCleanup: undefined,
      onError: undefined,
    },
  } = props;

  const [isInViewport, setIsInViewport] = useState(initialState);
  const internalRef = useRef<T>(null);
  const elementRef = externalRef || internalRef;
  const timeoutRef = useRef<NodeJS.Timeout>();
  const isFirstRender = useRef(true);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const metricsRef = useRef<{
    initTime: number;
    callbackTime: number;
    cleanupTime: number;
    totalTime: number;
    callbacks: number;
    errors: number;
  }>({
    initTime: 0,
    callbackTime: 0,
    cleanupTime: 0,
    totalTime: 0,
    callbacks: 0,
    errors: 0,
  });
  const statsRef = useRef<{
    averageCallbackTime: number;
    averageInitTime: number;
    averageCleanupTime: number;
    averageTotalTime: number;
    errorRate: number;
    callbackRate: number;
  }>({
    averageCallbackTime: 0,
    averageInitTime: 0,
    averageCleanupTime: 0,
    averageTotalTime: 0,
    errorRate: 0,
    callbackRate: 0,
  });

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    const element = elementRef.current;

    if (!element) {
      return;
    }

    const startTime = performance.now();

    try {
      if (performanceOptions.mark) {
        performance.mark('intersection-observer-init-start');
      }

      observerRef.current = new IntersectionObserver(
        ([entry]) => {
          try {
            if (performanceOptions.mark) {
              performance.mark('intersection-observer-callback-start');
            }

            const callbackStartTime = performance.now();

            const rect = entry.boundingClientRect;
            const rootRect = root ? root.getBoundingClientRect() : {
              top: 0,
              right: window.innerWidth,
              bottom: window.innerHeight,
              left: 0,
            };

            const elementArea = rect.width * rect.height;
            const intersectionArea = Math.max(0, Math.min(rect.right + margin.right, rootRect.right + rootMarginOptions.right) - Math.max(rect.left - margin.left, rootRect.left - rootMarginOptions.left)) *
              Math.max(0, Math.min(rect.bottom + margin.bottom, rootRect.bottom + rootMarginOptions.bottom) - Math.max(rect.top - margin.top, rootRect.top - rootMarginOptions.top));

            const intersectionPercentage = (intersectionArea / elementArea) * 100;
            const isIntersecting = intersectionPercentage >= thresholdPercentage;

            if (debug) {
              console.log('Intersection Observer Entry:', {
                rect,
                rootRect,
                elementArea,
                intersectionArea,
                intersectionPercentage,
                isIntersecting,
                threshold,
                thresholdPercentage,
                margin,
                rootMarginOptions,
                options,
              });
            }

            if (options.delay) {
              if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
              }

              timeoutRef.current = setTimeout(() => {
                setIsInViewport(isIntersecting);
                onIntersectionChange?.(isIntersecting, entry);
              }, options.delay);
            } else {
              setIsInViewport(isIntersecting);
              onIntersectionChange?.(isIntersecting, entry);
            }

            if (performanceOptions.mark) {
              performance.mark('intersection-observer-callback-end');
              performance.measure(
                'intersection-observer-callback',
                'intersection-observer-callback-start',
                'intersection-observer-callback-end'
              );
            }

            if (metricsOptions.track) {
              const callbackEndTime = performance.now();
              metricsRef.current.callbackTime += callbackEndTime - callbackStartTime;
              metricsRef.current.callbacks++;

              if (statsOptions.track) {
                statsRef.current.averageCallbackTime = metricsRef.current.callbackTime / metricsRef.current.callbacks;
                statsRef.current.callbackRate = metricsRef.current.callbacks / (metricsRef.current.callbacks + metricsRef.current.errors);

                if (statsOptions.log) {
                  console.log('Intersection Observer Stats:', statsRef.current);
                }

                statsOptions.onStatsUpdate?.(statsRef.current);
              }
            }

            events.onCallback?.();
          } catch (error) {
            if (debug) {
              console.error('Error in intersection observer callback:', error);
            }
            if (metricsOptions.track) {
              metricsRef.current.errors++;

              if (statsOptions.track) {
                statsRef.current.errorRate = metricsRef.current.errors / (metricsRef.current.callbacks + metricsRef.current.errors);

                if (statsOptions.log) {
                  console.log('Intersection Observer Stats:', statsRef.current);
                }

                statsOptions.onStatsUpdate?.(statsRef.current);
              }
            }
            events.onError?.(error instanceof Error ? error : new Error(String(error)));
            onError?.(error instanceof Error ? error : new Error(String(error)));
          }
        },
        {
          threshold,
          root,
          rootMargin,
          trackVisibility: options.trackVisibility,
        }
      );

      observerRef.current.observe(element);

      if (performanceOptions.mark) {
        performance.mark('intersection-observer-init-end');
        performance.measure(
          'intersection-observer-init',
          'intersection-observer-init-start',
          'intersection-observer-init-end'
        );
      }

      if (metricsOptions.track) {
        const initEndTime = performance.now();
        metricsRef.current.initTime = initEndTime - startTime;

        if (statsOptions.track) {
          statsRef.current.averageInitTime = metricsRef.current.initTime;

          if (statsOptions.log) {
            console.log('Intersection Observer Stats:', statsRef.current);
          }

          statsOptions.onStatsUpdate?.(statsRef.current);
        }
      }

      if (debug) {
        console.log('Intersection Observer initialized:', {
          element,
          root,
          threshold,
          rootMargin,
          options,
        });
      }

      events.onInit?.();
    } catch (error) {
      if (debug) {
        console.error('Error initializing intersection observer:', error);
      }
      if (metricsOptions.track) {
        metricsRef.current.errors++;

        if (statsOptions.track) {
          statsRef.current.errorRate = metricsRef.current.errors / (metricsRef.current.callbacks + metricsRef.current.errors);

          if (statsOptions.log) {
            console.log('Intersection Observer Stats:', statsRef.current);
          }

          statsOptions.onStatsUpdate?.(statsRef.current);
        }
      }
      events.onError?.(error instanceof Error ? error : new Error(String(error)));
      onError?.(error instanceof Error ? error : new Error(String(error)));
    }

    return () => {
      try {
        if (performanceOptions.mark) {
          performance.mark('intersection-observer-cleanup-start');
        }

        const cleanupStartTime = performance.now();

        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        if (observerRef.current) {
          observerRef.current.unobserve(element);
          observerRef.current.disconnect();
        }

        onCleanup?.();

        if (performanceOptions.mark) {
          performance.mark('intersection-observer-cleanup-end');
          performance.measure(
            'intersection-observer-cleanup',
            'intersection-observer-cleanup-start',
            'intersection-observer-cleanup-end'
          );
        }

        if (metricsOptions.track) {
          const cleanupEndTime = performance.now();
          metricsRef.current.cleanupTime = cleanupEndTime - cleanupStartTime;
          metricsRef.current.totalTime = cleanupEndTime - startTime;

          if (statsOptions.track) {
            statsRef.current.averageCleanupTime = metricsRef.current.cleanupTime;
            statsRef.current.averageTotalTime = metricsRef.current.totalTime;

            if (statsOptions.log) {
              console.log('Intersection Observer Stats:', statsRef.current);
            }

            statsOptions.onStatsUpdate?.(statsRef.current);
          }

          if (metricsOptions.log) {
            console.log('Intersection Observer Metrics:', metricsRef.current);
          }

          metricsOptions.onMetricsUpdate?.(metricsRef.current);
        }

        if (debug) {
          console.log('Intersection Observer cleaned up');
        }

        if (performanceOptions.log) {
          const measures = performance.getEntriesByType('measure');
          console.log('Performance measures:', measures);
        }

        events.onCleanup?.();
      } catch (error) {
        if (debug) {
          console.error('Error cleaning up intersection observer:', error);
        }
        if (metricsOptions.track) {
          metricsRef.current.errors++;

          if (statsOptions.track) {
            statsRef.current.errorRate = metricsRef.current.errors / (metricsRef.current.callbacks + metricsRef.current.errors);

            if (statsOptions.log) {
              console.log('Intersection Observer Stats:', statsRef.current);
            }

            statsOptions.onStatsUpdate?.(statsRef.current);
          }
        }
        events.onError?.(error instanceof Error ? error : new Error(String(error)));
        onError?.(error instanceof Error ? error : new Error(String(error)));
      }
    };
  }, [threshold, root, rootMargin, margin, thresholdPercentage, rootMarginOptions, options, onIntersectionChange, elementRef, onCleanup, onError, debug, performanceOptions, metricsOptions, statsOptions, events]);

  return [elementRef as React.RefObject<T>, isInViewport];
}

// Common margins
export const margins = {
  none: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  small: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10,
  },
  medium: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20,
  },
  large: {
    top: 50,
    right: 50,
    bottom: 50,
    left: 50,
  },
};

// Common threshold percentages
export const thresholdPercentages = {
  none: 0,
  quarter: 25,
  half: 50,
  threeQuarters: 75,
  full: 100,
};

// Common root margins
export const rootMargins = {
  none: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  small: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10,
  },
  medium: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20,
  },
  large: {
    top: 50,
    right: 50,
    bottom: 50,
    left: 50,
  },
};

// Common options
export const options = {
  default: {
    trackVisibility: false,
    delay: 0,
  },
  delayed: {
    trackVisibility: false,
    delay: 100,
  },
  tracked: {
    trackVisibility: true,
    delay: 0,
  },
  trackedAndDelayed: {
    trackVisibility: true,
    delay: 100,
  },
};

// Common performance options
export const performanceOptions = {
  none: {
    measure: false,
    mark: false,
    log: false,
  },
  basic: {
    measure: true,
    mark: true,
    log: false,
  },
  full: {
    measure: true,
    mark: true,
    log: true,
  },
};

// Common metrics options
export const metricsOptions = {
  none: {
    track: false,
    log: false,
    onMetricsUpdate: undefined,
  },
  basic: {
    track: true,
    log: false,
    onMetricsUpdate: undefined,
  },
  full: {
    track: true,
    log: true,
    onMetricsUpdate: undefined,
  },
};

// Common stats options
export const statsOptions = {
  none: {
    track: false,
    log: false,
    onStatsUpdate: undefined,
  },
  basic: {
    track: true,
    log: false,
    onStatsUpdate: undefined,
  },
  full: {
    track: true,
    log: true,
    onStatsUpdate: undefined,
  },
};

// Common events
export const events = {
  none: {
    onInit: undefined,
    onCallback: undefined,
    onCleanup: undefined,
    onError: undefined,
  },
  basic: {
    onInit: () => console.log('Intersection Observer initialized'),
    onCallback: () => console.log('Intersection Observer callback'),
    onCleanup: () => console.log('Intersection Observer cleaned up'),
    onError: (error: Error) => console.error('Intersection Observer error:', error),
  },
  full: {
    onInit: () => console.log('Intersection Observer initialized with full options'),
    onCallback: () => console.log('Intersection Observer callback with full options'),
    onCleanup: () => console.log('Intersection Observer cleaned up with full options'),
    onError: (error: Error) => console.error('Intersection Observer error with full options:', error),
  },
}; 