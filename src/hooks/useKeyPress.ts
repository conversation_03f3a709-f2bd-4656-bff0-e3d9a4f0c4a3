import { useEffect, useState } from 'react';

type KeyFilter = string | string[] | ((event: KeyboardEvent) => boolean);

export function useKeyPress(
  keyFilter: KeyFilter,
  options: {
    target?: Window | Document | HTMLElement;
    event?: 'keydown' | 'keyup' | 'keypress';
    exact?: boolean;
  } = {}
): boolean {
  const [pressed, setPressed] = useState(false);

  useEffect(() => {
    const target = options.target || window;
    const eventName = options.event || 'keydown';

    const downHandler = (event: KeyboardEvent) => {
      if (typeof keyFilter === 'function') {
        if (keyFilter(event)) {
          event.preventDefault();
          setPressed(true);
        }
      } else {
        const keys = Array.isArray(keyFilter) ? keyFilter : [keyFilter];
        const isExact = options.exact || false;

        if (isExact) {
          if (keys.includes(event.key)) {
            event.preventDefault();
            setPressed(true);
          }
        } else {
          if (keys.some((key) => event.key.toLowerCase() === key.toLowerCase())) {
            event.preventDefault();
            setPressed(true);
          }
        }
      }
    };

    const upHandler = (event: KeyboardEvent) => {
      if (typeof keyFilter === 'function') {
        if (keyFilter(event)) {
          setPressed(false);
        }
      } else {
        const keys = Array.isArray(keyFilter) ? keyFilter : [keyFilter];
        const isExact = options.exact || false;

        if (isExact) {
          if (keys.includes(event.key)) {
            setPressed(false);
          }
        } else {
          if (keys.some((key) => event.key.toLowerCase() === key.toLowerCase())) {
            setPressed(false);
          }
        }
      }
    };

    target.addEventListener(eventName, downHandler);
    target.addEventListener('keyup', upHandler);

    return () => {
      target.removeEventListener(eventName, downHandler);
      target.removeEventListener('keyup', upHandler);
    };
  }, [keyFilter, options.target, options.event, options.exact]);

  return pressed;
}

// Common key combinations
export const keyCombinations = {
  ctrlEnter: (event: KeyboardEvent) => event.ctrlKey && event.key === 'Enter',
  shiftEnter: (event: KeyboardEvent) => event.shiftKey && event.key === 'Enter',
  altEnter: (event: KeyboardEvent) => event.altKey && event.key === 'Enter',
  escape: (event: KeyboardEvent) => event.key === 'Escape',
  enter: (event: KeyboardEvent) => event.key === 'Enter',
  tab: (event: KeyboardEvent) => event.key === 'Tab',
  space: (event: KeyboardEvent) => event.key === ' ',
  arrowUp: (event: KeyboardEvent) => event.key === 'ArrowUp',
  arrowDown: (event: KeyboardEvent) => event.key === 'ArrowDown',
  arrowLeft: (event: KeyboardEvent) => event.key === 'ArrowLeft',
  arrowRight: (event: KeyboardEvent) => event.key === 'ArrowRight',
}; 