import { useEffect, useRef, useState } from 'react';

interface UseIsInViewportWithMarginAndThresholdProps {
  threshold?: number;
  root?: Element | null;
  rootMargin?: string;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  thresholdPercentage?: number;
}

export function useIsInViewportWithMarginAndThreshold<T extends HTMLElement = HTMLElement>(
  options: UseIsInViewportWithMarginAndThresholdProps = {}
): [React.RefObject<T>, boolean] {
  const {
    threshold = 0,
    root = null,
    rootMargin = '0px',
    margin = {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
    },
    thresholdPercentage = 0,
  } = options;

  const [isInViewport, setIsInViewport] = useState(false);
  const elementRef = useRef<T>(null);

  useEffect(() => {
    const element = elementRef.current;

    if (!element) {
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        const rect = entry.boundingClientRect;
        const rootRect = root ? root.getBoundingClientRect() : {
          top: 0,
          right: window.innerWidth,
          bottom: window.innerHeight,
          left: 0,
        };

        const elementArea = rect.width * rect.height;
        const intersectionArea = Math.max(0, Math.min(rect.right + margin.right, rootRect.right) - Math.max(rect.left - margin.left, rootRect.left)) *
          Math.max(0, Math.min(rect.bottom + margin.bottom, rootRect.bottom) - Math.max(rect.top - margin.top, rootRect.top));

        const intersectionPercentage = (intersectionArea / elementArea) * 100;

        setIsInViewport(intersectionPercentage >= thresholdPercentage);
      },
      {
        threshold,
        root,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, root, rootMargin, margin, thresholdPercentage]);

  return [elementRef, isInViewport];
}

// Common margins
export const margins = {
  none: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  small: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10,
  },
  medium: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20,
  },
  large: {
    top: 50,
    right: 50,
    bottom: 50,
    left: 50,
  },
};

// Common threshold percentages
export const thresholdPercentages = {
  none: 0,
  quarter: 25,
  half: 50,
  threeQuarters: 75,
  full: 100,
}; 