/**
 * Универсальная система интеграций с внешними сервисами
 * Поддержка OAuth, API ключей, webhooks, real-time соединений
 */

export interface IntegrationConfig {
  id: string;
  name: string;
  type: 'oauth' | 'api-key' | 'webhook' | 'websocket' | 'custom';
  enabled: boolean;
  credentials: IntegrationCredentials;
  endpoints: IntegrationEndpoint[];
  rateLimits: RateLimit[];
  retryPolicy: RetryPolicy;
  timeout: number;
  healthCheck: HealthCheckConfig;
  webhooks?: WebhookConfig[];
  realtime?: RealtimeConfig;
}

export interface IntegrationCredentials {
  type: 'oauth2' | 'bearer' | 'api-key' | 'basic' | 'custom';
  clientId?: string;
  clientSecret?: string;
  accessToken?: string;
  refreshToken?: string;
  apiKey?: string;
  username?: string;
  password?: string;
  customHeaders?: Record<string, string>;
  scopes?: string[];
  tokenEndpoint?: string;
  authEndpoint?: string;
}

export interface IntegrationEndpoint {
  id: string;
  name: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  headers?: Record<string, string>;
  queryParams?: Record<string, string>;
  bodyTemplate?: string;
  responseMapping?: ResponseMapping;
  caching?: CacheConfig;
}

export interface ResponseMapping {
  dataPath?: string;
  errorPath?: string;
  transformations?: DataTransformation[];
}

export interface DataTransformation {
  type: 'map' | 'filter' | 'reduce' | 'custom';
  field: string;
  operation: string;
  value?: any;
  customFunction?: (data: any) => any;
}

export interface RateLimit {
  requests: number;
  window: number; // in milliseconds
  burst?: number;
}

export interface RetryPolicy {
  maxRetries: number;
  backoffStrategy: 'linear' | 'exponential' | 'fixed';
  baseDelay: number;
  maxDelay: number;
  retryableErrors: string[];
}

export interface HealthCheckConfig {
  enabled: boolean;
  interval: number;
  endpoint: string;
  timeout: number;
  expectedStatus: number;
  expectedResponse?: string;
}

export interface WebhookConfig {
  id: string;
  url: string;
  events: string[];
  secret?: string;
  headers?: Record<string, string>;
  retryPolicy: RetryPolicy;
}

export interface RealtimeConfig {
  type: 'websocket' | 'sse' | 'polling';
  url: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval?: number;
  subscriptions?: string[];
}

export interface CacheConfig {
  enabled: boolean;
  ttl: number; // Time to live in milliseconds
  key?: string;
  invalidateOn?: string[];
}

export interface IntegrationResponse<T = any> {
  success: boolean;
  data?: T;
  error?: IntegrationError;
  metadata: {
    requestId: string;
    timestamp: Date;
    duration: number;
    cached: boolean;
    retryCount: number;
  };
}

export interface IntegrationError {
  code: string;
  message: string;
  details?: any;
  retryable: boolean;
}

export interface IntegrationMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  rateLimitHits: number;
  cacheHitRate: number;
  uptime: number;
  lastError?: IntegrationError;
}

export class IntegrationManager {
  private integrations = new Map<string, IntegrationConfig>();
  private connections = new Map<string, any>();
  private cache = new Map<string, { data: any; expires: number }>();
  private rateLimiters = new Map<string, RateLimiter>();
  private metrics = new Map<string, IntegrationMetrics>();
  private healthCheckers = new Map<string, NodeJS.Timeout>();

  constructor() {
    this.initialize();
  }

  /**
   * Инициализация системы интеграций
   */
  private initialize(): void {
    console.log('🔗 Initializing Integration Manager...');

    // Настраиваем очистку кэша
    setInterval(() => this.cleanupCache(), 60000); // Каждую минуту

    console.log('✅ Integration Manager initialized');
  }

  /**
   * Регистрирует новую интеграцию
   */
  registerIntegration(config: IntegrationConfig): void {
    this.integrations.set(config.id, config);
    
    // Инициализируем метрики
    this.metrics.set(config.id, {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      rateLimitHits: 0,
      cacheHitRate: 0,
      uptime: 100
    });

    // Настраиваем rate limiter
    if (config.rateLimits.length > 0) {
      this.rateLimiters.set(config.id, new RateLimiter(config.rateLimits[0]));
    }

    // Запускаем health check
    if (config.healthCheck.enabled) {
      this.startHealthCheck(config);
    }

    // Устанавливаем real-time соединение
    if (config.realtime) {
      this.establishRealtimeConnection(config);
    }

    console.log(`🔗 Registered integration: ${config.name}`);
  }

  /**
   * Выполняет запрос к интеграции
   */
  async executeRequest<T = any>(
    integrationId: string,
    endpointId: string,
    params?: Record<string, any>
  ): Promise<IntegrationResponse<T>> {
    const integration = this.integrations.get(integrationId);
    if (!integration) {
      throw new Error(`Integration ${integrationId} not found`);
    }

    const endpoint = integration.endpoints.find(ep => ep.id === endpointId);
    if (!endpoint) {
      throw new Error(`Endpoint ${endpointId} not found in integration ${integrationId}`);
    }

    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      // Проверяем rate limit
      if (!this.checkRateLimit(integrationId)) {
        this.updateMetrics(integrationId, false, 0, true);
        throw new Error('Rate limit exceeded');
      }

      // Проверяем кэш
      const cacheKey = this.generateCacheKey(integrationId, endpointId, params);
      const cachedResponse = this.getFromCache<T>(cacheKey);
      if (cachedResponse) {
        this.updateMetrics(integrationId, true, Date.now() - startTime, false, true);
        return {
          success: true,
          data: cachedResponse,
          metadata: {
            requestId,
            timestamp: new Date(),
            duration: Date.now() - startTime,
            cached: true,
            retryCount: 0
          }
        };
      }

      // Выполняем запрос с повторными попытками
      const response = await this.executeWithRetry(integration, endpoint, params, requestId);
      
      // Кэшируем ответ
      if (endpoint.caching?.enabled && response.success) {
        this.setCache(cacheKey, response.data, endpoint.caching.ttl);
      }

      const duration = Date.now() - startTime;
      this.updateMetrics(integrationId, response.success, duration);

      return {
        ...response,
        metadata: {
          requestId,
          timestamp: new Date(),
          duration,
          cached: false,
          retryCount: 0
        }
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateMetrics(integrationId, false, duration);

      return {
        success: false,
        error: {
          code: 'REQUEST_FAILED',
          message: (error as Error).message,
          retryable: false
        },
        metadata: {
          requestId,
          timestamp: new Date(),
          duration,
          cached: false,
          retryCount: 0
        }
      };
    }
  }

  /**
   * Выполняет запрос с повторными попытками
   */
  private async executeWithRetry<T>(
    integration: IntegrationConfig,
    endpoint: IntegrationEndpoint,
    params?: Record<string, any>,
    requestId?: string
  ): Promise<IntegrationResponse<T>> {
    let lastError: Error;
    let retryCount = 0;

    for (let attempt = 0; attempt <= integration.retryPolicy.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          const delay = this.calculateRetryDelay(integration.retryPolicy, attempt);
          await this.sleep(delay);
          retryCount++;
        }

        const response = await this.makeHttpRequest<T>(integration, endpoint, params);
        return { success: true, data: response };

      } catch (error) {
        lastError = error as Error;
        
        // Проверяем, можно ли повторить запрос
        if (!this.isRetryableError(integration.retryPolicy, lastError)) {
          break;
        }
      }
    }

    throw lastError!;
  }

  /**
   * Выполняет HTTP запрос
   */
  private async makeHttpRequest<T>(
    integration: IntegrationConfig,
    endpoint: IntegrationEndpoint,
    params?: Record<string, any>
  ): Promise<T> {
    const url = this.buildUrl(endpoint.url, params);
    const headers = await this.buildHeaders(integration, endpoint);
    const body = this.buildBody(endpoint, params);

    const requestOptions: RequestInit = {
      method: endpoint.method,
      headers,
      signal: AbortSignal.timeout(integration.timeout)
    };

    if (body && endpoint.method !== 'GET') {
      requestOptions.body = body;
    }

    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Применяем маппинг ответа
    if (endpoint.responseMapping) {
      return this.applyResponseMapping(data, endpoint.responseMapping);
    }

    return data;
  }

  /**
   * Строит URL с параметрами
   */
  private buildUrl(baseUrl: string, params?: Record<string, any>): string {
    if (!params) return baseUrl;

    const url = new URL(baseUrl);
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        url.searchParams.set(key, String(value));
      }
    });

    return url.toString();
  }

  /**
   * Строит заголовки запроса
   */
  private async buildHeaders(
    integration: IntegrationConfig,
    endpoint: IntegrationEndpoint
  ): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'A14-Browser-Integration/1.0',
      ...endpoint.headers
    };

    // Добавляем аутентификацию
    const authHeaders = await this.getAuthHeaders(integration.credentials);
    Object.assign(headers, authHeaders);

    return headers;
  }

  /**
   * Получает заголовки аутентификации
   */
  private async getAuthHeaders(credentials: IntegrationCredentials): Promise<Record<string, string>> {
    const headers: Record<string, string> = {};

    switch (credentials.type) {
      case 'bearer':
        if (credentials.accessToken) {
          headers['Authorization'] = `Bearer ${credentials.accessToken}`;
        }
        break;

      case 'api-key':
        if (credentials.apiKey) {
          headers['X-API-Key'] = credentials.apiKey;
        }
        break;

      case 'basic':
        if (credentials.username && credentials.password) {
          const encoded = btoa(`${credentials.username}:${credentials.password}`);
          headers['Authorization'] = `Basic ${encoded}`;
        }
        break;

      case 'oauth2':
        // Проверяем и обновляем токен если нужно
        const token = await this.ensureValidOAuthToken(credentials);
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }
        break;

      case 'custom':
        if (credentials.customHeaders) {
          Object.assign(headers, credentials.customHeaders);
        }
        break;
    }

    return headers;
  }

  /**
   * Обеспечивает валидный OAuth токен
   */
  private async ensureValidOAuthToken(credentials: IntegrationCredentials): Promise<string | null> {
    // Упрощенная реализация - в реальном проекте проверяем срок действия токена
    if (credentials.accessToken) {
      return credentials.accessToken;
    }

    // Обновляем токен используя refresh token
    if (credentials.refreshToken && credentials.tokenEndpoint) {
      try {
        const newToken = await this.refreshOAuthToken(credentials);
        credentials.accessToken = newToken;
        return newToken;
      } catch (error) {
        console.error('Failed to refresh OAuth token:', error);
      }
    }

    return null;
  }

  /**
   * Обновляет OAuth токен
   */
  private async refreshOAuthToken(credentials: IntegrationCredentials): Promise<string> {
    const response = await fetch(credentials.tokenEndpoint!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: credentials.refreshToken!,
        client_id: credentials.clientId!,
        client_secret: credentials.clientSecret!
      })
    });

    if (!response.ok) {
      throw new Error('Failed to refresh OAuth token');
    }

    const data = await response.json();
    return data.access_token;
  }

  /**
   * Строит тело запроса
   */
  private buildBody(endpoint: IntegrationEndpoint, params?: Record<string, any>): string | undefined {
    if (!endpoint.bodyTemplate || !params) {
      return undefined;
    }

    // Простая замена переменных в шаблоне
    let body = endpoint.bodyTemplate;
    Object.entries(params).forEach(([key, value]) => {
      body = body.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
    });

    return body;
  }

  /**
   * Применяет маппинг ответа
   */
  private applyResponseMapping<T>(data: any, mapping: ResponseMapping): T {
    let result = data;

    // Извлекаем данные по пути
    if (mapping.dataPath) {
      result = this.getNestedValue(data, mapping.dataPath);
    }

    // Применяем трансформации
    if (mapping.transformations) {
      result = this.applyTransformations(result, mapping.transformations);
    }

    return result;
  }

  /**
   * Получает вложенное значение по пути
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Применяет трансформации данных
   */
  private applyTransformations(data: any, transformations: DataTransformation[]): any {
    let result = data;

    for (const transformation of transformations) {
      switch (transformation.type) {
        case 'map':
          if (Array.isArray(result)) {
            result = result.map(item => ({
              ...item,
              [transformation.field]: transformation.value
            }));
          }
          break;

        case 'filter':
          if (Array.isArray(result)) {
            result = result.filter(item => 
              this.evaluateFilterCondition(item, transformation)
            );
          }
          break;

        case 'custom':
          if (transformation.customFunction) {
            result = transformation.customFunction(result);
          }
          break;
      }
    }

    return result;
  }

  /**
   * Оценивает условие фильтра
   */
  private evaluateFilterCondition(item: any, transformation: DataTransformation): boolean {
    const fieldValue = this.getNestedValue(item, transformation.field);
    
    switch (transformation.operation) {
      case 'equals':
        return fieldValue === transformation.value;
      case 'not_equals':
        return fieldValue !== transformation.value;
      case 'contains':
        return String(fieldValue).includes(String(transformation.value));
      case 'greater_than':
        return Number(fieldValue) > Number(transformation.value);
      case 'less_than':
        return Number(fieldValue) < Number(transformation.value);
      default:
        return true;
    }
  }

  /**
   * Устанавливает real-time соединение
   */
  private establishRealtimeConnection(integration: IntegrationConfig): void {
    if (!integration.realtime) return;

    const config = integration.realtime;
    
    switch (config.type) {
      case 'websocket':
        this.establishWebSocketConnection(integration.id, config);
        break;
      case 'sse':
        this.establishSSEConnection(integration.id, config);
        break;
      case 'polling':
        this.establishPollingConnection(integration.id, config);
        break;
    }
  }

  /**
   * Устанавливает WebSocket соединение
   */
  private establishWebSocketConnection(integrationId: string, config: RealtimeConfig): void {
    const ws = new WebSocket(config.url);
    
    ws.onopen = () => {
      console.log(`🔗 WebSocket connected for ${integrationId}`);
      this.connections.set(integrationId, ws);
      
      // Подписываемся на события
      if (config.subscriptions) {
        config.subscriptions.forEach(subscription => {
          ws.send(JSON.stringify({ type: 'subscribe', channel: subscription }));
        });
      }
    };

    ws.onmessage = (event) => {
      this.handleRealtimeMessage(integrationId, JSON.parse(event.data));
    };

    ws.onclose = () => {
      console.log(`🔗 WebSocket disconnected for ${integrationId}`);
      this.connections.delete(integrationId);
      
      // Переподключение
      setTimeout(() => {
        this.establishWebSocketConnection(integrationId, config);
      }, config.reconnectInterval);
    };

    ws.onerror = (error) => {
      console.error(`🔗 WebSocket error for ${integrationId}:`, error);
    };
  }

  /**
   * Устанавливает SSE соединение
   */
  private establishSSEConnection(integrationId: string, config: RealtimeConfig): void {
    const eventSource = new EventSource(config.url);
    
    eventSource.onopen = () => {
      console.log(`🔗 SSE connected for ${integrationId}`);
      this.connections.set(integrationId, eventSource);
    };

    eventSource.onmessage = (event) => {
      this.handleRealtimeMessage(integrationId, JSON.parse(event.data));
    };

    eventSource.onerror = (error) => {
      console.error(`🔗 SSE error for ${integrationId}:`, error);
    };
  }

  /**
   * Устанавливает polling соединение
   */
  private establishPollingConnection(integrationId: string, config: RealtimeConfig): void {
    const poll = async () => {
      try {
        const response = await fetch(config.url);
        const data = await response.json();
        this.handleRealtimeMessage(integrationId, data);
      } catch (error) {
        console.error(`🔗 Polling error for ${integrationId}:`, error);
      }
    };

    const interval = setInterval(poll, config.reconnectInterval);
    this.connections.set(integrationId, { type: 'polling', interval });
  }

  /**
   * Обрабатывает real-time сообщения
   */
  private handleRealtimeMessage(integrationId: string, data: any): void {
    // Эмитируем событие для подписчиков
    const event = new CustomEvent(`integration:${integrationId}:message`, {
      detail: data
    });
    window.dispatchEvent(event);
  }

  /**
   * Запускает health check
   */
  private startHealthCheck(integration: IntegrationConfig): void {
    const check = async () => {
      try {
        const response = await fetch(integration.healthCheck.endpoint, {
          method: 'GET',
          signal: AbortSignal.timeout(integration.healthCheck.timeout)
        });

        const isHealthy = response.status === integration.healthCheck.expectedStatus;
        this.updateHealthStatus(integration.id, isHealthy);

      } catch (error) {
        this.updateHealthStatus(integration.id, false);
      }
    };

    const interval = setInterval(check, integration.healthCheck.interval);
    this.healthCheckers.set(integration.id, interval);
  }

  /**
   * Обновляет статус здоровья интеграции
   */
  private updateHealthStatus(integrationId: string, isHealthy: boolean): void {
    const metrics = this.metrics.get(integrationId);
    if (metrics) {
      metrics.uptime = isHealthy ? Math.min(metrics.uptime + 1, 100) : Math.max(metrics.uptime - 5, 0);
    }

    // Эмитируем событие изменения статуса
    const event = new CustomEvent(`integration:${integrationId}:health`, {
      detail: { healthy: isHealthy }
    });
    window.dispatchEvent(event);
  }

  // Вспомогательные методы

  private checkRateLimit(integrationId: string): boolean {
    const rateLimiter = this.rateLimiters.get(integrationId);
    return rateLimiter ? rateLimiter.checkLimit() : true;
  }

  private updateMetrics(
    integrationId: string,
    success: boolean,
    duration: number,
    rateLimitHit = false,
    cacheHit = false
  ): void {
    const metrics = this.metrics.get(integrationId);
    if (!metrics) return;

    metrics.totalRequests++;
    if (success) {
      metrics.successfulRequests++;
    } else {
      metrics.failedRequests++;
    }

    // Обновляем среднее время ответа
    metrics.averageResponseTime = 
      (metrics.averageResponseTime * (metrics.totalRequests - 1) + duration) / metrics.totalRequests;

    if (rateLimitHit) {
      metrics.rateLimitHits++;
    }

    if (cacheHit) {
      metrics.cacheHitRate = 
        (metrics.cacheHitRate * (metrics.totalRequests - 1) + 100) / metrics.totalRequests;
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCacheKey(integrationId: string, endpointId: string, params?: Record<string, any>): string {
    const paramsStr = params ? JSON.stringify(params) : '';
    return `${integrationId}:${endpointId}:${btoa(paramsStr)}`;
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && cached.expires > Date.now()) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      expires: Date.now() + ttl
    });
  }

  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, cached] of this.cache.entries()) {
      if (cached.expires <= now) {
        this.cache.delete(key);
      }
    }
  }

  private calculateRetryDelay(policy: RetryPolicy, attempt: number): number {
    switch (policy.backoffStrategy) {
      case 'linear':
        return Math.min(policy.baseDelay * attempt, policy.maxDelay);
      case 'exponential':
        return Math.min(policy.baseDelay * Math.pow(2, attempt - 1), policy.maxDelay);
      case 'fixed':
        return policy.baseDelay;
      default:
        return policy.baseDelay;
    }
  }

  private isRetryableError(policy: RetryPolicy, error: Error): boolean {
    return policy.retryableErrors.some(errorCode => 
      error.message.includes(errorCode)
    );
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Получает метрики интеграции
   */
  getMetrics(integrationId: string): IntegrationMetrics | null {
    return this.metrics.get(integrationId) || null;
  }

  /**
   * Получает все интеграции
   */
  getIntegrations(): IntegrationConfig[] {
    return Array.from(this.integrations.values());
  }

  /**
   * Отключает интеграцию
   */
  disableIntegration(integrationId: string): void {
    const integration = this.integrations.get(integrationId);
    if (integration) {
      integration.enabled = false;
      
      // Закрываем соединения
      const connection = this.connections.get(integrationId);
      if (connection) {
        if (connection instanceof WebSocket) {
          connection.close();
        } else if (connection instanceof EventSource) {
          connection.close();
        } else if (connection.type === 'polling') {
          clearInterval(connection.interval);
        }
        this.connections.delete(integrationId);
      }

      // Останавливаем health check
      const healthChecker = this.healthCheckers.get(integrationId);
      if (healthChecker) {
        clearInterval(healthChecker);
        this.healthCheckers.delete(integrationId);
      }
    }
  }

  /**
   * Уничтожает менеджер интеграций
   */
  destroy(): void {
    // Закрываем все соединения
    this.connections.forEach((connection, integrationId) => {
      this.disableIntegration(integrationId);
    });

    // Очищаем все данные
    this.integrations.clear();
    this.connections.clear();
    this.cache.clear();
    this.rateLimiters.clear();
    this.metrics.clear();
    this.healthCheckers.clear();
  }
}

// Rate Limiter класс
class RateLimiter {
  private requests: number[] = [];
  private limit: RateLimit;

  constructor(limit: RateLimit) {
    this.limit = limit;
  }

  checkLimit(): boolean {
    const now = Date.now();
    
    // Удаляем старые запросы
    this.requests = this.requests.filter(timestamp => 
      now - timestamp < this.limit.window
    );

    // Проверяем лимит
    if (this.requests.length >= this.limit.requests) {
      return false;
    }

    // Добавляем текущий запрос
    this.requests.push(now);
    return true;
  }
}

// Предустановленные интеграции
export class PopularIntegrations {
  static createGoogleIntegration(): IntegrationConfig {
    return {
      id: 'google',
      name: 'Google APIs',
      type: 'oauth',
      enabled: true,
      credentials: {
        type: 'oauth2',
        clientId: process.env.GOOGLE_CLIENT_ID || '',
        clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
        scopes: ['profile', 'email'],
        authEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
        tokenEndpoint: 'https://oauth2.googleapis.com/token'
      },
      endpoints: [
        {
          id: 'user-profile',
          name: 'Get User Profile',
          method: 'GET',
          url: 'https://www.googleapis.com/oauth2/v2/userinfo',
          responseMapping: {
            dataPath: '',
            transformations: [
              {
                type: 'map',
                field: 'provider',
                operation: 'set',
                value: 'google'
              }
            ]
          },
          caching: {
            enabled: true,
            ttl: 300000 // 5 minutes
          }
        }
      ],
      rateLimits: [
        {
          requests: 100,
          window: 60000 // 1 minute
        }
      ],
      retryPolicy: {
        maxRetries: 3,
        backoffStrategy: 'exponential',
        baseDelay: 1000,
        maxDelay: 10000,
        retryableErrors: ['RATE_LIMITED', 'TIMEOUT', '5']
      },
      timeout: 10000,
      healthCheck: {
        enabled: true,
        interval: 300000, // 5 minutes
        endpoint: 'https://www.googleapis.com/oauth2/v2/userinfo',
        timeout: 5000,
        expectedStatus: 200
      }
    };
  }

  static createGitHubIntegration(): IntegrationConfig {
    return {
      id: 'github',
      name: 'GitHub API',
      type: 'oauth',
      enabled: true,
      credentials: {
        type: 'oauth2',
        clientId: process.env.GITHUB_CLIENT_ID || '',
        clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
        scopes: ['user', 'repo'],
        authEndpoint: 'https://github.com/login/oauth/authorize',
        tokenEndpoint: 'https://github.com/login/oauth/access_token'
      },
      endpoints: [
        {
          id: 'user-repos',
          name: 'Get User Repositories',
          method: 'GET',
          url: 'https://api.github.com/user/repos',
          queryParams: {
            sort: 'updated',
            per_page: '50'
          },
          responseMapping: {
            transformations: [
              {
                type: 'map',
                field: 'provider',
                operation: 'set',
                value: 'github'
              }
            ]
          },
          caching: {
            enabled: true,
            ttl: 600000 // 10 minutes
          }
        },
        {
          id: 'create-repo',
          name: 'Create Repository',
          method: 'POST',
          url: 'https://api.github.com/user/repos',
          bodyTemplate: '{"name": "{{name}}", "description": "{{description}}", "private": {{private}}}'
        }
      ],
      rateLimits: [
        {
          requests: 5000,
          window: 3600000 // 1 hour
        }
      ],
      retryPolicy: {
        maxRetries: 3,
        backoffStrategy: 'exponential',
        baseDelay: 1000,
        maxDelay: 10000,
        retryableErrors: ['403', '429', '5']
      },
      timeout: 15000,
      healthCheck: {
        enabled: true,
        interval: 300000,
        endpoint: 'https://api.github.com/user',
        timeout: 5000,
        expectedStatus: 200
      }
    };
  }

  static createSlackIntegration(): IntegrationConfig {
    return {
      id: 'slack',
      name: 'Slack API',
      type: 'oauth',
      enabled: true,
      credentials: {
        type: 'oauth2',
        clientId: process.env.SLACK_CLIENT_ID || '',
        clientSecret: process.env.SLACK_CLIENT_SECRET || '',
        scopes: ['chat:write', 'channels:read'],
        authEndpoint: 'https://slack.com/oauth/v2/authorize',
        tokenEndpoint: 'https://slack.com/api/oauth.v2.access'
      },
      endpoints: [
        {
          id: 'send-message',
          name: 'Send Message',
          method: 'POST',
          url: 'https://slack.com/api/chat.postMessage',
          headers: {
            'Content-Type': 'application/json'
          },
          bodyTemplate: '{"channel": "{{channel}}", "text": "{{text}}"}'
        },
        {
          id: 'list-channels',
          name: 'List Channels',
          method: 'GET',
          url: 'https://slack.com/api/conversations.list',
          caching: {
            enabled: true,
            ttl: 300000
          }
        }
      ],
      rateLimits: [
        {
          requests: 100,
          window: 60000
        }
      ],
      retryPolicy: {
        maxRetries: 3,
        backoffStrategy: 'exponential',
        baseDelay: 1000,
        maxDelay: 10000,
        retryableErrors: ['429', '5']
      },
      timeout: 10000,
      healthCheck: {
        enabled: true,
        interval: 300000,
        endpoint: 'https://slack.com/api/auth.test',
        timeout: 5000,
        expectedStatus: 200
      },
      realtime: {
        type: 'websocket',
        url: 'wss://wss-primary.slack.com/websocket',
        reconnectInterval: 5000,
        maxReconnectAttempts: 10,
        heartbeatInterval: 30000
      }
    };
  }

  static createStripeIntegration(): IntegrationConfig {
    return {
      id: 'stripe',
      name: 'Stripe API',
      type: 'api-key',
      enabled: true,
      credentials: {
        type: 'bearer',
        accessToken: process.env.STRIPE_SECRET_KEY || ''
      },
      endpoints: [
        {
          id: 'create-payment-intent',
          name: 'Create Payment Intent',
          method: 'POST',
          url: 'https://api.stripe.com/v1/payment_intents',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          bodyTemplate: 'amount={{amount}}&currency={{currency}}&payment_method_types[]=card'
        },
        {
          id: 'list-customers',
          name: 'List Customers',
          method: 'GET',
          url: 'https://api.stripe.com/v1/customers',
          caching: {
            enabled: true,
            ttl: 300000
          }
        }
      ],
      rateLimits: [
        {
          requests: 100,
          window: 1000
        }
      ],
      retryPolicy: {
        maxRetries: 3,
        backoffStrategy: 'exponential',
        baseDelay: 1000,
        maxDelay: 10000,
        retryableErrors: ['429', '5']
      },
      timeout: 15000,
      healthCheck: {
        enabled: true,
        interval: 300000,
        endpoint: 'https://api.stripe.com/v1/account',
        timeout: 5000,
        expectedStatus: 200
      },
      webhooks: [
        {
          id: 'payment-webhook',
          url: '/api/webhooks/stripe',
          events: ['payment_intent.succeeded', 'payment_intent.payment_failed'],
          secret: process.env.STRIPE_WEBHOOK_SECRET,
          retryPolicy: {
            maxRetries: 3,
            backoffStrategy: 'exponential',
            baseDelay: 1000,
            maxDelay: 10000,
            retryableErrors: ['5']
          }
        }
      ]
    };
  }

  static createTwilioIntegration(): IntegrationConfig {
    return {
      id: 'twilio',
      name: 'Twilio API',
      type: 'api-key',
      enabled: true,
      credentials: {
        type: 'basic',
        username: process.env.TWILIO_ACCOUNT_SID || '',
        password: process.env.TWILIO_AUTH_TOKEN || ''
      },
      endpoints: [
        {
          id: 'send-sms',
          name: 'Send SMS',
          method: 'POST',
          url: 'https://api.twilio.com/2010-04-01/Accounts/{{accountSid}}/Messages.json',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          bodyTemplate: 'From={{from}}&To={{to}}&Body={{body}}'
        },
        {
          id: 'make-call',
          name: 'Make Call',
          method: 'POST',
          url: 'https://api.twilio.com/2010-04-01/Accounts/{{accountSid}}/Calls.json',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          bodyTemplate: 'From={{from}}&To={{to}}&Url={{twimlUrl}}'
        }
      ],
      rateLimits: [
        {
          requests: 1000,
          window: 60000
        }
      ],
      retryPolicy: {
        maxRetries: 3,
        backoffStrategy: 'exponential',
        baseDelay: 1000,
        maxDelay: 10000,
        retryableErrors: ['429', '5']
      },
      timeout: 10000,
      healthCheck: {
        enabled: true,
        interval: 300000,
        endpoint: 'https://api.twilio.com/2010-04-01/Accounts.json',
        timeout: 5000,
        expectedStatus: 200
      }
    };
  }
}

// Глобальный экземпляр
export const integrationManager = new IntegrationManager();

// Инициализация популярных интеграций
export function initializePopularIntegrations(): void {
  const integrations = [
    PopularIntegrations.createGoogleIntegration(),
    PopularIntegrations.createGitHubIntegration(),
    PopularIntegrations.createSlackIntegration(),
    PopularIntegrations.createStripeIntegration(),
    PopularIntegrations.createTwilioIntegration()
  ];

  integrations.forEach(integration => {
    integrationManager.registerIntegration(integration);
  });

  console.log('🔗 Popular integrations initialized');
}
