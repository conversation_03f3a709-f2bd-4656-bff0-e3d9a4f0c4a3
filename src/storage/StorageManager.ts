import { EventEmitter } from 'events';
import { NetworkManager } from '../network/NetworkManager';
import { ErrorManager } from '../error/ErrorManager';
import { PerformanceMonitor } from '../performance/PerformanceMonitor';

interface StorageConfig {
  type: 'localStorage' | 'indexedDB' | 'memory';
  encryption: boolean;
  compression: boolean;
  syncInterval: number;
  maxSize: number;
  backupEnabled: boolean;
  backupInterval: number;
  version: number;
  prefix: string;
}

interface StorageItem {
  key: string;
  value: any;
  timestamp: number;
  version: number;
  metadata?: {
    size: number;
    type: string;
    tags?: string[];
    [key: string]: any;
  };
}

interface StorageStats {
  totalItems: number;
  totalSize: number;
  lastSync: number;
  lastBackup: number;
  version: number;
}

export class StorageManager {
  private static instance: StorageManager;
  private config: StorageConfig;
  private eventEmitter: EventEmitter;
  private networkManager: NetworkManager;
  private errorManager: ErrorManager;
  private performanceMonitor: PerformanceMonitor;
  private db: IDBDatabase | null;
  private syncTimer: NodeJS.Timeout | null;
  private backupTimer: NodeJS.Timeout | null;

  private constructor() {
    this.config = {
      type: 'indexedDB',
      encryption: true,
      compression: true,
      syncInterval: 300000, // 5 minutes
      maxSize: 50 * 1024 * 1024, // 50MB
      backupEnabled: true,
      backupInterval: 3600000, // 1 hour
      version: 1,
      prefix: 'novabrowser_',
    };

    this.eventEmitter = new EventEmitter();
    this.networkManager = NetworkManager.getInstance();
    this.errorManager = ErrorManager.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.db = null;
    this.syncTimer = null;
    this.backupTimer = null;

    // Initialize storage
    this.initialize();
  }

  public static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager();
    }
    return StorageManager.instance;
  }

  private async initialize(): Promise<void> {
    try {
      switch (this.config.type) {
        case 'indexedDB':
          await this.initializeIndexedDB();
          break;
        case 'localStorage':
          this.initializeLocalStorage();
          break;
        case 'memory':
          // Memory storage is already initialized
          break;
      }

      // Start sync and backup timers
      this.startSyncTimer();
      this.startBackupTimer();

      this.eventEmitter.emit('initialized');
    } catch (error) {
      this.errorManager.handleError(error, {
        context: 'StorageManager',
        operation: 'initialize',
      });
    }
  }

  private async initializeIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('NovaBrowserStorage', this.config.version);

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB'));
      };

      request.onsuccess = (event) => {
        this.db = (event.target as IDBOpenDBRequest).result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('items')) {
          db.createObjectStore('items', { keyPath: 'key' });
        }
      };
    });
  }

  private initializeLocalStorage(): void {
    // Check if localStorage is available
    if (!window.localStorage) {
      throw new Error('localStorage is not available');
    }
  }

  private startSyncTimer(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }

    this.syncTimer = setInterval(() => {
      this.sync().catch((error) => {
        this.errorManager.handleError(error, {
          context: 'StorageManager',
          operation: 'sync',
        });
      });
    }, this.config.syncInterval);
  }

  private startBackupTimer(): void {
    if (this.backupTimer) {
      clearInterval(this.backupTimer);
    }

    if (this.config.backupEnabled) {
      this.backupTimer = setInterval(() => {
        this.backup().catch((error) => {
          this.errorManager.handleError(error, {
            context: 'StorageManager',
            operation: 'backup',
          });
        });
      }, this.config.backupInterval);
    }
  }

  public async set(key: string, value: any, options: Partial<StorageItem> = {}): Promise<void> {
    const startTime = performance.now();

    try {
      const item: StorageItem = {
        key: this.getPrefixedKey(key),
        value,
        timestamp: Date.now(),
        version: this.config.version,
        metadata: {
          size: this.calculateSize(value),
          type: typeof value,
          ...options.metadata,
        },
      };

      // Apply compression if enabled
      if (this.config.compression) {
        item.value = await this.compress(item.value);
      }

      // Apply encryption if enabled
      if (this.config.encryption) {
        item.value = await this.encrypt(item.value);
      }

      // Store the item
      await this.storeItem(item);

      // Track performance
      const duration = performance.now() - startTime;
      this.performanceMonitor.trackMetric('storage_set_duration', duration, {
        key,
        size: item.metadata.size,
      });

      this.eventEmitter.emit('itemSet', { key, value });
    } catch (error) {
      this.errorManager.handleError(error, {
        context: 'StorageManager',
        operation: 'set',
        key,
      });
      throw error;
    }
  }

  public async get(key: string): Promise<any> {
    const startTime = performance.now();

    try {
      const item = await this.retrieveItem(this.getPrefixedKey(key));

      if (!item) {
        return null;
      }

      let value = item.value;

      // Apply decryption if enabled
      if (this.config.encryption) {
        value = await this.decrypt(value);
      }

      // Apply decompression if enabled
      if (this.config.compression) {
        value = await this.decompress(value);
      }

      // Track performance
      const duration = performance.now() - startTime;
      this.performanceMonitor.trackMetric('storage_get_duration', duration, {
        key,
        size: item.metadata.size,
      });

      this.eventEmitter.emit('itemGet', { key, value });
      return value;
    } catch (error) {
      this.errorManager.handleError(error, {
        context: 'StorageManager',
        operation: 'get',
        key,
      });
      throw error;
    }
  }

  public async remove(key: string): Promise<void> {
    try {
      await this.removeItem(this.getPrefixedKey(key));
      this.eventEmitter.emit('itemRemoved', { key });
    } catch (error) {
      this.errorManager.handleError(error, {
        context: 'StorageManager',
        operation: 'remove',
        key,
      });
      throw error;
    }
  }

  public async clear(): Promise<void> {
    try {
      switch (this.config.type) {
        case 'indexedDB':
          await this.clearIndexedDB();
          break;
        case 'localStorage':
          this.clearLocalStorage();
          break;
        case 'memory':
          // Memory storage is cleared automatically
          break;
      }

      this.eventEmitter.emit('cleared');
    } catch (error) {
      this.errorManager.handleError(error, {
        context: 'StorageManager',
        operation: 'clear',
      });
      throw error;
    }
  }

  public async getStats(): Promise<StorageStats> {
    try {
      const stats = await this.calculateStats();
      this.eventEmitter.emit('statsUpdated', stats);
      return stats;
    } catch (error) {
      this.errorManager.handleError(error, {
        context: 'StorageManager',
        operation: 'getStats',
      });
      throw error;
    }
  }

  private async storeItem(item: StorageItem): Promise<void> {
    switch (this.config.type) {
      case 'indexedDB':
        await this.storeInIndexedDB(item);
        break;
      case 'localStorage':
        this.storeInLocalStorage(item);
        break;
      case 'memory':
        // Memory storage is handled by the class itself
        break;
    }
  }

  private async retrieveItem(key: string): Promise<StorageItem | null> {
    switch (this.config.type) {
      case 'indexedDB':
        return this.retrieveFromIndexedDB(key);
      case 'localStorage':
        return this.retrieveFromLocalStorage(key);
      case 'memory':
        return null; // Memory storage is handled by the class itself
    }
  }

  private async removeItem(key: string): Promise<void> {
    switch (this.config.type) {
      case 'indexedDB':
        await this.removeFromIndexedDB(key);
        break;
      case 'localStorage':
        this.removeFromLocalStorage(key);
        break;
      case 'memory':
        // Memory storage is handled by the class itself
        break;
    }
  }

  private async storeInIndexedDB(item: StorageItem): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('IndexedDB not initialized'));
        return;
      }

      const transaction = this.db.transaction(['items'], 'readwrite');
      const store = transaction.objectStore('items');
      const request = store.put(item);

      request.onerror = () => {
        reject(new Error('Failed to store item in IndexedDB'));
      };

      request.onsuccess = () => {
        resolve();
      };
    });
  }

  private async retrieveFromIndexedDB(key: string): Promise<StorageItem | null> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('IndexedDB not initialized'));
        return;
      }

      const transaction = this.db.transaction(['items'], 'readonly');
      const store = transaction.objectStore('items');
      const request = store.get(key);

      request.onerror = () => {
        reject(new Error('Failed to retrieve item from IndexedDB'));
      };

      request.onsuccess = () => {
        resolve(request.result || null);
      };
    });
  }

  private async removeFromIndexedDB(key: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('IndexedDB not initialized'));
        return;
      }

      const transaction = this.db.transaction(['items'], 'readwrite');
      const store = transaction.objectStore('items');
      const request = store.delete(key);

      request.onerror = () => {
        reject(new Error('Failed to remove item from IndexedDB'));
      };

      request.onsuccess = () => {
        resolve();
      };
    });
  }

  private storeInLocalStorage(item: StorageItem): void {
    localStorage.setItem(item.key, JSON.stringify(item));
  }

  private retrieveFromLocalStorage(key: string): StorageItem | null {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : null;
  }

  private removeFromLocalStorage(key: string): void {
    localStorage.removeItem(key);
  }

  private async clearIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('IndexedDB not initialized'));
        return;
      }

      const transaction = this.db.transaction(['items'], 'readwrite');
      const store = transaction.objectStore('items');
      const request = store.clear();

      request.onerror = () => {
        reject(new Error('Failed to clear IndexedDB'));
      };

      request.onsuccess = () => {
        resolve();
      };
    });
  }

  private clearLocalStorage(): void {
    Object.keys(localStorage)
      .filter(key => key.startsWith(this.config.prefix))
      .forEach(key => localStorage.removeItem(key));
  }

  private async sync(): Promise<void> {
    // Implement sync logic with remote storage
    this.eventEmitter.emit('syncStarted');
    // ... sync implementation
    this.eventEmitter.emit('syncCompleted');
  }

  private async backup(): Promise<void> {
    // Implement backup logic
    this.eventEmitter.emit('backupStarted');
    // ... backup implementation
    this.eventEmitter.emit('backupCompleted');
  }

  private async calculateStats(): Promise<StorageStats> {
    // Implement stats calculation
    return {
      totalItems: 0,
      totalSize: 0,
      lastSync: Date.now(),
      lastBackup: Date.now(),
      version: this.config.version,
    };
  }

  private calculateSize(value: any): number {
    return new Blob([JSON.stringify(value)]).size;
  }

  private getPrefixedKey(key: string): string {
    return `${this.config.prefix}${key}`;
  }

  private async compress(data: any): Promise<any> {
    // Implement compression logic
    return data;
  }

  private async decompress(data: any): Promise<any> {
    // Implement decompression logic
    return data;
  }

  private async encrypt(data: any): Promise<any> {
    // Implement encryption logic
    return data;
  }

  private async decrypt(data: any): Promise<any> {
    // Implement decryption logic
    return data;
  }

  public setConfig(config: Partial<StorageConfig>): void {
    this.config = {
      ...this.config,
      ...config,
    };
    this.eventEmitter.emit('configUpdated', this.config);
  }

  public getConfig(): StorageConfig {
    return { ...this.config };
  }

  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  public cleanup(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    if (this.backupTimer) {
      clearInterval(this.backupTimer);
    }
    this.eventEmitter.removeAllListeners();
  }
} 