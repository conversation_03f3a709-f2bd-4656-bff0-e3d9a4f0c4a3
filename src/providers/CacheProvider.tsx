import React, { createContext, useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { compress, decompress } from 'lz-string';

interface CacheContextType {
  cache: {
    enabled: boolean;
    maxSize: number;
    maxAge: number;
    strategy: 'network-first' | 'cache-first' | 'stale-while-revalidate';
    patterns: string[];
    excluded: string[];
    version: string;
    lastCleanup: number;
    size: number;
    entries: number;
    hits: number;
    misses: number;
    updates: number;
    deletes: number;
    errors: number;
    prefetchEnabled: boolean;
    prefetchPatterns: string[];
    compressionEnabled: boolean;
    compressionLevel: number;
  };
  setCacheEnabled: (enabled: boolean) => void;
  setCacheMaxSize: (maxSize: number) => void;
  setCacheMaxAge: (maxAge: number) => void;
  setCacheStrategy: (strategy: CacheContextType['cache']['strategy']) => void;
  addCachePattern: (pattern: string) => void;
  removeCachePattern: (pattern: string) => void;
  addExcludedPattern: (pattern: string) => void;
  removeExcludedPattern: (pattern: string) => void;
  setCacheVersion: (version: string) => void;
  clearCache: () => Promise<void>;
  getCacheSize: () => Promise<number>;
  getCacheEntries: () => Promise<number>;
  getCacheHits: () => number;
  getCacheMisses: () => number;
  getCacheUpdates: () => number;
  getCacheDeletes: () => number;
  getCacheErrors: () => number;
  getCacheStats: () => CacheContextType['cache'];
  prefetchResource: (url: string) => Promise<void>;
  setPrefetchEnabled: (enabled: boolean) => void;
  addPrefetchPattern: (pattern: string) => void;
  removePrefetchPattern: (pattern: string) => void;
  setCompressionEnabled: (enabled: boolean) => void;
  setCompressionLevel: (level: number) => void;
}

const CacheContext = createContext<CacheContextType | null>(null);

export const useCache = () => {
  const context = useContext(CacheContext);
  if (!context) {
    throw new Error('useCache must be used within a CacheProvider');
  }
  return context;
};

interface CacheProviderProps {
  children: React.ReactNode;
}

export const CacheProvider: React.FC<CacheProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const [cache, setCacheState] = useState<CacheContextType['cache']>({
    enabled: true,
    maxSize: 100 * 1024 * 1024, // 100MB
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    strategy: 'network-first',
    patterns: ['*'],
    excluded: [],
    version: '1.0.0',
    lastCleanup: Date.now(),
    size: 0,
    entries: 0,
    hits: 0,
    misses: 0,
    updates: 0,
    deletes: 0,
    errors: 0,
    prefetchEnabled: false,
    prefetchPatterns: [],
    compressionEnabled: true,
    compressionLevel: 6,
  });

  useEffect(() => {
    const cleanup = async () => {
      try {
        const cacheStorage = await caches.open('browser-cache');
        const keys = await cacheStorage.keys();
        const now = Date.now();
        let totalSize = 0;

        for (const request of keys) {
          const response = await cacheStorage.match(request);
          if (response) {
            const headers = response.headers;
            const date = headers.get('date');
            const size = parseInt(headers.get('content-length') || '0', 10);
            
            if (date) {
              const age = now - new Date(date).getTime();
              if (age > cache.maxAge || totalSize + size > cache.maxSize) {
                await cacheStorage.delete(request);
                setCacheState((prev) => ({
                  ...prev,
                  deletes: prev.deletes + 1,
                  size: prev.size - size,
                }));
              } else {
                totalSize += size;
              }
            }
          }
        }

        if (cache.prefetchEnabled) {
          for (const pattern of cache.prefetchPatterns) {
            try {
              const response = await fetch(pattern);
              if (response.ok) {
                const cacheStorage = await caches.open('browser-cache');
                await cacheStorage.put(pattern, response);
              }
            } catch (error) {
              console.error(`Prefetch failed for ${pattern}:`, error);
            }
          }
        }

        setCacheState((prev) => ({
          ...prev,
          lastCleanup: now,
          size: totalSize,
        }));
      } catch (error) {
        console.error('Cache cleanup failed:', error);
        setCacheState((prev) => ({
          ...prev,
          errors: prev.errors + 1,
        }));
      }
    };

    const interval = setInterval(cleanup, 60 * 60 * 1000); // Cleanup every hour

    return () => {
      clearInterval(interval);
    };
  }, [cache.maxAge, cache.maxSize, cache.prefetchEnabled, cache.prefetchPatterns]);

  const setCacheEnabled = (enabled: boolean) => {
    setCacheState((prev) => ({
      ...prev,
      enabled,
    }));
  };

  const setCacheMaxSize = (maxSize: number) => {
    setCacheState((prev) => ({
      ...prev,
      maxSize,
    }));
  };

  const setCacheMaxAge = (maxAge: number) => {
    setCacheState((prev) => ({
      ...prev,
      maxAge,
    }));
  };

  const setCacheStrategy = (strategy: CacheContextType['cache']['strategy']) => {
    setCacheState((prev) => ({
      ...prev,
      strategy,
    }));
  };

  const addCachePattern = (pattern: string) => {
    setCacheState((prev) => ({
      ...prev,
      patterns: [...prev.patterns, pattern],
    }));
  };

  const removeCachePattern = (pattern: string) => {
    setCacheState((prev) => ({
      ...prev,
      patterns: prev.patterns.filter((p) => p !== pattern),
    }));
  };

  const addExcludedPattern = (pattern: string) => {
    setCacheState((prev) => ({
      ...prev,
      excluded: [...prev.excluded, pattern],
    }));
  };

  const removeExcludedPattern = (pattern: string) => {
    setCacheState((prev) => ({
      ...prev,
      excluded: prev.excluded.filter((p) => p !== pattern),
    }));
  };

  const setCacheVersion = (version: string) => {
    setCacheState((prev) => ({
      ...prev,
      version,
    }));
  };

  const clearCache = async () => {
    try {
      const cacheStorage = await caches.open('browser-cache');
      const keys = await cacheStorage.keys();
      await Promise.all(keys.map((key) => cacheStorage.delete(key)));
      setCacheState((prev) => ({
        ...prev,
        size: 0,
        entries: 0,
        hits: 0,
        misses: 0,
        updates: 0,
        deletes: 0,
        errors: 0,
      }));
    } catch (error) {
      console.error('Cache clear failed:', error);
      setCacheState((prev) => ({
        ...prev,
        errors: prev.errors + 1,
      }));
    }
  };

  const getCacheSize = async () => {
    try {
      const cacheStorage = await caches.open('browser-cache');
      const keys = await cacheStorage.keys();
      let size = 0;

      for (const request of keys) {
        const response = await cacheStorage.match(request);
        if (response) {
          const blob = await response.blob();
          size += blob.size;
        }
      }

      setCacheState((prev) => ({
        ...prev,
        size,
      }));

      return size;
    } catch (error) {
      console.error('Cache size calculation failed:', error);
      setCacheState((prev) => ({
        ...prev,
        errors: prev.errors + 1,
      }));
      return 0;
    }
  };

  const getCacheEntries = async () => {
    try {
      const cacheStorage = await caches.open('browser-cache');
      const keys = await cacheStorage.keys();
      setCacheState((prev) => ({
        ...prev,
        entries: keys.length,
      }));
      return keys.length;
    } catch (error) {
      console.error('Cache entries calculation failed:', error);
      setCacheState((prev) => ({
        ...prev,
        errors: prev.errors + 1,
      }));
      return 0;
    }
  };

  const getCacheHits = () => {
    return cache.hits;
  };

  const getCacheMisses = () => {
    return cache.misses;
  };

  const getCacheUpdates = () => {
    return cache.updates;
  };

  const getCacheDeletes = () => {
    return cache.deletes;
  };

  const getCacheErrors = () => {
    return cache.errors;
  };

  const getCacheStats = () => {
    return cache;
  };

  const prefetchResource = async (url: string) => {
    if (!cache.prefetchEnabled) return;

    try {
      const response = await fetch(url);
      if (response.ok) {
        const cacheStorage = await caches.open('browser-cache');
        await cacheStorage.put(url, response);
        setCacheState((prev) => ({
          ...prev,
          updates: prev.updates + 1,
        }));
      }
    } catch (error) {
      console.error(`Prefetch failed for ${url}:`, error);
      setCacheState((prev) => ({
        ...prev,
        errors: prev.errors + 1,
      }));
    }
  };

  const compressData = async (data: string): Promise<string> => {
    if (!cache.compressionEnabled) return data;
    return compress(data, cache.compressionLevel);
  };

  const decompressData = async (data: string): Promise<string> => {
    if (!cache.compressionEnabled) return data;
    return decompress(data);
  };

  const setPrefetchEnabled = (enabled: boolean) => {
    setCacheState((prev) => ({
      ...prev,
      prefetchEnabled: enabled,
    }));
  };

  const addPrefetchPattern = (pattern: string) => {
    setCacheState((prev) => ({
      ...prev,
      prefetchPatterns: [...prev.prefetchPatterns, pattern],
    }));
  };

  const removePrefetchPattern = (pattern: string) => {
    setCacheState((prev) => ({
      ...prev,
      prefetchPatterns: prev.prefetchPatterns.filter((p) => p !== pattern),
    }));
  };

  const setCompressionEnabled = (enabled: boolean) => {
    setCacheState((prev) => ({
      ...prev,
      compressionEnabled: enabled,
    }));
  };

  const setCompressionLevel = (level: number) => {
    setCacheState((prev) => ({
      ...prev,
      compressionLevel: Math.max(1, Math.min(9, level)),
    }));
  };

  const value = {
    cache,
    setCacheEnabled,
    setCacheMaxSize,
    setCacheMaxAge,
    setCacheStrategy,
    addCachePattern,
    removeCachePattern,
    addExcludedPattern,
    removeExcludedPattern,
    setCacheVersion,
    clearCache,
    getCacheSize,
    getCacheEntries,
    getCacheHits,
    getCacheMisses,
    getCacheUpdates,
    getCacheDeletes,
    getCacheErrors,
    getCacheStats,
    prefetchResource,
    setPrefetchEnabled,
    addPrefetchPattern,
    removePrefetchPattern,
    setCompressionEnabled,
    setCompressionLevel,
  };

  return (
    <CacheContext.Provider value={value}>
      {children}
    </CacheContext.Provider>
  );
}; 