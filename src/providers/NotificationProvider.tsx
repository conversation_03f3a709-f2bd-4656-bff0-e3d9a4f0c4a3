import React, { createContext, useContext, useEffect, useState, useCallback, useMemo, ReactNode } from 'react';
import { useDispatch } from 'react-redux';
import { v4 as uuidv4 } from 'uuid';
import { Transition } from 'framer-motion';
import type {
  Notification,
  NotificationGroup,
  NotificationAction,
  NotificationType,
  NotificationPriority,
  NotificationPosition,
  NotificationTheme,
  NotificationAnimation,
  NotificationSound,
  NotificationBehavior,
  NotificationLayout,
  NotificationAccessibility
} from '../types/notifications';

export interface NotificationTheme {
  // Base theme
  background?: string;
  text?: string;
  border?: string;
  shadow?: string;
  borderRadius?: number;
  fontFamily?: string;
  fontSize?: string;
  fontWeight?: number;
  lineHeight?: number;
  letterSpacing?: number;
  transition?: string;
  zIndex?: number;

  // Icon theme
  icon?: {
    color?: string;
    background?: string;
    size?: number;
    borderRadius?: number;
    border?: string;
    shadow?: string;
    hoverShadow?: string;
    hoverEffect?: string;
    tapEffect?: string;
    glow?: string;
    pulse?: string;
    rotate?: number;
    scale?: number;
    filter?: string;
    transition?: string;
    animation?: {
      duration?: number;
      easing?: string;
      delay?: number;
    };
  };

  // Content theme
  content?: {
    background?: string;
    text?: string;
    title?: string;
    message?: string;
    icon?: string;
    iconBackground?: string;
    iconSize?: number;
    border?: string;
    borderRadius?: number;
    padding?: number;
    shadow?: string;
    titleWeight?: number;
    progressHeight?: number;
    progressBackground?: string;
    progressFill?: string;
    progressBorderRadius?: number;
    progressGlow?: string;
    animation?: {
      duration?: number;
      easing?: string;
      delay?: number;
    };
  };

  // Header theme
  header?: {
    background?: string;
    text?: string;
    icon?: string;
    border?: string;
    shadow?: string;
    animation?: {
      duration?: number;
      easing?: string;
      delay?: number;
    };
  };

  // Action theme
  action?: {
    background?: string;
    text?: string;
    hover?: string;
    active?: string;
    icon?: string;
    border?: string;
    shadow?: string;
    animation?: {
      duration?: number;
      easing?: string;
      delay?: number;
    };
  };

  // Group theme
  group?: {
    background?: string;
    text?: string;
    border?: string;
    shadow?: string;
    borderRadius?: number;
    padding?: number;
    gap?: number;
    animation?: {
      duration?: number;
      easing?: string;
      delay?: number;
    };
  };

  // Accessibility theme
  accessibility?: {
    highContrast?: {
      background?: string;
      text?: string;
      border?: string;
    };
    reducedMotion?: {
      animation?: boolean;
      transition?: boolean;
    };
    screenReader?: {
      announce?: boolean;
      description?: string;
    };
  };
}

export interface NotificationAnimation {
  // Base animations
  initial?: Record<string, number>;
  animate?: Record<string, number>;
  exit?: Record<string, number>;
  hover?: Record<string, number>;
  tap?: Record<string, number>;
  transition?: Transition;

  // Group animations
  expand?: Record<string, number>;
  collapse?: Record<string, number>;
  drag?: {
    start?: Record<string, number>;
    end?: Record<string, number>;
  };
  reorder?: Record<string, number>;

  // Content animations
  content?: {
    initial?: Record<string, number>;
    animate?: Record<string, number>;
    exit?: Record<string, number>;
    hover?: Record<string, number>;
    tap?: Record<string, number>;
  };

  // Icon animations
  icon?: {
    initial?: Record<string, number>;
    animate?: Record<string, number>;
    hover?: Record<string, number>;
    tap?: Record<string, number>;
    pulse?: Record<string, number>;
    rotate?: Record<string, number>;
    glow?: Record<string, number>;
  };

  // Action animations
  action?: {
    initial?: Record<string, number>;
    animate?: Record<string, number>;
    hover?: Record<string, number>;
    tap?: Record<string, number>;
    loading?: Record<string, number>;
    success?: Record<string, number>;
    error?: Record<string, number>;
  };

  // Progress animations
  progress?: {
    initial?: Record<string, number>;
    animate?: Record<string, number>;
    exit?: Record<string, number>;
    hover?: Record<string, number>;
    tap?: Record<string, number>;
    complete?: Record<string, number>;
  };
}

interface NotificationState {
  notifications: Notification[];
  groups: NotificationGroup[];
  settings: {
    maxNotifications: number;
    maxGroups: number;
    defaultDuration: number;
    defaultPosition: NotificationPosition;
    defaultType: NotificationType;
    defaultPriority: NotificationPriority;
    defaultTheme: NotificationTheme;
    defaultAnimation: NotificationAnimation;
    defaultSound: NotificationSound;
    defaultBehavior: NotificationBehavior;
    defaultLayout: NotificationLayout;
    defaultAccessibility: NotificationAccessibility;
  };
}

interface NotificationContextType {
  notifications: Notification[];
  groups: NotificationGroup[];
  settings: NotificationState['settings'];
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt' | 'read'>) => void;
  removeNotification: (id: string) => void;
  updateNotification: (id: string, notification: Partial<Notification>) => void;
  clearNotifications: () => void;
  addGroup: (group: Omit<NotificationGroup, 'id'>) => void;
  removeGroup: (id: string) => void;
  updateGroup: (id: string, group: Partial<NotificationGroup>) => void;
  clearGroups: () => void;
  updateSettings: (settings: Partial<NotificationState['settings']>) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const [state, setState] = useState<NotificationState>({
    notifications: [],
    groups: [],
    settings: {
      maxNotifications: 5,
      maxGroups: 3,
      defaultDuration: 5000,
      defaultPosition: 'top-right',
      defaultType: 'info',
      defaultPriority: 'normal',
      defaultTheme: {},
      defaultAnimation: {},
      defaultSound: {},
      defaultBehavior: 'stack',
      defaultLayout: 'stack',
      defaultAccessibility: 'default'
    }
  });

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('notificationSettings');
    if (savedSettings) {
      setState((prev) => ({
        ...prev,
        settings: { ...prev.settings, ...JSON.parse(savedSettings) },
      }));
    }
  }, []);

  // Save settings to localStorage
  useEffect(() => {
    localStorage.setItem('notificationSettings', JSON.stringify(state.settings));
  }, [state.settings]);

  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'createdAt' | 'read'>) => {
    setState(prev => {
      const newNotification: Notification = {
        ...notification,
        id: uuidv4(),
        createdAt: Date.now(),
        read: false
      };

      let newNotifications = [...prev.notifications];

      switch (notification.behavior || prev.settings.defaultBehavior) {
        case 'replace':
          newNotifications = [newNotification];
          break;
        case 'queue':
          if (newNotifications.length >= prev.settings.maxNotifications) {
            newNotifications = [...newNotifications.slice(1), newNotification];
          } else {
            newNotifications = [...newNotifications, newNotification];
          }
          break;
        case 'clear':
          newNotifications = [newNotification];
          break;
        default: // stack
          if (newNotifications.length >= prev.settings.maxNotifications) {
            newNotifications = [newNotification, ...newNotifications.slice(0, -1)];
          } else {
            newNotifications = [newNotification, ...newNotifications];
          }
      }

      return {
        ...prev,
        notifications: newNotifications
      };
    });
  }, []);

  const removeNotification = useCallback((id: string) => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.filter(n => n.id !== id)
    }));
  }, []);

  const updateNotification = useCallback((id: string, notification: Partial<Notification>) => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.map(n => 
        n.id === id ? { ...n, ...notification } : n
      )
    }));
  }, []);

  const clearNotifications = useCallback(() => {
    setState(prev => ({
      ...prev,
      notifications: []
    }));
  }, []);

  const addGroup = useCallback((group: Omit<NotificationGroup, 'id'>) => {
    setState(prev => {
      const newGroup: NotificationGroup = {
        ...group,
        id: uuidv4()
      };

      let newGroups = [...prev.groups];

      if (newGroups.length >= prev.settings.maxGroups) {
        newGroups = [...newGroups.slice(1), newGroup];
      } else {
        newGroups = [...newGroups, newGroup];
      }

      return {
        ...prev,
        groups: newGroups
      };
    });
  }, []);

  const removeGroup = useCallback((id: string) => {
    setState(prev => ({
      ...prev,
      groups: prev.groups.filter(g => g.id !== id)
    }));
  }, []);

  const updateGroup = useCallback((id: string, group: Partial<NotificationGroup>) => {
    setState(prev => ({
      ...prev,
      groups: prev.groups.map(g => 
        g.id === id ? { ...g, ...group } : g
      )
    }));
  }, []);

  const clearGroups = useCallback(() => {
    setState(prev => ({
      ...prev,
      groups: []
    }));
  }, []);

  const updateSettings = useCallback((settings: Partial<NotificationState['settings']>) => {
    setState(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        ...settings
      }
    }));
  }, []);

  const value = useMemo(() => ({
    notifications: state.notifications,
    groups: state.groups,
    settings: state.settings,
    addNotification,
    removeNotification,
    updateNotification,
    clearNotifications,
    addGroup,
    removeGroup,
    updateGroup,
    clearGroups,
    updateSettings
  }), [
    state,
    addNotification,
    removeNotification,
    updateNotification,
    clearNotifications,
    addGroup,
    removeGroup,
    updateGroup,
    clearGroups,
    updateSettings
  ]);

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const createNotification = (notification: Omit<Notification, 'id' | 'createdAt' | 'read'>): Notification => {
  return {
    ...notification,
    id: uuidv4(),
    createdAt: Date.now(),
    read: false
  };
};

export const playNotificationSound = (sound: NotificationSound | string | undefined) => {
  if (!sound) return;
  
  if (typeof sound === 'string') {
    const audio = new Audio(sound);
    audio.play().catch(console.error);
  } else if (sound.hover) {
    const audio = new Audio(sound.hover);
    audio.play().catch(console.error);
  }
}; 