import React, { createContext, useContext, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

interface AnalyticsContextType {
  trackEvent: (eventName: string, properties?: Record<string, any>) => void;
  trackPageView: (path: string) => void;
  trackError: (error: Error) => void;
  trackPerformance: (metric: string, value: number) => void;
  trackUserAction: (action: string, details?: Record<string, any>) => void;
}

const AnalyticsContext = createContext<AnalyticsContextType | null>(null);

export const useAnalytics = () => {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
};

interface AnalyticsProviderProps {
  children: React.ReactNode;
}

export const AnalyticsProvider: React.FC<AnalyticsProviderProps> = ({ children }) => {
  const location = useLocation();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialize analytics
    const initializeAnalytics = async () => {
      try {
        // TODO: Initialize your analytics service here
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize analytics:', error);
      }
    };

    initializeAnalytics();
  }, []);

  useEffect(() => {
    if (isInitialized) {
      // Track page views
      trackPageView(location.pathname);
    }
  }, [location, isInitialized]);

  const trackEvent = (eventName: string, properties?: Record<string, any>) => {
    if (!isInitialized) return;

    try {
      // TODO: Implement event tracking
      console.log('Track event:', eventName, properties);
    } catch (error) {
      console.error('Failed to track event:', error);
    }
  };

  const trackPageView = (path: string) => {
    if (!isInitialized) return;

    try {
      // TODO: Implement page view tracking
      console.log('Track page view:', path);
    } catch (error) {
      console.error('Failed to track page view:', error);
    }
  };

  const trackError = (error: Error) => {
    if (!isInitialized) return;

    try {
      // TODO: Implement error tracking
      console.error('Track error:', error);
    } catch (err) {
      console.error('Failed to track error:', err);
    }
  };

  const trackPerformance = (metric: string, value: number) => {
    if (!isInitialized) return;

    try {
      // TODO: Implement performance tracking
      console.log('Track performance:', metric, value);
    } catch (error) {
      console.error('Failed to track performance:', error);
    }
  };

  const trackUserAction = (action: string, details?: Record<string, any>) => {
    if (!isInitialized) return;

    try {
      // TODO: Implement user action tracking
      console.log('Track user action:', action, details);
    } catch (error) {
      console.error('Failed to track user action:', error);
    }
  };

  const value = {
    trackEvent,
    trackPageView,
    trackError,
    trackPerformance,
    trackUserAction,
  };

  return (
    <AnalyticsContext.Provider value={value}>
      {children}
    </AnalyticsContext.Provider>
  );
}; 