import React, { createContext, useContext, useEffect, useState } from 'react';
import { useStorage } from './StorageProvider';

interface PerformanceMetrics {
  memoryUsage: number;
  cpuUsage: number;
  networkLatency: number;
  pageLoadTime: number;
  jsHeapSize: number;
  domNodes: number;
  timestamp: number;
}

interface PerformanceSettings {
  autoOptimize: boolean;
  memoryLimit: number;
  cpuLimit: number;
  networkLimit: number;
  monitoringInterval: number;
  lastUpdated: number;
}

interface PerformanceState {
  metrics: PerformanceMetrics[];
  settings: PerformanceSettings;
  isMonitoring: boolean;
  lastOptimization: number;
  optimizationCount: number;
  alerts: string[];
}

interface PerformanceContextType {
  state: PerformanceState;
  startMonitoring: () => Promise<void>;
  stopMonitoring: () => Promise<void>;
  updateSettings: (settings: Partial<PerformanceSettings>) => Promise<void>;
  getCurrentMetrics: () => PerformanceMetrics | null;
  getMetricsHistory: (limit?: number) => PerformanceMetrics[];
  optimizePerformance: () => Promise<void>;
  clearAlerts: () => Promise<void>;
  getPerformanceStatus: () => { status: 'good' | 'warning' | 'critical'; score: number };
}

const defaultPerformanceState: PerformanceState = {
  metrics: [],
  settings: {
    autoOptimize: true,
    memoryLimit: 80, // percentage
    cpuLimit: 70, // percentage
    networkLimit: 1000, // ms
    monitoringInterval: 5000, // ms
    lastUpdated: Date.now(),
  },
  isMonitoring: false,
  lastOptimization: 0,
  optimizationCount: 0,
  alerts: [],
};

const PerformanceContext = createContext<PerformanceContextType | null>(null);

export const usePerformance = () => {
  const context = useContext(PerformanceContext);
  if (!context) {
    throw new Error('usePerformance must be used within a PerformanceProvider');
  }
  return context;
};

interface PerformanceProviderProps {
  children: React.ReactNode;
}

export const PerformanceProvider: React.FC<PerformanceProviderProps> = ({ children }) => {
  const storage = useStorage();
  const [state, setState] = useState<PerformanceState>(defaultPerformanceState);
  const [monitoringInterval, setMonitoringInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const loadPerformance = async () => {
      try {
        const savedPerformance = await storage.getItem('browserPerformance');
        if (savedPerformance) {
          setState(JSON.parse(savedPerformance));
        }
      } catch (error) {
        console.error('Error loading performance settings:', error);
      }
    };

    loadPerformance();
  }, [storage]);

  const savePerformance = async (newState: PerformanceState) => {
    try {
      await storage.setItem('browserPerformance', JSON.stringify(newState));
      setState(newState);
    } catch (error) {
      console.error('Error saving performance settings:', error);
    }
  };

  const collectMetrics = (): PerformanceMetrics => {
    const performance = window.performance;
    const memory = (performance as any).memory;
    const timing = performance.timing;

    return {
      memoryUsage: memory ? (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100 : 0,
      cpuUsage: Math.random() * 100, // Simulated CPU usage
      networkLatency: timing ? timing.responseEnd - timing.requestStart : 0,
      pageLoadTime: timing ? timing.loadEventEnd - timing.navigationStart : 0,
      jsHeapSize: memory ? memory.usedJSHeapSize : 0,
      domNodes: document.getElementsByTagName('*').length,
      timestamp: Date.now(),
    };
  };

  const startMonitoring = async () => {
    if (state.isMonitoring) return;

    const newState = {
      ...state,
      isMonitoring: true,
    };

    await savePerformance(newState);

    const interval = setInterval(() => {
      const metrics = collectMetrics();
      const updatedState = {
        ...state,
        metrics: [...state.metrics, metrics].slice(-100), // Keep last 100 metrics
      };

      savePerformance(updatedState);

      // Check for performance issues
      if (state.settings.autoOptimize) {
        const status = getPerformanceStatus();
        if (status.status === 'critical') {
          optimizePerformance();
        }
      }
    }, state.settings.monitoringInterval);

    setMonitoringInterval(interval);
  };

  const stopMonitoring = async () => {
    if (!state.isMonitoring) return;

    if (monitoringInterval) {
      clearInterval(monitoringInterval);
      setMonitoringInterval(null);
    }

    const newState = {
      ...state,
      isMonitoring: false,
    };

    await savePerformance(newState);
  };

  const updateSettings = async (settings: Partial<PerformanceSettings>) => {
    const newState = {
      ...state,
      settings: {
        ...state.settings,
        ...settings,
        lastUpdated: Date.now(),
      },
    };

    await savePerformance(newState);

    // Restart monitoring if interval changed
    if (settings.monitoringInterval && state.isMonitoring) {
      await stopMonitoring();
      await startMonitoring();
    }
  };

  const getCurrentMetrics = (): PerformanceMetrics | null => {
    return state.metrics[state.metrics.length - 1] || null;
  };

  const getMetricsHistory = (limit: number = 100): PerformanceMetrics[] => {
    return state.metrics.slice(-limit);
  };

  const optimizePerformance = async () => {
    // Simulate performance optimization
    const newState = {
      ...state,
      lastOptimization: Date.now(),
      optimizationCount: state.optimizationCount + 1,
      alerts: [...state.alerts, `Performance optimized at ${new Date().toLocaleTimeString()}`].slice(-10),
    };

    await savePerformance(newState);

    // Clear browser cache
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
      } catch (error) {
        console.error('Error clearing cache:', error);
      }
    }

    // Clear memory
    if (window.gc) {
      try {
        window.gc();
      } catch (error) {
        console.error('Error clearing memory:', error);
      }
    }
  };

  const clearAlerts = async () => {
    const newState = {
      ...state,
      alerts: [],
    };

    await savePerformance(newState);
  };

  const getPerformanceStatus = () => {
    const currentMetrics = getCurrentMetrics();
    if (!currentMetrics) return { status: 'good' as const, score: 100 };

    const { memoryUsage, cpuUsage, networkLatency } = currentMetrics;
    const { memoryLimit, cpuLimit, networkLimit } = state.settings;

    let score = 100;
    let status: 'good' | 'warning' | 'critical' = 'good';

    // Calculate score based on metrics
    if (memoryUsage > memoryLimit) {
      score -= 20;
      status = 'warning';
    }
    if (cpuUsage > cpuLimit) {
      score -= 20;
      status = 'warning';
    }
    if (networkLatency > networkLimit) {
      score -= 20;
      status = 'warning';
    }

    // Check for critical conditions
    if (memoryUsage > memoryLimit * 1.5 || cpuUsage > cpuLimit * 1.5) {
      status = 'critical';
      score = Math.max(0, score - 40);
    }

    return { status, score };
  };

  const value = {
    state,
    startMonitoring,
    stopMonitoring,
    updateSettings,
    getCurrentMetrics,
    getMetricsHistory,
    optimizePerformance,
    clearAlerts,
    getPerformanceStatus,
  };

  return (
    <PerformanceContext.Provider value={value}>
      {children}
    </PerformanceContext.Provider>
  );
}; 