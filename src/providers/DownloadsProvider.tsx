import React, { createContext, useContext, useEffect, useState } from 'react';
import { useStorage } from './StorageProvider';

type DownloadStatus = 'pending' | 'downloading' | 'paused' | 'completed' | 'error';

interface DownloadItem {
  id: string;
  url: string;
  filename: string;
  path: string;
  size: number;
  received: number;
  status: DownloadStatus;
  startTime: number;
  endTime?: number;
  error?: string;
  mimeType?: string;
  progress: number;
  speed: number;
  remainingTime?: number;
}

interface DownloadsState {
  items: DownloadItem[];
  activeDownloads: number;
  totalDownloads: number;
  completedDownloads: number;
  failedDownloads: number;
  lastUpdated: number;
}

interface DownloadsContextType {
  downloads: DownloadsState;
  addDownload: (url: string, filename: string, path: string) => Promise<string>;
  removeDownload: (id: string) => Promise<void>;
  pauseDownload: (id: string) => Promise<void>;
  resumeDownload: (id: string) => Promise<void>;
  cancelDownload: (id: string) => Promise<void>;
  clearCompleted: () => Promise<void>;
  clearAll: () => Promise<void>;
  getDownload: (id: string) => DownloadItem | undefined;
  getActiveDownloads: () => DownloadItem[];
  getCompletedDownloads: () => DownloadItem[];
  getFailedDownloads: () => DownloadItem[];
  updateDownloadProgress: (id: string, received: number, speed: number) => Promise<void>;
}

const defaultDownloadsState: DownloadsState = {
  items: [],
  activeDownloads: 0,
  totalDownloads: 0,
  completedDownloads: 0,
  failedDownloads: 0,
  lastUpdated: Date.now(),
};

const DownloadsContext = createContext<DownloadsContextType | null>(null);

export const useDownloads = () => {
  const context = useContext(DownloadsContext);
  if (!context) {
    throw new Error('useDownloads must be used within a DownloadsProvider');
  }
  return context;
};

interface DownloadsProviderProps {
  children: React.ReactNode;
}

export const DownloadsProvider: React.FC<DownloadsProviderProps> = ({ children }) => {
  const storage = useStorage();
  const [downloads, setDownloads] = useState<DownloadsState>(defaultDownloadsState);

  useEffect(() => {
    const loadDownloads = async () => {
      try {
        const savedDownloads = await storage.getItem('browserDownloads');
        if (savedDownloads) {
          setDownloads(JSON.parse(savedDownloads));
        }
      } catch (error) {
        console.error('Error loading downloads:', error);
      }
    };

    loadDownloads();
  }, [storage]);

  const saveDownloads = async (newDownloads: DownloadsState) => {
    try {
      await storage.setItem('browserDownloads', JSON.stringify(newDownloads));
      setDownloads(newDownloads);
    } catch (error) {
      console.error('Error saving downloads:', error);
    }
  };

  const addDownload = async (url: string, filename: string, path: string): Promise<string> => {
    const now = Date.now();
    const id = `dl_${now}_${Math.random().toString(36).substr(2, 9)}`;
    
    const newDownload: DownloadItem = {
      id,
      url,
      filename,
      path,
      size: 0,
      received: 0,
      status: 'pending',
      startTime: now,
      progress: 0,
      speed: 0,
    };

    const newDownloads = {
      ...downloads,
      items: [...downloads.items, newDownload],
      activeDownloads: downloads.activeDownloads + 1,
      totalDownloads: downloads.totalDownloads + 1,
      lastUpdated: now,
    };

    await saveDownloads(newDownloads);
    return id;
  };

  const removeDownload = async (id: string) => {
    const download = downloads.items.find(item => item.id === id);
    if (!download) return;

    const newDownloads = {
      ...downloads,
      items: downloads.items.filter(item => item.id !== id),
      activeDownloads: download.status === 'downloading' ? downloads.activeDownloads - 1 : downloads.activeDownloads,
      lastUpdated: Date.now(),
    };

    await saveDownloads(newDownloads);
  };

  const pauseDownload = async (id: string) => {
    const newDownloads: DownloadsState = {
      ...downloads,
      items: downloads.items.map(item =>
        item.id === id
          ? { ...item, status: 'paused' as DownloadStatus }
          : item
      ),
      activeDownloads: downloads.activeDownloads - 1,
      lastUpdated: Date.now(),
    };

    await saveDownloads(newDownloads);
  };

  const resumeDownload = async (id: string) => {
    const newDownloads: DownloadsState = {
      ...downloads,
      items: downloads.items.map(item =>
        item.id === id
          ? { ...item, status: 'downloading' as DownloadStatus }
          : item
      ),
      activeDownloads: downloads.activeDownloads + 1,
      lastUpdated: Date.now(),
    };

    await saveDownloads(newDownloads);
  };

  const cancelDownload = async (id: string) => {
    const download = downloads.items.find(item => item.id === id);
    if (!download) return;

    const newDownloads: DownloadsState = {
      ...downloads,
      items: downloads.items.map(item =>
        item.id === id
          ? {
              ...item,
              status: 'error' as DownloadStatus,
              error: 'Download cancelled by user',
              endTime: Date.now(),
            }
          : item
      ),
      activeDownloads: download.status === 'downloading' ? downloads.activeDownloads - 1 : downloads.activeDownloads,
      failedDownloads: downloads.failedDownloads + 1,
      lastUpdated: Date.now(),
    };

    await saveDownloads(newDownloads);
  };

  const clearCompleted = async () => {
    const newDownloads = {
      ...downloads,
      items: downloads.items.filter(item => item.status !== 'completed'),
      completedDownloads: 0,
      lastUpdated: Date.now(),
    };

    await saveDownloads(newDownloads);
  };

  const clearAll = async () => {
    const newDownloads = {
      ...defaultDownloadsState,
      lastUpdated: Date.now(),
    };

    await saveDownloads(newDownloads);
  };

  const getDownload = (id: string): DownloadItem | undefined => {
    return downloads.items.find(item => item.id === id);
  };

  const getActiveDownloads = (): DownloadItem[] => {
    return downloads.items.filter(item => item.status === 'downloading');
  };

  const getCompletedDownloads = (): DownloadItem[] => {
    return downloads.items.filter(item => item.status === 'completed');
  };

  const getFailedDownloads = (): DownloadItem[] => {
    return downloads.items.filter(item => item.status === 'error');
  };

  const updateDownloadProgress = async (id: string, received: number, speed: number) => {
    const download = downloads.items.find(item => item.id === id);
    if (!download) return;

    const progress = download.size > 0 ? (received / download.size) * 100 : 0;
    const remainingTime = speed > 0 ? (download.size - received) / speed : undefined;
    const isCompleted = progress >= 100;

    const newDownloads: DownloadsState = {
      ...downloads,
      items: downloads.items.map(item =>
        item.id === id
          ? {
              ...item,
              received,
              speed,
              progress,
              remainingTime,
              status: isCompleted ? 'completed' as DownloadStatus : 'downloading' as DownloadStatus,
              endTime: isCompleted ? Date.now() : undefined,
            }
          : item
      ),
      completedDownloads: isCompleted ? downloads.completedDownloads + 1 : downloads.completedDownloads,
      activeDownloads: isCompleted ? downloads.activeDownloads - 1 : downloads.activeDownloads,
      lastUpdated: Date.now(),
    };

    await saveDownloads(newDownloads);
  };

  const value = {
    downloads,
    addDownload,
    removeDownload,
    pauseDownload,
    resumeDownload,
    cancelDownload,
    clearCompleted,
    clearAll,
    getDownload,
    getActiveDownloads,
    getCompletedDownloads,
    getFailedDownloads,
    updateDownloadProgress,
  };

  return (
    <DownloadsContext.Provider value={value}>
      {children}
    </DownloadsContext.Provider>
  );
}; 