import React, { createContext, useContext, useEffect, useState } from 'react';
import { useStorage } from './StorageProvider';

interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  enabled: boolean;
  permissions: string[];
  settings: Record<string, any>;
  icon?: string;
  homepage?: string;
  repository?: string;
  dependencies?: string[];
  scripts?: {
    background?: string;
    content?: string;
    popup?: string;
  };
}

interface ExtensionContextType {
  extensions: Extension[];
  installExtension: (extension: Omit<Extension, 'id'>) => Promise<string>;
  uninstallExtension: (id: string) => Promise<void>;
  enableExtension: (id: string) => Promise<void>;
  disableExtension: (id: string) => Promise<void>;
  updateExtension: (id: string, updates: Partial<Extension>) => Promise<void>;
  getExtension: (id: string) => Extension | undefined;
  getEnabledExtensions: () => Extension[];
  getExtensionSettings: (id: string) => Record<string, any>;
  updateExtensionSettings: (id: string, settings: Record<string, any>) => Promise<void>;
}

const ExtensionContext = createContext<ExtensionContextType | null>(null);

export const useExtensions = () => {
  const context = useContext(ExtensionContext);
  if (!context) {
    throw new Error('useExtensions must be used within an ExtensionProvider');
  }
  return context;
};

interface ExtensionProviderProps {
  children: React.ReactNode;
}

export const ExtensionProvider: React.FC<ExtensionProviderProps> = ({ children }) => {
  const storage = useStorage();
  const [extensions, setExtensions] = useState<Extension[]>([]);

  useEffect(() => {
    const loadExtensions = async () => {
      try {
        const extensionsData = await storage.getItem('extensions');
        if (extensionsData) {
          setExtensions(JSON.parse(extensionsData));
        }
      } catch (error) {
        console.error('Error loading extensions:', error);
      }
    };

    loadExtensions();
  }, [storage]);

  const saveExtensions = async (newExtensions: Extension[]) => {
    try {
      await storage.setItem('extensions', JSON.stringify(newExtensions));
      setExtensions(newExtensions);
    } catch (error) {
      console.error('Error saving extensions:', error);
    }
  };

  const installExtension = async (extension: Omit<Extension, 'id'>): Promise<string> => {
    const id = `ext_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newExtension: Extension = { ...extension, id };
    
    const newExtensions = [...extensions, newExtension];
    await saveExtensions(newExtensions);
    
    return id;
  };

  const uninstallExtension = async (id: string): Promise<void> => {
    const newExtensions = extensions.filter(ext => ext.id !== id);
    await saveExtensions(newExtensions);
  };

  const enableExtension = async (id: string): Promise<void> => {
    const newExtensions = extensions.map(ext =>
      ext.id === id ? { ...ext, enabled: true } : ext
    );
    await saveExtensions(newExtensions);
  };

  const disableExtension = async (id: string): Promise<void> => {
    const newExtensions = extensions.map(ext =>
      ext.id === id ? { ...ext, enabled: false } : ext
    );
    await saveExtensions(newExtensions);
  };

  const updateExtension = async (id: string, updates: Partial<Extension>): Promise<void> => {
    const newExtensions = extensions.map(ext =>
      ext.id === id ? { ...ext, ...updates } : ext
    );
    await saveExtensions(newExtensions);
  };

  const getExtension = (id: string): Extension | undefined => {
    return extensions.find(ext => ext.id === id);
  };

  const getEnabledExtensions = (): Extension[] => {
    return extensions.filter(ext => ext.enabled);
  };

  const getExtensionSettings = (id: string): Record<string, any> => {
    const extension = getExtension(id);
    return extension?.settings || {};
  };

  const updateExtensionSettings = async (id: string, settings: Record<string, any>): Promise<void> => {
    const newExtensions = extensions.map(ext =>
      ext.id === id ? { ...ext, settings: { ...ext.settings, ...settings } } : ext
    );
    await saveExtensions(newExtensions);
  };

  const value = {
    extensions,
    installExtension,
    uninstallExtension,
    enableExtension,
    disableExtension,
    updateExtension,
    getExtension,
    getEnabledExtensions,
    getExtensionSettings,
    updateExtensionSettings,
  };

  return (
    <ExtensionContext.Provider value={value}>
      {children}
    </ExtensionContext.Provider>
  );
}; 