import React, { createContext, useContext, useEffect, useState } from 'react';
import { useStorage } from './StorageProvider';

interface HistoryEntry {
  id: string;
  url: string;
  title: string;
  timestamp: number;
  visitCount: number;
  lastVisitTime: number;
  favicon?: string;
  referrer?: string;
  transitionType?: 'link' | 'typed' | 'auto_bookmark' | 'auto_subframe' | 'manual_subframe' | 'generated' | 'start_page' | 'form_submitted' | 'reload' | 'keyword' | 'keyword_generated';
}

interface HistoryState {
  entries: HistoryEntry[];
  lastUpdated: number;
  totalVisits: number;
  searchHistory: string[];
}

interface HistoryContextType {
  history: HistoryState;
  addEntry: (entry: Omit<HistoryEntry, 'id' | 'timestamp' | 'visitCount' | 'lastVisitTime'>) => Promise<void>;
  removeEntry: (id: string) => Promise<void>;
  clearHistory: () => Promise<void>;
  searchHistory: (query: string) => HistoryEntry[];
  getMostVisited: (limit?: number) => HistoryEntry[];
  getRecentHistory: (limit?: number) => HistoryEntry[];
  getHistoryByDate: (startDate: Date, endDate: Date) => HistoryEntry[];
  addSearchQuery: (query: string) => Promise<void>;
  clearSearchHistory: () => Promise<void>;
  getSearchSuggestions: (query: string) => string[];
}

const defaultHistoryState: HistoryState = {
  entries: [],
  lastUpdated: Date.now(),
  totalVisits: 0,
  searchHistory: [],
};

const HistoryContext = createContext<HistoryContextType | null>(null);

export const useHistory = () => {
  const context = useContext(HistoryContext);
  if (!context) {
    throw new Error('useHistory must be used within a HistoryProvider');
  }
  return context;
};

interface HistoryProviderProps {
  children: React.ReactNode;
}

export const HistoryProvider: React.FC<HistoryProviderProps> = ({ children }) => {
  const storage = useStorage();
  const [history, setHistory] = useState<HistoryState>(defaultHistoryState);

  useEffect(() => {
    const loadHistory = async () => {
      try {
        const savedHistory = await storage.getItem('browserHistory');
        if (savedHistory) {
          setHistory(JSON.parse(savedHistory));
        }
      } catch (error) {
        console.error('Error loading history:', error);
      }
    };

    loadHistory();
  }, [storage]);

  const saveHistory = async (newHistory: HistoryState) => {
    try {
      await storage.setItem('browserHistory', JSON.stringify(newHistory));
      setHistory(newHistory);
    } catch (error) {
      console.error('Error saving history:', error);
    }
  };

  const addEntry = async (entry: Omit<HistoryEntry, 'id' | 'timestamp' | 'visitCount' | 'lastVisitTime'>) => {
    const now = Date.now();
    const existingEntry = history.entries.find(e => e.url === entry.url);

    let updatedEntries: HistoryEntry[];
    if (existingEntry) {
      updatedEntries = history.entries.map(e =>
        e.url === entry.url
          ? {
              ...e,
              title: entry.title,
              visitCount: e.visitCount + 1,
              lastVisitTime: now,
              favicon: entry.favicon || e.favicon,
            }
          : e
      );
    } else {
      const newEntry: HistoryEntry = {
        ...entry,
        id: `hist_${now}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: now,
        visitCount: 1,
        lastVisitTime: now,
      };
      updatedEntries = [...history.entries, newEntry];
    }

    const newHistory = {
      ...history,
      entries: updatedEntries,
      lastUpdated: now,
      totalVisits: history.totalVisits + 1,
    };

    await saveHistory(newHistory);
  };

  const removeEntry = async (id: string) => {
    const updatedEntries = history.entries.filter(entry => entry.id !== id);
    const newHistory = {
      ...history,
      entries: updatedEntries,
      lastUpdated: Date.now(),
    };
    await saveHistory(newHistory);
  };

  const clearHistory = async () => {
    const newHistory = {
      ...defaultHistoryState,
      lastUpdated: Date.now(),
    };
    await saveHistory(newHistory);
  };

  const searchHistory = (query: string): HistoryEntry[] => {
    const searchTerm = query.toLowerCase();
    return history.entries.filter(
      entry =>
        entry.url.toLowerCase().includes(searchTerm) ||
        entry.title.toLowerCase().includes(searchTerm)
    );
  };

  const getMostVisited = (limit: number = 10): HistoryEntry[] => {
    return [...history.entries]
      .sort((a, b) => b.visitCount - a.visitCount)
      .slice(0, limit);
  };

  const getRecentHistory = (limit: number = 10): HistoryEntry[] => {
    return [...history.entries]
      .sort((a, b) => b.lastVisitTime - a.lastVisitTime)
      .slice(0, limit);
  };

  const getHistoryByDate = (startDate: Date, endDate: Date): HistoryEntry[] => {
    const startTime = startDate.getTime();
    const endTime = endDate.getTime();
    return history.entries.filter(
      entry => entry.lastVisitTime >= startTime && entry.lastVisitTime <= endTime
    );
  };

  const addSearchQuery = async (query: string) => {
    const newHistory = {
      ...history,
      searchHistory: [...history.searchHistory, query],
      lastUpdated: Date.now(),
    };
    await saveHistory(newHistory);
  };

  const clearSearchHistory = async () => {
    const newHistory = {
      ...history,
      searchHistory: [],
      lastUpdated: Date.now(),
    };
    await saveHistory(newHistory);
  };

  const getSearchSuggestions = (query: string): string[] => {
    const searchTerm = query.toLowerCase();
    return history.searchHistory
      .filter(suggestion => suggestion.toLowerCase().includes(searchTerm))
      .slice(0, 5);
  };

  const value = {
    history,
    addEntry,
    removeEntry,
    clearHistory,
    searchHistory,
    getMostVisited,
    getRecentHistory,
    getHistoryByDate,
    addSearchQuery,
    clearSearchHistory,
    getSearchSuggestions,
  };

  return (
    <HistoryContext.Provider value={value}>
      {children}
    </HistoryContext.Provider>
  );
}; 