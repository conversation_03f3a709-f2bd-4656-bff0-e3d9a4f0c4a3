import React, { createContext, useContext, useEffect, useState } from 'react';
import { useStorage } from './StorageProvider';

interface Tab {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  isActive: boolean;
  isPinned: boolean;
  isMuted: boolean;
  isLoading: boolean;
  canGoBack: boolean;
  canGoForward: boolean;
  groupId: string | null;
  createdAt: number;
  lastAccessed: number;
  order: number;
  status: 'loading' | 'complete' | 'error';
  error?: string;
}

interface TabGroup {
  id: string;
  name: string;
  color: string;
  createdAt: number;
  lastAccessed: number;
  order: number;
}

interface TabsState {
  tabs: Tab[];
  groups: TabGroup[];
  activeTabId: string | null;
  lastUpdated: number;
  totalTabs: number;
  totalGroups: number;
}

interface TabsContextType {
  tabs: TabsState;
  addTab: (url: string, options?: Partial<Omit<Tab, 'id' | 'createdAt' | 'lastAccessed' | 'order'>>) => Promise<string>;
  removeTab: (id: string) => Promise<void>;
  updateTab: (id: string, updates: Partial<Tab>) => Promise<void>;
  activateTab: (id: string) => Promise<void>;
  moveTab: (id: string, newIndex: number) => Promise<void>;
  pinTab: (id: string) => Promise<void>;
  unpinTab: (id: string) => Promise<void>;
  muteTab: (id: string) => Promise<void>;
  unmuteTab: (id: string) => Promise<void>;
  createGroup: (name: string, color: string) => Promise<string>;
  updateGroup: (id: string, updates: Partial<TabGroup>) => Promise<void>;
  removeGroup: (id: string) => Promise<void>;
  addTabToGroup: (tabId: string, groupId: string | null) => Promise<void>;
  moveGroup: (groupId: string, newIndex: number) => Promise<void>;
  getTab: (id: string) => Tab | undefined;
  getGroup: (id: string) => TabGroup | undefined;
  getTabsInGroup: (groupId: string | null) => Tab[];
  getActiveTab: () => Tab | undefined;
  getPinnedTabs: () => Tab[];
  getUnpinnedTabs: () => Tab[];
  closeAllTabs: () => Promise<void>;
  closeOtherTabs: (id: string) => Promise<void>;
  closeTabsToTheRight: (id: string) => Promise<void>;
  closeTabsToTheLeft: (id: string) => Promise<void>;
  duplicateTab: (id: string) => Promise<string>;
}

const defaultTabsState: TabsState = {
  tabs: [],
  groups: [],
  activeTabId: null,
  lastUpdated: Date.now(),
  totalTabs: 0,
  totalGroups: 0,
};

const TabsContext = createContext<TabsContextType | null>(null);

export const useTabs = () => {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('useTabs must be used within a TabsProvider');
  }
  return context;
};

interface TabsProviderProps {
  children: React.ReactNode;
}

export const TabsProvider: React.FC<TabsProviderProps> = ({ children }) => {
  const storage = useStorage();
  const [tabs, setTabs] = useState<TabsState>(defaultTabsState);

  useEffect(() => {
    const loadTabs = async () => {
      try {
        const savedTabs = await storage.getItem('browserTabs');
        if (savedTabs) {
          setTabs(JSON.parse(savedTabs));
        }
      } catch (error) {
        console.error('Error loading tabs:', error);
      }
    };

    loadTabs();
  }, [storage]);

  const saveTabs = async (newTabs: TabsState) => {
    try {
      await storage.setItem('browserTabs', JSON.stringify(newTabs));
      setTabs(newTabs);
    } catch (error) {
      console.error('Error saving tabs:', error);
    }
  };

  const addTab = async (url: string, options: Partial<Omit<Tab, 'id' | 'createdAt' | 'lastAccessed' | 'order'>> = {}): Promise<string> => {
    const now = Date.now();
    const id = `tab_${now}_${Math.random().toString(36).substr(2, 9)}`;
    
    const newTab: Tab = {
      id,
      url,
      title: '',
      isActive: true,
      isPinned: false,
      isMuted: false,
      isLoading: true,
      canGoBack: false,
      canGoForward: false,
      groupId: null,
      createdAt: now,
      lastAccessed: now,
      order: tabs.tabs.length,
      status: 'loading',
      ...options,
    };

    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.map(tab => ({ ...tab, isActive: false })).concat(newTab),
      activeTabId: id,
      totalTabs: tabs.totalTabs + 1,
      lastUpdated: now,
    };

    await saveTabs(newTabs);
    return id;
  };

  const removeTab = async (id: string) => {
    const tab = tabs.tabs.find(t => t.id === id);
    if (!tab) return;

    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.filter(t => t.id !== id),
      activeTabId: tab.isActive ? tabs.tabs.find(t => t.id !== id)?.id || null : tabs.activeTabId,
      totalTabs: tabs.totalTabs - 1,
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const updateTab = async (id: string, updates: Partial<Tab>) => {
    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.map(tab =>
        tab.id === id
          ? { ...tab, ...updates, lastAccessed: Date.now() }
          : tab
      ),
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const activateTab = async (id: string) => {
    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.map(tab =>
        tab.id === id
          ? { ...tab, isActive: true, lastAccessed: Date.now() }
          : { ...tab, isActive: false }
      ),
      activeTabId: id,
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const moveTab = async (id: string, newIndex: number) => {
    const tab = tabs.tabs.find(t => t.id === id);
    if (!tab) return;

    const oldIndex = tabs.tabs.indexOf(tab);
    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.map((t, index) => {
        if (index === oldIndex) return { ...t, order: newIndex };
        if (index >= newIndex && index < oldIndex) return { ...t, order: t.order + 1 };
        if (index <= newIndex && index > oldIndex) return { ...t, order: t.order - 1 };
        return t;
      }).sort((a, b) => a.order - b.order),
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const pinTab = async (id: string) => {
    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.map(tab =>
        tab.id === id
          ? { ...tab, isPinned: true, lastAccessed: Date.now() }
          : tab
      ),
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const unpinTab = async (id: string) => {
    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.map(tab =>
        tab.id === id
          ? { ...tab, isPinned: false, lastAccessed: Date.now() }
          : tab
      ),
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const muteTab = async (id: string) => {
    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.map(tab =>
        tab.id === id
          ? { ...tab, isMuted: true, lastAccessed: Date.now() }
          : tab
      ),
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const unmuteTab = async (id: string) => {
    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.map(tab =>
        tab.id === id
          ? { ...tab, isMuted: false, lastAccessed: Date.now() }
          : tab
      ),
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const createGroup = async (name: string, color: string): Promise<string> => {
    const now = Date.now();
    const id = `group_${now}_${Math.random().toString(36).substr(2, 9)}`;
    
    const newGroup: TabGroup = {
      id,
      name,
      color,
      createdAt: now,
      lastAccessed: now,
      order: tabs.groups.length,
    };

    const newTabs = {
      ...tabs,
      groups: [...tabs.groups, newGroup],
      totalGroups: tabs.totalGroups + 1,
      lastUpdated: now,
    };

    await saveTabs(newTabs);
    return id;
  };

  const updateGroup = async (id: string, updates: Partial<TabGroup>) => {
    const newTabs = {
      ...tabs,
      groups: tabs.groups.map(group =>
        group.id === id
          ? { ...group, ...updates, lastAccessed: Date.now() }
          : group
      ),
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const removeGroup = async (id: string) => {
    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.map(tab =>
        tab.groupId === id
          ? { ...tab, groupId: null, lastAccessed: Date.now() }
          : tab
      ),
      groups: tabs.groups.filter(group => group.id !== id),
      totalGroups: tabs.totalGroups - 1,
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const addTabToGroup = async (tabId: string, groupId: string | null) => {
    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.map(tab =>
        tab.id === tabId
          ? { ...tab, groupId, lastAccessed: Date.now() }
          : tab
      ),
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const moveGroup = async (groupId: string, newIndex: number) => {
    const group = tabs.groups.find(g => g.id === groupId);
    if (!group) return;

    const oldIndex = tabs.groups.indexOf(group);
    const newTabs = {
      ...tabs,
      groups: tabs.groups.map((g, index) => {
        if (index === oldIndex) return { ...g, order: newIndex };
        if (index >= newIndex && index < oldIndex) return { ...g, order: g.order + 1 };
        if (index <= newIndex && index > oldIndex) return { ...g, order: g.order - 1 };
        return g;
      }).sort((a, b) => a.order - b.order),
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const getTab = (id: string): Tab | undefined => {
    return tabs.tabs.find(tab => tab.id === id);
  };

  const getGroup = (id: string): TabGroup | undefined => {
    return tabs.groups.find(group => group.id === id);
  };

  const getTabsInGroup = (groupId: string | null): Tab[] => {
    return tabs.tabs.filter(tab => tab.groupId === groupId);
  };

  const getActiveTab = (): Tab | undefined => {
    return tabs.tabs.find(tab => tab.id === tabs.activeTabId);
  };

  const getPinnedTabs = (): Tab[] => {
    return tabs.tabs.filter(tab => tab.isPinned);
  };

  const getUnpinnedTabs = (): Tab[] => {
    return tabs.tabs.filter(tab => !tab.isPinned);
  };

  const closeAllTabs = async () => {
    const newTabs = {
      ...defaultTabsState,
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const closeOtherTabs = async (id: string) => {
    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.filter(tab => tab.id === id),
      activeTabId: id,
      totalTabs: 1,
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const closeTabsToTheRight = async (id: string) => {
    const tab = tabs.tabs.find(t => t.id === id);
    if (!tab) return;

    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.filter(t => t.order <= tab.order),
      totalTabs: tab.order + 1,
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const closeTabsToTheLeft = async (id: string) => {
    const tab = tabs.tabs.find(t => t.id === id);
    if (!tab) return;

    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.filter(t => t.order >= tab.order),
      totalTabs: tabs.tabs.length - tab.order,
      lastUpdated: Date.now(),
    };

    await saveTabs(newTabs);
  };

  const duplicateTab = async (id: string): Promise<string> => {
    const tab = tabs.tabs.find(t => t.id === id);
    if (!tab) throw new Error('Tab not found');

    const now = Date.now();
    const newId = `tab_${now}_${Math.random().toString(36).substr(2, 9)}`;
    
    const newTab: Tab = {
      ...tab,
      id: newId,
      isActive: true,
      createdAt: now,
      lastAccessed: now,
      order: tabs.tabs.length,
    };

    const newTabs = {
      ...tabs,
      tabs: tabs.tabs.map(t => ({ ...t, isActive: false })).concat(newTab),
      activeTabId: newId,
      totalTabs: tabs.totalTabs + 1,
      lastUpdated: now,
    };

    await saveTabs(newTabs);
    return newId;
  };

  const value = {
    tabs,
    addTab,
    removeTab,
    updateTab,
    activateTab,
    moveTab,
    pinTab,
    unpinTab,
    muteTab,
    unmuteTab,
    createGroup,
    updateGroup,
    removeGroup,
    addTabToGroup,
    moveGroup,
    getTab,
    getGroup,
    getTabsInGroup,
    getActiveTab,
    getPinnedTabs,
    getUnpinnedTabs,
    closeAllTabs,
    closeOtherTabs,
    closeTabsToTheRight,
    closeTabsToTheLeft,
    duplicateTab,
  };

  return (
    <TabsContext.Provider value={value}>
      {children}
    </TabsContext.Provider>
  );
}; 