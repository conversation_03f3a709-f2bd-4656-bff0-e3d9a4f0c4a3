import React, { createContext, useContext, useEffect, useState } from 'react';
import { useStorage } from './StorageProvider';
import { useAppState } from './StateProvider';

interface SyncItem {
  id: string;
  type: 'bookmark' | 'history' | 'setting' | 'extension' | 'tab' | 'download';
  data: any;
  timestamp: number;
  status: 'pending' | 'synced' | 'error';
  error?: string;
}

interface SyncState {
  items: SyncItem[];
  lastSync: number;
  isSyncing: boolean;
  error?: string;
  syncInterval: number;
  autoSync: boolean;
}

interface SyncContextType {
  state: SyncState;
  sync: () => Promise<void>;
  addItem: (type: SyncItem['type'], data: any) => Promise<void>;
  removeItem: (id: string) => Promise<void>;
  clearItems: () => Promise<void>;
  updateSettings: (settings: Partial<Pick<SyncState, 'syncInterval' | 'autoSync'>>) => Promise<void>;
  getItemsByType: (type: SyncItem['type']) => SyncItem[];
  getPendingItems: () => SyncItem[];
  getLastSyncTime: () => number;
}

const defaultSyncState: SyncState = {
  items: [],
  lastSync: 0,
  isSyncing: false,
  syncInterval: 5 * 60 * 1000, // 5 minutes
  autoSync: true,
};

const SyncContext = createContext<SyncContextType | null>(null);

export const useSync = () => {
  const context = useContext(SyncContext);
  if (!context) {
    throw new Error('useSync must be used within a SyncProvider');
  }
  return context;
};

interface SyncProviderProps {
  children: React.ReactNode;
}

export const SyncProvider: React.FC<SyncProviderProps> = ({ children }) => {
  const storage = useStorage();
  const { getState, setState: setAppState } = useAppState();
  const [syncState, setSyncState] = useState<SyncState>(defaultSyncState);

  useEffect(() => {
    const loadSync = async () => {
      try {
        const savedSync = await storage.getItem('browserSync');
        if (savedSync) {
          setSyncState(JSON.parse(savedSync));
        }
      } catch (error) {
        console.error('Error loading sync settings:', error);
      }
    };

    loadSync();
  }, [storage]);

  useEffect(() => {
    let syncInterval: NodeJS.Timeout;

    if (syncState.autoSync) {
      syncInterval = setInterval(() => {
        sync();
      }, syncState.syncInterval);
    }

    return () => {
      if (syncInterval) {
        clearInterval(syncInterval);
      }
    };
  }, [syncState.autoSync, syncState.syncInterval]);

  const saveSync = async (newState: SyncState) => {
    try {
      await storage.setItem('browserSync', JSON.stringify(newState));
      setSyncState(newState);
    } catch (error) {
      console.error('Error saving sync settings:', error);
    }
  };

  const sync = async () => {
    if (syncState.isSyncing) return;

    setSyncState((prev: SyncState) => ({ ...prev, isSyncing: true, error: undefined }));

    try {
      const pendingItems = syncState.items.filter(item => item.status === 'pending');
      
      for (const item of pendingItems) {
        try {
          // Simulate sync operation
          await new Promise(resolve => setTimeout(resolve, 100));

          // Update item status
          const updatedItems = syncState.items.map(i => 
            i.id === item.id ? { ...i, status: 'synced' as const, timestamp: Date.now() } : i
          );

          setSyncState((prev: SyncState) => ({
            ...prev,
            items: updatedItems,
            lastSync: Date.now(),
          }));

          // Update corresponding state
          const currentState = getState(item.type) || {};
          await setAppState(item.type, { ...currentState, ...item.data });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          
          setSyncState((prev: SyncState) => ({
            ...prev,
            items: prev.items.map(i => 
              i.id === item.id ? { ...i, status: 'error', error: errorMessage } : i
            ),
          }));
        }
      }

      await saveSync(syncState);
    } catch (error) {
      setSyncState((prev: SyncState) => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error',
      }));
    } finally {
      setSyncState((prev: SyncState) => ({ ...prev, isSyncing: false }));
    }
  };

  const addItem = async (type: SyncItem['type'], data: any) => {
    const newItem: SyncItem = {
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: Date.now(),
      status: 'pending',
    };

    const newState = {
      ...syncState,
      items: [...syncState.items, newItem],
    };

    await saveSync(newState);

    if (syncState.autoSync) {
      await sync();
    }
  };

  const removeItem = async (id: string) => {
    const newState = {
      ...syncState,
      items: syncState.items.filter(item => item.id !== id),
    };

    await saveSync(newState);
  };

  const clearItems = async () => {
    const newState = {
      ...syncState,
      items: [],
      lastSync: Date.now(),
    };

    await saveSync(newState);
  };

  const updateSettings = async (settings: Partial<Pick<SyncState, 'syncInterval' | 'autoSync'>>) => {
    const newState = {
      ...syncState,
      ...settings,
    };

    await saveSync(newState);
  };

  const getItemsByType = (type: SyncItem['type']) => {
    return syncState.items.filter(item => item.type === type);
  };

  const getPendingItems = () => {
    return syncState.items.filter(item => item.status === 'pending');
  };

  const getLastSyncTime = () => {
    return syncState.lastSync;
  };

  const value = {
    state: syncState,
    sync,
    addItem,
    removeItem,
    clearItems,
    updateSettings,
    getItemsByType,
    getPendingItems,
    getLastSyncTime,
  };

  return (
    <SyncContext.Provider value={value}>
      {children}
    </SyncContext.Provider>
  );
}; 