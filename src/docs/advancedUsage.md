# Расширенное использование

## Архитектурная схема
```mermaid
flowchart TD
  A[Компонент формы] --> B(useFormValidation)
  B --> C{Валидация}
  C -->|Синхронная| D[Локальные правила]
  C -->|Асинхронная| E[Серверная валидация]
  E --> F[Утилиты безопасности]
  E --> G[Retry-логика]
  B --> H[Обработка ошибок]
  H --> I[Логирование]
  H --> J[Пользовательские сообщения]
```

## Миграция с v1.x на v2.0
```ts
// Было
const { errors } = useForm(initialValues);

// Стало
const {
  errors,
  validateField,
  resetValidation
} = useFormValidation(initialValues, schema);
```

## Мультиязычная валидация
```tsx
const i18nSchema = {
  email: (v) => ({
    en: /^[^\s@]+@[^\s@]+$/.test(v) || 'Invalid email',
    ru: /^[^\s@]+@[^\s@]+$/.test(v) || 'Некорректный email'
  })[locale]
};
```

## Тестирование
```tsx
const TestWrapper = ({ children }) => (
  <SecurityProvider config={testSecurityConfig}>
    <ValidationProvider>
      {children}
    </ValidationProvider>
  </SecurityProvider>
);

const { result } = renderHook(
  () => useFormValidation(testValues, testSchema),
  { wrapper: TestWrapper }
);
```

## Частые сценарии
### Динамические поля
```tsx
const dynamicSchema = {
  [fieldName]: dynamicValidationRules
};
```

### Составные правила
```ts
const compositeValidator = (value) => {
  const basicCheck = required(value);
  if (basicCheck !== true) return basicCheck;
  return formatCheck(value);
};
```