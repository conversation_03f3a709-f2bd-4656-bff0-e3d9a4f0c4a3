{
  "app.name": "A11 Browser",
  "app.description": "Modern and secure web browser",
  "tabs.new": "New Tab",
  "tabs.close": "Close Tab",
  "tabs.closeOthers": "Close Other Tabs",
  "tabs.reopen": "Reopen Closed Tab",
  "tabs.group": "Group Tabs",
  "tabs.ungroup": "Ungroup Tabs",
  "tabs.groupName": "Group Name",
  "navigation.back": "Back",
  "navigation.forward": "Forward",
  "navigation.reload": "Reload",
  "navigation.home": "Home",
  "bookmarks.add": "Add Bookmark",
  "bookmarks.remove": "Remove Bookmark",
  "bookmarks.all": "All Bookmarks",
  "history.show": "History",
  "history.clear": "Clear History",
  "downloads.show": "Downloads",
  "settings.show": "Settings",
  "settings.theme": "Theme",
  "settings.language": "Language",
  "settings.privacy": "Privacy",
  "settings.security": "Security",
  "settings.advanced": "Advanced",
  "search.placeholder": "Search or enter website address",
  "search.engines": "Search Engines",
  "error.pageNotFound": "Page Not Found",
  "error.connectionFailed": "Connection Failed",
  "error.insecureConnection": "Insecure Connection",
  "security.phishingDetected": "Phishing Site Detected",
  "security.malwareDetected": "Malware Site Detected",
  "security.certificateError": "Certificate Error",
  "security.proceedAnyway": "Proceed Anyway (Unsafe)",
  "security.goBack": "Go Back",
  "preload.status": "Page Preloading",
  "preload.enable": "Enable Preloading",
  "preload.disable": "Disable Preloading",
  "wasm.status": "WebAssembly Support",
  "wasm.enable": "Enable WebAssembly",
  "wasm.disable": "Disable WebAssembly"
,
,
  "passwords.saved": "Saved Passwords",
  "passwords.add": "Add Password",
  "passwords.cancel": "Cancel",
  "passwords.username": "Username",
  "passwords.password": "Password",
  "passwords.notes": "Notes",
  "passwords.copy": "Copy password",
  "passwords.show": "Show password",
  "passwords.hide": "Hide password",
  "passwords.delete": "Delete",
  "passwords.used": "Last used: {{date}}",
  "passwords.changed": "Last changed: {{date}}",
  "passwords.strength.weak": "Weak",
  "passwords.strength.medium": "Medium",
  "passwords.strength.strong": "Strong"
  "browser.title": "A14 Browser",
  "browser.welcome": "Welcome to A14 Browser! This is a modern, feature-rich browser built with React and Electron.",
  "errorBoundary.header": "Something went wrong",
  "errorBoundary.reload": "Reload Page"
}