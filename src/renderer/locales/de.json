{"app.name": "A11 Browser", "app.description": "<PERSON><PERSON> und sicherer <PERSON>", "tabs.new": "<PERSON><PERSON><PERSON>", "tabs.close": "<PERSON><PERSON> s<PERSON>n", "tabs.closeOthers": "Andere Tabs schließen", "tabs.reopen": "Geschlossenen Tab wieder <PERSON>", "tabs.group": "Tabs gruppieren", "tabs.ungroup": "Gruppierung aufheben", "tabs.groupName": "Gruppenname", "navigation.back": "Zurück", "navigation.forward": "Vorwärts", "navigation.reload": "Aktualisieren", "navigation.home": "Startseite", "bookmarks.add": "Lesezeichen hinzufügen", "bookmarks.remove": "Lesezeichen entfernen", "bookmarks.all": "Alle Lesezeichen", "history.show": "<PERSON><PERSON><PERSON><PERSON>", "history.clear": "<PERSON><PERSON><PERSON><PERSON>", "downloads.show": "Downloads", "settings.show": "Einstellungen", "settings.theme": "Design", "settings.language": "<PERSON><PERSON><PERSON>", "settings.privacy": "Datenschutz", "settings.security": "Sicherheit", "settings.advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search.placeholder": "Suchen oder Website-Adresse eingeben", "search.engines": "Suchmaschinen", "error.pageNotFound": "Seite nicht gefunden", "error.connectionFailed": "Verbindung fehlgeschlagen", "error.insecureConnection": "Unsichere Verbindung", "security.phishingDetected": "Phishing-<PERSON><PERSON> er<PERSON>", "security.malwareDetected": "Malware-<PERSON><PERSON> er<PERSON>", "security.certificateError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "security.proceedAnyway": "Trotzdem fortfahren (unsicher)", "security.goBack": "Zurückgehen", "preload.status": "Seiten-Vorabladen", "preload.enable": "Vorabladen aktivieren", "preload.disable": "Vorabladen deaktivieren", "wasm.status": "WebAssembly-Unterstützung", "wasm.enable": "WebAssembly aktivieren", "wasm.disable": "WebAssembly deaktivieren"}