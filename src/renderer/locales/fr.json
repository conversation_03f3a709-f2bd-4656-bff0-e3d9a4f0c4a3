{"app.name": "Navigateur A11", "app.description": "Navigateur Web moderne et sécurisé", "tabs.new": "Nouvel onglet", "tabs.close": "<PERSON><PERSON><PERSON> l'on<PERSON>t", "tabs.closeOthers": "<PERSON><PERSON><PERSON> les autres onglets", "tabs.reopen": "Réouvrir l'onglet fermé", "tabs.group": "Grouper <PERSON>", "tabs.ungroup": "Dégrouper les onglets", "tabs.groupName": "Nom du groupe", "navigation.back": "Précédent", "navigation.forward": "Suivant", "navigation.reload": "Actualiser", "navigation.home": "Page d'accueil", "bookmarks.add": "Ajouter aux favoris", "bookmarks.remove": "Supprimer des favoris", "bookmarks.all": "Tous les favoris", "history.show": "Historique", "history.clear": "Effacer l'historique", "downloads.show": "Téléchargements", "settings.show": "Paramètres", "settings.theme": "Thème", "settings.language": "<PERSON><PERSON>", "settings.privacy": "Confidentialité", "settings.security": "Sécurité", "settings.advanced": "<PERSON><PERSON><PERSON>", "search.placeholder": "Rechercher ou entrer l'adresse du site Web", "search.engines": "Moteurs de recherche", "error.pageNotFound": "Page non trouvée", "error.connectionFailed": "Échec de la connexion", "error.insecureConnection": "Connexion non sécurisée", "security.phishingDetected": "Site de phishing détecté", "security.malwareDetected": "Site malveillant détecté", "security.certificateError": "Erreur de certificat", "security.proceedAnyway": "Continuer quand même (non sécurisé)", "security.goBack": "<PERSON><PERSON><PERSON>", "preload.status": "Préchargement des pages", "preload.enable": "<PERSON>r le préchargement", "preload.disable": "Désactiver le préchargement", "wasm.status": "Prise en charge de WebAssembly", "wasm.enable": "Activer WebAssembly", "wasm.disable": "Désactiver WebAssembly"}