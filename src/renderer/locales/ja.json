{"app.name": "A11 ブラウザ", "app.description": "モダンで安全なウェブブラウザ", "tabs.new": "新しいタブ", "tabs.close": "タブを閉じる", "tabs.closeOthers": "他のタブを閉じる", "tabs.reopen": "閉じたタブを再度開く", "tabs.group": "タブをグループ化", "tabs.ungroup": "グループ化を解除", "tabs.groupName": "グループ名", "navigation.back": "戻る", "navigation.forward": "進む", "navigation.reload": "再読み込み", "navigation.home": "ホームページ", "bookmarks.add": "ブックマークに追加", "bookmarks.remove": "ブックマークから削除", "bookmarks.all": "すべてのブックマーク", "history.show": "履歴", "history.clear": "履歴を消去", "downloads.show": "ダウンロード", "settings.show": "設定", "settings.theme": "テーマ", "settings.language": "言語", "settings.privacy": "プライバシー", "settings.security": "セキュリティ", "settings.advanced": "詳細設定", "search.placeholder": "検索またはウェブサイトアドレスを入力", "search.engines": "検索エンジン", "error.pageNotFound": "ページが見つかりません", "error.connectionFailed": "接続に失敗しました", "error.insecureConnection": "安全でない接続", "security.phishingDetected": "フィッシングサイトが検出されました", "security.malwareDetected": "マルウェアサイトが検出されました", "security.certificateError": "証明書エラー", "security.proceedAnyway": "続行する（安全ではありません）", "security.goBack": "戻る", "preload.status": "ページのプリロード", "preload.enable": "プリロードを有効にする", "preload.disable": "プリロードを無効にする", "wasm.status": "WebAssembly サポート", "wasm.enable": "WebAssembly を有効にする", "wasm.disable": "WebAssembly を無効にする"}