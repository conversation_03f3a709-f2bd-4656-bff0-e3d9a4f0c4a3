// Основной файл рендерера (renderer.js)
// Этот файл теперь должен координировать работу различных менеджеров.

// Импортируем менеджеры
// Важно: убедитесь, что пути к файлам корректны и используется type="module" в HTML для поддержки ES6 модулей.
import tabManager from './tabManager.js';
import navigationManager from './navigationManager.js';
import bookmarkManager from './bookmarkManager.js';
import historyManager from './historyManager.js';
import settingsManager from './settingsManager.js';
import uiUtils from './uiUtils.js'; // uiUtils может не иметь 'default' экспорта, если все функции именованные
import localizationManager from './localizationManager.js';
import themeManager from './theme-manager.js';
import { uiManager } from './ui-manager.js'; // Импортируем uiManager

// Глобальное пространство имен для доступа к менеджерам из консоли или других скриптов (если необходимо)
// В идеале, модули должны взаимодействовать через импорты/экспорты или систему событий.
window.tabManager = tabManager;
window.navigationManager = navigationManager;
window.bookmarkManager = bookmarkManager;
window.historyManager = historyManager;
window.settingsManager = settingsManager;
window.uiUtils = uiUtils; // Если uiUtils экспортирует объект по умолчанию
window.localizationManager = localizationManager;
window.themeManager = themeManager;
window.uiManager = uiManager; // Добавляем uiManager в глобальное пространство имен

/**
 * @description Initializes all browser modules and managers.
 * Порядок инициализации может быть важен, если есть зависимости.
 */
async function initializeBrowser() {
    console.log('Initializing A11 Browser components...');

    // 0. Менеджер локализации (должен быть инициализирован рано для применения переводов)
    await localizationManager.initialize(); // Загружаем и применяем переводы

    // 0.1. Менеджер тем (должен быть инициализирован рано для применения темы)
    await themeManager.initialize(); // Загружаем и применяем тему

    // 0.2. Менеджер UI (должен быть инициализирован перед модулями, которые его используют, например, tabManager)
    if (uiManager && typeof uiManager.initialize === 'function') {
      await uiManager.initialize(); // Загружаем настройки анимаций и т.д.
    } else {
      console.warn('UIManager не найден или не имеет метода initialize.');
    }

    // 1. Настройки (могут влиять на другие модули, например, тема, домашняя страница)
    // settingsManager.initializeSettings() вызывается внутри самого settingsManager.js при DOMContentLoaded
    // Убедимся, что он загружен и готов к моменту, когда другие модули могут запросить настройки.
    // Если есть асинхронные операции в initializeSettings, можно дождаться их завершения.
    if (settingsManager && typeof settingsManager.initializeSettings === 'function') {
        await settingsManager.initializeSettings();
    } else {
        console.error('SettingsManager не найден или не имеет метода initializeSettings.');
    }

    // 2. UI Утилиты (могут понадобиться другим менеджерам для отображения элементов)
    // uiUtils обычно не требует специальной инициализации, его функции просто вызываются.

    // 3. Менеджер вкладок (основной компонент для отображения контента)
    // Убедимся, что tabManager.initializeTabManager вызывается после uiManager
    if (tabManager && typeof tabManager.initializeTabManager === 'function') {
        tabManager.initializeTabManager();
    } else {
        console.error('TabManager не найден или не имеет метода initializeTabManager.');
    }
    // await tabManager.initializeTabs(); // Если бы это было необходимо

    // 4. Менеджер навигации (кнопки назад/вперед, адресная строка)
    // navigationManager.initializeNavigation() вызывается внутри navigationManager.js

    // 5. Менеджер закладок
    // bookmarkManager.initializeBookmarks() вызывается внутри bookmarkManager.js

    // 6. Менеджер истории
    // historyManager.initializeHistory() вызывается внутри historyManager.js

    // После инициализации всех основных менеджеров, можно создать начальную вкладку, если это не делает tabManager
    // tabManager.createNewTab(); // Это уже должно происходить внутри tabManager.js

    console.log('A11 Browser components initialized.');

    // Пример: Проверка доступности менеджеров (для отладки)
    // setTimeout(() => {
    //     console.log('Active Tab (from renderer.js):', window.tabManager?.getActiveTab());
    //     console.log('Current Settings (from renderer.js):', window.settingsManager?.getCurrentSettings());
    // }, 2000);
}

// Запускаем инициализацию после полной загрузки DOM
document.addEventListener('DOMContentLoaded', initializeBrowser);

// Обработка IPC сообщений от главного процесса (если есть)
// Пример: получение обновленных настроек
if (window.electronAPI && typeof window.electronAPI.onSettingsChanged === 'function') {
    window.electronAPI.onSettingsChanged((updatedSettings) => {
        console.log('Renderer.js: Settings updated from main process:', updatedSettings);
        // settingsManager должен сам слушать это событие или иметь метод для обновления
        if (window.settingsManager && typeof window.settingsManager.applySettings === 'function') {
            window.settingsManager.applySettings(updatedSettings); // Применяем глобально
            window.settingsManager.updateSettingsUI(); // Обновляем UI настроек
        }
    });
}

// Пример: обработка запроса на создание новой вкладки из главного процесса
if (window.electronAPI && typeof window.electronAPI.requestNewTab === 'function') {
    window.electronAPI.requestNewTab((url) => {
        console.log(`Renderer.js: New tab requested from main process with URL: ${url}`);
        if (window.tabManager && typeof window.tabManager.createNewTab === 'function') {
            window.tabManager.createNewTab(url || window.settingsManager?.getSetting('homepage') || 'about:blank', true);
        }
    });
}

console.log('renderer.js loaded and executing.');