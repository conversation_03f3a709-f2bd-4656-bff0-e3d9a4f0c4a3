// Модуль для управления закладками

/**
 * @file bookmarkManager.js
 * @description Manages browser bookmarks, including adding, removing, and displaying bookmarks.
 */

// import { getActiveTab } from './tabManager.js'; // Предполагается для получения URL активной вкладки

// DOM Elements
const addBookmarkButton = document.getElementById('add-bookmark-button');
const bookmarksList = document.getElementById('bookmarks-list'); // Предполагается, что есть такой элемент для отображения
const bookmarkTemplate = document.getElementById('bookmark-item-template'); // Для создания элементов списка закладок

let bookmarks = [];

/**
 * @description Initializes bookmark functionality, loads existing bookmarks, and sets up event listeners.
 */
async function initializeBookmarks() {
  await loadBookmarks();
  renderBookmarks();

  if (addBookmarkButton) {
    addBookmarkButton.addEventListener('click', toggleBookmark);
  }

  // Обновляем состояние кнопки закладки при смене вкладки или навигации
  document.addEventListener('active-tab-changed', () => {
    const activeTab = window.tabManager.getActiveTab();
    if (activeTab) {
      updateBookmarkButtonState(activeTab.webview.getURL());
    }
  });
  document.addEventListener('webview-did-stop-loading', (event) => {
    if (event.detail && event.detail.tabId === window.tabManager.activeTabId) {
        updateBookmarkButtonState(event.detail.url);
    }
  });

  // Первоначальное обновление состояния кнопки
  const initialActiveTab = window.tabManager.getActiveTab();
  if (initialActiveTab) {
    updateBookmarkButtonState(initialActiveTab.webview.getURL());
  }
}

/**
 * @description Loads bookmarks from storage (e.g., main process via IPC).
 */
async function loadBookmarks() {
  try {
    bookmarks = await window.browserAPI.getBookmarks();
  } catch (error) {
    console.error('Error loading bookmarks:', error);
    bookmarks = [];
  }
}

/**
 * @description Saves bookmarks to storage.
 */
async function saveBookmarks() {
  try {
    await window.browserAPI.saveBookmarks(bookmarks);
  } catch (error) {
    console.error('Error saving bookmarks:', error);
  }
}

/**
 * @description Adds a new bookmark.
 * @param {string} url - The URL of the page to bookmark.
 * @param {string} title - The title of the page.
 */
async function addBookmark(url, title) {
  if (!url || !title) return;
  if (isBookmarked(url)) return; // Уже в закладках

  const newBookmark = {
    id: `bookmark-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    url,
    title,
    timestamp: Date.now(),
    tags: [] // Можно добавить позже
  };
  bookmarks.push(newBookmark);
  await saveBookmarks();
  renderBookmarks(); // Обновляем отображение списка
  updateBookmarkButtonState(url); // Обновляем состояние кнопки
  console.log('Bookmark added:', newBookmark);
}

/**
 * @description Removes a bookmark by its URL.
 * @param {string} url - The URL of the bookmark to remove.
 */
async function removeBookmark(url) {
  bookmarks = bookmarks.filter(bm => bm.url !== url);
  await saveBookmarks();
  renderBookmarks();
  updateBookmarkButtonState(url);
  console.log('Bookmark removed for URL:', url);
}

/**
 * @description Toggles a bookmark for the current active tab.
 */
async function toggleBookmark() {
  const activeTab = window.tabManager.getActiveTab();
  if (!activeTab) return;

  const url = activeTab.webview.getURL();
  const title = activeTab.webview.getTitle();

  if (isBookmarked(url)) {
    await removeBookmark(url);
  } else {
    await addBookmark(url, title);
  }
}

/**
 * @description Checks if a URL is already bookmarked.
 * @param {string} url - The URL to check.
 * @returns {boolean} True if bookmarked, false otherwise.
 */
function isBookmarked(url) {
  return bookmarks.some(bm => bm.url === url);
}

/**
 * @description Updates the visual state of the 'add bookmark' button.
 * @param {string} currentUrl - The URL of the currently active page.
 */
function updateBookmarkButtonState(currentUrl) {
  if (!addBookmarkButton) return;
  if (isBookmarked(currentUrl)) {
    addBookmarkButton.classList.add('active'); // Например, для изменения иконки на 'заполнено'
    addBookmarkButton.title = 'Удалить из закладок';
  } else {
    addBookmarkButton.classList.remove('active');
    addBookmarkButton.title = 'Добавить в закладки';
  }
  // Неактивна для about:blank и других внутренних страниц
  addBookmarkButton.disabled = !currentUrl || currentUrl === 'about:blank' || currentUrl.startsWith('data:');
}

/**
 * @description Renders the list of bookmarks in the UI.
 */
function renderBookmarks() {
  if (!bookmarksList || !bookmarkTemplate) return;

  bookmarksList.innerHTML = ''; // Очищаем предыдущий список

  if (bookmarks.length === 0) {
    bookmarksList.innerHTML = '<p>Закладок пока нет.</p>';
    return;
  }

  bookmarks.forEach(bookmark => {
    const bookmarkNode = bookmarkTemplate.content.cloneNode(true);
    const itemElement = bookmarkNode.querySelector('.bookmark-item'); // Предполагаем такой класс
    const linkElement = itemElement.querySelector('a');
    const removeButton = itemElement.querySelector('.remove-bookmark-button');

    linkElement.href = bookmark.url;
    linkElement.textContent = bookmark.title || bookmark.url;
    linkElement.title = bookmark.url;
    linkElement.addEventListener('click', (e) => {
      e.preventDefault();
      window.tabManager.createNewTab(bookmark.url, true);
    });

    if (removeButton) {
      removeButton.addEventListener('click', async (e) => {
        e.stopPropagation();
        await removeBookmark(bookmark.url);
      });
    }
    bookmarksList.appendChild(itemElement);
  });
}

// Инициализация менеджера закладок
document.addEventListener('DOMContentLoaded', () => {
    if (window.tabManager && typeof window.tabManager.getActiveTab === 'function') {
        initializeBookmarks();
    } else {
        console.warn('BookmarkManager: tabManager not found immediately, will try again after a short delay.');
        setTimeout(() => {
            if (window.tabManager && typeof window.tabManager.getActiveTab === 'function') {
                initializeBookmarks();
            } else {
                console.error('BookmarkManager: tabManager is still not available. Bookmark functionality might not work correctly.');
            }
        }, 150); // Небольшая задержка, чтобы tabManager успел загрузиться
    }
});

// Экспорт для возможного использования другими модулями
export {
  initializeBookmarks,
  addBookmark,
  removeBookmark,
  isBookmarked,
  updateBookmarkButtonState,
  renderBookmarks,
  loadBookmarks, // Экспортируем, если нужно будет перезагружать извне
  bookmarks // Экспортируем сам массив, если нужен прямой доступ (с осторожностью)
};