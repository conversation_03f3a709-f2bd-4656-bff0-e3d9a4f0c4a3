import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Grid,
  Paper,
  ListItemIcon,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Lock as LockIcon,
  VpnKey as VpnKeyIcon,
  History as HistoryIcon,
  Devices as DevicesIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';
import { RootState } from '../../../store';
import {
  updateUserSettings,
  updateUserProfile,
  UserSettings,
} from '../../../store/slices/authSlice';
import { addNotification } from '../../../store/slices/notificationsSlice';

const SecuritySettings: React.FC = () => {
  const dispatch = useDispatch();
  const user = useSelector((state: RootState) => state.auth.currentUser);
  const [openDialog, setOpenDialog] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);

  const handlePasswordChange = () => {
    if (newPassword !== confirmPassword) {
      setError('Пароли не совпадают');
      return;
    }

    if (newPassword.length < 8) {
      setError('Пароль должен содержать минимум 8 символов');
      return;
    }

    // Здесь должна быть логика проверки текущего пароля и обновления
    dispatch(updateUserSettings({
      security: {
        ...user?.settings.security,
        passwordLastChanged: Date.now(),
      },
    }));

    dispatch(addNotification({
      type: 'success',
      title: 'Пароль обновлен',
      message: 'Ваш пароль был успешно изменен',
      priority: 'high',
      category: 'security',
    }));

    setOpenDialog(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
    setError(null);
  };

  const handleTwoFactorToggle = () => {
    dispatch(updateUserSettings({
      security: {
        ...user?.settings.security,
        twoFactorEnabled: !user?.settings.security.twoFactorEnabled,
      },
    }));

    dispatch(addNotification({
      type: 'info',
      title: 'Двухфакторная аутентификация',
      message: user?.settings.security.twoFactorEnabled
        ? 'Двухфакторная аутентификация отключена'
        : 'Двухфакторная аутентификация включена',
      priority: 'high',
      category: 'security',
    }));
  };

  const handlePrivacyToggle = (setting: keyof UserSettings['privacy']) => {
    if (!user) return;
    dispatch(updateUserSettings({
      privacy: {
        ...user.settings.privacy,
        [setting]: !user.settings.privacy[setting],
      },
    }));
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <SecurityIcon />
        Настройки безопасности
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>Аутентификация</Typography>
            <List>
              <ListItem>
                <ListItemText
                  primary="Двухфакторная аутентификация"
                  secondary="Дополнительный уровень защиты вашего аккаунта"
                />
                <ListItemSecondaryAction>
                  <Switch
                    edge="end"
                    checked={user?.settings.security.twoFactorEnabled}
                    onChange={handleTwoFactorToggle}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Сменить пароль"
                  secondary={`Последнее изменение: ${formatDate(user?.settings.security.passwordLastChanged || 0)}`}
                />
                <ListItemSecondaryAction>
                  <Button
                    variant="outlined"
                    onClick={() => setOpenDialog(true)}
                  >
                    Изменить
                  </Button>
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>Конфиденциальность</Typography>
            <List>
              <ListItem>
                <ListItemText
                  primary="История просмотров"
                  secondary="Сохранять историю посещенных страниц"
                />
                <ListItemSecondaryAction>
                  <Switch
                    edge="end"
                    checked={user?.settings.privacy.historyEnabled}
                    onChange={() => handlePrivacyToggle('historyEnabled')}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Cookies"
                  secondary="Разрешить сохранение cookies"
                />
                <ListItemSecondaryAction>
                  <Switch
                    edge="end"
                    checked={user?.settings.privacy.cookiesEnabled}
                    onChange={() => handlePrivacyToggle('cookiesEnabled')}
                  />
                </ListItemSecondaryAction>
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Отслеживание"
                  secondary="Разрешить отслеживание активности"
                />
                <ListItemSecondaryAction>
                  <Switch
                    edge="end"
                    checked={user?.settings.privacy.trackingEnabled}
                    onChange={() => handlePrivacyToggle('trackingEnabled')}
                  />
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>Активность</Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <HistoryIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Последний вход"
                  secondary={formatDate(user?.lastLogin || 0)}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <DevicesIcon />
                </ListItemIcon>
                <ListItemText
                  primary="Активные устройства"
                  secondary="2 устройства"
                />
                <ListItemSecondaryAction>
                  <Button variant="outlined" size="small">
                    Управление
                  </Button>
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </Paper>
        </Grid>
      </Grid>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Сменить пароль</DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          <TextField
            autoFocus
            margin="dense"
            label="Текущий пароль"
            type="password"
            fullWidth
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
          />
          <TextField
            margin="dense"
            label="Новый пароль"
            type="password"
            fullWidth
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
          />
          <TextField
            margin="dense"
            label="Подтвердите новый пароль"
            type="password"
            fullWidth
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Отмена</Button>
          <Button onClick={handlePasswordChange} variant="contained">
            Сохранить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SecuritySettings; 