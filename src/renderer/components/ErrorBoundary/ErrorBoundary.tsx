import React, { Component, ErrorInfo, ReactNode, createRef } from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import { styled } from '@mui/material/styles';
import { withTranslation, WithTranslation } from 'react-i18next';

const ErrorContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  margin: theme.spacing(2),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(2),
}));

interface Props extends WithTranslation {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundaryBase extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  };

  private reloadButtonRef = createRef<HTMLButtonElement>();

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  componentDidUpdate(_: Props, prevState: State) {
    // Фокусируем кнопку Reload при ошибке
    if (this.state.hasError && !prevState.hasError && this.reloadButtonRef.current) {
      this.reloadButtonRef.current.focus();
    }
  }

  private handleReload = () => {
    window.location.reload();
  };

  public render() {
    const { t } = this.props;
    if (this.state.hasError) {
      return (
        <ErrorContainer
          elevation={3}
          role="alert"
          aria-live="assertive"
        >
          <Typography variant="h5" color="error" gutterBottom>
            {t('errorBoundary.header')}
          </Typography>
          <Typography variant="body1" color="textSecondary" gutterBottom>
            {this.state.error?.toString()}
          </Typography>
          {this.state.errorInfo && (
            <Box
              component="pre"
              sx={{
                backgroundColor: 'grey.100',
                padding: 2,
                borderRadius: 1,
                overflow: 'auto',
                maxWidth: '100%',
                fontSize: '0.875rem',
              }}
            >
              {this.state.errorInfo.componentStack}
            </Box>
          )}
          <Button
            variant="contained"
            color="primary"
            onClick={this.handleReload}
            sx={{ mt: 2 }}
            autoFocus
            inputRef={this.reloadButtonRef}
          >
            {t('errorBoundary.reload')}
          </Button>
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

export default withTranslation()(ErrorBoundaryBase);