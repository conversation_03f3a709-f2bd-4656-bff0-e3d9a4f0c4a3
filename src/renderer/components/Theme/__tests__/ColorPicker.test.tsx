import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ColorPicker from '../ColorPicker';

describe('ColorPicker', () => {
  it('renders without crashing', () => {
    render(<ColorPicker color="#000000" onChange={() => {}} />);
  });

  it('displays the current color', () => {
    render(<ColorPicker color="#FF0000" onChange={() => {}} />);
    const colorInput = screen.getByRole('textbox');
    expect(colorInput).toHaveValue('#FF0000');
  });

  it('calls onChange when color is changed', () => {
    const handleChange = jest.fn();
    render(<ColorPicker color="#000000" onChange={handleChange} />);
    
    const colorInput = screen.getByRole('textbox');
    fireEvent.change(colorInput, { target: { value: '#FFFFFF' } });

    expect(handleChange).toHaveBeenCalledWith('#FFFFFF');
  });
}); 