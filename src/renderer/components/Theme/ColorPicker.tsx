import React, { useState } from 'react';
import { TextField, Popover } from '@mui/material';
import { SketchPicker } from 'react-color';

export interface ColorPickerProps {
  color: string;
  onChange: (color: string) => void;
}

const ColorPicker: React.FC<ColorPickerProps> = ({ color, onChange }) => {
  const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleChange = (newColor: any) => {
    onChange(newColor.hex);
  };

  const open = Boolean(anchorEl);

  return (
    <div>
      <TextField
        value={color}
        onClick={handleClick}
        onChange={(e) => onChange(e.target.value)}
        fullWidth
        size="small"
      />
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <SketchPicker color={color} onChange={handleChange} />
      </Popover>
    </div>
  );
};

export default ColorPicker; 