import React from 'react';
import { useSelector } from 'react-redux';
import { ThemeProvider as MuiThemeProvider, createTheme } from '@mui/material/styles';
import type { PaletteMode } from '@mui/material';
import CssBaseline from '@mui/material/CssBaseline';
import { RootState } from '../../../store';

const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const themeSettings = useSelector((state: RootState) => state.theme.current);

  const theme = createTheme({
    palette: {
      mode: themeSettings.mode === 'system' ? 'light' : (themeSettings.mode as PaletteMode),
      primary: {
        main: themeSettings.customColors.primary,
      },
      secondary: {
        main: themeSettings.customColors.secondary,
      },
      background: {
        default: themeSettings.customColors.background,
        paper: themeSettings.customColors.surface,
      },
      error: {
        main: themeSettings.customColors.error,
      },
      warning: {
        main: themeSettings.customColors.warning,
      },
      info: {
        main: themeSettings.customColors.info,
      },
      success: {
        main: themeSettings.customColors.success,
      },
    },
    typography: {
      fontFamily: themeSettings.typography.fontFamily,
      fontSize: themeSettings.typography.fontSize.base,
      fontWeightLight: themeSettings.typography.fontWeight.light,
      fontWeightRegular: themeSettings.typography.fontWeight.regular,
      fontWeightMedium: themeSettings.typography.fontWeight.medium,
      fontWeightBold: themeSettings.typography.fontWeight.bold,
      h1: {
        fontSize: themeSettings.typography.fontSize.base * themeSettings.typography.fontSize.scale * 2.5,
        lineHeight: themeSettings.typography.lineHeight.tight,
      },
      h2: {
        fontSize: themeSettings.typography.fontSize.base * themeSettings.typography.fontSize.scale * 2,
        lineHeight: themeSettings.typography.lineHeight.tight,
      },
      h3: {
        fontSize: themeSettings.typography.fontSize.base * themeSettings.typography.fontSize.scale * 1.75,
        lineHeight: themeSettings.typography.lineHeight.tight,
      },
      h4: {
        fontSize: themeSettings.typography.fontSize.base * themeSettings.typography.fontSize.scale * 1.5,
        lineHeight: themeSettings.typography.lineHeight.tight,
      },
      h5: {
        fontSize: themeSettings.typography.fontSize.base * themeSettings.typography.fontSize.scale * 1.25,
        lineHeight: themeSettings.typography.lineHeight.tight,
      },
      h6: {
        fontSize: themeSettings.typography.fontSize.base * themeSettings.typography.fontSize.scale,
        lineHeight: themeSettings.typography.lineHeight.tight,
      },
      body1: {
        fontSize: themeSettings.typography.fontSize.base,
        lineHeight: themeSettings.typography.lineHeight.normal,
      },
      body2: {
        fontSize: themeSettings.typography.fontSize.base * 0.875,
        lineHeight: themeSettings.typography.lineHeight.normal,
      },
    },
    shape: {
      borderRadius: themeSettings.borderRadius,
    },
    spacing: themeSettings.spacing,
    shadows: [
      'none',
      themeSettings.shadows.sm,
      themeSettings.shadows.md,
      themeSettings.shadows.lg,
      themeSettings.shadows.xl,
      ...Array(20).fill(themeSettings.shadows.xl),
    ] as any,
    transitions: {
      duration: {
        shortest: themeSettings.animation.duration.shortest,
        shorter: themeSettings.animation.duration.shorter,
        short: themeSettings.animation.duration.short,
        standard: themeSettings.animation.duration.standard,
        complex: themeSettings.animation.duration.complex,
        enteringScreen: themeSettings.animation.duration.enteringScreen,
        leavingScreen: themeSettings.animation.duration.leavingScreen,
      },
      easing: {
        easeInOut: themeSettings.animation.easing.easeInOut,
        easeOut: themeSettings.animation.easing.easeOut,
        easeIn: themeSettings.animation.easing.easeIn,
        sharp: themeSettings.animation.easing.sharp,
      },
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            transition: themeSettings.transitions.default,
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            transition: themeSettings.transitions.default,
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            transition: themeSettings.transitions.default,
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            transition: themeSettings.transitions.default,
          },
        },
      },
      MuiListItem: {
        styleOverrides: {
          root: {
            transition: themeSettings.transitions.default,
          },
        },
      },
      MuiIconButton: {
        styleOverrides: {
          root: {
            transition: themeSettings.transitions.default,
          },
        },
      },
    },
  });

  return (
    <MuiThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </MuiThemeProvider>
  );
};

export default ThemeProvider; 