import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { NotificationProvider, useNotification } from '../NotificationProvider';

const TestComponent = () => {
  const { showNotification } = useNotification();
  return (
    <button onClick={() => showNotification({ message: 'Test notification', severity: 'info' })}>
      Show Notification
    </button>
  );
};

describe('NotificationProvider', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders children without notification by default', () => {
    render(
      <NotificationProvider>
        <TestComponent />
      </NotificationProvider>
    );

    expect(screen.getByText('Show Notification')).toBeInTheDocument();
    expect(screen.queryByText('Test notification')).not.toBeInTheDocument();
  });

  it('shows notification when showNotification is called', () => {
    render(
      <NotificationProvider>
        <TestComponent />
      </NotificationProvider>
    );

    screen.getByText('Show Notification').click();
    expect(screen.getByText('Test notification')).toBeInTheDocument();
  });

  it('hides notification after timeout', () => {
    render(
      <NotificationProvider>
        <TestComponent />
      </NotificationProvider>
    );

    screen.getByText('Show Notification').click();
    expect(screen.getByText('Test notification')).toBeInTheDocument();

    act(() => {
      jest.advanceTimersByTime(6000);
    });

    expect(screen.queryByText('Test notification')).not.toBeInTheDocument();
  });
}); 