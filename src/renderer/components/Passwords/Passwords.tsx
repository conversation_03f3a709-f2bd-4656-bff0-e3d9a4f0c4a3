import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Typography,
  Button,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Switch,
  InputAdornment,
  Tooltip,
} from '@mui/material';
import {
  Lock as LockIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  ContentCopy as CopyIcon,
  Edit as EditIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import { RootState } from '../../../store';
import {
  addPassword,
  removePassword,
  updatePassword,
  togglePasswordVisibility,
} from '../../../store/slices/passwordsSlice';

interface Password {
  id: string;
  url: string;
  username: string;
  password: string;
  isVisible: boolean;
  lastUsed: number;
  lastChanged: number;
  strength: 'weak' | 'medium' | 'strong';
  notes?: string;
}

const Passwords: React.FC = () => {
  const dispatch = useDispatch();
  const passwords = useSelector((state: RootState) => state.passwords.items);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPassword, setSelectedPassword] = useState<Password | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [newPassword, setNewPassword] = useState({
    url: '',
    username: '',
    password: '',
    notes: '',
  });

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, password: Password) => {
    setAnchorEl(event.currentTarget);
    setSelectedPassword(password);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedPassword(null);
  };

  const handleRemovePassword = (id: string) => {
    dispatch(removePassword(id));
    handleMenuClose();
  };

  const handleToggleVisibility = (id: string) => {
    dispatch(togglePasswordVisibility(id));
  };

  const handleCopyPassword = (password: string) => {
    navigator.clipboard.writeText(password);
  };

  const handleAddPassword = () => {
    if (newPassword.url && newPassword.username && newPassword.password) {
      dispatch(addPassword({
        ...newPassword,
        id: Date.now().toString(),
        isVisible: false,
        lastUsed: Date.now(),
        lastChanged: Date.now(),
        strength: calculatePasswordStrength(newPassword.password),
      }));
      setOpenDialog(false);
      setNewPassword({
        url: '',
        username: '',
        password: '',
        notes: '',
      });
    }
  };

  const calculatePasswordStrength = (password: string): 'weak' | 'medium' | 'strong' => {
    const hasLower = /[a-z]/.test(password);
    const hasUpper = /[A-Z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const length = password.length;

    let score = 0;
    if (hasLower) score++;
    if (hasUpper) score++;
    if (hasNumber) score++;
    if (hasSpecial) score++;
    if (length >= 8) score++;
    if (length >= 12) score++;

    if (score <= 2) return 'weak';
    if (score <= 4) return 'medium';
    return 'strong';
  };

  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'weak':
        return 'error';
      case 'medium':
        return 'warning';
      case 'strong':
        return 'success';
      default:
        return 'default';
    }
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Сохраненные пароли</Typography>
        <Button
          variant="contained"
          onClick={() => setOpenDialog(true)}
        >
          Добавить пароль
        </Button>
      </Box>

      <List>
        {passwords.map((password) => (
          <ListItem
            key={password.id}
            secondaryAction={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconButton
                  edge="end"
                  onClick={() => handleToggleVisibility(password.id)}
                >
                  {password.isVisible ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
                <IconButton
                  edge="end"
                  onClick={() => handleCopyPassword(password.password)}
                >
                  <CopyIcon />
                </IconButton>
                <IconButton edge="end" onClick={(e) => handleMenuOpen(e, password)}>
                  <MoreVertIcon />
                </IconButton>
              </Box>
            }
          >
            <ListItemIcon>
              <LockIcon />
            </ListItemIcon>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="subtitle1">{password.url}</Typography>
                  <Chip
                    size="small"
                    label={password.strength}
                    color={getStrengthColor(password.strength)}
                    variant="outlined"
                  />
                </Box>
              }
              secondary={
                <Box component="span" sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    Пользователь: {password.username}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Пароль: {password.isVisible ? password.password : '••••••••'}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      size="small"
                      label={`Использован: ${formatDate(password.lastUsed)}`}
                      variant="outlined"
                    />
                    <Chip
                      size="small"
                      label={`Изменен: ${formatDate(password.lastChanged)}`}
                      variant="outlined"
                    />
                  </Box>
                  {password.notes && (
                    <Typography variant="body2" color="text.secondary">
                      Заметки: {password.notes}
                    </Typography>
                  )}
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedPassword && (
          <>
            <MenuItem onClick={() => handleCopyPassword(selectedPassword.password)}>
              <CopyIcon sx={{ mr: 1 }} />
              Копировать пароль
            </MenuItem>
            <MenuItem onClick={() => handleToggleVisibility(selectedPassword.id)}>
              {selectedPassword.isVisible ? (
                <>
                  <VisibilityOffIcon sx={{ mr: 1 }} />
                  Скрыть пароль
                </>
              ) : (
                <>
                  <VisibilityIcon sx={{ mr: 1 }} />
                  Показать пароль
                </>
              )}
            </MenuItem>
            <MenuItem onClick={() => handleRemovePassword(selectedPassword.id)}>
              <DeleteIcon sx={{ mr: 1 }} />
              Удалить
            </MenuItem>
          </>
        )}
      </Menu>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Добавить пароль</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="URL"
            fullWidth
            value={newPassword.url}
            onChange={(e) => setNewPassword({ ...newPassword, url: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Имя пользователя"
            fullWidth
            value={newPassword.username}
            onChange={(e) => setNewPassword({ ...newPassword, username: e.target.value })}
          />
          <TextField
            margin="dense"
            label="Пароль"
            type="password"
            fullWidth
            value={newPassword.password}
            onChange={(e) => setNewPassword({ ...newPassword, password: e.target.value })}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <Chip
                    size="small"
                    label={calculatePasswordStrength(newPassword.password)}
                    color={getStrengthColor(calculatePasswordStrength(newPassword.password))}
                  />
                </InputAdornment>
              ),
            }}
          />
          <TextField
            margin="dense"
            label="Заметки"
            fullWidth
            multiline
            rows={2}
            value={newPassword.notes}
            onChange={(e) => setNewPassword({ ...newPassword, notes: e.target.value })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Отмена</Button>
          <Button onClick={handleAddPassword} variant="contained">
            Добавить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Passwords; 