import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Typography,
  LinearProgress,
  Button,
  Menu,
  MenuItem,
  Chip,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Folder as FolderIcon,
  Pause as PauseIcon,
  PlayArrow as PlayArrowIcon,
} from '@mui/icons-material';
import { RootState } from '../../../store';
import { removeDownload, pauseDownload, resumeDownload, clearDownloads } from '../../../store/slices/downloadsSlice';

interface Download {
  id: string;
  filename: string;
  url: string;
  size: number;
  progress: number;
  status: 'downloading' | 'paused' | 'completed' | 'error';
  speed: number;
  startTime: number;
  endTime?: number;
  error?: string;
}

const Downloads: React.FC = () => {
  const dispatch = useDispatch();
  const downloads = useSelector((state: RootState) => state.downloads.items);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedDownload, setSelectedDownload] = useState<Download | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, download: Download) => {
    setAnchorEl(event.currentTarget);
    setSelectedDownload(download);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedDownload(null);
  };

  const handleRemoveDownload = (id: string) => {
    dispatch(removeDownload(id));
    handleMenuClose();
  };

  const handlePauseResume = (download: Download) => {
    if (download.status === 'downloading') {
      dispatch(pauseDownload(download.id));
    } else if (download.status === 'paused') {
      dispatch(resumeDownload(download.id));
    }
    handleMenuClose();
  };

  const handleClearDownloads = () => {
    dispatch(clearDownloads());
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    return `${formatFileSize(bytesPerSecond)}/s`;
  };

  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Загрузки</Typography>
        <Button
          variant="outlined"
          color="error"
          onClick={handleClearDownloads}
          disabled={downloads.length === 0}
        >
          Очистить все
        </Button>
      </Box>

      <List>
        {downloads.map((download) => (
          <ListItem
            key={download.id}
            secondaryAction={
              <IconButton edge="end" onClick={(e) => handleMenuOpen(e, download)}>
                <MoreVertIcon />
              </IconButton>
            }
          >
            <ListItemIcon>
              <DownloadIcon />
            </ListItemIcon>
            <ListItemText
              primary={download.filename}
              secondary={
                <Box component="span" sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      {download.url}
                    </Typography>
                    <Chip
                      size="small"
                      label={formatFileSize(download.size)}
                      variant="outlined"
                    />
                    {download.status === 'downloading' && (
                      <Chip
                        size="small"
                        label={formatSpeed(download.speed)}
                        variant="outlined"
                      />
                    )}
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={download.progress}
                    color={download.status === 'error' ? 'error' : 'primary'}
                  />
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      size="small"
                      label={download.status}
                      color={
                        download.status === 'completed'
                          ? 'success'
                          : download.status === 'error'
                          ? 'error'
                          : 'default'
                      }
                    />
                    <Typography variant="body2" color="text.secondary">
                      Начало: {formatTime(download.startTime)}
                    </Typography>
                    {download.endTime && (
                      <Typography variant="body2" color="text.secondary">
                        Завершение: {formatTime(download.endTime)}
                      </Typography>
                    )}
                  </Box>
                  {download.error && (
                    <Typography variant="body2" color="error">
                      Ошибка: {download.error}
                    </Typography>
                  )}
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedDownload && (
          <>
            {(selectedDownload.status === 'downloading' || selectedDownload.status === 'paused') && (
              <MenuItem onClick={() => handlePauseResume(selectedDownload)}>
                {selectedDownload.status === 'downloading' ? (
                  <>
                    <PauseIcon sx={{ mr: 1 }} />
                    Приостановить
                  </>
                ) : (
                  <>
                    <PlayArrowIcon sx={{ mr: 1 }} />
                    Возобновить
                  </>
                )}
              </MenuItem>
            )}
            <MenuItem onClick={() => selectedDownload && handleRemoveDownload(selectedDownload.id)}>
              <DeleteIcon sx={{ mr: 1 }} />
              Удалить
            </MenuItem>
            {selectedDownload.status === 'completed' && (
              <MenuItem onClick={() => window.electron.openPath(selectedDownload.filename)}>
                <FolderIcon sx={{ mr: 1 }} />
                Открыть папку
              </MenuItem>
            )}
          </>
        )}
      </Menu>
    </Box>
  );
};

export default Downloads; 