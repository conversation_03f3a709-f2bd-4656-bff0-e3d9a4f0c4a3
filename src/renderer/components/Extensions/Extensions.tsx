import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Typography,
  Switch,
  Button,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Tooltip,
} from '@mui/material';
import {
  Extension as ExtensionIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
  Update as UpdateIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import { RootState } from '../../../store';
import {
  installExtension,
  uninstallExtension,
  enableExtension,
  disableExtension,
  updateExtension,
} from '../../../store/slices/extensionsSlice';

interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  homepage: string;
  enabled: boolean;
  permissions: string[];
  updateAvailable: boolean;
  lastUpdated: number;
  size: number;
  icon?: string;
}

const Extensions: React.FC = () => {
  const dispatch = useDispatch();
  const extensions = useSelector((state: RootState) => state.extensions.items);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedExtension, setSelectedExtension] = useState<Extension | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [newExtensionUrl, setNewExtensionUrl] = useState('');

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, extension: Extension) => {
    setAnchorEl(event.currentTarget);
    setSelectedExtension(extension);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedExtension(null);
  };

  const handleToggleExtension = (extension: Extension) => {
    if (extension.enabled) {
      dispatch(disableExtension(extension.id));
    } else {
      dispatch(enableExtension(extension.id));
    }
  };

  const handleUninstallExtension = (id: string) => {
    dispatch(uninstallExtension(id));
    handleMenuClose();
  };

  const handleUpdateExtension = (id: string) => {
    dispatch(updateExtension(id));
    handleMenuClose();
  };

  const handleInstallExtension = () => {
    if (newExtensionUrl) {
      dispatch(installExtension(newExtensionUrl));
      setOpenDialog(false);
      setNewExtensionUrl('');
    }
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };

  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Расширения</Typography>
        <Button
          variant="contained"
          onClick={() => setOpenDialog(true)}
        >
          Установить расширение
        </Button>
      </Box>

      <List>
        {extensions.map((extension) => (
          <ListItem
            key={extension.id}
            secondaryAction={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Switch
                  edge="end"
                  checked={extension.enabled}
                  onChange={() => handleToggleExtension(extension)}
                />
                <IconButton edge="end" onClick={(e) => handleMenuOpen(e, extension)}>
                  <MoreVertIcon />
                </IconButton>
              </Box>
            }
          >
            <ListItemIcon>
              {extension.icon ? (
                <img
                  src={extension.icon}
                  alt={extension.name}
                  style={{ width: 24, height: 24 }}
                />
              ) : (
                <ExtensionIcon />
              )}
            </ListItemIcon>
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {extension.name}
                  {extension.updateAvailable && (
                    <Tooltip title="Доступно обновление">
                      <UpdateIcon color="primary" fontSize="small" />
                    </Tooltip>
                  )}
                </Box>
              }
              secondary={
                <Box component="span" sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    {extension.description}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      size="small"
                      label={`Версия ${extension.version}`}
                      variant="outlined"
                    />
                    <Chip
                      size="small"
                      label={formatSize(extension.size)}
                      variant="outlined"
                    />
                    <Chip
                      size="small"
                      label={`Обновлено: ${formatDate(extension.lastUpdated)}`}
                      variant="outlined"
                    />
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {extension.permissions.map((permission) => (
                      <Chip
                        key={permission}
                        size="small"
                        icon={<SecurityIcon />}
                        label={permission}
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedExtension && (
          <>
            <MenuItem onClick={() => window.electron.openExternal(selectedExtension.homepage)}>
              <InfoIcon sx={{ mr: 1 }} />
              Информация
            </MenuItem>
            <MenuItem onClick={() => window.electron.openExternal(selectedExtension.homepage)}>
              <SettingsIcon sx={{ mr: 1 }} />
              Настройки
            </MenuItem>
            {selectedExtension.updateAvailable && (
              <MenuItem onClick={() => handleUpdateExtension(selectedExtension.id)}>
                <UpdateIcon sx={{ mr: 1 }} />
                Обновить
              </MenuItem>
            )}
            <MenuItem onClick={() => handleUninstallExtension(selectedExtension.id)}>
              <DeleteIcon sx={{ mr: 1 }} />
              Удалить
            </MenuItem>
          </>
        )}
      </Menu>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Установить расширение</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="URL расширения"
            fullWidth
            value={newExtensionUrl}
            onChange={(e) => setNewExtensionUrl(e.target.value)}
            placeholder="https://..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Отмена</Button>
          <Button onClick={handleInstallExtension} variant="contained">
            Установить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Extensions; 