import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import themeReducer from '../../../store/slices/themeSlice';
import Browser from '../Browser';

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      theme: themeReducer,
    },
    preloadedState: {
      theme: {
        settings: {
          mode: 'light',
          colorScheme: 'default',
          customColors: {
            primary: '#1976d2',
            secondary: '#dc004e',
          },
          typography: {
            fontFamily: 'Roboto',
            fontSize: 16,
          },
          animation: {
            enabled: true,
            duration: 300,
          },
        },
        presets: [],
        current: 'default',
        isCustomizing: false,
        severity: 'info',
        ...initialState,
      },
    },
  });
};

describe('Browser', () => {
  it('renders ThemeCustomizer when isCustomizing is true', () => {
    const store = createMockStore({ isCustomizing: true });
    render(
      <Provider store={store}>
        <Browser />
      </Provider>
    );

    expect(screen.getByText(/настройка темы/i)).toBeInTheDocument();
  });

  it('renders main browser content when isCustomizing is false', () => {
    const store = createMockStore({ isCustomizing: false });
    render(
      <Provider store={store}>
        <Browser />
      </Provider>
    );

    expect(screen.queryByText(/настройка темы/i)).not.toBeInTheDocument();
  });
}); 