import React, { useState } from 'react';
import { Box, Tabs, Tab, Paper } from '@mui/material';
import TabManager from '../Tabs/TabManager';
import BookmarkManager from '../Bookmarks/BookmarkManager';
import DownloadManager from '../Downloads/DownloadManager';

const MainBrowser: React.FC = () => {
  const [section, setSection] = useState(0);

  return (
    <Paper sx={{ height: '100vh', width: '100vw', borderRadius: 0 }}>
      <Tabs
        value={section}
        onChange={(_, v) => setSection(v)}
        indicatorColor="primary"
        textColor="primary"
        sx={{ borderBottom: 1, borderColor: 'divider' }}
      >
        <Tab label="Tabs" />
        <Tab label="Bookmarks" />
        <Tab label="Downloads" />
      </Tabs>
      <Box sx={{ height: 'calc(100vh - 48px)', overflow: 'auto' }}>
        {section === 0 && <TabManager />}
        {section === 1 && <BookmarkManager />}
        {section === 2 && <DownloadManager />}
      </Box>
    </Paper>
  );
};

export default MainBrowser; 