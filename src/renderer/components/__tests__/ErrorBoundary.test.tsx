import React from 'react';
import { render, screen } from '@testing-library/react';
import ErrorBoundary from '../ErrorBoundary';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../i18n/index';

const ThrowError = () => {
  throw new Error('Test error');
};

describe('ErrorBoundary', () => {
  beforeEach(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders children when there is no error', () => {
    render(
      <I18nextProvider i18n={i18n}>
        <ErrorBoundary>
          <div>Test Content</div>
        </ErrorBoundary>
      </I18nextProvider>
    );
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders error message when there is an error', () => {
    // Насильно переключаем язык на en, чтобы не зависеть от окружения
    i18n.changeLanguage('en');
    render(
      <I18nextProvider i18n={i18n}>
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      </I18nextProvider>
    );
    expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
  });
});