export interface Hotkey {
  id: string;
  key: string;
  description: string;
  category: HotkeyCategory;
  action: () => void;
  enabled: boolean;
  global?: boolean;
}

export type HotkeyCategory =
  | 'navigation'
  | 'tabs'
  | 'bookmarks'
  | 'downloads'
  | 'extensions'
  | 'search'
  | 'window'
  | 'custom';

export interface HotkeyState {
  hotkeys: Hotkey[];
  enabledHotkeys: string[];
  loading: boolean;
  error: string | null;
}

export interface HotkeyAction {
  type: string;
  payload: any;
}

export interface HotkeyContext {
  hotkeys: Hotkey[];
  registerHotkey: (hotkey: Hotkey) => void;
  unregisterHotkey: (id: string) => void;
  enableHotkey: (id: string) => void;
  disableHotkey: (id: string) => void;
  isHotkeyEnabled: (id: string) => boolean;
}

export interface HotkeyConfig {
  key: string;
  description: string;
  category: HotkeyCategory;
  action: () => void;
  enabled?: boolean;
  global?: boolean;
} 