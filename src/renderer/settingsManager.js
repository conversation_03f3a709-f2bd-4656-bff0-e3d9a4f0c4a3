// Модуль для управления настройками

/**
 * @file settingsManager.js
 * @description Manages browser settings, including loading, saving, and applying settings.
 */

// ... existing code ...

const themeManager = new ThemeManager();

// ... existing code ...

// DOM Elements
const themeSelector = document.getElementById('theme-selector');
const homepageInput = document.getElementById('home-page'); // ID из index.html
const saveSettingsButton = document.getElementById('save-settings-button'); // Если есть отдельная кнопка сохранения
const incognitoToggle = document.getElementById('incognito-mode'); // ID из index.html
const adBlockerToggle = document.getElementById('adblocker-enabled'); // ID из index.html

// Custom theme elements
const customThemeNameInput = document.getElementById('custom-theme-name');
const customThemeCssInput = document.getElementById('custom-theme-css');
const addCustomThemeButton = document.getElementById('add-custom-theme-button');
const userThemesList = document.getElementById('user-themes-list');

let currentSettings = {
  theme: 'light', // default theme
  homepage: 'https://www.google.com',
  // ... другие настройки
  searchEngine: 'google',
  showBookmarksBar: true,
  adBlockEnabled: true,
  doNotTrack: false,
  // ... etc.
};

let isIncognito = false; // Локальное состояние режима инкогнито для рендерера

/**
 * @description Initializes settings management, loads existing settings, and sets up event listeners.
 */
async function initializeSettings() {
  await loadAllSettings();

  if (themeSelector) {
    await populateThemeSelector();
    themeSelector.addEventListener('change', async (event) => {
      const selectedTheme = event.target.value;
      await saveSetting('theme', selectedTheme);
      themeManager.applyTheme(selectedTheme);
    });
  }

  if (addCustomThemeButton) {
    addCustomThemeButton.addEventListener('click', async () => {
      const themeName = customThemeNameInput.value.trim();
      const themeCSS = customThemeCssInput.value.trim();
      if (themeName && themeCSS) {
        try {
          themeManager.addCustomTheme(themeName, themeCSS);
          customThemeNameInput.value = '';
          customThemeCssInput.value = '';
          await populateThemeSelector(); // Обновить список тем
          await populateUserThemesList(); // Обновить список пользовательских тем
          alert('Пользовательская тема добавлена!');
        } catch (error) {
          console.error('Error adding custom theme:', error);
          alert('Ошибка при добавлении темы: ' + error.message);
        }
      }
    });
  }
  await populateUserThemesList(); // Заполнить список пользовательских тем при инициализации

  // Применяем настройки и обновляем UI после того, как селектор темы потенциально настроен
  applySettings(currentSettings);
  updateSettingsUI();

  setupEventListeners();

  if (saveSettingsButton) {
    saveSettingsButton.addEventListener('click', async () => {
      // For settings that are not updated on input/change, gather them here
      // For example, if there's a multi-line text area for custom CSS that's only saved on button click
      alert('Настройки сохранены!');
    });
  }

  if (incognitoToggle) {
    incognitoToggle.addEventListener('change', (event) => {
      setIncognitoMode(event.target.checked);
      // Это изменение обычно влияет на новые вкладки или поведение браузера,
      // и может потребовать уведомления других модулей или главного процесса.
    });
  }

  // Listen for settings changes from the main process (if they can change there)
  window.electronAPI.onSettingsChanged((updatedSettings) => {
    console.log('Settings updated from main process:', updatedSettings);
    currentSettings = { ...currentSettings, ...updatedSettings };
    applySettings(currentSettings);
    updateSettingsUI();
  });
}

/**
 * @description Sets up event listeners for various UI elements to save settings on change.
 */
function setupEventListeners() {
  if (homepageInput) {
    homepageInput.addEventListener('change', (event) => {
      saveSetting('homepage', event.target.value);
    });
  }

  if (adBlockerToggle) {
    adBlockerToggle.addEventListener('change', (event) => {
      saveSetting('adBlockEnabled', event.target.checked);
    });
  }

  // Add listeners for other settings as they are added to the UI
  // Example for a generic text input:
  // const someTextInput = document.getElementById('some-text-input');
  // if (someTextInput) {
  //   someTextInput.addEventListener('change', (event) => {
  //     saveSetting('someTextKey', event.target.value);
  //   });
  // }

  // Example for a checkbox:
  // const someCheckbox = document.getElementById('some-checkbox');
  // if (someCheckbox) {
  //   someCheckbox.addEventListener('change', (event) => {
  //     saveSetting('someCheckboxKey', event.target.checked);
  //   });
  // }

  // Example for a select dropdown:
  // const someSelect = document.getElementById('some-select');
  // if (someSelect) {
  //   someSelect.addEventListener('change', (event) => {
  //     saveSetting('someSelectKey', event.target.value);
  //   });
  // }

  // If there's a reset button for settings
  const resetSettingsButton = document.getElementById('reset-settings-button');
  if (resetSettingsButton) {
    resetSettingsButton.addEventListener('click', async () => {
      if (confirm('Вы уверены, что хотите сбросить все настройки до значений по умолчанию?')) {
        await resetAllSettings();
        alert('Настройки сброшены до значений по умолчанию.');
      }
    });
  }
}

/**
 * @description Loads settings from storage (e.g., main process via IPC).
 */
/**
 * @description Loads all settings from the main process.
 */
async function loadAllSettings() {
  try {
    const allSettings = await window.electronAPI.settings.getAllSettings();
    if (allSettings) {
      currentSettings = { ...currentSettings, ...allSettings };
    }
    console.log('All settings loaded:', currentSettings);
  } catch (error) {
    console.error('Error loading all settings:', error);
  }
}

/**
 * @description Saves a specific setting to the main process.
 * @param {string} key - The key of the setting to save.
 * @param {*} value - The value of the setting.
 */
async function saveSetting(key, value) {
  try {
    await window.electronAPI.settings.setSetting(key, value);
    currentSettings[key] = value; // Update local copy
    console.log(`Setting '${key}' saved with value:`, value);
  } catch (error) {
    console.error(`Error saving setting '${key}':`, error);
  }
}

/**
 * @description Resets all settings to their default values.
 */
async function resetAllSettings() {
  try {
    await window.electronAPI.settings.resetSettings();
    // After resetting, reload all settings to get the default values
    await loadAllSettings();
    updateSettingsUI(); // Update UI to reflect reset settings
    console.log('All settings have been reset to default values.');
  } catch (error) {
    console.error('Error resetting settings:', error);
  }
}

/**
 * @description Applies settings to the browser UI and behavior.
 * @param {object} settings - The settings object to apply.
 */
function applySettings(settings) {
  if (settings.theme && window.themeManager && typeof window.themeManager.applyTheme === 'function') {
    window.themeManager.applyTheme(settings.theme);
  } else if (settings.theme) {
    // Фоллбэк, если themeManager или его метод applyTheme недоступны
    document.body.setAttribute('data-theme', settings.theme); // Простой фоллбэк
    console.warn('ThemeManager or themeManager.applyTheme not available. Applied theme via data-theme attribute as fallback.');
  }

  // Применить другие настройки: домашняя страница по умолчанию для новых вкладок, и т.д.
  // Например, window.tabManager?.setDefaultNewUrl(settings.homepage);
  console.log('Settings applied:', settings);
}

/**
 * @description Updates the settings UI elements to reflect the current settings.
 */
function updateSettingsUI() {
  if (themeSelector) {
    themeSelector.value = currentSettings.theme;
  }
  if (homepageInput) {
    homepageInput.value = currentSettings.homepage;
  }
  if (incognitoToggle) {
    incognitoToggle.checked = isIncognito;
  }
  if (adBlockerToggle) {
    adBlockerToggle.checked = currentSettings.adBlockEnabled;
  }
  // Обновить другие элементы UI настроек
}

/**
 * @description Sets the incognito mode state.
 * @param {boolean} enabled - True to enable incognito mode, false to disable.
 */
function setIncognitoMode(enabled) {
  isIncognito = enabled;
  console.log(`Incognito mode ${enabled ? 'enabled' : 'disabled'}.`);
  // Это может потребовать дополнительных действий, например, уведомления главного процесса
  // или изменения поведения создания новых вкладок.
  // window.electronAPI.setIncognitoMode(enabled); // Если главный процесс должен знать
  updateSettingsUI(); // Обновить UI, если есть переключатель

  // Уведомить другие модули, если необходимо
  const event = new CustomEvent('incognito-mode-changed', { detail: { isIncognito: enabled } });
  document.dispatchEvent(event);
}

/**
 * @description Populates the theme selector with available themes from the main process.
 */
async function populateThemeSelector() {
  if (!themeSelector || !window.electronAPI) return;

  themeSelector.innerHTML = ''; // Clear existing options
  try {
    const availableThemes = themeManager.getAvailableThemes();
    const currentAppliedTheme = themeManager.getCurrentTheme();

    availableThemes.forEach(theme => {
      const option = document.createElement('option');
      option.value = theme.id; // theme.id (e.g., 'light', 'dark', 'user-custom-theme')
      option.textContent = theme.name; // theme.name (e.g., 'Light', 'Dark', 'My Custom Theme')
      themeSelector.appendChild(option);
    });

    if (currentAppliedTheme) {
      themeSelector.value = currentAppliedTheme.id;
    } else if (currentSettings.theme) {
        themeSelector.value = currentSettings.theme;
    } else {
        // Fallback if no current theme is set or found
        if (availableThemes.length > 0) themeSelector.value = availableThemes[0].id;
    }
  } catch (error) {
    console.error('Error populating theme selector:', error);
  }
}

/**
 * @description Populates the list of user-defined themes.
 */
async function populateUserThemesList() {
  if (!userThemesList) return;

  userThemesList.innerHTML = ''; // Clear existing list
  try {
    const availableThemes = themeManager.getAvailableThemes();
    const userThemes = availableThemes.filter(theme => theme.isCustom);

    if (userThemes.length === 0) {
      const li = document.createElement('li');
      li.textContent = 'Пользовательские темы отсутствуют.';
      li.classList.add('no-user-themes');
      userThemesList.appendChild(li);
      return;
    }

    userThemes.forEach(theme => {
      const listItem = document.createElement('li');
      listItem.textContent = theme.name;
      
      const removeButton = document.createElement('button');
      removeButton.textContent = 'Удалить';
      removeButton.classList.add('remove-custom-theme-button');
      removeButton.dataset.themeName = theme.id; // Используем id темы для удаления
      removeButton.addEventListener('click', async (event) => {
        const themeNameToRemove = event.target.dataset.themeName;
        if (confirm(`Вы уверены, что хотите удалить тему "${theme.name}"?`)) {
          try {
            themeManager.removeCustomTheme(themeNameToRemove);
            await populateThemeSelector(); // Refresh dropdown
            await populateUserThemesList(); // Refresh this list
            alert('Тема удалена.');
          } catch (error) {
            console.error('Error removing custom theme:', error);
            alert('Ошибка при удалении темы: ' + error.message);
          }
        }
      });
      
      listItem.appendChild(removeButton);
      userThemesList.appendChild(listItem);
    });
  } catch (error) {
    console.error('Error populating user themes list:', error);
  }
}

// Make sure to call initializeSettings when the DOM is ready
// or if settingsManager.js is loaded as a module at the end of body.
// document.addEventListener('DOMContentLoaded', initializeSettings);
// If using modules and renderer.js imports this, initializeSettings might be called from there.

/**
 * @description Checks if incognito mode is currently active.
 * @returns {boolean} True if incognito mode is active, false otherwise.
 */
function isIncognitoMode() {
  return isIncognito;
}

/**
 * @description Gets a specific setting value.
 * @param {string} key - The key of the setting to retrieve.
 * @returns {any} The value of the setting, or undefined if not found.
 */
function getSetting(key) {
  return currentSettings[key];
}

// Инициализация менеджера настроек
document.addEventListener('DOMContentLoaded', () => {
    // Убедимся, что uiUtils доступен, если он нужен для применения темы при инициализации
    if (window.uiUtils || (document.body && typeof document.body.setAttribute === 'function')) { // Проверка на случай, если uiUtils не используется
        initializeSettings();
    } else {
        console.warn('SettingsManager: uiUtils not found immediately, will try again after a short delay.');
        setTimeout(() => {
            if (window.uiUtils || (document.body && typeof document.body.setAttribute === 'function')) {
                initializeSettings();
            } else {
                console.error('SettingsManager: uiUtils is still not available. Settings functionality might be affected.');
            }
        }, 150); // Небольшая задержка
    }
});

// Экспорт для использования в других модулях
window.settingsManager = {
  initializeSettings,
  loadSettings,
  saveSettings,
  applySettings,
  updateSettingsUI,
  setIncognitoMode,
  isIncognitoMode,
  getSetting,
  getCurrentSettings: () => ({ ...currentSettings }) // Возвращаем копию
};

export default window.settingsManager;
export { // Также экспортируем отдельные функции, если это предпочтительнее для импорта в других модулях
  initializeSettings,
  loadSettings,
  saveSettings,
  applySettings,
  updateSettingsUI,
  setIncognitoMode,
  isIncognitoMode,
  getSetting
};