/**
 * Менеджер навигации с группами вкладок для A11 Browser
 * Обеспечивает интеллектуальную навигацию и управление группами вкладок
 */

class TabNavigationManager {
  constructor(tabGroupManager, securityPreloadManager, uiManager, localizationManager) {
    // Зависимости от других компонентов
    this.tabGroupManager = tabGroupManager;
    this.securityPreloadManager = securityPreloadManager;
    this.uiManager = uiManager;
    this.localizationManager = localizationManager;
    
    // Настройки навигации
    this.navigationSettings = {
      groupTabsByDomain: true, // Автоматически группировать вкладки по домену
      groupTabsByTopic: false, // Группировать вкладки по теме (требует ИИ)
      autoCloseInactiveTabs: false, // Автоматически закрывать неактивные вкладки
      inactiveTabTimeout: 24, // Время в часах для закрытия неактивных вкладок
      showTabPreview: true, // Показывать превью вкладки при наведении
      tabPreviewDelay: 500, // Задержка перед показом превью (мс)
      showSuggestions: true, // Показывать предложения по навигации
      maxSuggestions: 5, // Максимальное количество предложений
      rememberClosedTabs: true, // Запоминать закрытые вкладки
      maxRememberedTabs: 20 // Максимальное количество запоминаемых вкладок
    };
    
    // Состояние навигации
    this.navigationState = {
      closedTabs: [], // История закрытых вкладок
      tabActivity: {}, // Активность вкладок (время последнего взаимодействия)
      suggestedTabs: [], // Предлагаемые вкладки
      tabPreviews: {}, // Кэш превью вкладок
      activeTabId: null, // ID активной вкладки
      lastActiveTabIds: [] // История активных вкладок
    };
    
    // Загрузка сохраненных настроек
    this._loadSettings();
    
    // Инициализация обработчиков событий
    this._initEventListeners();
  }

  /**
   * Загружает сохраненные настройки
   * @private
   */
  _loadSettings() {
    try {
      const savedSettings = JSON.parse(localStorage.getItem('a11_navigation_settings') || '{}');
      this.navigationSettings = {
        ...this.navigationSettings,
        ...savedSettings
      };
      
      // Загружаем историю закрытых вкладок
      const closedTabs = JSON.parse(localStorage.getItem('a11_closed_tabs') || '[]');
      this.navigationState.closedTabs = closedTabs;
      
      // Загружаем активность вкладок
      const tabActivity = JSON.parse(localStorage.getItem('a11_tab_activity') || '{}');
      this.navigationState.tabActivity = tabActivity;
    } catch (error) {
      console.error('Ошибка при загрузке настроек навигации:', error);
    }
  }

  /**
   * Сохраняет настройки
   * @private
   */
  _saveSettings() {
    try {
      localStorage.setItem('a11_navigation_settings', JSON.stringify(this.navigationSettings));
      
      // Сохраняем только необходимые данные из состояния
      localStorage.setItem('a11_closed_tabs', JSON.stringify(this.navigationState.closedTabs));
      localStorage.setItem('a11_tab_activity', JSON.stringify(this.navigationState.tabActivity));
    } catch (error) {
      console.error('Ошибка при сохранении настроек навигации:', error);
    }
  }

  /**
   * Инициализирует обработчики событий
   * @private
   */
  _initEventListeners() {
    if (typeof window === 'undefined') return;
    
    // Обработчик активации вкладки
    window.addEventListener('tabactivated', (event) => {
      if (!event.detail || !event.detail.tabId) return;
      
      const tabId = event.detail.tabId;
      this._updateTabActivity(tabId);
      this._updateActiveTabHistory(tabId);
    });
    
    // Обработчик закрытия вкладки
    window.addEventListener('tabclosed', (event) => {
      if (!event.detail || !event.detail.tab) return;
      
      const tab = event.detail.tab;
      this._addToClosedTabs(tab);
    });
    
    // Обработчик создания новой вкладки
    window.addEventListener('tabcreated', (event) => {
      if (!event.detail || !event.detail.tab) return;
      
      const tab = event.detail.tab;
      this._updateTabActivity(tab.id);
      
      // Если включена автоматическая группировка по домену
      if (this.navigationSettings.groupTabsByDomain) {
        this._autoGroupTabByDomain(tab);
      }
    });
    
    // Обработчик изменения URL вкладки
    window.addEventListener('taburlchanged', (event) => {
      if (!event.detail || !event.detail.tabId || !event.detail.url) return;
      
      const { tabId, url } = event.detail;
      
      // Если включена автоматическая группировка по домену
      if (this.navigationSettings.groupTabsByDomain) {
        this._autoGroupTabByDomain({ id: tabId, url });
      }
    });
    
    // Обработчик наведения на вкладку для превью
    if (this.navigationSettings.showTabPreview) {
      document.addEventListener('mouseover', (event) => {
        const tabElement = event.target.closest('.tab');
        if (!tabElement || !tabElement.dataset.tabId) return;
        
        const tabId = tabElement.dataset.tabId;
        
        // Добавляем задержку перед показом превью
        setTimeout(() => {
          this._showTabPreview(tabId);
        }, this.navigationSettings.tabPreviewDelay);
      }, { passive: true });
      
      document.addEventListener('mouseout', (event) => {
        const tabElement = event.target.closest('.tab');
        if (!tabElement) {
          this._hideTabPreview();
        }
      }, { passive: true });
    }
    
    // Периодическая проверка неактивных вкладок
    if (this.navigationSettings.autoCloseInactiveTabs) {
      setInterval(() => {
        this._checkInactiveTabs();
      }, 60 * 60 * 1000); // Проверка раз в час
    }
  }

  /**
   * Обновляет информацию об активности вкладки
   * @param {string} tabId - ID вкладки
   * @private
   */
  _updateTabActivity(tabId) {
    this.navigationState.tabActivity[tabId] = Date.now();
    this._saveSettings();
  }

  /**
   * Обновляет историю активных вкладок
   * @param {string} tabId - ID вкладки
   * @private
   */
  _updateActiveTabHistory(tabId) {
    // Устанавливаем активную вкладку
    this.navigationState.activeTabId = tabId;
    
    // Добавляем в историю активных вкладок
    this.navigationState.lastActiveTabIds = [
      tabId,
      ...this.navigationState.lastActiveTabIds.filter(id => id !== tabId)
    ].slice(0, 10); // Храним только 10 последних
  }

  /**
   * Добавляет вкладку в историю закрытых вкладок
   * @param {Object} tab - Объект вкладки
   * @private
   */
  _addToClosedTabs(tab) {
    if (!this.navigationSettings.rememberClosedTabs) return;
    
    // Добавляем вкладку в начало массива
    this.navigationState.closedTabs.unshift({
      ...tab,
      closedAt: Date.now()
    });
    
    // Ограничиваем размер истории
    if (this.navigationState.closedTabs.length > this.navigationSettings.maxRememberedTabs) {
      this.navigationState.closedTabs.pop();
    }
    
    this._saveSettings();
    
    // Генерируем событие добавления в историю закрытых вкладок
    window.dispatchEvent(new CustomEvent('tabaddedtoclosed', {
      detail: { tab }
    }));
  }

  /**
   * Автоматически группирует вкладку по домену
   * @param {Object} tab - Объект вкладки
   * @private
   */
  _autoGroupTabByDomain(tab) {
    try {
      if (!tab.url || tab.url === 'about:blank') return;
      
      const url = new URL(tab.url);
      const domain = url.hostname;
      
      // Ищем группу для этого домена
      let domainGroup = this.tabGroupManager.groups.find(group => {
        return group.name.toLowerCase() === domain.toLowerCase() || 
               group.name.toLowerCase() === domain.replace('www.', '').toLowerCase();
      });
      
      // Если группа не найдена, создаем новую
      if (!domainGroup && domain) {
        const groupId = this.tabGroupManager.createGroup(
          domain,
          this._generateColorForDomain(domain),
          []
        );
        
        domainGroup = this.tabGroupManager.groups.find(group => group.id === groupId);
      }
      
      // Добавляем вкладку в группу
      if (domainGroup) {
        this.tabGroupManager.addTabToGroup(domainGroup.id, tab);
      }
    } catch (error) {
      console.error('Ошибка при автоматической группировке вкладки:', error);
    }
  }

  /**
   * Генерирует цвет для домена на основе его имени
   * @param {string} domain - Доменное имя
   * @returns {string} - HEX-код цвета
   * @private
   */
  _generateColorForDomain(domain) {
    // Простая хеш-функция для генерации цвета
    let hash = 0;
    for (let i = 0; i < domain.length; i++) {
      hash = domain.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    // Преобразуем хеш в цвет
    const color = '#' + ((hash & 0x00FFFFFF).toString(16).padStart(6, '0'));
    return color;
  }

  /**
   * Показывает превью вкладки
   * @param {string} tabId - ID вкладки
   * @private
   */
  _showTabPreview(tabId) {
    // В реальном приложении здесь был бы код для создания и отображения превью
    // Например, создание скриншота вкладки или отображение миниатюры
    
    // Генерируем событие показа превью
    window.dispatchEvent(new CustomEvent('tabpreviewshow', {
      detail: { tabId }
    }));
  }

  /**
   * Скрывает превью вкладки
   * @private
   */
  _hideTabPreview() {
    // В реальном приложении здесь был бы код для скрытия превью
    
    // Генерируем событие скрытия превью
    window.dispatchEvent(new CustomEvent('tabpreviewhide'));
  }

  /**
   * Проверяет неактивные вкладки и закрывает их при необходимости
   * @private
   */
  _checkInactiveTabs() {
    if (!this.navigationSettings.autoCloseInactiveTabs) return;
    
    const now = Date.now();
    const inactiveThreshold = this.navigationSettings.inactiveTabTimeout * 60 * 60 * 1000; // Часы в миллисекунды
    
    // Получаем список вкладок для закрытия
    const tabsToClose = Object.entries(this.navigationState.tabActivity)
      .filter(([tabId, lastActivity]) => {
        // Не закрываем активную вкладку
        if (tabId === this.navigationState.activeTabId) return false;
        
        // Проверяем время неактивности
        return (now - lastActivity) > inactiveThreshold;
      })
      .map(([tabId]) => tabId);
    
    // Закрываем неактивные вкладки
    if (tabsToClose.length > 0) {
      // Генерируем событие для закрытия вкладок
      window.dispatchEvent(new CustomEvent('closeinactivetabs', {
        detail: { tabIds: tabsToClose }
      }));
    }
  }

  /**
   * Восстанавливает закрытую вкладку
   * @param {number} index - Индекс вкладки в истории закрытых вкладок (по умолчанию последняя)
   * @returns {Object|null} - Восстановленная вкладка или null, если история пуста
   */
  reopenClosedTab(index = 0) {
    if (!this.navigationState.closedTabs.length || index >= this.navigationState.closedTabs.length) {
      return null;
    }
    
    // Извлекаем вкладку из истории
    const tab = this.navigationState.closedTabs.splice(index, 1)[0];
    this._saveSettings();
    
    // Генерируем событие восстановления вкладки
    window.dispatchEvent(new CustomEvent('tabreopened', {
      detail: { tab }
    }));
    
    return tab;
  }

  /**
   * Получает список закрытых вкладок
   * @returns {Array} - Список закрытых вкладок
   */
  getClosedTabs() {
    return [...this.navigationState.closedTabs];
  }

  /**
   * Очищает историю закрытых вкладок
   */
  clearClosedTabs() {
    this.navigationState.closedTabs = [];
    this._saveSettings();
    
    // Генерируем событие очистки истории закрытых вкладок
    window.dispatchEvent(new CustomEvent('closedtabscleared'));
  }

  /**
   * Получает предложения по навигации на основе истории и паттернов
   * @returns {Array} - Список предложений
   */
  getNavigationSuggestions() {
    if (!this.navigationSettings.showSuggestions) {
      return [];
    }
    
    // В реальном приложении здесь был бы более сложный алгоритм
    // для анализа истории и генерации предложений
    
    // Получаем предложения от менеджера предзагрузки
    const preloadSuggestions = this.securityPreloadManager.getPredictedUrls() || [];
    
    // Объединяем с предложениями на основе истории закрытых вкладок
    const recentlyClosed = this.navigationState.closedTabs
      .slice(0, 3)
      .map(tab => ({
        type: 'recentlyClosed',
        title: tab.title,
        url: tab.url,
        favicon: tab.favicon,
        closedAt: tab.closedAt
      }));
    
    // Объединяем все предложения и ограничиваем количество
    const allSuggestions = [
      ...preloadSuggestions,
      ...recentlyClosed
    ].slice(0, this.navigationSettings.maxSuggestions);
    
    return allSuggestions;
  }

  /**
   * Устанавливает настройки навигации
   * @param {Object} settings - Новые настройки
   */
  setSettings(settings) {
    this.navigationSettings = {
      ...this.navigationSettings,
      ...settings
    };
    
    this._saveSettings();
    
    // Генерируем событие изменения настроек
    window.dispatchEvent(new CustomEvent('navigationsettingschanged', {
      detail: { settings: this.navigationSettings }
    }));
  }

  /**
   * Получает текущие настройки навигации
   * @returns {Object} - Настройки навигации
   */
  getSettings() {
    return { ...this.navigationSettings };
  }

  /**
   * Получает статистику навигации
   * @returns {Object} - Статистика навигации
   */
  getNavigationStats() {
    return {
      closedTabsCount: this.navigationState.closedTabs.length,
      activeTabId: this.navigationState.activeTabId,
      lastActiveTabs: this.navigationState.lastActiveTabIds.length
    };
  }
}

// Экспортируем класс для использования в других модулях
module.exports = TabNavigationManager;