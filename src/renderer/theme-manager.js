// theme-manager.js
// Manages browser themes, allowing users to switch between light, dark, or custom themes.

// NOTE: Consider using electron-store for more robust storage, especially for CSS content.
const SYSTEM_THEMES = {
    LIGHT: { name: 'light', path: './themes/light.css', type: 'system' },
    DARK: { name: 'dark', path: './themes/dark.css', type: 'system' },
    MODERN_DARK: { name: 'modern-dark', path: './themes/modern-dark.css', type: 'system' }
    // Add more predefined themes here, e.g., { name: 'sepia', path: './themes/sepia.css', type: 'system' }
};

const DEFAULT_THEME_NAME = SYSTEM_THEMES.LIGHT.name;
const CURRENT_THEME_STORAGE_KEY = 'a11-browser-current-theme'; // Stores the name of the active theme
const USER_THEMES_STORAGE_KEY = 'a11-browser-user-themes'; // Stores an array of user theme objects {name, css}

class ThemeManager {
    constructor() {
        this.currentThemeName = DEFAULT_THEME_NAME;
        this.userThemes = {}; // Stores { themeName: { name: 'themeName', css: 'css content', type: 'user' } }

        this.customThemeStyleElement = null; // For applying custom CSS directly
        this._initializeThemeElements();
    }

    /**
     * Initializes the <link> element for system themes and <style> for custom themes.
     * These elements will be added to the <head> of the document.
     * @private
     */
    _initializeThemeElements() {
        this.themeLinkElement = document.createElement('link');
        this.themeLinkElement.setAttribute('rel', 'stylesheet');
        this.themeLinkElement.setAttribute('id', 'theme-stylesheet');
        document.head.appendChild(this.themeLinkElement);

        this.customThemeStyleElement = document.createElement('style');
        this.customThemeStyleElement.setAttribute('id', 'custom-theme-stylesheet');
        document.head.appendChild(this.customThemeStyleElement);
    }

    /**
     * Loads theme settings from localStorage or applies the default theme.
     * This should be called early in the browser initialization process.
     */
    async initialize() {
        console.log('Initializing ThemeManager...');
        this._loadUserThemes(); // Load custom themes first

        const savedThemeName = localStorage.getItem(CURRENT_THEME_STORAGE_KEY);
        const allThemes = this.getAvailableThemes();

        if (savedThemeName && allThemes.find(t => t.name === savedThemeName)) {
            this.currentThemeName = savedThemeName;
        } else {
            // Check for system preference if no theme is saved
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                this.currentThemeName = SYSTEM_THEMES.DARK.name;
            }
        }
        await this.applyTheme(this.currentThemeName);
        this._listenForSystemThemeChanges();
        console.log(`ThemeManager initialized. Current theme: ${this.currentThemeName}`);
    }

    /**
     * Applies the specified theme by updating the href of the theme <link> element (for system themes)
     * or by setting the content of the <style> element (for user themes).
     * @param {string} themeName - The name of the theme to apply.
     */
    async applyTheme(themeName) {
        const allThemes = this.getAvailableThemes();
        const themeToApply = allThemes.find(t => t.name === themeName);

        if (!themeToApply) {
            console.warn(`ThemeManager: Theme "${themeName}" not found. Applying default theme: ${DEFAULT_THEME_NAME}.`);
            await this.applyTheme(DEFAULT_THEME_NAME); // Recurse with default
            return;
        }

        if (themeToApply.type === 'system') {
            document.documentElement.classList.remove('light-theme', 'dark-theme', 'modern-dark-theme'); // Ensure classes are removed
            this.themeLinkElement.setAttribute('href', themeToApply.path);
            this.customThemeStyleElement.textContent = ''; // Clear custom styles
        } else if (themeToApply.type === 'user') {
            document.documentElement.classList.remove('light-theme', 'dark-theme', 'modern-dark-theme');
            this.themeLinkElement.removeAttribute('href'); // Clear system theme
            this.customThemeStyleElement.textContent = themeToApply.css;

        }

        this.currentThemeName = themeToApply.name;
        localStorage.setItem(CURRENT_THEME_STORAGE_KEY, themeToApply.name);
        // The class list on document.documentElement is now used for theme targeting

        console.log(`ThemeManager: Applied theme "${themeToApply.name}".`);

        // Dispatch an event to notify other components of the theme change
        document.dispatchEvent(new CustomEvent('themechanged', { detail: { themeName: themeToApply.name, themeType: themeToApply.type } }));
    }

    /**
     * Switches to the next available theme in a cycle or to a specific theme.
     * @param {string} [themeName] - Optional. The specific theme to switch to.
     */
    async toggleTheme(themeName) {
        const allThemes = this.getAvailableThemes();
        if (themeName && allThemes.find(t => t.name === themeName)) {
            await this.applyTheme(themeName);
        } else {
            // Cycle through themes if no specific theme is provided
            let currentIndex = allThemes.findIndex(t => t.name === this.currentThemeName);
            currentIndex = (currentIndex + 1) % allThemes.length;
            await this.applyTheme(allThemes[currentIndex].name);
        }
    }

    /**
     * Gets the currently active theme name.
     * @returns {string} The name of the current theme.
     */
    getCurrentThemeName() {
        return this.currentThemeName;
    }

    /**
     * Gets a list of available theme objects (system and user).
     * @returns {Array<{name: string, path?: string, css?: string, type: 'system'|'user'}>} An array of available theme objects.
     */
    getAvailableThemes() {
        const systemThemeArray = Object.values(SYSTEM_THEMES);
        const userThemeArray = Object.values(this.userThemes);
        return [...systemThemeArray, ...userThemeArray];
    }

    /**
     * Listens for changes in the system's preferred color scheme and updates the theme accordingly.
     * @private
     */
    _listenForSystemThemeChanges() {
        if (window.matchMedia) {
            const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeMediaQuery.addEventListener('change', (e) => {
                const userHasSetTheme = localStorage.getItem(CURRENT_THEME_STORAGE_KEY) !== null;
                // TODO: Integrate with settingsManager for 'syncSystemTheme' setting
                const syncEnabled = !userHasSetTheme || (window.settingsManager && window.settingsManager.getSetting('syncSystemTheme'));

                if (syncEnabled) {
                    const newSystemThemeName = e.matches ? SYSTEM_THEMES.DARK.name : SYSTEM_THEMES.LIGHT.name;
                    const currentThemeDetails = this.getAvailableThemes().find(t => t.name === this.currentThemeName);
                    if (this.currentThemeName !== newSystemThemeName) {
                        // Only switch if the current theme is a system theme or if explicitly allowed to override user theme
                        if (currentThemeDetails && currentThemeDetails.type === 'system') {
                             console.log(`ThemeManager: System theme changed. Switching to ${newSystemThemeName}.`);
                             this.applyTheme(newSystemThemeName);
                        }
                    }
                }
            });
        }
    }

    /**
     * Adds a new custom theme.
     * @param {string} name - The name for the new theme. Must be unique.
     * @param {string} cssContent - The CSS content for the theme.
     * @returns {boolean} True if the theme was added successfully, false otherwise.
     */
    addCustomTheme(name, cssContent) {
        if (typeof name !== 'string' || name.trim() === '') {
            console.error('ThemeManager: Custom theme name cannot be empty.');
            return false;
        }
        if (SYSTEM_THEMES[name.toUpperCase()] || this.userThemes[name]) {
            console.warn(`ThemeManager: Theme name "${name}" already exists (either as system or user theme).`);
            return false;
        }
        if (typeof cssContent !== 'string' || cssContent.trim() === '') {
            console.error('ThemeManager: Custom theme CSS content cannot be empty.');
            return false;
        }

        this.userThemes[name] = { name, css: cssContent, type: 'user' };
        this._saveUserThemes();
        console.log(`ThemeManager: Custom theme "${name}" added.`);
        // Optionally apply the new theme immediately
        // this.applyTheme(name);
        document.dispatchEvent(new CustomEvent('themeListChanged', { detail: { themes: this.getAvailableThemes() } }));
        return true;
    }

    /**
     * Removes a custom theme.
     * @param {string} name - The name of the custom theme to remove.
     * @returns {boolean} True if the theme was removed successfully, false otherwise.
     */
    removeCustomTheme(name) {
        if (!this.userThemes[name]) {
            console.warn(`ThemeManager: Custom theme "${name}" not found.`);
            return false;
        }

        delete this.userThemes[name];
        this._saveUserThemes();
        console.log(`ThemeManager: Custom theme "${name}" removed.`);

        // If the removed theme was the current theme, revert to default
        if (this.currentThemeName === name) {
            this.applyTheme(DEFAULT_THEME_NAME);
        }
        document.dispatchEvent(new CustomEvent('themeListChanged', { detail: { themes: this.getAvailableThemes() } }));
        return true;
    }

    /**
     * Loads user themes from localStorage.
     * @private
     */
    _loadUserThemes() {
        const storedUserThemes = localStorage.getItem(USER_THEMES_STORAGE_KEY);
        if (storedUserThemes) {
            try {
                this.userThemes = JSON.parse(storedUserThemes);
            } catch (error) {
                console.error('ThemeManager: Failed to parse user themes from localStorage.', error);
                this.userThemes = {}; // Reset if parsing fails
            }
        }
    }

    /**
     * Saves user themes to localStorage.
     * @private
     */
    _saveUserThemes() {
        try {
            localStorage.setItem(USER_THEMES_STORAGE_KEY, JSON.stringify(this.userThemes));
        } catch (error) {
            console.error('ThemeManager: Failed to save user themes to localStorage.', error);
            // Potentially handle quota exceeded errors
        }
    }
}

// Export a single instance of the ThemeManager
const themeManager = new ThemeManager();
export default themeManager;