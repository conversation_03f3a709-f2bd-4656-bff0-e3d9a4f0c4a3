/**
 * Менеджер WebAssembly для A11 Browser
 * Обеспечивает загрузку, компиляцию и выполнение WebAssembly модулей
 */

class WasmManager {
  constructor() {
    // Кэш скомпилированных модулей
    this.moduleCache = new Map();
    
    // Настройки WebAssembly
    this.wasmSettings = {
      enabled: true,
      cacheModules: true,
      maxCacheSize: 50, // Максимальное количество модулей в кэше
      allowUnsafeOperations: false, // Разрешать небезопасные операции
      timeoutMs: 5000, // Таймаут выполнения в миллисекундах
      memoryLimitMb: 256 // Ограничение памяти в МБ
    };
    
    // Статистика использования
    this.stats = {
      totalModulesLoaded: 0,
      totalExecutions: 0,
      errors: 0,
      averageCompileTime: 0,
      averageExecutionTime: 0
    };
    
    // Загрузка сохраненных настроек
    this._loadSettings();
  }

  /**
   * Загружает сохраненные настройки
   * @private
   */
  _loadSettings() {
    try {
      const savedSettings = JSON.parse(localStorage.getItem('a11_wasm_settings') || '{}');
      
      this.wasmSettings = {
        ...this.wasmSettings,
        ...savedSettings
      };
    } catch (error) {
      console.error('Ошибка при загрузке настроек WebAssembly:', error);
    }
  }

  /**
   * Сохраняет настройки
   * @private
   */
  _saveSettings() {
    try {
      localStorage.setItem('a11_wasm_settings', JSON.stringify(this.wasmSettings));
    } catch (error) {
      console.error('Ошибка при сохранении настроек WebAssembly:', error);
    }
  }

  /**
   * Загружает и компилирует WebAssembly модуль
   * @param {string|ArrayBuffer|Response} source - Источник модуля (URL, ArrayBuffer или Response)
   * @param {Object} importObject - Объект импорта для модуля
   * @returns {Promise<WebAssembly.Module>} - Скомпилированный модуль
   */
  async loadModule(source, importObject = {}) {
    if (!this.wasmSettings.enabled) {
      throw new Error('WebAssembly отключен в настройках браузера');
    }
    
    try {
      const startTime = performance.now();
      let moduleBytes;
      let moduleKey;
      
      // Определяем тип источника и загружаем модуль
      if (typeof source === 'string') {
        // URL модуля
        moduleKey = source;
        
        // Проверяем кэш
        if (this.wasmSettings.cacheModules && this.moduleCache.has(moduleKey)) {
          return this.moduleCache.get(moduleKey).module;
        }
        
        // Загружаем модуль
        const response = await fetch(source);
        if (!response.ok) {
          throw new Error(`Ошибка загрузки WebAssembly модуля: ${response.status} ${response.statusText}`);
        }
        moduleBytes = await response.arrayBuffer();
      } else if (source instanceof ArrayBuffer) {
        // ArrayBuffer с байтами модуля
        moduleBytes = source;
        moduleKey = `buffer_${Date.now()}`;
      } else if (source instanceof Response) {
        // Response объект
        if (!source.ok) {
          throw new Error(`Ошибка загрузки WebAssembly модуля: ${source.status} ${source.statusText}`);
        }
        moduleBytes = await source.arrayBuffer();
        moduleKey = source.url || `response_${Date.now()}`;
        
        // Проверяем кэш
        if (this.wasmSettings.cacheModules && this.moduleCache.has(moduleKey)) {
          return this.moduleCache.get(moduleKey).module;
        }
      } else {
        throw new Error('Неподдерживаемый тип источника WebAssembly модуля');
      }
      
      // Компилируем модуль
      const module = await WebAssembly.compile(moduleBytes);
      
      // Обновляем статистику
      const compileTime = performance.now() - startTime;
      this.stats.totalModulesLoaded++;
      this.stats.averageCompileTime = (
        (this.stats.averageCompileTime * (this.stats.totalModulesLoaded - 1) + compileTime) / 
        this.stats.totalModulesLoaded
      );
      
      // Сохраняем в кэш, если включено кэширование
      if (this.wasmSettings.cacheModules) {
      // Очищаем кэш, если достигнут лимит
      if (this.moduleCache.size >= this.wasmSettings.maxCacheSize) {
        // Удаляем наименее недавно использованный модуль (LRU)
        let lruKey = null;
        let oldestTimestamp = Infinity;
        for (const [key, value] of this.moduleCache.entries()) {
          if (value.timestamp < oldestTimestamp) {
            oldestTimestamp = value.timestamp;
            lruKey = key;
          }
        }
        if (lruKey !== null) {
          this.moduleCache.delete(lruKey);
        }
      }
      
      this.moduleCache.set(moduleKey, {
        module,
        timestamp: Date.now(),
        size: moduleBytes.byteLength
      });


      }
      
      // Генерируем событие загрузки модуля
      window.dispatchEvent(new CustomEvent('wasmmoduleloaded', {
        detail: {
          moduleKey,
          size: moduleBytes.byteLength,
          compileTime
        }
      }));
      
      return module;
    } catch (error) {
      this.stats.errors++;
      console.error('Ошибка при загрузке WebAssembly модуля:', error);
      
      // Генерируем событие ошибки
      window.dispatchEvent(new CustomEvent('wasmerror', {
        detail: {
          phase: 'load',
          error: error.message,
          source: typeof source === 'string' ? source : 'binary_data'
        }
      }));
      
      throw error;
    }
  }

  /**
   * Создает экземпляр WebAssembly модуля
   * @param {WebAssembly.Module} module - Скомпилированный модуль
   * @param {Object} importObject - Объект импорта для модуля
   * @returns {Promise<WebAssembly.Instance>} - Экземпляр модуля
   */
  async instantiateModule(module, importObject = {}) {
    if (!this.wasmSettings.enabled) {
      throw new Error('WebAssembly отключен в настройках браузера');
    }
    
    try {
      // Добавляем безопасное окружение для модуля
      const safeImports = this._createSafeImports(importObject);
      
      // Создаем экземпляр модуля
      const instance = await WebAssembly.instantiate(module, safeImports);
      
      return instance;
    } catch (error) {
      this.stats.errors++;
      console.error('Ошибка при создании экземпляра WebAssembly модуля:', error);
      
      // Генерируем событие ошибки
      window.dispatchEvent(new CustomEvent('wasmerror', {
        detail: {
          phase: 'instantiate',
          error: error.message
        }
      }));
      
      throw error;
    }
  }

  /**
   * Загружает, компилирует и создает экземпляр WebAssembly модуля
   * @param {string|ArrayBuffer|Response} source - Источник модуля
   * @param {Object} importObject - Объект импорта для модуля
   * @returns {Promise<{module: WebAssembly.Module, instance: WebAssembly.Instance}>} - Модуль и его экземпляр
   */
  async loadAndInstantiate(source, importObject = {}) {
    if (!this.wasmSettings.enabled) {
      throw new Error('WebAssembly отключен в настройках браузера');
    }
    
    try {
      // Загружаем модуль
      const module = await this.loadModule(source);
      
      // Создаем экземпляр
      const instance = await this.instantiateModule(module, importObject);
      
      return { module, instance };
    } catch (error) {
      // Ошибки уже обрабатываются в loadModule и instantiateModule
      throw error;
    }
  }

  /**
   * Выполняет функцию из WebAssembly модуля с таймаутом
   * @param {WebAssembly.Instance} instance - Экземпляр модуля
   * @param {string} functionName - Имя функции для вызова
   * @param {Array} args - Аргументы функции
   * @returns {Promise<any>} - Результат выполнения функции
   */
  async executeFunction(instance, functionName, args = []) {
    if (!this.wasmSettings.enabled) {
      throw new Error('WebAssembly отключен в настройках браузера');
    }
    
    if (!instance.exports[functionName]) {
      throw new Error(`Функция "${functionName}" не найдена в WebAssembly модуле`);
    }
    
    try {
      const startTime = performance.now();
      
      // Создаем промис с таймаутом
      const result = await Promise.race([
        // Выполнение функции
        Promise.resolve().then(() => instance.exports[functionName](...args)),
        
        // Таймаут
        new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error(`Превышено время выполнения функции "${functionName}" (${this.wasmSettings.timeoutMs}ms)`));
          }, this.wasmSettings.timeoutMs);
        })
      ]);
      
      // Обновляем статистику
      const executionTime = performance.now() - startTime;
      this.stats.totalExecutions++;
      this.stats.averageExecutionTime = (
        (this.stats.averageExecutionTime * (this.stats.totalExecutions - 1) + executionTime) / 
        this.stats.totalExecutions
      );
      
      // Генерируем событие выполнения функции
      window.dispatchEvent(new CustomEvent('wasmfunctionexecuted', {
        detail: {
          functionName,
          executionTime,
          success: true
        }
      }));
      
      return result;
    } catch (error) {
      this.stats.errors++;
      console.error(`Ошибка при выполнении WebAssembly функции "${functionName}":`, error);
      
      // Генерируем событие ошибки
      window.dispatchEvent(new CustomEvent('wasmerror', {
        detail: {
          phase: 'execute',
          functionName,
          error: error.message
        }
      }));
      
      throw error;
    }
  }

  /**
   * Создает безопасное окружение для WebAssembly модуля
   * @param {Object} importObject - Исходный объект импорта
   * @returns {Object} - Безопасный объект импорта
   * @private
   */
  _createSafeImports(importObject) {
    // Базовое безопасное окружение
    const safeEnvironment = {
      env: {
        // Безопасные функции для работы с памятью
        memory: importObject.env?.memory || new WebAssembly.Memory({ 
          initial: 1, 
          maximum: this.wasmSettings.memoryLimitMb / 64 // 64KB на страницу памяти
        }),
        
        // Безопасные функции для вывода
        log: (ptr, len) => {
          if (!importObject.env?.memory) return;
          
          const memory = importObject.env.memory;
          const bytes = new Uint8Array(memory.buffer, ptr, len);
          const text = new TextDecoder('utf-8').decode(bytes);
          console.log('[WASM]', text);
        },
        
        // Функция для получения текущего времени
        now: () => Date.now(),
        
        // Безопасная функция для генерации случайных чисел
        random: () => Math.random()
      }
    };
    
    // Если разрешены небезопасные операции, добавляем дополнительные функции
    if (this.wasmSettings.allowUnsafeOperations) {
      // Добавляем функции для работы с DOM (с ограничениями)
      safeEnvironment.dom = {
        createElement: (tagNamePtr, tagNameLen) => {
          if (!importObject.env?.memory) return -1;
          
          try {
            const memory = importObject.env.memory;
            const bytes = new Uint8Array(memory.buffer, tagNamePtr, tagNameLen);
            const tagName = new TextDecoder('utf-8').decode(bytes);
            
            // Ограничиваем список разрешенных тегов
            const allowedTags = ['div', 'span', 'p', 'button', 'input', 'canvas'];
            if (!allowedTags.includes(tagName.toLowerCase())) {
              console.warn(`[WASM] Попытка создать запрещенный элемент: ${tagName}`);
              return -1;
            }
            
            const element = document.createElement(tagName);
            // Генерируем уникальный ID для элемента
            const elementId = `wasm-element-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
            element.setAttribute('data-wasm-id', elementId);
            
            return elementId;
          } catch (error) {
            console.error('[WASM] Ошибка при создании элемента:', error);
            return -1;
          }
        },
        
        // Другие безопасные функции для работы с DOM...
      };
    }
    
    // Объединяем безопасное окружение с пользовательскими импортами
    return {
      ...safeEnvironment,
      ...importObject,
      // Переопределяем env для обеспечения безопасности
      env: {
        ...safeEnvironment.env,
        ...importObject.env
      }
    };
  }

  /**
   * Очищает кэш модулей
   */
  clearModuleCache() {
    this.moduleCache.clear();
    
    // Генерируем событие очистки кэша
    window.dispatchEvent(new CustomEvent('wasmcachecleared'));
  }

  /**
   * Устанавливает настройки WebAssembly
   * @param {Object} settings - Новые настройки
   */
  setSettings(settings) {
    this.wasmSettings = {
      ...this.wasmSettings,
      ...settings
    };
    
    this._saveSettings();
    
    // Генерируем событие изменения настроек
    window.dispatchEvent(new CustomEvent('wasmsettingschanged', {
      detail: { settings: this.wasmSettings }
    }));
  }

  /**
   * Получает текущие настройки WebAssembly
   * @returns {Object} - Настройки WebAssembly
   */
  getSettings() {
    return { ...this.wasmSettings };
  }

  /**
   * Получает статистику использования WebAssembly
   * @returns {Object} - Статистика использования
   */
  getStats() {
    return { 
      ...this.stats,
      cacheSize: this.moduleCache.size,
      cachedModules: Array.from(this.moduleCache.entries()).map(([key, value]) => ({
        key,
        size: value.size,
        timestamp: value.timestamp
      }))
    };
  }

  /**
   * Проверяет поддержку WebAssembly в браузере
   * @returns {boolean} - Флаг поддержки
   */
  static isSupported() {
    return typeof WebAssembly === 'object' && 
           typeof WebAssembly.compile === 'function' &&
           typeof WebAssembly.instantiate === 'function';
  }
}

// Экспортируем класс для использования в других модулях
module.exports = WasmManager;
