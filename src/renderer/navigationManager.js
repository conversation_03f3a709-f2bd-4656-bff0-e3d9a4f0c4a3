// Модуль для управления навигацией

/**
 * @file navigationManager.js
 * @description Handles browser navigation controls like back, forward, reload, and URL input.
 */

// import { getActiveTab } from './tabManager.js'; // Предполагается, что эта функция будет импортирована

// Получение элементов DOM
const backButton = document.getElementById('back-button');
const forwardButton = document.getElementById('forward-button');
const reloadButton = document.getElementById('reload-button');
const urlInput = document.getElementById('url-input');

/**
 * @description Initializes navigation controls and event listeners.
 */
function initializeNavigation() {
  if (backButton) {
    backButton.addEventListener('click', () => {
      const activeTab = window.tabManager.getActiveTab(); // Используем window.tabManager, если tabManager экспортирует глобально
      if (activeTab) {
        activeTab.goBack();
      }
    });
  }

  if (forwardButton) {
    forwardButton.addEventListener('click', () => {
      const activeTab = window.tabManager.getActiveTab();
      if (activeTab) {
        activeTab.goForward();
      }
    });
  }

  if (reloadButton) {
    reloadButton.addEventListener('click', () => {
      const activeTab = window.tabManager.getActiveTab();
      if (activeTab) {
        if (activeTab.isLoading) {
          activeTab.stopLoading();
        } else {
          activeTab.reload();
        }
      }
    });
  }

  if (urlInput) {
    urlInput.addEventListener('keydown', (event) => {
      if (event.key === 'Enter') {
        const activeTab = window.tabManager.getActiveTab();
        if (activeTab) {
          let url = urlInput.value.trim();
          activeTab.navigateTo(url);
        }
      }
    });
  }

  // Обновляем состояние кнопок при смене вкладки или загрузке страницы
  document.addEventListener('active-tab-changed', updateNavigationState);
  document.addEventListener('webview-did-stop-loading', updateNavigationState);
  document.addEventListener('webview-did-start-loading', updateNavigationState); // Также при начале загрузки

  // Первоначальное обновление состояния
  updateNavigationState();
}

/**
 * @description Updates the state of navigation buttons (enabled/disabled) based on the active tab's state.
 */
function updateNavigationState() {
  const activeTab = window.tabManager.getActiveTab();

  if (activeTab && activeTab.webview) {
    if (backButton) backButton.disabled = !activeTab.webview.canGoBack();
    if (forwardButton) forwardButton.disabled = !activeTab.webview.canGoForward();
    if (reloadButton) {
        // Меняем иконку/текст кнопки в зависимости от состояния загрузки
        const icon = reloadButton.querySelector('i'); // Предполагаем, что используется иконка
        if (activeTab.isLoading) {
            reloadButton.title = 'Остановить загрузку';
            if (icon) {
                icon.classList.remove('fa-redo');
                icon.classList.add('fa-times');
            }
        } else {
            reloadButton.title = 'Обновить';
            if (icon) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-redo');
            }
        }
    }
    if (urlInput) {
        // Обновляем URL в строке, если он изменился не через ввод пользователя
        // (например, при навигации по ссылке внутри страницы)
        const currentWebviewURL = activeTab.webview.getURL();
        if (urlInput.value !== currentWebviewURL) {
            urlInput.value = currentWebviewURL;
        }
    }
  } else {
    // Если нет активной вкладки или webview
    if (backButton) backButton.disabled = true;
    if (forwardButton) forwardButton.disabled = true;
    if (reloadButton) {
        reloadButton.title = 'Обновить';
        const icon = reloadButton.querySelector('i');
        if (icon) {
            icon.classList.remove('fa-times');
            icon.classList.add('fa-redo');
        }
    }
    if (urlInput) urlInput.value = '';
  }
}

// Вызываем инициализацию при загрузке скрипта
// Убедитесь, что DOM загружен, если элементы получаются сразу
document.addEventListener('DOMContentLoaded', () => {
    // Проверяем, существует ли tabManager и его функции, прежде чем вызывать initializeNavigation
    // Это важно, так как порядок загрузки модулей может влиять
    if (window.tabManager && typeof window.tabManager.getActiveTab === 'function') {
        initializeNavigation();
    } else {
        // Попытка инициализации после небольшой задержки, если tabManager еще не загружен
        // Это не идеальное решение, лучше использовать import/export и управлять зависимостями
        console.warn('NavigationManager: tabManager not found immediately, will try again after a short delay.');
        setTimeout(() => {
            if (window.tabManager && typeof window.tabManager.getActiveTab === 'function') {
                initializeNavigation();
            } else {
                console.error('NavigationManager: tabManager is still not available. Navigation controls might not work.');
            }
        }, 100);
    }
});

// Экспорт функций (если они будут использоваться другими модулями)
export {
  initializeNavigation,
  updateNavigationState
};