import React from 'react';
import { Provider } from 'react-redux';
import { store } from '../store';
import ThemeProvider from './components/Theme/ThemeProvider';
import Browser from './components/Browser/Browser';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingProvider from './components/Loading/LoadingProvider';
import NotificationProvider from './components/Notifications/NotificationProvider';

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <Provider store={store}>
        <ThemeProvider>
          <LoadingProvider>
            <NotificationProvider>
              <Browser />
            </NotificationProvider>
          </LoadingProvider>
        </ThemeProvider>
      </Provider>
    </ErrorBoundary>
  );
};

export default App; 