// Модуль для управления историей просмотров

/**
 * @file historyManager.js
 * @description Manages browser history, including adding entries and displaying history.
 */

// import { isIncognitoMode } from './settingsManager.js'; // Для проверки режима инкогнито

// DOM Elements (предполагаемые)
const historyListElement = document.getElementById('history-list'); // Элемент для отображения списка истории
const historyItemTemplate = document.getElementById('history-item-template'); // Шаблон для элемента истории
const clearHistoryButton = document.getElementById('clear-history-button'); // Кнопка очистки истории

let historyItems = [];

/**
 * @description Initializes history management, loads existing history, and sets up event listeners.
 */
async function initializeHistory() {
  await loadHistory();
  renderHistory();

  // Добавляем элемент в историю, когда вкладка завершает загрузку
  document.addEventListener('webview-did-stop-loading', (event) => {
    // TODO: Проверить, не находится ли браузер в режиме инкогнито через settingsManager
    // const incognito = window.settingsManager?.isIncognitoMode(); // Пример
    const incognito = false; // Заглушка

    if (!incognito && event.detail && event.detail.url && event.detail.url !== 'about:blank' && !event.detail.url.startsWith('data:')) {
      addHistoryItem({
        url: event.detail.url,
        title: event.detail.title || event.detail.url, // Используем URL, если заголовок пуст
        timestamp: Date.now()
      });
    }
  });

  if (clearHistoryButton) {
    clearHistoryButton.addEventListener('click', async () => {
      if (confirm('Вы уверены, что хотите очистить всю историю просмотров?')) {
        await clearAllHistory();
      }
    });
  }
}

/**
 * @description Loads history from storage (e.g., main process via IPC).
 */
async function loadHistory() {
  try {
    historyItems = await window.browserAPI.getHistory();
    // Сортируем по убыванию времени для отображения
    historyItems.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error('Error loading history:', error);
    historyItems = [];
  }
}

/**
 * @description Saves the current history to storage.
 */
async function saveHistory() {
  try {
    // Нет необходимости сохранять всю историю каждый раз, если главный процесс управляет этим.
    // Эта функция может быть вызвана при добавлении/удалении элементов, если renderer отвечает за сохранение.
    // В текущей реализации, browserAPI.addHistoryItem и browserAPI.clearHistory обрабатывают сохранение.
  } catch (error) {
    console.error('Error saving history:', error);
  }
}

/**
 * @description Adds a new item to the browsing history.
 * @param {object} item - The history item to add (should include url, title, timestamp).
 */
async function addHistoryItem(item) {
  if (!item || !item.url || !item.title) return;

  // Предотвращаем дублирование последних записей (например, при обновлении страницы)
  // Можно сделать более сложную логику, если нужно
  const lastItem = historyItems[0]; // Так как отсортировано по убыванию времени
  if (lastItem && lastItem.url === item.url && lastItem.title === item.title) {
    // Можно обновить timestamp последнего элемента, если это недавнее посещение той же страницы
    if (Date.now() - lastItem.timestamp < 60000) { // Например, в течение минуты
        lastItem.timestamp = item.timestamp;
        await window.browserAPI.updateHistoryItem(lastItem); // Предполагается такой метод в API
        renderHistory();
        return;
    }
  }

  const newHistoryEntry = {
    id: `history-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    ...item
  };

  try {
    await window.browserAPI.addHistoryItem(newHistoryEntry); // Сохраняем через главный процесс
    historyItems.unshift(newHistoryEntry); // Добавляем в начало массива для отображения
    // Ограничиваем размер истории в памяти рендерера, если нужно
    // if (historyItems.length > MAX_HISTORY_ITEMS_IN_RENDERER) { historyItems.pop(); }
    renderHistory();
    console.log('History item added:', newHistoryEntry);
  } catch (error) {
    console.error('Error adding history item:', error);
  }
}

/**
 * @description Removes a specific history item by its ID.
 * @param {string} itemId - The ID of the history item to remove.
 */
async function removeHistoryItemById(itemId) {
  try {
    await window.browserAPI.removeHistoryItem(itemId);
    historyItems = historyItems.filter(item => item.id !== itemId);
    renderHistory();
    console.log('History item removed:', itemId);
  } catch (error) {
    console.error('Error removing history item:', error);
  }
}

/**
 * @description Clears all browsing history.
 */
async function clearAllHistory() {
  try {
    await window.browserAPI.clearHistory();
    historyItems = [];
    renderHistory();
    console.log('All history cleared.');
  } catch (error) {
    console.error('Error clearing history:', error);
  }
}

/**
 * @description Renders the history list in the UI.
 */
function renderHistory() {
  if (!historyListElement || !historyItemTemplate) return;

  historyListElement.innerHTML = ''; // Очищаем старый список

  if (historyItems.length === 0) {
    historyListElement.innerHTML = '<p>История просмотров пуста.</p>';
    return;
  }

  // Группировка по датам для лучшего отображения (опционально)
  const groupedByDate = historyItems.reduce((acc, item) => {
    const date = new Date(item.timestamp).toLocaleDateString('ru-RU', { year: 'numeric', month: 'long', day: 'numeric' });
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(item);
    return acc;
  }, {});

  for (const date in groupedByDate) {
    const dateHeader = document.createElement('h3');
    dateHeader.classList.add('history-date-header');
    dateHeader.textContent = date;
    historyListElement.appendChild(dateHeader);

    groupedByDate[date].forEach(item => {
      const historyNode = historyItemTemplate.content.cloneNode(true);
      const itemElement = historyNode.querySelector('.history-item'); // Предполагаемый класс
      const linkElement = itemElement.querySelector('a');
      const timeElement = itemElement.querySelector('.history-item-time');
      const removeButton = itemElement.querySelector('.remove-history-item-button');

      linkElement.href = item.url;
      linkElement.textContent = item.title;
      linkElement.title = item.url;
      linkElement.addEventListener('click', (e) => {
        e.preventDefault();
        window.tabManager.createNewTab(item.url, true);
      });

      if (timeElement) {
        timeElement.textContent = new Date(item.timestamp).toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });
      }

      if (removeButton) {
        removeButton.dataset.itemId = item.id;
        removeButton.addEventListener('click', async (e) => {
          e.stopPropagation();
          const itemIdToRemove = e.currentTarget.dataset.itemId;
          if (itemIdToRemove) {
            await removeHistoryItemById(itemIdToRemove);
          }
        });
      }
      historyListElement.appendChild(itemElement);
    });
  }
}

// Инициализация менеджера истории
document.addEventListener('DOMContentLoaded', () => {
    // Убедимся, что tabManager доступен, так как он может быть нужен для открытия ссылок из истории
    if (window.tabManager) {
        initializeHistory();
    } else {
        console.warn('HistoryManager: tabManager not found immediately, will try again after a short delay.');
        setTimeout(() => {
            if (window.tabManager) {
                initializeHistory();
            } else {
                console.error('HistoryManager: tabManager is still not available. History functionality might be affected.');
            }
        }, 200); // Задержка чуть больше, чем у других менеджеров
    }
});

// Экспорт для возможного использования
export {
  initializeHistory,
  addHistoryItem,
  loadHistory,
  renderHistory,
  removeHistoryItemById,
  clearAllHistory,
  historyItems
};