// Модуль для управления вкладками

/**
 * @file tabManager.js
 * @description Manages browser tabs, including creation, activation, closing, and state.
 */

// Глобальные переменные для управления вкладками
let tabs = [];
let activeTabId = null;

// Получение элементов DOM
const tabsBar = document.getElementById('tabs-bar');
const newTabButton = document.getElementById('new-tab-button');
const browserContent = document.getElementById('browser-content');
const urlInput = document.getElementById('url-input'); // Нужен для обновления URL при активации вкладки
const tabTemplate = document.getElementById('tab-template');
const webviewTemplate = document.getElementById('webview-template');

// Зависимости, которые могут понадобиться (будут импортированы или переданы)
// import { updateNavigationState, updateBookmarkState, addToHistory } from './navigationManager.js'; // Пример
// import { isIncognitoMode } from './settingsManager.js'; // Пример
import { uiManager } from './ui-manager.js'; // Добавляем импорт uiManager

/**
 * @class Tab
 * @description Represents a browser tab with its associated webview and UI elements.
 */
class Tab {
  constructor(id, title = 'Новая вкладка', url = 'about:blank') {
    this.id = id;
    this.title = title;
    this.url = url;
    this.element = null;
    this.webview = null;
    this.favicon = null;
    this.isLoading = false;
    this.history = []; // История навигации внутри вкладки
    this.historyIndex = -1; // Текущая позиция в истории вкладки

    this._createTabElement();
    this._createWebviewElement();
    this._setupEventListeners();
  }

  /**
   * @private
   * @description Creates the DOM element for the tab in the tab bar.
   */
  _createTabElement() {
    const tabNode = tabTemplate.content.cloneNode(true);
    const tabElement = tabNode.querySelector('.tab');
    tabElement.id = `tab-${this.id}`;
    tabElement.dataset.tabId = this.id;

    const tabTitle = tabElement.querySelector('.tab-title');
    tabTitle.textContent = this.title;

    // Иконка загрузки
    const loadingSpinner = document.createElement('div');
    loadingSpinner.classList.add('loading-spinner');
    tabElement.insertBefore(loadingSpinner, tabTitle); // Вставляем перед заголовком

    tabsBar.appendChild(tabElement);
    this.element = tabElement;

    // Анимация открытия вкладки
    if (uiManager && typeof uiManager.animateTabOpen === 'function') {
      uiManager.animateTabOpen(this.element);
    }
  }

  /**
   * @private
   * @description Creates the webview element for the tab's content.
   */
  _createWebviewElement() {
    const webviewNode = webviewTemplate.content.cloneNode(true);
    const webviewElement = webviewNode.querySelector('webview');
    webviewElement.id = `webview-${this.id}`;
    webviewElement.dataset.tabId = this.id;
    webviewElement.setAttribute('preload', `../preload.js`); // Убедитесь, что путь правильный
    // webviewElement.setAttribute('nodeintegration', 'false'); // Уже установлено в main.js, но для ясности
    // webviewElement.setAttribute('contextisolation', 'true'); // Уже установлено в main.js

    webviewElement.src = this.url;

    browserContent.appendChild(webviewElement);
    this.webview = webviewElement;
  }

  /**
   * @private
   * @description Sets up event listeners for the tab and its webview.
   */
  _setupEventListeners() {
    this.element.addEventListener('click', (e) => {
      if (e.target.classList.contains('tab-close')) return;
      this.activate(); // Вызываем метод activate экземпляра
    });

    const closeButton = this.element.querySelector('.tab-close');
    closeButton.addEventListener('click', (e) => {
      e.stopPropagation(); // Предотвращаем активацию вкладки при клике на кнопку закрытия
      this.close(); // Вызываем метод close экземпляра
    });

    this.webview.addEventListener('page-title-updated', (e) => {
      this.title = e.title;
      this.element.querySelector('.tab-title').textContent = e.title;
      // Обновить заголовок окна, если это активная вкладка
      if (this.id === activeTabId) {
        document.title = `${e.title} - A11 Browser`;
      }
    });

    this.webview.addEventListener('page-favicon-updated', (e) => {
      if (e.favicons && e.favicons.length > 0) {
        this.favicon = e.favicons[0];
        const faviconElement = this.element.querySelector('.tab-favicon') || document.createElement('img');
        faviconElement.classList.add('tab-favicon');
        faviconElement.src = this.favicon;
        if (!this.element.querySelector('.tab-favicon')) {
            this.element.insertBefore(faviconElement, this.element.querySelector('.tab-title'));
        }
      }
    });

    this.webview.addEventListener('did-start-loading', () => {
      this.isLoading = true;
      this.element.classList.add('loading');
      this.element.querySelector('.loading-spinner').style.display = 'inline-block';
      if (this.id === activeTabId) {
        // navigationManager.updateNavigationState(); // Предполагается, что эта функция будет в navigationManager
      }
    });

    this.webview.addEventListener('did-stop-loading', () => {
      this.isLoading = false;
      this.element.classList.remove('loading');
      this.element.querySelector('.loading-spinner').style.display = 'none';
      const currentUrl = this.webview.getURL();
      const currentTitle = this.webview.getTitle();

      if (this.id === activeTabId) {
        urlInput.value = currentUrl;
        document.title = `${currentTitle} - A11 Browser`;
        // navigationManager.updateNavigationState();
        // bookmarkManager.updateBookmarkState(currentUrl);
      }

      // Добавляем в историю вкладки, если URL изменился
      if (this.history.length === 0 || this.history[this.history.length -1].url !== currentUrl) {
          this.history = this.history.slice(0, this.historyIndex + 1); // Обрезаем историю, если мы вернулись назад и перешли на новый URL
          this.history.push({url: currentUrl, title: currentTitle});
          this.historyIndex = this.history.length -1;
      }

      if (window.settingsManager && window.historyManager) {
        if (!window.settingsManager.isIncognitoMode() && currentUrl !== 'about:blank') {
          window.historyManager.addHistoryItem({
            url: currentUrl,
            title: this.title,
            timestamp: Date.now()
          });
        }
      }
      if (window.navigationManager) {
        window.navigationManager.updateNavigationState();
      }
      if (window.bookmarkManager) {
        window.bookmarkManager.updateBookmarkState(this.webview.getURL() || this.url);
      }
    });

    this.webview.addEventListener('context-menu', (params) => {
      if (window.browserAPI && typeof window.browserAPI.showContextMenu === 'function') {
        window.browserAPI.showContextMenu({
          x: params.x,
          y: params.y,
          linkURL: params.linkURL,
          srcURL: params.srcURL,
          selectionText: params.selectionText,
          isEditable: params.isEditable
        });
      } else {
        console.log('Context menu event:', params);
      }
    });
  }

  /**
   * @description Activates the tab, making it the visible tab.
   */
  activate() {
    if (activeTabId === this.id) return; // Уже активна

    // Деактивируем предыдущую активную вкладку
    const previousActiveTab = getTabById(activeTabId);
    if (previousActiveTab) {
      previousActiveTab.deactivate();
    }

    this.element.classList.add('active');
    this.webview.classList.add('active');
    this.webview.focus(); // Передаем фокус в webview
    activeTabId = this.id;

    urlInput.value = this.webview.getURL() || this.url; // Обновляем URL в адресной строке
    document.title = `${this.webview.getTitle() || this.title} - A11 Browser`;

    if (window.navigationManager) {
      window.navigationManager.updateNavigationState();
    }
    if (window.bookmarkManager) {
      window.bookmarkManager.updateBookmarkState(this.webview.getURL() || this.url);
    }

    // Сообщаем об изменении активной вкладки
    const event = new CustomEvent('active-tab-changed', { detail: { tabId: this.id } });
    document.dispatchEvent(event);
  }

  /**
   * @description Deactivates the tab, hiding its content.
   */
  deactivate() {
    this.element.classList.remove('active');
    this.webview.classList.remove('active');
  }

  /**
   * @description Closes the tab and removes its elements from the DOM.
   */
  close() {
    // Находим индекс вкладки
    const tabIndex = tabs.findIndex(t => t.id === this.id);
    if (tabIndex === -1) return;

    // Анимация закрытия вкладки
    if (uiManager && typeof uiManager.animateTabClose === 'function') {
      uiManager.animateTabClose(this.element, () => {
        this._performCloseActions(tabIndex);
      });
    } else {
      this._performCloseActions(tabIndex);
    }
  }

  /**
   * @private
   * @description Performs the actual closing actions after animation (if any).
   * @param {number} tabIndex - The index of the tab to close.
   */
  _performCloseActions(tabIndex) {
    // Удаляем элемент вкладки и webview из DOM
    if (this.element) {
      this.element.remove();
    }
    if (this.webview) {
      this.webview.remove();
    }

    // Удаляем вкладку из массива tabs
    tabs.splice(tabIndex, 1);

    // Если закрыта активная вкладка, активируем другую или создаем новую
    if (this.id === activeTabId) {
      activeTabId = null; // Сбрасываем активную вкладку
      if (tabs.length > 0) {
        // Активируем предыдущую или первую вкладку
        const newActiveTabIndex = Math.max(0, tabIndex - 1);
        tabs[newActiveTabIndex].activate();
      } else {
        // Если вкладок не осталось, создаем новую
        createNewTab();
      }
    }

    // Если вкладок не осталось, а кнопка "Новая вкладка" скрыта (например, в режиме одной вкладки), показываем ее
    if (tabs.length === 0 && newTabButton.style.display === 'none') {
        // Логика для отображения кнопки новой вкладки, если это необходимо
    }

    // Оповещаем другие модули о закрытии вкладки
    const event = new CustomEvent('tab-closed', { detail: { tabId: this.id } });
    document.dispatchEvent(event);

    console.log(`Tab closed: ${this.id}`);
  }

  /**
   * @description Navigates the tab's webview to a new URL.
   * @param {string} url - The URL to navigate to.
   */
  navigateTo(url) {
    if (!url || typeof url !== 'string') return;
    // Простая проверка на валидность URL (можно улучшить)
    let targetUrl = url;
    try {
      let defaultSearchEngine = 'https://www.google.com/search?q=';
      if (window.settingsManager) {
        const defaultEngine = window.settingsManager.getSetting('defaultSearchEngine');
        if (defaultEngine) {
          defaultSearchEngine = defaultEngine;
        }
      }
      const testUrl = new URL(`http://${url}`);
      if (testUrl.hostname.includes('.')) {
          targetUrl = `http://${url}`;
      } else {
          targetUrl = `${defaultSearchEngine}${encodeURIComponent(url)}`;
      }
    } catch (e) {
      targetUrl = `https://www.google.com/search?q=${encodeURIComponent(url)}`;
    }
    this.webview.loadURL(targetUrl);
  }

  /**
   * @description Navigates back in the tab's history.
   */
  goBack() {
    if (this.webview.canGoBack()) {
      this.webview.goBack();
    }
  }

  /**
   * @description Navigates forward in the tab's history.
   */
  goForward() {
    if (this.webview.canGoForward()) {
      this.webview.goForward();
    }
  }

  /**
   * @description Reloads the current page in the tab.
   */
  reload() {
    this.webview.reload();
  }

  /**
   * @description Stops loading the current page in the tab.
   */
  stopLoading() {
    this.webview.stop();
  }
}

/**
 * @description Creates a new tab.
 * @param {string} [url='about:blank'] - The initial URL for the new tab.
 * @param {boolean} [activate=true] - Whether to activate the new tab immediately.
 * @returns {Tab} The newly created tab object.
 */
function createNewTab(url = 'about:blank', activate = true) {
  const tabId = `tab-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
  const newTab = new Tab(tabId, 'Новая вкладка', url);
  tabs.push(newTab);

  if (activate) {
    newTab.activate();
  }
  if (window.sessionManager && typeof window.sessionManager.saveCurrentSession === 'function') {
    window.sessionManager.saveCurrentSession();
  }
  window.browserAPI.sendTabsChanged(getTabsInfo()); // Уведомляем главный процесс об изменении вкладок
  return newTab;
}

function activateTab(tabId) {
  const tabToActivate = getTabById(tabId);
  if (tabToActivate) {
    tabToActivate.activate();
  }
}

function closeTab(tabId) {
  const tabToClose = getTabById(tabId);
  if (tabToClose) {
    tabToClose.close();
  }
}

function getTabById(tabId) {
  return tabs.find(tab => tab.id === tabId) || null;
}

function getActiveTab() {
  return getTabById(activeTabId);
}

function getTabsInfo() {
  return tabs.map(tab => ({
    id: tab.id,
    url: tab.webview.getURL() || tab.url,
    title: tab.webview.getTitle() || tab.title,
    favicon: tab.favicon,
    isActive: tab.id === activeTabId,
    isLoading: tab.isLoading
  }));
}

// Инициализация первой вкладки при загрузке
// document.addEventListener('DOMContentLoaded', () => {
//   if (tabs.length === 0) {
//     createNewTab();
//   }
// });

// Обработчик для кнопки новой вкладки
if (newTabButton) {
  newTabButton.addEventListener('click', () => createNewTab(undefined, true));
}

export {
  Tab,
  tabs,
  activeTabId,
  createNewTab,
  activateTab,
  closeTab,
  getTabById,
  getActiveTab,
  getTabsInfo
};
