:root {
    /* Dark Theme Colors */
    --bg-color: #1e1e1e;
    --text-color: #e0e0e0;
    --primary-color: #bb86fc;
    --secondary-color: #03dac6;
    --accent-color: #ff4081;

    --toolbar-bg: #2d2d2d;
    --toolbar-button-hover-bg: #3a3a3a;
    --toolbar-button-active-bg: #4a4a4a;

    --tab-bg: #2d2d2d;
    --tab-active-bg: #1e1e1e;
    --tab-hover-bg: #3a3a3a;
    --tab-text-color: #e0e0e0;
    --tab-close-button-hover-bg: #e53935;

    --address-bar-bg: #3a3a3a;
    --address-bar-text-color: #e0e0e0;
    --address-bar-border-color: #555;

    --panel-bg: #2d2d2d;
    --panel-border-color: #555;
    --panel-header-bg: #3a3a3a;
    --panel-text-color: #e0e0e0;
    --panel-item-hover-bg: #3a3a3a;

    --scrollbar-thumb-bg: #555;
    --scrollbar-track-bg: #2d2d2d;

    /* Specific element colors */
    --button-bg: var(--primary-color);
    --button-text-color: #ffffff;
    --button-hover-bg: #9c27b0;

    --input-border-color: #555;
    --input-focus-border-color: var(--primary-color);

    /* Shadows and borders */
    --shadow-color: rgba(0, 0, 0, 0.5);
    --border-color: #444;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
}

.browser-container {
    background-color: var(--bg-color);
    border-color: var(--border-color);
}

.toolbar {
    background-color: var(--toolbar-bg);
    border-bottom: 1px solid var(--border-color);
}

.toolbar-button {
    color: var(--text-color);
}

.toolbar-button:hover {
    background-color: var(--toolbar-button-hover-bg);
}

.toolbar-button:active {
    background-color: var(--toolbar-button-active-bg);
}

.address-bar-container {
    background-color: var(--address-bar-bg);
    border: 1px solid var(--address-bar-border-color);
    color: var(--address-bar-text-color);
}

.tab-bar {
    background-color: var(--toolbar-bg);
    border-bottom: 1px solid var(--border-color);
}

.tab {
    background-color: var(--tab-bg);
    color: var(--tab-text-color);
    border-right: 1px solid var(--border-color);
}

.tab.active {
    background-color: var(--tab-active-bg);
    border-bottom-color: var(--primary-color);
}

.tab:hover:not(.active) {
    background-color: var(--tab-hover-bg);
}

.tab-close-button:hover {
    background-color: var(--tab-close-button-hover-bg);
}

.webview-container {
    background-color: var(--bg-color);
}

.panel {
    background-color: var(--panel-bg);
    border-left: 1px solid var(--panel-border-color);
}

.panel-header {
    background-color: var(--panel-header-bg);
    border-bottom: 1px solid var(--panel-border-color);
    color: var(--panel-text-color);
}

.panel-item:hover {
    background-color: var(--panel-item-hover-bg);
}

/* Scrollbar styles */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: var(--scrollbar-track-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-bg);
    border-radius: 6px;
    border: 3px solid var(--scrollbar-track-bg);
}

::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* Input field styles */
input[type="text"],
input[type="search"],
input[type="number"],
textarea {
    background-color: var(--address-bar-bg);
    color: var(--address-bar-text-color);
    border: 1px solid var(--input-border-color);
}

input[type="text"]:focus,
input[type="search"]:focus,
input[type="number"]:focus,
textarea:focus {
    border-color: var(--input-focus-border-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.3);
}

/* Button styles */
button {
    background-color: var(--button-bg);
    color: var(--button-text-color);
    border: none;
}

button:hover {
    background-color: var(--button-hover-bg);
}

/* Checkbox toggle */
.switch input:checked + .slider {
    background-color: var(--primary-color);
}

.switch input:focus + .slider {
    box-shadow: 0 0 1px var(--primary-color);
}

.switch input:checked + .slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}

/* Add more specific styles as needed */