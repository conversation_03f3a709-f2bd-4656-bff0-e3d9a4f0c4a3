:root {
  /* Light Theme Colors */
  --color-background-light: #f5f5f5;
  --color-text-light: #333;
  --color-primary-light: #007bff;
  --color-primary-hover-light: #0056b3;
  --color-secondary-light: #6c757d;
  --color-secondary-hover-light: #5a6268;
  --color-border-light: #ddd;
  --color-shadow-light: rgba(0, 0, 0, 0.1);

  /* Dark Theme Colors */
  --color-background-dark: #2c2c2c;
  --color-text-dark: #e0e0e0;
  --color-primary-dark: #66b3ff;
  --color-primary-hover-dark: #3399ff;
  --color-secondary-dark: #999;
  --color-secondary-hover-dark: #777;
  --color-border-dark: #444;
  --color-shadow-dark: rgba(0, 0, 0, 0.3);

  /* Modern Dark Theme Colors */
  --color-background-modern-dark: #1e1e1e;
  --color-text-modern-dark: #e0e0e0;
  --color-primary-modern-dark: #bb86fc;
  --color-primary-hover-modern-dark: #9c27b0;
  --color-secondary-modern-dark: #03dac6;
  --color-secondary-hover-modern-dark: #018786;
  --color-border-modern-dark: #444;
  --color-shadow-modern-dark: rgba(0, 0, 0, 0.5);

  /* Цвета для вкладок */
  --color-tabs-container-bg: #e0e0e0;
  --color-tabs-container-border: #ccc;
  --color-tab-bg: #d0d0d0;
  --color-tab-text: #333;
  --color-tab-hover-bg: #c0c0c0;
  --color-tab-active-bg: #f5f5f5;
  --color-tab-active-text: #000;
  --color-tab-active-border: #ccc;
  --color-tab-close-icon: #555;
  --color-tab-close-hover-bg: rgba(0, 0, 0, 0.1);
  --color-tab-close-hover-icon: #000;
  --color-tab-active-close-icon: #333;
  --color-tab-active-close-hover-bg: rgba(0, 0, 0, 0.15);
  --color-tab-active-close-hover-icon: #000;
  --color-new-tab-icon: #555;
  --color-new-tab-hover-bg: #ccc;
  --color-new-tab-hover-icon: #000;

  /* Цвета для панели инструментов */
  --color-toolbar-bg: #f0f0f0;
  --color-toolbar-border: #ddd;
  --color-button-bg: transparent;
  --color-button-text: #555;
  --color-button-hover-bg: #e9e9e9;
  --color-button-hover-text: #000;
  --color-button-active-bg: #d0d0d0;
  --color-button-active-text: #000;
  --color-input-bg: #fff;
  --color-input-border: #ccc;
  --color-input-text: #333;
  --color-input-placeholder: #888;
  --color-go-button-bg: #007bff;
  --color-go-button-text: #fff;
  --color-go-button-hover-bg: #0056b3;

  /* Цвета для webview */
  --color-webview-border: #ccc;

  /* Цвета для панелей (настроек, расширений и т.д.) */
  --color-panel-bg: #fff;
  --color-panel-border: #ccc;
  --color-panel-shadow: rgba(0, 0, 0, 0.2);
  --color-panel-header-bg: #f0f0f0;
  --color-panel-header-text: #333;
  --color-panel-close-icon: #555;
  --color-panel-close-hover-bg: rgba(0, 0, 0, 0.1);
  --color-panel-close-hover-icon: #000;
  --color-settings-group-border: #eee;

  /* Цвета для скроллбаров */
  --color-scrollbar-thumb: #aaa;
  --color-scrollbar-track: #e0e0e0;

  /* Анимации */
  --animation-speed-theme: 0.3s;
  --animation-speed-interface: 0.2s;
  --animation-speed: 0.3s;

  /* Шрифты */
  --font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-size-base: 14px;
  --font-size-small: 12px;
  --font-size-medium: 14px;
  --font-size-large: 16px;
  --font-size-xl: 20px;

  /* Отступы и размеры */
  --spacing-unit: 8px;
  --border-radius-base: 4px;
  --border-radius-medium: 8px;
  --border-radius-large: 12px;

  /* Общие стили для кнопок и полей ввода */
  --color-button-primary-bg: var(--color-primary);
  --color-button-primary-text: #fff;
  --color-button-primary-hover-bg: var(--color-primary-hover);
  --color-button-secondary-bg: var(--color-secondary);
  --color-button-secondary-text: #fff;
  --color-button-secondary-hover-bg: var(--color-secondary-hover);
  --color-input-border: var(--color-border);
  --color-input-bg: var(--color-background);
  --color-input-text: var(--color-text);
  --color-input-placeholder: #888;

  /* Цвета для модальных окон */
  --color-modal-bg: var(--color-background);
  --color-modal-border: var(--color-border);
  --color-modal-shadow: var(--color-shadow);
  --color-modal-title: var(--color-text);
  --color-modal-message: var(--color-text);

  /* Цвета для контекстного меню */
  --color-context-menu-bg: var(--color-panel-bg);
  --color-context-menu-border: var(--color-panel-border);
  --color-context-menu-shadow: var(--color-panel-shadow);
  --color-context-menu-item-text: var(--color-text);
  --color-context-menu-item-hover-bg: var(--color-primary);
  --color-context-menu-item-hover-text: #fff;

  /* Цвета для скроллбаров */
  --color-scrollbar-thumb: #aaa;
  --color-scrollbar-track: #e0e0e0;

  /* Цвета для адресной строки */
  --color-address-bar-bg: var(--color-input-bg);
  --color-address-bar-border: var(--color-input-border);
  --color-address-bar-text: var(--color-input-text);
  --color-address-bar-focus-border: var(--color-primary);
  --color-address-bar-focus-shadow: rgba(0, 123, 255, 0.25);
}

  /* Tabs Colors */
  --color-tabs-container-bg-light: #e0e0e0;
  --color-tabs-container-border-light: #ccc;
  --color-tab-bg-light: #d0d0d0;
  --color-tab-text-light: #333;
  --color-tab-hover-bg-light: #c0c0c0;
  --color-tab-active-bg-light: #f5f5f5;
  --color-tab-active-text-light: #000;
  --color-tab-active-border-light: #ccc;
  --color-tab-close-icon-light: #555;
  --color-tab-close-hover-bg-light: rgba(0, 0, 0, 0.1);
  --color-tab-close-hover-icon-light: #000;
  --color-tab-active-close-icon-light: #333;
  --color-tab-active-close-hover-bg-light: rgba(0, 0, 0, 0.15);
  --color-tab-active-close-hover-icon-light: #000;
  --color-new-tab-icon-light: #555;
  --color-new-tab-hover-bg-light: #ccc;
  --color-new-tab-hover-icon-light: #000;

  --color-tabs-container-bg-dark: #3c3c3c;
  --color-tabs-container-border-dark: #555;
  --color-tab-bg-dark: #4c4c4c;
  --color-tab-text-dark: #e0e0e0;
  --color-tab-hover-bg-dark: #5c5c5c;
  --color-tab-active-bg-dark: #2c2c2c;
  --color-tab-active-text-dark: #fff;
  --color-tab-active-border-dark: #555;
  --color-tab-close-icon-dark: #bbb;
  --color-tab-close-hover-bg-dark: rgba(255, 255, 255, 0.1);
  --color-tab-close-hover-icon-dark: #fff;
  --color-tab-active-close-icon-dark: #e0e0e0;
  --color-tab-active-close-hover-bg-dark: rgba(255, 255, 255, 0.15);
  --color-tab-active-close-hover-icon-dark: #fff;
  --color-new-tab-icon-dark: #bbb;
  --color-new-tab-hover-bg-dark: #5c5c5c;
  --color-new-tab-hover-icon-dark: #fff;

  --color-tabs-container-bg-modern-dark: #2d2d2d;
  --color-tabs-container-border-modern-dark: #555;
  --color-tab-bg-modern-dark: #2d2d2d;
  --color-tab-text-modern-dark: #e0e0e0;
  --color-tab-hover-bg-modern-dark: #3a3a3a;
  --color-tab-active-bg-modern-dark: #1e1e1e;
  --color-tab-active-text-modern-dark: #e0e0e0;
  --color-tab-active-border-modern-dark: #555;
  --color-tab-close-icon-modern-dark: #e0e0e0;
  --color-tab-close-hover-bg-modern-dark: #e53935;
  --color-tab-close-hover-icon-modern-dark: #e0e0e0;
  --color-tab-active-close-icon-modern-dark: #e0e0e0;
  --color-tab-active-close-hover-bg-modern-dark: #e53935;
  --color-tab-active-close-hover-icon-modern-dark: #e0e0e0;
  --color-new-tab-icon-modern-dark: #e0e0e0;
  --color-new-tab-hover-bg-modern-dark: #3a3a3a;
  --color-new-tab-hover-icon-modern-dark: #e0e0e0;

  /* Toolbar Colors */
  --color-toolbar-bg-light: #f0f0f0;
  --color-toolbar-border-light: #ddd;
  --color-button-bg-light: transparent;
  --color-button-text-light: #555;
  --color-button-hover-bg-light: #e9e9e9;
  --color-button-hover-text-light: #000;
  --color-button-active-bg-light: #d0d0d0;
  --color-button-active-text-light: #000;
  --color-input-bg-light: #fff;
  --color-input-border-light: #ccc;
  --color-input-text-light: #333;
  --color-input-placeholder-light: #888;
  --color-go-button-bg-light: #007bff;
  --color-go-button-text-light: #fff;
  --color-go-button-hover-bg-light: #0056b3;

  --color-toolbar-bg-dark: #333;
  --color-toolbar-border-dark: #555;
  --color-button-text-dark: #bbb;
  --color-button-hover-bg-dark: #444;
  --color-button-hover-text-dark: #fff;
  --color-button-active-bg-dark: #555;
  --color-button-active-text-dark: #fff;
  --color-input-bg-dark: #444;
  --color-input-border-dark: #666;
  --color-input-text-dark: #e0e0e0;
  --color-input-placeholder-dark: #999;
  --color-go-button-bg-dark: #66b3ff;
  --color-go-button-text-dark: #2c2c2c;
  --color-go-button-hover-bg-dark: #3399ff;

  --color-toolbar-bg-modern-dark: #2d2d2d;
  --color-toolbar-border-modern-dark: #555;
  --color-button-text-modern-dark: #e0e0e0;
  --color-button-hover-bg-modern-dark: #3a3a3a;
  --color-button-hover-text-modern-dark: #e0e0e0;
  --color-button-active-bg-modern-dark: #4a4a4a;
  --color-button-active-text-modern-dark: #e0e0e0;
  --color-input-bg-modern-dark: #3a3a3a;
  --color-input-border-modern-dark: #555;
  --color-input-text-modern-dark: #e0e0e0;
  --color-input-placeholder-modern-dark: #999;
  --color-go-button-bg-modern-dark: #bb86fc;
  --color-go-button-text-modern-dark: #ffffff;
  --color-go-button-hover-bg-modern-dark: #9c27b0;

  /* Webview Colors */
  --color-webview-border-light: #ccc;
  --color-webview-border-dark: #555;
  --color-webview-border-modern-dark: #555;

  /* Panel Colors */
  --color-panel-bg-light: #fff;
  --color-panel-border-light: #ccc;
  --color-panel-shadow-light: rgba(0, 0, 0, 0.2);
  --color-panel-header-bg-light: #f0f0f0;
  --color-panel-header-text-light: #333;
  --color-panel-close-icon-light: #555;
  --color-panel-close-hover-bg-light: rgba(0, 0, 0, 0.1);
  --color-panel-close-hover-icon-light: #000;
  --color-settings-group-border-light: #eee;

  --color-panel-bg-dark: #3a3a3a;
  --color-panel-border-dark: #555;
  --color-panel-shadow-dark: rgba(0, 0, 0, 0.4);
  --color-panel-header-bg-dark: #444;
  --color-panel-header-text-dark: #e0e0e0;
  --color-panel-close-icon-dark: #bbb;
  --color-panel-close-hover-bg-dark: rgba(255, 255, 255, 0.1);
  --color-panel-close-hover-icon-dark: #fff;
  --color-settings-group-border-dark: #555;

  --color-panel-bg-modern-dark: #2d2d2d;
  --color-panel-border-modern-dark: #555;
  --color-panel-shadow-modern-dark: rgba(0, 0, 0, 0.4);
  --color-panel-header-bg-modern-dark: #3a3a3a;
  --color-panel-header-text-modern-dark: #e0e0e0;
  --color-panel-close-icon-modern-dark: #e0e0e0;
  --color-panel-close-hover-bg-modern-dark: #e53935;
  --color-panel-close-hover-icon-modern-dark: #e0e0e0;
  --color-settings-group-border-modern-dark: #555;

  /* Scrollbar Colors */
  --color-scrollbar-thumb-light: #aaa;
  --color-scrollbar-track-light: #e0e0e0;

  --color-scrollbar-thumb-dark: #777;
  --color-scrollbar-track-dark: #3c3c3c;

  --color-scrollbar-thumb-modern-dark: #555;
  --color-scrollbar-track-modern-dark: #2d2d2d;