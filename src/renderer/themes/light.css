:root {
  --color-background: var(--color-background-light);
  --color-text: var(--color-text-light);
  --color-primary: var(--color-primary-light);
  --color-primary-hover: var(--color-primary-hover-light);
  --color-secondary: var(--color-secondary-light);
  --color-secondary-hover: var(--color-secondary-hover-light);
  --color-border: var(--color-border-light);
  --color-shadow: var(--color-shadow-light);

  --color-tabs-container-bg: var(--color-tabs-container-bg-light);
  --color-tabs-container-border: var(--color-tabs-container-border-light);
  --color-tab-bg: var(--color-tab-bg-light);
  --color-tab-text: var(--color-tab-text-light);
  --color-tab-hover-bg: var(--color-tab-hover-bg-light);
  --color-tab-active-bg: var(--color-tab-active-bg-light);
  --color-tab-active-text: var(--color-tab-active-text-light);
  --color-tab-active-border: var(--color-tab-active-border-light);
  --color-tab-close-icon: var(--color-tab-close-icon-light);
  --color-tab-close-hover-bg: var(--color-tab-close-hover-bg-light);
  --color-tab-close-hover-icon: var(--color-tab-close-hover-icon-light);
  --color-tab-active-close-icon: var(--color-tab-active-close-icon-light);
  --color-tab-active-close-hover-bg: var(--color-tab-active-close-hover-bg-light);
  --color-tab-active-close-hover-icon: var(--color-tab-active-close-hover-icon-light);
  --color-new-tab-icon: var(--color-new-tab-icon-light);
  --color-new-tab-hover-bg: var(--color-new-tab-hover-bg-light);
  --color-new-tab-hover-icon: var(--color-new-tab-hover-icon-light);

  --color-toolbar-bg: var(--color-toolbar-bg-light);
  --color-toolbar-border: var(--color-toolbar-border-light);
  --color-button-text: var(--color-button-text-light);
  --color-button-hover-bg: var(--color-button-hover-bg-light);
  --color-button-hover-text: var(--color-button-hover-text-light);
  --color-button-active-bg: var(--color-button-active-bg-light);
  --color-button-active-text: var(--color-button-active-text-light);
  --color-input-bg: var(--color-input-bg-light);
  --color-input-border: var(--color-input-border-light);
  --color-input-text: var(--color-input-text-light);
  --color-input-placeholder: var(--color-input-placeholder-light);
  --color-go-button-bg: var(--color-go-button-bg-light);
  --color-go-button-text: var(--color-go-button-text-light);
  --color-go-button-hover-bg: var(--color-go-button-hover-bg-light);

  --color-webview-border: var(--color-webview-border-light);

  --color-panel-bg: var(--color-panel-bg-light);
  --color-panel-border: var(--color-panel-border-light);
  --color-panel-shadow: var(--color-panel-shadow-light);
  --color-panel-header-bg: var(--color-panel-header-bg-light);
  --color-panel-header-text: var(--color-panel-header-text-light);
  --color-panel-close-icon: var(--color-panel-close-icon-light);
  --color-panel-close-hover-bg: var(--color-panel-close-hover-bg-light);
  --color-panel-close-hover-icon: var(--color-panel-close-hover-icon-light);
  --color-settings-group-border: var(--color-settings-group-border-light);

  --color-scrollbar-thumb: var(--color-scrollbar-thumb-light);
  --color-scrollbar-track: var(--color-scrollbar-track-light);
}