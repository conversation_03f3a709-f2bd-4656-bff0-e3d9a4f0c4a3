:root {
  --color-background: var(--color-background-dark);
  --color-text: var(--color-text-dark);
  --color-primary: var(--color-primary-dark);
  --color-primary-hover: var(--color-primary-hover-dark);
  --color-secondary: var(--color-secondary-dark);
  --color-secondary-hover: var(--color-secondary-hover-dark);
  --color-border: var(--color-border-dark);
  --color-shadow: var(--color-shadow-dark);

  --color-tabs-container-bg: var(--color-tabs-container-bg-dark);
  --color-tabs-container-border: var(--color-tabs-container-border-dark);
  --color-tab-bg: var(--color-tab-bg-dark);
  --color-tab-text: var(--color-tab-text-dark);
  --color-tab-hover-bg: var(--color-tab-hover-bg-dark);
  --color-tab-active-bg: var(--color-tab-active-bg-dark);
  --color-tab-active-text: var(--color-tab-active-text-dark);
  --color-tab-active-border: var(--color-tab-active-border-dark);
  --color-tab-close-icon: var(--color-tab-close-icon-dark);
  --color-tab-close-hover-bg: var(--color-tab-close-hover-bg-dark);
  --color-tab-close-hover-icon: var(--color-tab-close-hover-icon-dark);
  --color-tab-active-close-icon: var(--color-tab-active-close-icon-dark);
  --color-tab-active-close-hover-bg: var(--color-tab-active-close-hover-bg-dark);
  --color-tab-active-close-hover-icon: var(--color-tab-active-close-hover-icon-dark);
  --color-new-tab-icon: var(--color-new-tab-icon-dark);
  --color-new-tab-hover-bg: var(--color-new-tab-hover-bg-dark);
  --color-new-tab-hover-icon: var(--color-new-tab-hover-icon-dark);

  --color-toolbar-bg: var(--color-toolbar-bg-dark);
  --color-toolbar-border: var(--color-toolbar-border-dark);
  --color-button-text: var(--color-button-text-dark);
  --color-button-hover-bg: var(--color-button-hover-bg-dark);
  --color-button-hover-text: var(--color-button-hover-text-dark);
  --color-button-active-bg: var(--color-button-active-bg-dark);
  --color-button-active-text: var(--color-button-active-text-dark);
  --color-input-bg: var(--color-input-bg-dark);
  --color-input-border: var(--color-input-border-dark);
  --color-input-text: var(--color-input-text-dark);
  --color-input-placeholder: var(--color-input-placeholder-dark);
  --color-go-button-bg: var(--color-go-button-bg-dark);
  --color-go-button-text: var(--color-go-button-text-dark);
  --color-go-button-hover-bg: var(--color-go-button-hover-bg-dark);

  --color-webview-border: var(--color-webview-border-dark);

  --color-panel-bg: var(--color-panel-bg-dark);
  --color-panel-border: var(--color-panel-border-dark);
  --color-panel-shadow: var(--color-panel-shadow-dark);
  --color-panel-header-bg: var(--color-panel-header-bg-dark);
  --color-panel-header-text: var(--color-panel-header-text-dark);
  --color-panel-close-icon: var(--color-panel-close-icon-dark);
  --color-panel-close-hover-bg: var(--color-panel-close-hover-bg-dark);
  --color-panel-close-hover-icon: var(--color-panel-close-hover-icon-dark);
  --color-settings-group-border: var(--color-settings-group-border-dark);

  --color-scrollbar-thumb: var(--color-scrollbar-thumb-dark);
  --color-scrollbar-track: var(--color-scrollbar-track-dark);
}]}