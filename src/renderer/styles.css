/* Основные стили для браузера */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

@import url('./themes/_variables.css');

body {
  font-family: sans-serif;
  margin: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
  overflow: hidden; /* Hide scrollbars */
  transition: background-color 0.3s ease, color 0.3s ease;
}

.browser-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.light-theme body {
  background-color: var(--color-background-light);
  color: var(--color-text-light);
}

.dark-theme body {
  background-color: var(--color-background-dark);
  color: var(--color-text-dark);
}

.modern-dark-theme body {
  background-color: var(--color-background-modern-dark);
  color: var(--color-text-modern-dark);
}

.browser-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden; /* Ensure no overflow from panels */
}

.light-theme .browser-container {
  background-color: var(--color-background-light);
  border-color: var(--color-border-light);
}

.dark-theme .browser-container {
  background-color: var(--color-background-dark);
  border-color: var(--color-border-dark);
}

.modern-dark-theme .browser-container {
  background-color: var(--color-background-modern-dark);
  border-color: var(--color-border-modern-dark);
}

/* Toolbar Styles */
.browser-toolbar {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--toolbar-bg);
  border-bottom: 1px solid var(--border-color);
  -webkit-app-region: drag;
  z-index: 100;
  position: relative;
  gap: var(--spacing-sm);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.light-theme .browser-toolbar {
  background-color: var(--color-toolbar-bg-light);
  border-color: var(--color-toolbar-border-light);
}

.dark-theme .browser-toolbar {
  background-color: var(--color-toolbar-bg-dark);
  border-color: var(--color-toolbar-border-dark);
}

.modern-dark-theme .browser-toolbar {
  background-color: var(--color-toolbar-bg-modern-dark);
  border-color: var(--color-toolbar-border-modern-dark);
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 5px;
}

.navigation-controls {
  flex-shrink: 0;
}

.address-bar-container {
  flex-grow: 1;
  display: flex;
  align-items: center;
  background-color: var(--address-bar-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xs) var(--spacing-sm);
  margin: 0 var(--spacing-sm);
  transition: background-color 0.3s ease;
}

.address-bar input {
  flex-grow: 1;
  border: none;
  background: transparent;
  color: var(--text-color);
  font-size: var(--font-size-md);
  padding: 0;
  outline: none;
}

.address-bar input::placeholder {
  color: var(--placeholder-color);
}

.address-bar input::placeholder {
  color: var(--placeholder-color);
}

.address-bar .security-indicator {
  width: 16px;
  height: 16px;
  margin-right: var(--spacing-xs);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.address-bar .security-indicator.secure {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234CAF50"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h-2v-2h2v2zm0-4h-2v-2h2v2z"/></svg>');
}

.address-bar .security-indicator.insecure {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23F44336"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h-2v-2h2v2zm0-4h-2v-2h2v2z"/></svg>');
}

.address-bar .security-indicator.neutral {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFC107"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h-2v-2h2v2zm0-4h-2v-2h2v2z"/></svg>');
}

.go-button {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: none;
  padding: var(--spacing-sm) var(--spacing-md);
  margin-left: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.go-button:hover {
  background-color: var(--button-primary-hover-bg);
}

.main-actions {
  flex-shrink: 0;
}

.toolbar-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toolbar-button {
  background: var(--button-bg);
  border: var(--button-border);
  color: var(--button-text);
  font-size: var(--font-size-lg);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.toolbar-button:hover {
  background-color: var(--button-hover-bg);
  color: var(--button-hover-text);
  border-color: var(--button-hover-border);
}

.toolbar-button:active {
  transform: translateY(1px);
}

/* Settings Panel Styles */
.settings-panel {
  position: fixed;
  top: 0;
  right: -100%; /* Start off-screen */
  width: 80%; /* Adjust as needed */
  max-width: 600px;
  height: 100%;
  background-color: var(--panel-bg);
  border-left: 1px solid var(--border-color);
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
  transition: right 0.3s ease-in-out;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.settings-panel.open {
  right: 0;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.settings-header h2 {
  margin: 0;
  color: var(--text-color);
}

.settings-content {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
}

.settings-nav {
  width: 180px;
  flex-shrink: 0;
  border-right: 1px solid var(--border-color);
  padding: var(--spacing-md) 0;
  overflow-y: auto;
}

.settings-nav-item {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-color);
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.settings-nav-item:hover,
.settings-nav-item.active {
  background-color: var(--hover-bg);
}

.settings-sections {
  flex-grow: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
}

.settings-section {
  display: none;
}

.settings-section.active {
  display: block;
}

.settings-section h3 {
  color: var(--text-color);
  margin-top: 0;
  margin-bottom: var(--spacing-md);
}

.setting-item {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color-light);
}

.setting-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.setting-item label {
  display: block;
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
  font-weight: bold;
}

.setting-item input[type="text"],
.setting-item input[type="number"],
.setting-item select {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-base);
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: var(--font-size-base);
}

.setting-item input[type="checkbox"] {
  margin-right: var(--spacing-xs);
}

.setting-item .description {
  font-size: var(--font-size-sm);
  color: var(--text-color-light);
  margin-top: var(--spacing-xs);
}

.button-group {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.button-group button {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  font-size: var(--font-size-base);
}

.button-group button.primary {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: none;
}

.button-group button.primary:hover {
  background-color: var(--button-primary-hover-bg);
}

.button-group button.secondary {
  background-color: var(--button-bg);
  color: var(--button-text);
  border: 1px solid var(--button-border);
}

.button-group button.secondary:hover {
  background-color: var(--button-hover-bg);
  color: var(--button-hover-text);
  border-color: var(--button-hover-border);
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--toggle-off-bg);
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: var(--toggle-handle-color);
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--toggle-on-bg);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--toggle-on-bg);
}

input:checked + .slider:before {
  transform: translateX(16px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Theme selection */
.theme-selection {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.theme-selection label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
}

.theme-selection input[type="radio"] {
  margin: 0;
}

.custom-theme-inputs {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  padding-left: var(--spacing-md);
  border-left: 2px solid var(--border-color-light);
}

.custom-theme-inputs label {
  font-weight: normal;
}

.custom-theme-inputs input[type="color"] {
  width: 50px;
  height: 30px;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
}

.user-themes-list {
  margin-top: var(--spacing-md);
  border-top: 1px solid var(--border-color-light);
  padding-top: var(--spacing-md);
}

.user-themes-list h4 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.user-theme-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px dashed var(--border-color-light);
}

.user-theme-item:last-child {
  border-bottom: none;
}

.user-theme-item span {
  color: var(--text-color);
}

.user-theme-item button {
  background-color: var(--button-danger-bg);
  color: var(--button-danger-text);
  border: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.user-theme-item button:hover {
  background-color: var(--button-danger-hover-bg);
}

/* Overlay for panels */
#overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

#overlay.active {
  display: block;
}

.toolbar-button.primary {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border-color: var(--button-primary-border);
}

.toolbar-button.primary:hover {
  background-color: var(--button-primary-hover-bg);
  color: var(--button-primary-hover-text);
  border-color: var(--button-primary-hover-border);
}

.toolbar-button.secondary {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border-color: var(--button-secondary-border);
}

.toolbar-button.secondary:hover {
  background-color: var(--button-secondary-hover-bg);
  color: var(--button-secondary-hover-text);
  border-color: var(--button-secondary-hover-border);
}

.toolbar-button:hover {
  background-color: var(--color-toolbar-button-hover-bg);
  color: var(--color-toolbar-button-hover-text);
}

.toolbar-button:active {
  transform: translateY(1px);
}

/* Settings Panel Styles */
.settings-panel {
  position: fixed;
  top: 0;
  right: -100%; /* Start off-screen */
  width: 80%; /* Adjust as needed */
  max-width: 600px;
  height: 100%;
  background-color: var(--panel-bg);
  border-left: 1px solid var(--border-color);
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
  transition: right 0.3s ease-in-out;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.settings-panel.open {
  right: 0;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.settings-header h2 {
  margin: 0;
  color: var(--text-color);
}

.settings-content {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
}

.settings-nav {
  width: 180px;
  flex-shrink: 0;
  border-right: 1px solid var(--border-color);
  padding: var(--spacing-md) 0;
  overflow-y: auto;
}

.settings-nav-item {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-color);
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.settings-nav-item:hover,
.settings-nav-item.active {
  background-color: var(--hover-bg);
}

.settings-sections {
  flex-grow: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
}

.settings-section {
  display: none;
}

.settings-section.active {
  display: block;
}

.settings-section h3 {
  color: var(--text-color);
  margin-top: 0;
  margin-bottom: var(--spacing-md);
}

.setting-item {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color-light);
}

.setting-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.setting-item label {
  display: block;
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
  font-weight: bold;
}

.setting-item input[type="text"],
.setting-item input[type="number"],
.setting-item select {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-base);
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: var(--font-size-base);
}

.setting-item input[type="checkbox"] {
  margin-right: var(--spacing-xs);
}

.setting-item .description {
  font-size: var(--font-size-sm);
  color: var(--text-color-light);
  margin-top: var(--spacing-xs);
}

.button-group {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.button-group button {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  font-size: var(--font-size-base);
}

.button-group button.primary {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: none;
}

.button-group button.primary:hover {
  background-color: var(--button-primary-hover-bg);
}

.button-group button.secondary {
  background-color: var(--button-bg);
  color: var(--button-text);
  border: 1px solid var(--button-border);
}

.button-group button.secondary:hover {
  background-color: var(--button-hover-bg);
  color: var(--button-hover-text);
  border-color: var(--button-hover-border);
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--toggle-off-bg);
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: var(--toggle-handle-color);
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--toggle-on-bg);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--toggle-on-bg);
}

input:checked + .slider:before {
  transform: translateX(16px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Theme selection */
.theme-selection {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.theme-selection label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
}

.theme-selection input[type="radio"] {
  margin: 0;
}

.custom-theme-inputs {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  padding-left: var(--spacing-md);
  border-left: 2px solid var(--border-color-light);
}

.custom-theme-inputs label {
  font-weight: normal;
}

.custom-theme-inputs input[type="color"] {
  width: 50px;
  height: 30px;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
}

.user-themes-list {
  margin-top: var(--spacing-md);
  border-top: 1px solid var(--border-color-light);
  padding-top: var(--spacing-md);
}

.user-themes-list h4 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.user-theme-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px dashed var(--border-color-light);
}

.user-theme-item:last-child {
  border-bottom: none;
}

.user-theme-item span {
  color: var(--text-color);
}

.user-theme-item button {
  background-color: var(--button-danger-bg);
  color: var(--button-danger-text);
  border: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.user-theme-item button:hover {
  background-color: var(--button-danger-hover-bg);
}

/* Overlay for panels */
#overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

#overlay.active {
  display: block;
}

/* Main Content Area */
.browser-content {
  flex-grow: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.webview-container {
  width: 100%;
  height: 100%;
  position: relative;
  flex-grow: 1;
  overflow: hidden;
}

webview {
  width: 100%;
  height: 100%;
  display: inline-flex; /* Required for webview to render correctly */
  border: none;
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 0.3s ease;
}

webview.active {
  opacity: 1;
  z-index: 1;
}

webview:not(.active) {
  opacity: 0;
  z-index: 0;
}

/* Panels (Settings, Extensions, Bookmarks, History, DevTools) */
.panel {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 350px;
  background-color: var(--panel-bg);
  border-left: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out, background-color 0.3s ease, border-color 0.3s ease;
}

.panel.open {
  transform: translateX(0);
}

.panel.open {
  right: 0;
}

.light-theme .panel {
  background-color: var(--color-panel-bg-light);
  border-color: var(--color-panel-border-light);
}

.dark-theme .panel {
  background-color: var(--color-panel-bg-dark);
  border-color: var(--color-panel-border-dark);
}

.modern-dark-theme .panel {
  background-color: var(--color-panel-bg-modern-dark);
  border-color: var(--color-panel-border-modern-dark);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--panel-header-bg);
  flex-shrink: 0;
  color: var(--panel-header-text);
}

.panel-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--panel-header-text);
}

.panel-close-button {
  background: var(--button-bg);
  border: none;
  color: var(--button-text);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.panel-close-button:hover {
  background-color: var(--button-hover-bg);
  color: var(--button-hover-text);
}

.panel-close-button:hover {
  background-color: var(--color-panel-close-button-hover-bg);
  color: var(--color-panel-close-button-hover-text);
}

.panel-content {
  flex-grow: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

/* Overlay for panels */
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--animation-speed-panel) ease-in-out, visibility var(--animation-speed-panel) ease-in-out;
}

.overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Settings Panel Specific Styles */
.settings-nav {
  margin-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-md);
}

.settings-nav ul {
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.settings-nav li a {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  text-decoration: none;
  color: var(--text-color);
  background-color: var(--bg-color);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.settings-nav li a:hover {
  background-color: var(--button-secondary-hover-bg);
}

.settings-nav li a.active {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
}

.settings-sections .settings-section {
  display: none;
}

.settings-sections .settings-section.active {
  display: block;
}

/* Context Menu Styles */
.context-menu {
  position: absolute;
  background-color: var(--context-menu-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  z-index: 2000;
  min-width: 180px;
  padding: var(--spacing-xs) 0;
  display: none; /* Hidden by default */
}

.context-menu.open {
  display: block;
}

.context-menu.open {
  display: block;
}

.context-menu-list {
  list-style: none;
}

.context-menu-item {
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.context-menu-item:hover {
  background-color: var(--context-menu-hover-bg);
}

.context-menu-item i {
  width: 20px; /* Align icons */
  text-align: center;
}

.context-menu-item i {
  width: 20px; /* Align icons */
  text-align: center;
}

/* Modal Styles */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--overlay-bg);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal-container.open {
  opacity: 1;
  visibility: visible;
}

.modal-container.open {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: var(--modal-bg);
  color: var(--text-color);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  text-align: center;
  transform: translateY(-20px);
  transition: transform 0.3s ease;
}

.modal-container.open .modal-content {
  transform: translateY(0);
}

.modal-container.open .modal-content {
  transform: translateY(0);
}

.modal-title {
  font-size: var(--font-size-xxl);
  margin-bottom: var(--spacing-md);
  color: var(--heading-color);
}

.modal-message {
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-lg);
  line-height: 1.5;
}

.modal-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.button {
  padding: 10px 25px;
  border: none;
  border-radius: var(--border-radius-base);
  cursor: pointer;
  font-size: 1em;
  font-weight: 600;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.button.primary {
  background-color: var(--color-button-primary-bg);
  color: var(--color-button-primary-text);
}

.button.primary:hover {
  background-color: var(--color-button-primary-hover-bg);
}

.button.secondary {
  background-color: var(--color-button-secondary-bg);
  color: var(--color-button-secondary-text);
  border: 1px solid var(--color-button-secondary-border);
}

.button.secondary:hover {
  background-color: var(--color-button-secondary-hover-bg);
  color: var(--color-button-secondary-hover-text);
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Tab Styles */
.tabs-container {
  display: flex;
  background-color: var(--tabs-container-bg);
  padding: var(--spacing-xs) var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.3s ease, border-bottom-color 0.3s ease;
  position: relative;
  z-index: 10;
}

.tabs-bar {
  display: flex;
  flex: 1;
  overflow-x: auto;
  scrollbar-width: none; /* Hide scrollbar for a cleaner look */
}

.tabs-bar::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Webkit browsers */
}





.tab {
  display: flex;
  align-items: center;
  min-width: 150px;
  max-width: 220px;
  height: var(--tab-height);
  padding: 0 var(--spacing-md) 0 var(--spacing-lg);
  margin-right: 1px;
  background-color: var(--tab-bg);
  color: var(--tab-text);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  border: 1px solid transparent;
  border-bottom: none;
  opacity: 0;
  transform: translateY(10px);
  animation: tab-appear 0.3s ease forwards;
}

.tab:hover {
  background-color: var(--tab-hover-bg);
}

.tab.active {
  background-color: var(--tab-active-bg);
  color: var(--tab-active-text);
  font-weight: 600;
  z-index: 2;
  border-left: 1px solid var(--tab-active-border);
  border-right: 1px solid var(--tab-active-border);
  border-top: 1px solid var(--tab-active-border);
}

/* Tab Animations */
@keyframes tab-appear {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tab Close Animation */
.tab.closing {
  animation: tab-disappear 0.2s ease forwards;
  pointer-events: none; /* Disable interaction during animation */
}

@keyframes tab-disappear {
  from {
    opacity: 1;
    transform: scaleY(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scaleY(0.8) translateY(10px);
    max-width: 0; /* Collapse tab width */
    padding: 0;
    margin-right: 0;
    overflow: hidden;
  }
}

.tab-favicon {
  width: 16px;
  height: 16px;
  margin-right: var(--spacing-xs);
  object-fit: contain;
}

.tab-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: var(--font-size-sm); /* Slightly smaller for compactness */
}

.tab-close {
  background: transparent;
  border: none;
  font-size: var(--font-size-lg);
  margin-left: var(--spacing-xs);
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-circle);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--tab-close-icon);
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease, background-color 0.2s ease, color 0.2s ease;
}

.tab-close:hover {
  opacity: 1;
  background-color: var(--tab-close-hover-bg);
  color: var(--tab-close-hover-icon);
}

.tab.active .tab-close {
  color: var(--tab-active-close-icon);
}

.tab.active .tab-close:hover {
  background-color: var(--tab-active-close-hover-bg);
  color: var(--tab-active-close-hover-icon);
}

#new-tab-button {
  background-color: transparent;
  border: none;
  font-size: var(--font-size-xl);
  width: var(--tab-height);
  height: var(--tab-height);
  border-radius: var(--border-radius-circle);
  cursor: pointer;
  margin-left: var(--spacing-xs);
  color: var(--new-tab-icon);
  transition: background-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
}

#new-tab-button:hover {
  background-color: var(--new-tab-hover-bg);
  color: var(--new-tab-hover-icon);
  transform: rotate(90deg);
}

/* Панель инструментов */
.browser-toolbar {
  display: flex;
  padding: var(--spacing-unit) 10px;
  background-color: var(--color-toolbar-bg);
  border-bottom: 1px solid var(--color-toolbar-border);
  align-items: center;
  transition: background-color var(--animation-speed-theme) ease, border-bottom-color var(--animation-speed-theme) ease;
}

.toolbar-buttons {
  display: flex;
  -webkit-app-region: no-drag;
}

.toolbar-button {
  background-color: transparent;
  border: none;
  color: var(--color-button-text);
  font-size: 18px;
  padding: 6px 10px;
  margin: 0 4px;
  cursor: pointer;
  border-radius: var(--border-radius-base);
  transition: background-color var(--animation-speed-interface) ease, color var(--animation-speed-interface) ease;
}

.toolbar-button:hover {
  background-color: var(--color-button-hover-bg);
  color: var(--color-button-hover-text);
}

.toolbar-button:active {
  background-color: var(--color-button-active-bg);
  color: var(--color-button-active-text);
}

.address-bar-container {
  flex-grow: 1;
  display: flex;
  align-items: center;
  margin: 0 var(--spacing-unit);
}

.address-bar {
  flex-grow: 1;
  padding: 6px 10px;
  border: 1px solid var(--color-input-border);
  border-radius: var(--border-radius-base);
  background-color: var(--color-input-bg);
  color: var(--color-input-text);
  outline: none;
  transition: border-color var(--animation-speed-interface) ease, background-color var(--animation-speed-interface) ease;
}

.address-bar::placeholder {
  color: var(--color-input-placeholder);
}

.go-button {
  background-color: var(--color-go-button-bg);
  color: var(--color-go-button-text);
  border: none;
  padding: 6px 12px;
  margin-left: 4px;
  border-radius: var(--border-radius-base);
  cursor: pointer;
  transition: background-color var(--animation-speed-interface) ease;
}

.go-button:hover {
  background-color: var(--color-go-button-hover-bg);
}

.address-bar {
  flex: 1;
  display: flex;
  align-items: center; /* Для вертикального выравнивания */
  position: relative; /* Для иконки закладки */
}

#url-input {
  flex: 1;
  padding: 8px 40px 8px 15px; /* Место для иконки закладки и отступ слева */
  border: 1px solid var(--color-url-input-border, #ccc);
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  background-color: var(--color-url-input-bg, #fff);
  color: var(--color-url-input-text, #333);
  transition: border-color var(--animation-speed-interface, 0.2s) ease, box-shadow var(--animation-speed-interface, 0.2s) ease, background-color var(--animation-speed-theme, 0.3s) ease, color var(--animation-speed-theme, 0.3s) ease;
}

#url-input:focus {
  border-color: var(--color-url-input-focus-border, #4285f4);
  box-shadow: 0 0 0 3px var(--color-url-input-focus-shadow, rgba(66, 133, 244, 0.2));
}

#go-button { /* Скрываем, так как Enter обычно используется */
  display: none;
}

.toolbar-actions {
  margin-left: 8px;
  display: flex;
  align-items: center;
}

/* Основное содержимое */
.browser-content {
  flex: 1;
  position: relative;
  background-color: var(--color-content-bg, #f5f5f5);
  overflow: hidden; /* Чтобы анимации не вылезали */
  transition: background-color var(--animation-speed-theme, 0.3s) ease;
}

.browser-content webview {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none; /* Убираем границу webview */
  opacity: 0;
  visibility: hidden;
  transform: translateX(0); /* Начальное состояние для slide */
  transition: opacity var(--animation-speed, 0.3s) ease, transform var(--animation-speed, 0.3s) ease, visibility 0s var(--animation-speed, 0.3s);
  /* transition-delay нужен чтобы visibility менялось после opacity */
}

.browser-content webview.active {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
  z-index: 1;
  transition-delay: 0s;
}

/* Анимации перехода страниц/webview */
.page-transition-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Fade Transition */
.fade-transition.transition-out webview.active {
  opacity: 0;
  transition-duration: calc(var(--animation-speed) / 2);
}

.fade-transition.transition-in webview.active {
  opacity: 1;
  transition-duration: calc(var(--animation-speed) / 2);
  transition-delay: calc(var(--animation-speed) / 2);
}

/* Slide Transition */
.slide-transition webview {
  transition: opacity var(--animation-speed) ease, transform var(--animation-speed) cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-transition.transition-out webview.active {
  opacity: 0;
  transform: translateX(-30px); /* Сдвиг влево при уходе */
}

.slide-transition.transition-in webview.next-active {
  /* Начальное состояние для входящего webview */
  opacity: 0;
  transform: translateX(30px);
}

.slide-transition.transition-in webview.active {
  /* Конечное состояние для входящего webview */
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0s; /* Убираем задержку для активного */
}

/* No Animations */
.no-animations * {
  animation: none !important;
  transition: none !important;
}

/* Переменные для скорости анимации (могут быть переопределены JS) */
:root {
  --animation-speed-slow: 0.5s;
  --animation-speed-normal: 0.3s;
  --animation-speed-fast: 0.15s;
  --animation-speed: var(--animation-speed-normal); /* По умолчанию normal */
  --animation-speed-interface: 0.2s; /* Для мелких элементов интерфейса */
  --animation-speed-theme: 0.3s; /* Для смены тем */
}

/* Стили для светлой темы (пример, будут управляться через JS и CSS переменные) */
body.theme-light {
  --color-background: #ffffff;
  --color-text: #212529;
  --color-tabs-container-bg: #f0f0f0;
  --color-tabs-container-border: #d1d1d1;
  --color-tab-bg: #e0e0e0;
  --color-tab-text: #333;
  --color-tab-hover-bg: #d5d5d5;
  --color-tab-active-bg: #ffffff;
  --color-tab-active-text: #212529;
  --color-tab-active-border: #d1d1d1;
  --color-tab-close-icon: #555;
  --color-tab-close-hover-bg: rgba(0,0,0,0.08);
  --color-tab-close-hover-icon: #000;
  --color-tab-active-close-icon: #333;
  --color-tab-active-close-hover-bg: rgba(0,0,0,0.1);
  --color-new-tab-icon: #555;
  --color-new-tab-hover-bg: #dcdcdc;
  --color-new-tab-hover-icon: #000;
  --color-toolbar-bg: #f8f9fa;
  --color-toolbar-border: #d6d6d6;
  --color-toolbar-icon: #444;
  --color-toolbar-icon-hover: #000;
  --color-toolbar-button-hover-bg: #e9ecef;
  --color-url-input-bg: #ffffff;
  --color-url-input-text: #495057;
  --color-url-input-border: #ced4da;
  --color-url-input-focus-border: #86b7fe;
  --color-url-input-focus-shadow: rgba(13, 110, 253, 0.25);
  --color-content-bg: #ffffff;
  --color-scrollbar-thumb: #b0b0b0;
  --color-scrollbar-track: #f0f0f0;
  --color-panel-bg: #fdfdfd;
  --color-panel-border: #e0e0e0;
  --color-panel-text: #333;
  --color-panel-header-border: #e0e0e0;
  --color-button-bg: #f0f0f0;
  --color-button-text: #333;
  --color-button-border: #ccc;
  --color-button-hover-bg: #e0e0e0;
  --color-input-bg: #fff;
  --color-input-text: #333;
  --color-input-border: #ccc;
}

/* Стили для темной темы (пример) */
body.theme-dark {
  --color-background: #1e1e1e;
  --color-text: #e0e0e0;
  --color-tabs-container-bg: #252526;
  --color-tabs-container-border: #333333;
  --color-tab-bg: #2d2d2d;
  --color-tab-text: #cccccc;
  --color-tab-hover-bg: #3a3a3a;
  --color-tab-active-bg: #1e1e1e;
  --color-tab-active-text: #ffffff;
  --color-tab-active-border: #333333;
  --color-tab-close-icon: #aaa;
  --color-tab-close-hover-bg: rgba(255,255,255,0.1);
  --color-tab-close-hover-icon: #fff;
  --color-tab-active-close-icon: #ccc;
  --color-tab-active-close-hover-bg: rgba(255,255,255,0.15);
  --color-new-tab-icon: #aaa;
  --color-new-tab-hover-bg: #383838;
  --color-new-tab-hover-icon: #fff;
  --color-toolbar-bg: #333333;
  --color-toolbar-border: #444444;
  --color-toolbar-icon: #ccc;
  --color-toolbar-icon-hover: #fff;
  --color-toolbar-button-hover-bg: #444444;
  --color-url-input-bg: #2a2a2a;
  --color-url-input-text: #e0e0e0;
  --color-url-input-border: #444444;
  --color-url-input-focus-border: #007bff;
  --color-url-input-focus-shadow: rgba(0, 123, 255, 0.25);
  --color-content-bg: #1e1e1e;
  --color-scrollbar-thumb: #555;
  --color-scrollbar-track: #252526;
  --color-panel-bg: #252526;
  --color-panel-border: #383838;
  --color-panel-text: #e0e0e0;
  --color-panel-header-border: #383838;
  --color-button-bg: #383838;
  --color-button-text: #e0e0e0;
  --color-button-border: #555555;
  --color-button-hover-bg: #4a4a4a;
  --color-input-bg: #2a2a2a;
  --color-input-text: #e0e0e0;
  --color-input-border: #444444;
}

/* Панели настроек, расширений, закладок и истории */
.panel {
  position: absolute;
  top: 50px; /* Отступ от тулбара */
  right: 10px;
  width: 350px;
  max-height: calc(100vh - 70px); /* Чтобы не вылезала за экран */
  overflow-y: auto;
  background-color: var(--color-panel-bg, white);
  border: 1px solid var(--color-panel-border, #ddd);
  border-radius: 8px; /* Более скругленные углы */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  padding: 20px;
  z-index: 1000; /* Панели всегда сверху */
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
  animation: panel-appear 0.25s ease-out forwards;
  color: var(--color-panel-text, #333);
  transition: background-color var(--animation-speed-theme, 0.3s) ease, border-color var(--animation-speed-theme, 0.3s) ease, color var(--animation-speed-theme, 0.3s) ease;
}

@keyframes panel-appear {
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.panel.hidden {
  /* Используем анимацию для скрытия, если нужно */
  display: none !important; /* Важно для JS, который проверяет display */
}

/* Стили для панели закладок и истории */
#bookmarks-panel, #history-panel {
  width: 400px;
}

.bookmark-item, .history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid var(--color-panel-header-border, #eee);
  margin-bottom: 5px;
  border-radius: 4px;
  transition: background-color var(--animation-speed-interface, 0.15s) ease;
}

.bookmark-item:hover, .history-item:hover {
  background-color: var(--color-button-hover-bg, #f0f0f0);
}

.bookmark-info, .history-info {
  flex: 1;
  overflow: hidden;
  padding-right: 10px;
}

.bookmark-info h3, .history-info h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--color-panel-text, #333);
}

.bookmark-url, .history-url {
  color: var(--color-text-muted, #666);
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-time {
  color: var(--color-text-secondary, #999);
  font-size: 11px;
}

.bookmark-actions, .history-actions {
  display: flex;
  gap: 5px;
}

.bookmark-open, .bookmark-delete, .history-open {
  background-color: var(--color-button-bg, #f0f0f0);
  color: var(--color-button-text, #333);
  border: 1px solid var(--color-button-border, #ccc);
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color var(--animation-speed-interface, 0.2s) ease, border-color var(--animation-speed-interface, 0.2s) ease;
}

.bookmark-open:hover, .history-open:hover {
  background-color: var(--color-button-hover-bg, #e0e0e0);
  border-color: var(--color-button-border, #bbb);
}

.bookmark-delete {
  background-color: var(--color-danger-bg, #fceded);
  color: var(--color-danger-text, #c53030);
  border-color: var(--color-danger-border, #f5c6cb);
}

.bookmark-delete:hover {
  background-color: var(--color-danger-hover-bg, #f8d7da);
  border-color: var(--color-danger-hover-border, #f1b0b7);
}

.history-day-header {
  font-size: 14px;
  font-weight: 500;
  margin: 20px 0 10px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--color-panel-header-border, #eee);
  color: var(--color-text-secondary, #666);
}

.clear-history-button {
  background-color: var(--color-danger-bg, #f44336);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 15px;
  font-size: 13px;
  transition: background-color var(--animation-speed-interface, 0.2s) ease;
}

.clear-history-button:hover {
  background-color: var(--color-danger-hover-bg, #d32f2f);
}

.panel h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-panel-text, #333);
  padding-bottom: 10px;
  border-bottom: 1px solid var(--color-panel-header-border, #eee);
}

.settings-group {
  margin-bottom: 20px;
}

.settings-group label,
.home-page-setting label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 14px;
  color: var(--color-panel-text, #333);
}

.settings-group input[type="text"],
.settings-group select,
.home-page-setting input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--color-input-border, #ccc);
  border-radius: 4px;
  font-size: 14px;
  background-color: var(--color-input-bg, #fff);
  color: var(--color-input-text, #333);
  transition: border-color var(--animation-speed-interface, 0.2s) ease, background-color var(--animation-speed-theme, 0.3s) ease, color var(--animation-speed-theme, 0.3s) ease;
}

.settings-group input[type="text"]:focus,
.settings-group select:focus,
.home-page-setting input:focus {
  border-color: var(--color-url-input-focus-border, #4285f4);
  box-shadow: 0 0 0 2px var(--color-url-input-focus-shadow, rgba(66, 133, 244, 0.15));
  outline: none;
}

.settings-group input[type="checkbox"] {
  margin-right: 8px;
  vertical-align: middle;
}

.settings-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.settings-buttons button,
.panel button.primary {
  flex: 1;
  padding: 10px;
  background-color: var(--color-primary-button-bg, #007bff);
  color: var(--color-primary-button-text, white);
  border: 1px solid var(--color-primary-button-border, #007bff);
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color var(--animation-speed-interface, 0.2s) ease, border-color var(--animation-speed-interface, 0.2s) ease;
}

.settings-buttons button:hover,
.panel button.primary:hover {
  background-color: var(--color-primary-button-hover-bg, #0056b3);
  border-color: var(--color-primary-button-hover-border, #0056b3);
}

.settings-buttons button.secondary,
.panel button.secondary {
  background-color: var(--color-button-bg, #f0f0f0);
  color: var(--color-button-text, #333);
  border: 1px solid var(--color-button-border, #ccc);
}

.settings-buttons button.secondary:hover,
.panel button.secondary:hover {
  background-color: var(--color-button-hover-bg, #e0e0e0);
}

/* Extensions specific styles */
.extensions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.extension-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-color);
}

.extension-info {
  flex: 1;
  padding-right: 10px;
}

.extension-item:last-child {
  border-bottom: none;
}

.extension-item img {
  width: var(--icon-size-md);
  height: var(--icon-size-md);
  margin-right: var(--spacing-sm);
}

.extension-item span {
  flex-grow: 1;
  color: var(--text-color);
}

.extension-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.extension-toggle input[type="checkbox"] {
  /* Стилизуем чекбокс как переключатель */
  appearance: none;
  width: 36px;
  height: 20px;
  background-color: var(--color-button-border, #ccc);
  border-radius: 10px;
  position: relative;
  cursor: pointer;
  transition: background-color var(--animation-speed-interface, 0.2s) ease;
}

.extension-toggle input[type="checkbox"]::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  top: 2px;
  left: 2px;
  transition: transform var(--animation-speed-interface, 0.2s) ease;
}

.extension-toggle input[type="checkbox"]:checked {
  background-color: var(--color-primary-button-bg, #007bff);
}

.extension-toggle input[type="checkbox"]:checked::before {
  transform: translateX(16px);
}

.extension-toggle span {
  font-size: 14px;
  margin-left: 8px;
  color: var(--color-panel-text, #333);
}

/* Стили для кнопки закладок в тулбаре */
#bookmark-button {
  color: var(--color-toolbar-icon, #666);
  transition: color var(--animation-speed-interface, 0.2s) ease, transform var(--animation-speed-interface, 0.2s) ease;
  position: absolute; /* Позиционируем внутри address-bar */
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  padding: 6px;
}

#bookmark-button.active {
  color: var(--color-bookmark-active, #ffcc00);
  transform: translateY(-50%) scale(1.2);
}

#bookmark-button:hover {
  color: var(--color-bookmark-hover, #ffdd44);
}

/* Incognito mode indicator */
.incognito-indicator {
  background-color: var(--incognito-bg);
  color: var(--incognito-text);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  margin-left: var(--spacing-sm);
}

/* Light Theme */
.light-theme {
  --bg-color: #ffffff;
  --text-color: #333333;
  --primary-color: #007bff;
  --secondary-color: #6c757d;
  --border-color: #e0e0e0;
  --input-bg: #f8f9fa;
  --input-border: #ced4da;
  --input-text: #333333;
  --placeholder-color: #6c757d;
  --toolbar-bg: #f8f9fa;
  --address-bar-bg: #e9ecef;
  --button-bg: #e9ecef;
  --button-text: #333333;
  --button-border: #ced4da;
  --button-hover-bg: #dee2e6;
  --button-hover-text: #212529;
  --button-primary-bg: #007bff;
  --button-primary-text: #ffffff;
  --button-primary-hover-bg: #0056b3;
  --button-secondary-bg: #6c757d;
  --button-secondary-text: #ffffff;
  --button-secondary-hover-bg: #5a6268;
  --panel-bg: #ffffff;
  --panel-header-bg: #f8f9fa;
  --panel-header-text: #333333;
  --tab-bg: #e9ecef;
  --tab-text: #495057;
  --tab-hover-bg: #dee2e6;
  --tab-active-bg: #ffffff;
  --tab-active-text: #333333;
  --tab-active-border: #e0e0e0;
  --tab-close-icon: #6c757d;
  --tab-close-hover-bg: #e0e0e0;
  --tab-close-hover-icon: #333333;
  --tab-active-close-icon: #495057;
  --tab-active-close-hover-bg: #f0f0f0;
  --new-tab-icon: #6c757d;
  --new-tab-hover-bg: #e0e0e0;
  --new-tab-hover-icon: #333333;
  --context-menu-bg: #ffffff;
  --context-menu-hover-bg: #e9ecef;
  --modal-bg: #ffffff;
  --overlay-bg: rgba(0, 0, 0, 0.5);
  --heading-color: #212529;
  --incognito-bg: #6f42c1;
  --incognito-text: #ffffff;
  --loading-spinner-color: #007bff;
  --webview-bg: #ffffff;
  --scrollbar-track-bg: #f8f9fa;
  --scrollbar-thumb-bg: #ced4da;
  --scrollbar-thumb-hover-bg: #a7b0b8;
}

/* Dark Theme */
.dark-theme {
  --bg-color: #282c34;
  --text-color: #e0e0e0;
  --primary-color: #61afef;
  --secondary-color: #abb2bf;
  --border-color: #3e4451;
  --input-bg: #3e4451;
  --input-border: #4b5263;
  --input-text: #e0e0e0;
  --placeholder-color: #abb2bf;
  --toolbar-bg: #21252b;
  --address-bar-bg: #3e4451;
  --button-bg: #3e4451;
  --button-text: #e0e0e0;
  --button-border: #4b5263;
  --button-hover-bg: #4b5263;
  --button-hover-text: #ffffff;
  --button-primary-bg: #61afef;
  --button-primary-text: #ffffff;
  --button-primary-hover-bg: #529ac2;
  --button-secondary-bg: #abb2bf;
  --button-secondary-text: #282c34;
  --button-secondary-hover-bg: #98a1aa;
  --panel-bg: #21252b;
  --panel-header-bg: #282c34;
  --panel-header-text: #e0e0e0;
  --tab-bg: #3e4451;
  --tab-text: #abb2bf;
  --tab-hover-bg: #4b5263;
  --tab-active-bg: #282c34;
  --tab-active-text: #e0e0e0;
  --tab-active-border: #3e4451;
  --tab-close-icon: #abb2bf;
  --tab-close-hover-bg: #4b5263;
  --tab-close-hover-icon: #e0e0e0;
  --tab-active-close-icon: #abb2bf;
  --tab-active-close-hover-bg: #3e4451;
  --new-tab-icon: #abb2bf;
  --new-tab-hover-bg: #3e4451;
  --new-tab-hover-icon: #e0e0e0;
  --context-menu-bg: #21252b;
  --context-menu-hover-bg: #3e4451;
  --modal-bg: #21252b;
  --overlay-bg: rgba(0, 0, 0, 0.7);
  --heading-color: #e0e0e0;
  --incognito-bg: #563d7c;
  --incognito-text: #ffffff;
  --loading-spinner-color: #61afef;
  --webview-bg: #282c34;
  --scrollbar-track-bg: #21252b;
  --scrollbar-thumb-bg: #4b5263;
  --scrollbar-thumb-hover-bg: #61afef;
}

/* Loading Spinner */
.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--loading-spinner-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    margin-right: var(--spacing-xs);
    display: none; /* Hidden by default, shown by JS */
}

.tab.loading .loading-spinner {
    display: inline-block;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Webview Enhancements */
.browser-content webview {
    background-color: var(--webview-bg); /* Background for webview while loading */
}

body.theme-dark .browser-content webview {
    --webview-bg: var(--bg-color); /* In dark theme, webview background = body background */
}

.webview-container {
  position: relative;
  flex-grow: 1;
  overflow: hidden;
  background-color: var(--webview-bg);
  transition: background-color 0.3s ease;
}

.webview {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--webview-bg);
}

/* Panels (Settings, Extensions, Bookmarks, History) */
.panel {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 350px;
  background-color: var(--panel-bg);
  border-left: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out, background-color 0.3s ease, border-color 0.3s ease;
}

.panel.open {
  transform: translateX(0);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--panel-header-bg);
  flex-shrink: 0;
  color: var(--panel-header-text);
}

.panel-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--panel-header-text);
}

.panel-close-button {
  background: var(--button-bg);
  border: none;
  color: var(--button-text);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.panel-close-button:hover {
  background-color: var(--button-hover-bg);
  color: var(--button-hover-text);
}

.panel-content {
  flex-grow: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

/* Settings specific styles */
.settings-group {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.settings-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.settings-group h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  color: var(--heading-color);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.setting-item label {
  color: var(--text-color);
}

.setting-item input[type="checkbox"] {
  margin-left: var(--spacing-sm);
}

/* Custom Scrollbar for Webkit */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-bg);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-bg);
}

::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: var(--border-radius-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}