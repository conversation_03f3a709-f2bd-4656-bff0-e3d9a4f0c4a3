/**
 * Менеджер пользовательского интерфейса A11 Browser
 * Обеспечивает современный, адаптивный интерфейс с поддержкой жестов и тем
 */

// Forward declaration for GestureManager if it's defined later or in another file
// class GestureManager {}

class UIManager {
  constructor() {
    // Инициализация жестов
    // this.gestureManager = new GestureManager(); // Assuming GestureManager is defined elsewhere or will be integrated

    // Инициализация анимаций
    this.animations = {
      pageTransition: 'fade', // fade, slide, none
      animationSpeed: 'normal', // slow, normal, fast
      enableAnimations: true
    };

    // Загрузка сохраненных настроек анимаций
    this._loadAnimationSettings();

    // Слушаем изменения темы от theme-manager
    window.addEventListener('themechanged', this._handleThemeChange.bind(this));
    // Запрашиваем и применяем текущую тему при инициализации
    this.initializeCurrentTheme();

    // Инициализация обработчиков событий для панели настроек
    this._initializeSettingsPanelHandlers();
  }

  /**
   * Загружает сохраненные настройки анимаций
   * @private
   */
  _loadAnimationSettings() {
    try {
      const savedAnimations = JSON.parse(localStorage.getItem('a11_animations') || '{}');
      if (savedAnimations) {
        this.animations = {
          ...this.animations,
          ...savedAnimations
        };
      }
    } catch (error) {
      console.error('Ошибка при загрузке настроек анимаций:', error);
    }
  }

  /**
   * Сохраняет настройки анимаций
   * @private
   */
  _saveAnimationSettings() {
    try {
      localStorage.setItem('a11_animations', JSON.stringify(this.animations));
    } catch (error) {
      console.error('Ошибка при сохранении настроек анимаций:', error);
    }
  }

  /**
   * Инициализирует и применяет текущую тему при запуске.
   */
  async initializeCurrentTheme() {
    if (window.electronAPI && typeof window.electronAPI.getCurrentTheme === 'function') {
      try {
        const currentThemeData = await window.electronAPI.getCurrentTheme();
        if (currentThemeData) {
          this._applyThemeVariables(currentThemeData.name, currentThemeData.details.colors, currentThemeData.details.fonts);
        }
      } catch (error) {
        console.error('UIManager: Ошибка при получении текущей темы:', error);
      }
    }
  }

  /**
   * Обрабатывает событие изменения темы.
   * @param {CustomEvent} event - Событие с деталями темы.
   * @private
   */
  _handleThemeChange(event) {
    const { themeName, themeDetails } = event.detail;
    if (themeName && themeDetails && themeDetails.colors && themeDetails.fonts) {
      this._applyThemeVariables(themeName, themeDetails.colors, themeDetails.fonts);
    } else {
      console.warn('UIManager: Получены неполные данные о теме в событии themechanged', event.detail);
    }
  }

  /**
   * Применяет CSS-переменные для указанной темы.
   * @param {string} themeName - Название темы.
   * @param {object} colors - Объект с цветами темы.
   * @param {object} fonts - Объект со шрифтами темы.
   * @private
   */
  _applyThemeVariables(themeName, colors, fonts) {
    try {
      const root = document.documentElement;

      // Устанавливаем цвета
      if (colors) {
        Object.entries(colors).forEach(([key, value]) => {
          root.style.setProperty(`--color-${key}`, value);
        });
      }

      // Устанавливаем шрифты
      if (fonts) {
        Object.entries(fonts).forEach(([key, value]) => {
          root.style.setProperty(`--font-${key}`, value);
        });
      }

      // Добавляем класс темы к body
      document.body.className = document.body.className
        .replace(/theme-\w+/g, '')
        .trim();
      document.body.classList.add(`theme-${themeName.toLowerCase().replace(/\s+/g, '-')}`);

      console.log(`UIManager: Тема "${themeName}" применена.`);
    } catch (error) {
      console.error(`UIManager: Ошибка при применении CSS-переменных для темы "${themeName}":`, error);
    }
  }

  /**
   * Устанавливает тему интерфейса через theme-manager.
   * @param {string} themeName - Название темы
   * @returns {Promise<boolean>} - Успешность операции
   */
  async setTheme(themeName) {
    if (!window.electronAPI || typeof window.electronAPI.applyTheme !== 'function') {
      console.error('UIManager: electronAPI.applyTheme не доступен.');
      return false;
    }
    try {
      await window.electronAPI.applyTheme(themeName);
      // После успешного вызова applyTheme, событие 'themechanged' должно быть вызвано
      // из main процесса (через preload), и _handleThemeChange обновит CSS переменные.
      // Нет необходимости дублировать логику применения переменных здесь.
      return true;
    } catch (error) {
      console.error(`UIManager: Ошибка при установке темы "${themeName}" через electronAPI:`, error);
      return false;
    }
  }

  /**
   * Создает пользовательскую тему
   * @param {Object} themeConfig - Конфигурация темы
   * @returns {boolean} - Успешность операции
   */
  createCustomTheme(themeConfig) {
    try {
      // Проверяем наличие необходимых параметров
      if (!themeConfig || !themeConfig.colors) {
        throw new Error('Некорректная конфигурация темы');
      }
      
      // Обновляем пользовательскую тему
      this.availableThemes.custom = {
        name: themeConfig.name || 'Пользовательская',
        colors: {
          ...this.availableThemes.light.colors, // Базовые цвета по умолчанию
          ...themeConfig.colors
        },
        fonts: {
          ...this.availableThemes.light.fonts, // Базовые шрифты по умолчанию
          ...themeConfig.fonts
        }
      };
      
      // Применяем пользовательскую тему
      this.setTheme('custom');
      
      return true;
    } catch (error) {
      console.error('Ошибка при создании пользовательской темы:', error);
      return false;
    }
  }

  /**
   * Инициализирует обработчики событий для панели настроек.
   * @private
   */
  _initializeSettingsPanelHandlers() {
    const settingsButton = document.getElementById('settings-button');
    const closeSettingsButton = document.getElementById('close-settings-button');
    const settingsPanel = document.getElementById('settings-panel');

    if (settingsButton) {
      settingsButton.addEventListener('click', () => this.openSettings());
    }

    if (closeSettingsButton) {
      closeSettingsButton.addEventListener('click', () => this.closeSettings());
    }

    // Закрытие панели настроек при клике вне ее
    if (settingsPanel) {
      document.addEventListener('click', (event) => {
        if (!settingsPanel.contains(event.target) && !settingsButton.contains(event.target) && settingsPanel.classList.contains('open')) {
          this.closeSettings();
        }
      });
    }
  }

  /**
   * Открывает панель настроек.
   */
  openSettings() {
    const settingsPanel = document.getElementById('settings-panel');
    if (settingsPanel) {
      settingsPanel.classList.add('open');
      document.body.classList.add('settings-open'); // Добавляем класс для затемнения фона
    }
  }

  /**
   * Закрывает панель настроек.
   */
  closeSettings() {
    const settingsPanel = document.getElementById('settings-panel');
    if (settingsPanel) {
      settingsPanel.classList.remove('open');
      document.body.classList.remove('settings-open'); // Удаляем класс затемнения фона
    }
  }

  /**
   * Устанавливает настройки анимаций
   * @param {Object} animationConfig - Конфигурация анимаций
   * @returns {boolean} - Успешность операции
   */
  setAnimations(animationConfig) {
    try {
      this.animations = {
        ...this.animations,
        ...animationConfig
      };
      
      // Применяем настройки анимаций
      const root = document.documentElement;
      const body = document.body;
      
      // Устанавливаем скорость анимаций через CSS переменную
      const speedMap = {
        slow: 'var(--animation-speed-slow)',
        normal: 'var(--animation-speed-normal)',
        fast: 'var(--animation-speed-fast)'
      };
      root.style.setProperty('--animation-speed', speedMap[this.animations.animationSpeed] || 'var(--animation-speed-normal)');

      // Управляем классами анимаций на body
      body.classList.remove('fade-transition', 'slide-transition', 'no-animations');

      if (!this.animations.enableAnimations) {
        body.classList.add('no-animations');
      } else {
        switch (this.animations.pageTransition) {
          case 'fade':
            body.classList.add('fade-transition');
            break;
          case 'slide':
            body.classList.add('slide-transition');
            break;
          // 'none' case or default doesn't need a specific class if 'no-animations' handles it
        }
      }
      
      this._saveAnimationSettings();
      console.log('UIManager: Настройки анимаций применены:', this.animations);
      return true;
    } catch (error) {
      console.error('Ошибка при установке настроек анимаций:', error);
      return false;
    }
  }

  /**
   * Получает текущие настройки анимаций
   * @returns {Object} - Текущие настройки анимаций
   */
  getAnimationSettings() {
    return { ...this.animations };
  }

  /**
   * Применяет анимацию к элементу
   * @param {HTMLElement} element - Элемент для анимации
   * @param {string} animationName - Название класса анимации
   * @param {Function} [callback] - Функция обратного вызова после завершения анимации
   */
  animateElement(element, animationName, callback) {
    if (!this.animations.enableAnimations || !element) return;

    const handleAnimationEnd = () => {
      element.classList.remove(animationName, 'animating');
      element.removeEventListener('animationend', handleAnimationEnd);
      if (callback) callback();
    };

    element.classList.add(animationName, 'animating');
    element.addEventListener('animationend', handleAnimationEnd);
  }

  /**
   * Анимирует смену активного webview (контента вкладки)
   * @param {HTMLElement} oldWebview - Покидающий webview
   * @param {HTMLElement} newWebview - Входящий webview
   */
  animatePageTransition(oldWebview, newWebview) {
    if (!this.animations.enableAnimations || this.animations.pageTransition === 'none') {
      if (oldWebview) oldWebview.classList.remove('active');
      if (newWebview) newWebview.classList.add('active');
      return;
    }

    const transitionType = this.animations.pageTransition; // 'fade' или 'slide'
    const container = document.querySelector('.browser-content'); // Или другой контейнер webview

    if (!container) {
        console.warn('UIManager: Контейнер для webview не найден.');
        if (oldWebview) oldWebview.classList.remove('active');
        if (newWebview) newWebview.classList.add('active');
        return;
    }

    // Устанавливаем классы для типа перехода на контейнер
    container.classList.remove('fade-transition', 'slide-transition');
    if (transitionType === 'fade') {
        container.classList.add('fade-transition');
    } else if (transitionType === 'slide') {
        container.classList.add('slide-transition');
    }

    if (oldWebview) {
      oldWebview.classList.add('transition-out');
      // Webview сам станет display: none или opacity: 0 через CSS
      // Убираем 'active' после завершения анимации
      const onOldOut = () => {
        oldWebview.classList.remove('active', 'transition-out');
        oldWebview.removeEventListener('transitionend', onOldOut);
        oldWebview.removeEventListener('animationend', onOldOut);
      };
      oldWebview.addEventListener('transitionend', onOldOut);
      oldWebview.addEventListener('animationend', onOldOut);
    }

    if (newWebview) {
      // Сначала делаем webview видимым (но прозрачным/смещенным для анимации)
      newWebview.classList.add('next-active'); // Для slide, чтобы задать начальное положение
      newWebview.style.display = ''; // Убедимся, что он не display:none
      
      // Форсируем reflow, чтобы анимация применилась
      // eslint-disable-next-line no-unused-expressions
      newWebview.offsetHeight;

      newWebview.classList.add('active', 'transition-in');
      newWebview.classList.remove('next-active');

      const onNewIn = () => {
        newWebview.classList.remove('transition-in');
        newWebview.removeEventListener('transitionend', onNewIn);
        newWebview.removeEventListener('animationend', onNewIn);
      };
      newWebview.addEventListener('transitionend', onNewIn);
      newWebview.addEventListener('animationend', onNewIn);
    }
  }

  /**
   * Анимирует закрытие вкладки
   * @param {HTMLElement} tabElement - Элемент вкладки для анимации
   * @param {Function} callback - Функция обратного вызова после завершения анимации
   */
  animateTabClose(tabElement, callback) {
    if (!this.animations.enableAnimations || !tabElement) {
      if (callback) callback();
      return;
    }
    tabElement.classList.add('closing');
    
    const handleAnimationEnd = () => {
      tabElement.removeEventListener('animationend', handleAnimationEnd);
      if (callback) callback();
    };
    tabElement.addEventListener('animationend', handleAnimationEnd);
  }

  /**
   * Анимирует появление новой вкладки
   * @param {HTMLElement} tabElement - Элемент вкладки для анимации
   */
  animateTabOpen(tabElement) {
    if (!this.animations.enableAnimations || !tabElement) return;
    // Класс 'tab-appear' уже должен быть в CSS и применяться автоматически при добавлении
    // Если нужно принудительно запустить анимацию (например, если элемент уже в DOM, но был скрыт)
    // можно добавить и убрать класс, но обычно это не требуется для новых элементов.
    // tabElement.classList.add('tab-appear'); // Анимация 'tab-appear' в CSS должна сработать
  }

  /**
   * Показывает или скрывает панель с анимацией.
   * @param {string} panelId - ID панели (например, 'settings-panel').
   * @param {boolean} show - True для показа, false для скрытия.
   */
  togglePanel(panelId, show) {
    const panel = document.getElementById(panelId);
    if (!panel) {
      console.warn(`UIManager: Панель с ID '${panelId}' не найдена.`);
      return;
    }

    if (show) {
      // Закрываем другие открытые панели перед открытием новой
      document.querySelectorAll('.panel').forEach(p => {
        if (p.id !== panelId && !p.classList.contains('hidden')) {
          p.classList.add('hidden'); // Скрываем без анимации, если нужно быстро
          // или используем this.animateElement(p, 'panel-disappear', () => p.classList.add('hidden'));
        }
      });
      panel.classList.remove('hidden'); // Анимация 'panel-appear' сработает из CSS
    } else {
      // Для скрытия можно добавить анимацию 'panel-disappear'
      // this.animateElement(panel, 'panel-disappear', () => panel.classList.add('hidden'));
      // Пока просто скрываем
      panel.classList.add('hidden');
    }
  }

  /**
   * Получает список доступных тем
   * @returns {Array} - Список тем
   */
  getAvailableThemes() {
    return Object.entries(this.availableThemes).map(([id, theme]) => ({
      id,
      name: theme.name
    }));
  }

  /**
   * Получает текущую тему
   * @returns {Object} - Текущая тема
   */
  getCurrentTheme() {
    return {
      id: this.currentTheme,
      ...this.availableThemes[this.currentTheme]
    };
  }

  /**
   * Применяет эффект перехода между страницами
   * @param {HTMLElement} container - Контейнер для анимации
   * @param {Function} callback - Функция, вызываемая после завершения анимации
   */
  applyPageTransition(container, callback) {
    if (!this.animations.enableAnimations || this.animations.pageTransition === 'none') {
      callback();
      return;
    }
    
    // Удаляем предыдущие классы анимаций
    container.classList.remove('fade-transition', 'slide-transition');
    
    // Добавляем класс для текущей анимации
    container.classList.add(`${this.animations.pageTransition}-transition`);
    
    // Запускаем анимацию выхода
    container.classList.add('transition-out');
    
    // Ждем завершения анимации
    setTimeout(() => {
      // Вызываем коллбэк для обновления содержимого
      callback();
      
      // Запускаем анимацию входа
      container.classList.remove('transition-out');
      container.classList.add('transition-in');
      
      // Удаляем класс анимации входа после завершения
      setTimeout(() => {
        container.classList.remove('transition-in');
      }, parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--animation-speed')) * 1000);
    }, parseFloat(getComputedStyle(document.documentElement).getPropertyValue('--animation-speed')) * 1000);
  }
}

/**
 * Менеджер жестов для навигации и управления интерфейсом
 */
class GestureManager {
  constructor() {
    this.enabled = true;
    this.touchStartX = 0;
    this.touchStartY = 0;
    this.touchEndX = 0;
    this.touchEndY = 0;
    this.minSwipeDistance = 50; // Минимальное расстояние для свайпа в пикселях
    this.maxSwipeTime = 300; // Максимальное время для свайпа в миллисекундах
    this.touchStartTime = 0;
    this.touchEndTime = 0;
    
    // Инициализация обработчиков жестов
    this._initGestureHandlers();
  }

  /**
   * Инициализирует обработчики жестов
   * @private
   */
  _initGestureHandlers() {
    if (typeof document === 'undefined') return;
    
    // Обработчик начала касания
    document.addEventListener('touchstart', (event) => {
      if (!this.enabled) return;
      
      this.touchStartX = event.changedTouches[0].screenX;
      this.touchStartY = event.changedTouches[0].screenY;
      this.touchStartTime = new Date().getTime();
    }, { passive: true });
    
    // Обработчик окончания касания
    document.addEventListener('touchend', (event) => {
      if (!this.enabled) return;
      
      this.touchEndX = event.changedTouches[0].screenX;
      this.touchEndY = event.changedTouches[0].screenY;
      this.touchEndTime = new Date().getTime();
      
      // Проверяем, был ли это свайп
      this._handleSwipe();
    }, { passive: true });
  }

  /**
   * Обрабатывает свайп
   * @private
   */
  _handleSwipe() {
    // Проверяем время свайпа
    const swipeTime = this.touchEndTime - this.touchStartTime;
    if (swipeTime > this.maxSwipeTime) return;
    
    // Вычисляем расстояние свайпа
    const swipeDistanceX = Math.abs(this.touchEndX - this.touchStartX);
    const swipeDistanceY = Math.abs(this.touchEndY - this.touchStartY);
    
    // Определяем направление свайпа
    if (swipeDistanceX > this.minSwipeDistance && swipeDistanceX > swipeDistanceY) {
      // Горизонтальный свайп
      if (this.touchEndX < this.touchStartX) {
        // Свайп влево
        this._triggerSwipeEvent('left');
      } else {
        // Свайп вправо
        this._triggerSwipeEvent('right');
      }
    } else if (swipeDistanceY > this.minSwipeDistance && swipeDistanceY > swipeDistanceX) {
      // Вертикальный свайп
      if (this.touchEndY < this.touchStartY) {
        // Свайп вверх
        this._triggerSwipeEvent('up');
      } else {
        // Свайп вниз
        this._triggerSwipeEvent('down');
      }
    }
  }

  /**
   * Генерирует событие свайпа
   * @param {string} direction - Направление свайпа
   * @private
   */
  _triggerSwipeEvent(direction) {
    const swipeEvent = new CustomEvent('swipe', {
      detail: {
        direction,
        distance: {
          x: Math.abs(this.touchEndX - this.touchStartX),
          y: Math.abs(this.touchEndY - this.touchStartY)
        },
        time: this.touchEndTime - this.touchStartTime
      }
    });
    
    document.dispatchEvent(swipeEvent);
    
    // Также генерируем специфическое событие для направления
    const directionEvent = new CustomEvent(`swipe${direction}`, {
      detail: {
        distance: {
          x: Math.abs(this.touchEndX - this.touchStartX),
          y: Math.abs(this.touchEndY - this.touchStartY)
        },
        time: this.touchEndTime - this.touchStartTime
      }
    });
    
    document.dispatchEvent(directionEvent);
  }

  /**
   * Включает или отключает обработку жестов
   * @param {boolean} enabled - Статус включения
   */
  setEnabled(enabled) {
    this.enabled = enabled;
  }

  /**
   * Устанавливает минимальное расстояние для свайпа
   * @param {number} distance - Расстояние в пикселях
   */
  setMinSwipeDistance(distance) {
    if (typeof distance === 'number' && distance > 0) {
      this.minSwipeDistance = distance;
    }
  }

  /**
   * Устанавливает максимальное время для свайпа
   * @param {number} time - Время в миллисекундах
   */
  setMaxSwipeTime(time) {
    if (typeof time === 'number' && time > 0) {
      this.maxSwipeTime = time;
    }
  }
}

// Экспортируем классы для использования в других модулях
module.exports = { UIManager, GestureManager };