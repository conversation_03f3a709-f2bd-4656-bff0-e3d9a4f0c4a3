import { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { Hotkey } from '../hotkeys/types';
import { enableHotkey, disableHotkey } from '../../store/slices/hotkeySlice';

export const useHotkeys = () => {
  const dispatch = useDispatch();
  const { hotkeys, enabledHotkeys } = useSelector(
    (state: RootState) => state.hotkeys as { hotkeys: Hotkey[]; enabledHotkeys: string[] }
  );

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      const pressedKey = event.key.toLowerCase();
      const isCtrlPressed = event.ctrlKey;
      const isShiftPressed = event.shiftKey;
      const isAltPressed = event.altKey;
      const isMetaPressed = event.metaKey;

      const keyCombination = [
        isCtrlPressed ? 'ctrl' : '',
        isShiftPressed ? 'shift' : '',
        isAltPressed ? 'alt' : '',
        isMetaPressed ? 'meta' : '',
        pressedKey,
      ]
        .filter(Boolean)
        .join('+')
        .toLowerCase();

      const matchingHotkey = hotkeys.find(
        (hotkey) =>
          hotkey.enabled &&
          enabledHotkeys.includes(hotkey.id) &&
          hotkey.key.toLowerCase() === keyCombination
      );

      if (matchingHotkey) {
        event.preventDefault();
        matchingHotkey.action();
      }
    },
    [hotkeys, enabledHotkeys]
  );

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  const registerHotkey = useCallback(
    (hotkey: Hotkey) => {
      dispatch(enableHotkey(hotkey.id));
    },
    [dispatch]
  );

  const unregisterHotkey = useCallback(
    (id: string) => {
      dispatch(disableHotkey(id));
    },
    [dispatch]
  );

  const isHotkeyEnabled = useCallback(
    (id: string) => {
      return enabledHotkeys.includes(id);
    },
    [enabledHotkeys]
  );

  return {
    hotkeys,
    enabledHotkeys,
    registerHotkey,
    unregisterHotkey,
    isHotkeyEnabled,
  };
}; 