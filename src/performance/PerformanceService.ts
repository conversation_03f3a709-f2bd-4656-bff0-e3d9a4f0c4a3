import { PerformanceMetrics } from '../types';
import { logger } from '../logging/Logger';

export class PerformanceService {
  private static instance: PerformanceService;
  private metrics: PerformanceMetrics[] = [];
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private config: {
    cache: {
      enabled: boolean;
      ttl: number;
      maxSize: number;
    };
    compression: {
      enabled: boolean;
      level: number;
    };
  };

  private constructor(config: {
    cache: {
      enabled: boolean;
      ttl: number;
      maxSize: number;
    };
    compression: {
      enabled: boolean;
      level: number;
    };
  }) {
    this.config = config;
    this.initialize();
  }

  public static getInstance(config: {
    cache: {
      enabled: boolean;
      ttl: number;
      maxSize: number;
    };
    compression: {
      enabled: boolean;
      level: number;
    };
  }): PerformanceService {
    if (!PerformanceService.instance) {
      PerformanceService.instance = new PerformanceService(config);
    }
    return PerformanceService.instance;
  }

  private initialize(): void {
    if (this.config.cache.enabled) {
      this.startCacheCleanup();
    }
  }

  private startCacheCleanup(): void {
    setInterval(() => {
      this.cleanupCache();
    }, this.config.cache.ttl * 1000);
  }

  public setCache(key: string, data: any): void {
    if (!this.config.cache.enabled) {
      return;
    }

    try {
      if (this.cache.size >= this.config.cache.maxSize) {
        this.cleanupCache();
      }

      this.cache.set(key, {
        data,
        timestamp: Date.now(),
      });
    } catch (error) {
      logger.error('Cache set failed:', error);
    }
  }

  public getCache(key: string): any {
    if (!this.config.cache.enabled) {
      return null;
    }

    try {
      const cached = this.cache.get(key);
      if (!cached) {
        return null;
      }

      if (Date.now() - cached.timestamp > this.config.cache.ttl * 1000) {
        this.cache.delete(key);
        return null;
      }

      return cached.data;
    } catch (error) {
      logger.error('Cache get failed:', error);
      return null;
    }
  }

  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.config.cache.ttl * 1000) {
        this.cache.delete(key);
      }
    }
  }

  public compress(data: string): string {
    if (!this.config.compression.enabled) {
      return data;
    }

    try {
      // Implement compression logic here
      return data;
    } catch (error) {
      logger.error('Compression failed:', error);
      return data;
    }
  }

  public decompress(data: string): string {
    if (!this.config.compression.enabled) {
      return data;
    }

    try {
      // Implement decompression logic here
      return data;
    } catch (error) {
      logger.error('Decompression failed:', error);
      return data;
    }
  }

  public trackMetric(metric: PerformanceMetrics): void {
    try {
      this.metrics.push(metric);
      this.cleanupOldMetrics();
    } catch (error) {
      logger.error('Metric tracking failed:', error);
    }
  }

  private cleanupOldMetrics(): void {
    const oneHourAgo = new Date(Date.now() - 3600000);
    this.metrics = this.metrics.filter(metric => metric.timestamp > oneHourAgo);
  }

  public getMetrics(): PerformanceMetrics[] {
    return this.metrics;
  }

  public getLatestMetrics(): PerformanceMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }

  public clearMetrics(): void {
    this.metrics = [];
  }
}

export const performanceService = PerformanceService.getInstance({
  cache: {
    enabled: true,
    ttl: 3600,
    maxSize: 1000,
  },
  compression: {
    enabled: true,
    level: 6,
  },
}); 