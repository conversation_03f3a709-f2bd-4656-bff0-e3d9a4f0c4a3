import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';
import { configManager } from '../core/ConfigurationManager';
import { cacheManager } from '../core/CacheManager';

export interface PerformanceOptimization {
  id: string;
  type: 'bundle' | 'render' | 'memory' | 'network' | 'cache' | 'lazy_loading' | 'code_splitting';
  description: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  estimatedImprovement: {
    loadTime?: number; // milliseconds
    memoryReduction?: number; // bytes
    bundleSize?: number; // bytes
    renderTime?: number; // milliseconds
  };
  implementation: () => Promise<void>;
  rollback: () => Promise<void>;
  applied: boolean;
  timestamp: number;
}

export interface PerformanceBudget {
  loadTime: number; // milliseconds
  firstContentfulPaint: number; // milliseconds
  largestContentfulPaint: number; // milliseconds
  cumulativeLayoutShift: number; // score
  firstInputDelay: number; // milliseconds
  bundleSize: number; // bytes
  memoryUsage: number; // bytes
  networkRequests: number; // count
}

export interface PerformanceMetrics {
  loadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
  bundleSize: number;
  memoryUsage: number;
  networkRequests: number;
  renderTime: number;
  domNodes: number;
  jsHeapSize: number;
  timestamp: number;
}

export interface OptimizationReport {
  id: string;
  timestamp: number;
  currentMetrics: PerformanceMetrics;
  budget: PerformanceBudget;
  violations: Array<{
    metric: keyof PerformanceBudget;
    current: number;
    budget: number;
    severity: 'warning' | 'error';
  }>;
  recommendations: PerformanceOptimization[];
  score: number; // 0-100
}

export class PerformanceOptimizer extends EventEmitter {
  private static instance: PerformanceOptimizer;
  private optimizations: Map<string, PerformanceOptimization> = new Map();
  private budget: PerformanceBudget;
  private observer: PerformanceObserver | null = null;
  private metricsHistory: PerformanceMetrics[] = [];

  private constructor() {
    super();
    this.budget = {
      loadTime: 3000, // 3 seconds
      firstContentfulPaint: 1500, // 1.5 seconds
      largestContentfulPaint: 2500, // 2.5 seconds
      cumulativeLayoutShift: 0.1, // CLS score
      firstInputDelay: 100, // 100ms
      bundleSize: 1024 * 1024, // 1MB
      memoryUsage: 50 * 1024 * 1024, // 50MB
      networkRequests: 50, // 50 requests
    };

    this.initializeOptimizations();
    this.setupPerformanceObserver();
  }

  public static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  private initializeOptimizations(): void {
    // Bundle optimization
    this.addOptimization({
      id: 'bundle_splitting',
      type: 'bundle',
      description: 'Split large bundles into smaller chunks',
      impact: 'high',
      estimatedImprovement: {
        loadTime: 1000,
        bundleSize: 500 * 1024,
      },
      implementation: async () => {
        await this.implementBundleSplitting();
      },
      rollback: async () => {
        await this.rollbackBundleSplitting();
      },
      applied: false,
      timestamp: Date.now(),
    });

    // Lazy loading optimization
    this.addOptimization({
      id: 'lazy_loading_images',
      type: 'lazy_loading',
      description: 'Implement lazy loading for images',
      impact: 'medium',
      estimatedImprovement: {
        loadTime: 500,
        networkRequests: -10,
      },
      implementation: async () => {
        await this.implementLazyLoading();
      },
      rollback: async () => {
        await this.rollbackLazyLoading();
      },
      applied: false,
      timestamp: Date.now(),
    });

    // Memory optimization
    this.addOptimization({
      id: 'memory_cleanup',
      type: 'memory',
      description: 'Implement automatic memory cleanup',
      impact: 'medium',
      estimatedImprovement: {
        memoryReduction: 10 * 1024 * 1024,
      },
      implementation: async () => {
        await this.implementMemoryCleanup();
      },
      rollback: async () => {
        await this.rollbackMemoryCleanup();
      },
      applied: false,
      timestamp: Date.now(),
    });

    // Cache optimization
    this.addOptimization({
      id: 'aggressive_caching',
      type: 'cache',
      description: 'Implement aggressive caching strategy',
      impact: 'high',
      estimatedImprovement: {
        loadTime: 800,
        networkRequests: -20,
      },
      implementation: async () => {
        await this.implementAggressiveCaching();
      },
      rollback: async () => {
        await this.rollbackAggressiveCaching();
      },
      applied: false,
      timestamp: Date.now(),
    });

    // Render optimization
    this.addOptimization({
      id: 'virtual_scrolling',
      type: 'render',
      description: 'Implement virtual scrolling for large lists',
      impact: 'high',
      estimatedImprovement: {
        renderTime: 200,
        memoryReduction: 5 * 1024 * 1024,
      },
      implementation: async () => {
        await this.implementVirtualScrolling();
      },
      rollback: async () => {
        await this.rollbackVirtualScrolling();
      },
      applied: false,
      timestamp: Date.now(),
    });
  }

  private addOptimization(optimization: PerformanceOptimization): void {
    this.optimizations.set(optimization.id, optimization);
  }

  public async analyzePerformance(): Promise<OptimizationReport> {
    const currentMetrics = await this.collectMetrics();
    const violations = this.checkBudgetViolations(currentMetrics);
    const recommendations = this.generateRecommendations(currentMetrics, violations);
    const score = this.calculatePerformanceScore(currentMetrics);

    const report: OptimizationReport = {
      id: `report_${Date.now()}`,
      timestamp: Date.now(),
      currentMetrics,
      budget: this.budget,
      violations,
      recommendations,
      score,
    };

    this.emit('performance_analyzed', report);
    logger.info('Performance analysis completed', {
      score,
      violations: violations.length,
      recommendations: recommendations.length,
    });

    return report;
  }

  public async applyOptimization(optimizationId: string): Promise<void> {
    const optimization = this.optimizations.get(optimizationId);
    if (!optimization) {
      throw new Error(`Optimization ${optimizationId} not found`);
    }

    if (optimization.applied) {
      logger.warn('Optimization already applied', { optimizationId });
      return;
    }

    try {
      await optimization.implementation();
      optimization.applied = true;
      optimization.timestamp = Date.now();

      this.emit('optimization_applied', optimization);
      logger.info('Optimization applied successfully', {
        optimizationId,
        type: optimization.type,
        impact: optimization.impact,
      });
    } catch (error) {
      logger.error('Failed to apply optimization', error, { optimizationId });
      throw error;
    }
  }

  public async rollbackOptimization(optimizationId: string): Promise<void> {
    const optimization = this.optimizations.get(optimizationId);
    if (!optimization) {
      throw new Error(`Optimization ${optimizationId} not found`);
    }

    if (!optimization.applied) {
      logger.warn('Optimization not applied, cannot rollback', { optimizationId });
      return;
    }

    try {
      await optimization.rollback();
      optimization.applied = false;

      this.emit('optimization_rolled_back', optimization);
      logger.info('Optimization rolled back successfully', { optimizationId });
    } catch (error) {
      logger.error('Failed to rollback optimization', error, { optimizationId });
      throw error;
    }
  }

  public async applyAutoOptimizations(): Promise<void> {
    const report = await this.analyzePerformance();
    const criticalOptimizations = report.recommendations.filter(opt => opt.impact === 'critical');
    const highImpactOptimizations = report.recommendations.filter(opt => opt.impact === 'high');

    // Apply critical optimizations first
    for (const optimization of criticalOptimizations) {
      try {
        await this.applyOptimization(optimization.id);
      } catch (error) {
        logger.error('Failed to apply critical optimization', error, {
          optimizationId: optimization.id,
        });
      }
    }

    // Apply high impact optimizations if performance is still poor
    const updatedReport = await this.analyzePerformance();
    if (updatedReport.score < 70) {
      for (const optimization of highImpactOptimizations) {
        try {
          await this.applyOptimization(optimization.id);
        } catch (error) {
          logger.error('Failed to apply high impact optimization', error, {
            optimizationId: optimization.id,
          });
        }
      }
    }
  }

  private async collectMetrics(): Promise<PerformanceMetrics> {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    const memory = (performance as any).memory;

    const loadTime = navigation ? navigation.loadEventEnd - navigation.fetchStart : 0;
    const firstContentfulPaint = paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0;
    
    // Get LCP from observer or estimate
    const largestContentfulPaint = this.getLastLCP() || firstContentfulPaint + 1000;
    
    // Estimate CLS and FID (would be collected from real observers)
    const cumulativeLayoutShift = Math.random() * 0.2;
    const firstInputDelay = Math.random() * 200;

    const metrics: PerformanceMetrics = {
      loadTime,
      firstContentfulPaint,
      largestContentfulPaint,
      cumulativeLayoutShift,
      firstInputDelay,
      bundleSize: this.estimateBundleSize(),
      memoryUsage: memory ? memory.usedJSHeapSize : 0,
      networkRequests: performance.getEntriesByType('resource').length,
      renderTime: this.measureRenderTime(),
      domNodes: document.querySelectorAll('*').length,
      jsHeapSize: memory ? memory.totalJSHeapSize : 0,
      timestamp: Date.now(),
    };

    this.metricsHistory.push(metrics);
    if (this.metricsHistory.length > 100) {
      this.metricsHistory = this.metricsHistory.slice(-100);
    }

    return metrics;
  }

  private checkBudgetViolations(metrics: PerformanceMetrics): Array<{
    metric: keyof PerformanceBudget;
    current: number;
    budget: number;
    severity: 'warning' | 'error';
  }> {
    const violations = [];

    Object.entries(this.budget).forEach(([key, budgetValue]) => {
      const metricKey = key as keyof PerformanceBudget;
      const currentValue = metrics[metricKey] as number;

      if (currentValue > budgetValue) {
        const severity = currentValue > budgetValue * 1.5 ? 'error' : 'warning';
        violations.push({
          metric: metricKey,
          current: currentValue,
          budget: budgetValue,
          severity,
        });
      }
    });

    return violations;
  }

  private generateRecommendations(
    metrics: PerformanceMetrics,
    violations: Array<{ metric: keyof PerformanceBudget; severity: string }>
  ): PerformanceOptimization[] {
    const recommendations = [];

    // Recommend optimizations based on violations
    violations.forEach(violation => {
      switch (violation.metric) {
        case 'bundleSize':
          recommendations.push(this.optimizations.get('bundle_splitting'));
          break;
        case 'loadTime':
          recommendations.push(this.optimizations.get('aggressive_caching'));
          recommendations.push(this.optimizations.get('lazy_loading_images'));
          break;
        case 'memoryUsage':
          recommendations.push(this.optimizations.get('memory_cleanup'));
          recommendations.push(this.optimizations.get('virtual_scrolling'));
          break;
        case 'renderTime':
          recommendations.push(this.optimizations.get('virtual_scrolling'));
          break;
      }
    });

    // Filter out already applied optimizations and remove duplicates
    return Array.from(new Set(recommendations))
      .filter(opt => opt && !opt.applied) as PerformanceOptimization[];
  }

  private calculatePerformanceScore(metrics: PerformanceMetrics): number {
    let score = 100;

    // Deduct points for budget violations
    Object.entries(this.budget).forEach(([key, budgetValue]) => {
      const metricKey = key as keyof PerformanceBudget;
      const currentValue = metrics[metricKey] as number;

      if (currentValue > budgetValue) {
        const violation = (currentValue - budgetValue) / budgetValue;
        score -= Math.min(20, violation * 10); // Max 20 points per metric
      }
    });

    return Math.max(0, Math.round(score));
  }

  // Optimization implementations
  private async implementBundleSplitting(): Promise<void> {
    logger.info('Implementing bundle splitting optimization');
    // Implementation would involve webpack configuration changes
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private async rollbackBundleSplitting(): Promise<void> {
    logger.info('Rolling back bundle splitting optimization');
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private async implementLazyLoading(): Promise<void> {
    logger.info('Implementing lazy loading optimization');
    
    const images = document.querySelectorAll('img[src]');
    images.forEach(img => {
      if ('loading' in HTMLImageElement.prototype) {
        img.setAttribute('loading', 'lazy');
      } else {
        // Fallback for browsers without native lazy loading
        this.implementIntersectionObserverLazyLoading(img as HTMLImageElement);
      }
    });
  }

  private async rollbackLazyLoading(): Promise<void> {
    logger.info('Rolling back lazy loading optimization');
    
    const images = document.querySelectorAll('img[loading="lazy"]');
    images.forEach(img => {
      img.removeAttribute('loading');
    });
  }

  private async implementMemoryCleanup(): Promise<void> {
    logger.info('Implementing memory cleanup optimization');
    
    // Set up periodic memory cleanup
    setInterval(() => {
      this.performMemoryCleanup();
    }, 60000); // Every minute
  }

  private async rollbackMemoryCleanup(): Promise<void> {
    logger.info('Rolling back memory cleanup optimization');
    // Would clear the cleanup interval
  }

  private async implementAggressiveCaching(): Promise<void> {
    logger.info('Implementing aggressive caching optimization');
    
    // Configure cache manager for aggressive caching
    await cacheManager.updateConfig({
      defaultTTL: 24 * 60 * 60 * 1000, // 24 hours
      maxSize: 200 * 1024 * 1024, // 200MB
      enableCompression: true,
    });
  }

  private async rollbackAggressiveCaching(): Promise<void> {
    logger.info('Rolling back aggressive caching optimization');
    
    // Reset cache configuration
    await cacheManager.updateConfig({
      defaultTTL: 60 * 60 * 1000, // 1 hour
      maxSize: 100 * 1024 * 1024, // 100MB
      enableCompression: false,
    });
  }

  private async implementVirtualScrolling(): Promise<void> {
    logger.info('Implementing virtual scrolling optimization');
    
    // Find large lists and implement virtual scrolling
    const largeLists = document.querySelectorAll('ul, ol').forEach(list => {
      if (list.children.length > 100) {
        this.convertToVirtualScrolling(list);
      }
    });
  }

  private async rollbackVirtualScrolling(): Promise<void> {
    logger.info('Rolling back virtual scrolling optimization');
    // Would restore original list rendering
  }

  // Helper methods
  private setupPerformanceObserver(): void {
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          this.emit('performance_entry', entry);
        });
      });

      try {
        this.observer.observe({ entryTypes: ['largest-contentful-paint', 'layout-shift', 'first-input'] });
      } catch (error) {
        logger.warn('Failed to setup performance observer', { error });
      }
    }
  }

  private getLastLCP(): number {
    // Would get the last LCP value from the observer
    return 0;
  }

  private estimateBundleSize(): number {
    // Estimate bundle size based on script tags
    const scripts = document.querySelectorAll('script[src]');
    return scripts.length * 100 * 1024; // Rough estimate
  }

  private measureRenderTime(): number {
    const start = performance.now();
    // Force a reflow
    document.body.offsetHeight;
    return performance.now() - start;
  }

  private implementIntersectionObserverLazyLoading(img: HTMLImageElement): void {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const target = entry.target as HTMLImageElement;
          target.src = target.dataset.src || target.src;
          observer.unobserve(target);
        }
      });
    });

    observer.observe(img);
  }

  private performMemoryCleanup(): void {
    // Clear caches
    cacheManager.cleanup();
    
    // Force garbage collection if available
    if ((window as any).gc) {
      (window as any).gc();
    }
  }

  private convertToVirtualScrolling(list: Element): void {
    // Implementation would convert list to virtual scrolling
    logger.debug('Converting list to virtual scrolling', { listSize: list.children.length });
  }

  public setBudget(budget: Partial<PerformanceBudget>): void {
    this.budget = { ...this.budget, ...budget };
    logger.info('Performance budget updated', { budget: this.budget });
  }

  public getMetricsHistory(): PerformanceMetrics[] {
    return [...this.metricsHistory];
  }

  public getOptimizations(): PerformanceOptimization[] {
    return Array.from(this.optimizations.values());
  }
}

// Export singleton instance
export const performanceOptimizer = PerformanceOptimizer.getInstance();
