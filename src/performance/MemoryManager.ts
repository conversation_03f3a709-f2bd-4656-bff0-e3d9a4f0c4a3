import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';
import { configManager } from '../core/ConfigurationManager';

export interface MemoryConfig {
  maxHeapSize: number; // Maximum heap size in bytes
  gcThreshold: number; // GC threshold as percentage of max heap
  memoryWarningThreshold: number; // Warning threshold percentage
  memoryCriticalThreshold: number; // Critical threshold percentage
  enableAutoGC: boolean;
  enableMemoryProfiling: boolean;
  enableLeakDetection: boolean;
  leakDetectionInterval: number;
  retentionSamples: number;
}

export interface MemoryUsage {
  used: number;
  total: number;
  limit: number;
  percentage: number;
  timestamp: number;
}

export interface MemoryLeak {
  id: string;
  type: 'dom' | 'event' | 'closure' | 'cache' | 'timer' | 'other';
  description: string;
  size: number;
  growth: number;
  detected: number;
  source?: string;
  stackTrace?: string;
}

export interface MemoryProfile {
  id: string;
  timestamp: number;
  duration: number;
  heapUsed: number;
  heapTotal: number;
  external: number;
  arrayBuffers: number;
  objects: MemoryObjectProfile[];
  leaks: MemoryLeak[];
}

export interface MemoryObjectProfile {
  type: string;
  count: number;
  size: number;
  retainedSize: number;
}

export interface GCEvent {
  id: string;
  timestamp: number;
  type: 'minor' | 'major' | 'incremental';
  duration: number;
  freedMemory: number;
  heapBefore: number;
  heapAfter: number;
  reason: string;
}

export class MemoryManager extends EventEmitter {
  private static instance: MemoryManager;
  private config: MemoryConfig;
  private memoryHistory: MemoryUsage[] = [];
  private memoryProfiles: Map<string, MemoryProfile> = new Map();
  private detectedLeaks: Map<string, MemoryLeak> = new Map();
  private gcEvents: GCEvent[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private leakDetectionInterval: NodeJS.Timeout | null = null;
  private objectReferences: Map<string, WeakRef<any>> = new Map();
  private retainedObjects: Map<string, any> = new Map();

  private constructor() {
    super();
    this.config = {
      maxHeapSize: 1024 * 1024 * 1024, // 1GB
      gcThreshold: 80,
      memoryWarningThreshold: 70,
      memoryCriticalThreshold: 90,
      enableAutoGC: true,
      enableMemoryProfiling: true,
      enableLeakDetection: true,
      leakDetectionInterval: 30000, // 30 seconds
      retentionSamples: 1000,
    };

    this.initializeMemoryManager();
  }

  public static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  private async initializeMemoryManager(): Promise<void> {
    // Load configuration
    const memoryConfig = configManager.get('memory', {});
    this.config = { ...this.config, ...memoryConfig };

    // Start memory monitoring
    this.startMemoryMonitoring();

    // Start leak detection
    if (this.config.enableLeakDetection) {
      this.startLeakDetection();
    }

    // Setup GC monitoring
    this.setupGCMonitoring();

    logger.info('Memory manager initialized', {
      maxHeapSize: this.config.maxHeapSize,
      enableAutoGC: this.config.enableAutoGC,
      enableLeakDetection: this.config.enableLeakDetection,
    });
  }

  public getCurrentMemoryUsage(): MemoryUsage {
    const memoryInfo = this.getMemoryInfo();
    
    return {
      used: memoryInfo.usedJSHeapSize,
      total: memoryInfo.totalJSHeapSize,
      limit: memoryInfo.jsHeapSizeLimit,
      percentage: (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100,
      timestamp: Date.now(),
    };
  }

  private getMemoryInfo(): {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  } {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory;
    }

    // Fallback for environments without performance.memory
    return {
      usedJSHeapSize: 50 * 1024 * 1024, // 50MB estimate
      totalJSHeapSize: 100 * 1024 * 1024, // 100MB estimate
      jsHeapSizeLimit: this.config.maxHeapSize,
    };
  }

  public async forceGarbageCollection(): Promise<GCEvent> {
    const before = this.getCurrentMemoryUsage();
    const startTime = performance.now();

    // Force GC if available
    if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc();
    } else if (typeof global !== 'undefined' && (global as any).gc) {
      (global as any).gc();
    }

    const after = this.getCurrentMemoryUsage();
    const duration = performance.now() - startTime;
    const freedMemory = before.used - after.used;

    const gcEvent: GCEvent = {
      id: `gc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      type: 'major',
      duration,
      freedMemory,
      heapBefore: before.used,
      heapAfter: after.used,
      reason: 'manual',
    };

    this.gcEvents.push(gcEvent);
    this.emit('gc_completed', gcEvent);

    logger.info('Manual garbage collection completed', {
      duration,
      freedMemory,
      heapBefore: before.used,
      heapAfter: after.used,
    });

    return gcEvent;
  }

  public async createMemoryProfile(): Promise<MemoryProfile> {
    const profileId = `profile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = performance.now();

    const memoryUsage = this.getCurrentMemoryUsage();
    const objects = await this.analyzeObjectTypes();
    const leaks = Array.from(this.detectedLeaks.values());

    const profile: MemoryProfile = {
      id: profileId,
      timestamp: Date.now(),
      duration: performance.now() - startTime,
      heapUsed: memoryUsage.used,
      heapTotal: memoryUsage.total,
      external: 0, // Would be calculated from actual external memory
      arrayBuffers: this.calculateArrayBufferSize(),
      objects,
      leaks,
    };

    this.memoryProfiles.set(profileId, profile);

    // Limit profile storage
    if (this.memoryProfiles.size > 100) {
      const oldestProfiles = Array.from(this.memoryProfiles.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp)
        .slice(0, 10);

      oldestProfiles.forEach(([id]) => {
        this.memoryProfiles.delete(id);
      });
    }

    this.emit('memory_profile_created', profile);
    logger.debug('Memory profile created', {
      profileId,
      heapUsed: profile.heapUsed,
      objectTypes: profile.objects.length,
      leaks: profile.leaks.length,
    });

    return profile;
  }

  private async analyzeObjectTypes(): Promise<MemoryObjectProfile[]> {
    // In a real implementation, this would use heap snapshots
    // For now, provide estimated object analysis
    const objectTypes = [
      { type: 'String', count: 10000, size: 1024 * 1024, retainedSize: 512 * 1024 },
      { type: 'Array', count: 5000, size: 2 * 1024 * 1024, retainedSize: 1024 * 1024 },
      { type: 'Object', count: 8000, size: 3 * 1024 * 1024, retainedSize: 2 * 1024 * 1024 },
      { type: 'Function', count: 2000, size: 512 * 1024, retainedSize: 256 * 1024 },
      { type: 'HTMLElement', count: 1000, size: 1024 * 1024, retainedSize: 800 * 1024 },
    ];

    return objectTypes;
  }

  private calculateArrayBufferSize(): number {
    // Estimate ArrayBuffer usage
    return 5 * 1024 * 1024; // 5MB estimate
  }

  public trackObject(id: string, object: any, type: string = 'unknown'): void {
    if (this.config.enableMemoryProfiling) {
      // Use WeakRef to avoid creating strong references
      this.objectReferences.set(id, new WeakRef(object));
      
      // Store metadata without strong reference to object
      this.retainedObjects.set(id, {
        type,
        timestamp: Date.now(),
        size: this.estimateObjectSize(object),
      });
    }
  }

  public untrackObject(id: string): void {
    this.objectReferences.delete(id);
    this.retainedObjects.delete(id);
  }

  private estimateObjectSize(obj: any): number {
    if (obj === null || obj === undefined) return 0;
    
    try {
      // Simple size estimation
      const jsonString = JSON.stringify(obj);
      return jsonString.length * 2; // UTF-16 characters
    } catch {
      // Circular reference or non-serializable object
      return 1024; // Default estimate
    }
  }

  private startMemoryMonitoring(): void {
    this.monitoringInterval = setInterval(() => {
      const usage = this.getCurrentMemoryUsage();
      this.memoryHistory.push(usage);

      // Limit history size
      if (this.memoryHistory.length > this.config.retentionSamples) {
        this.memoryHistory = this.memoryHistory.slice(-this.config.retentionSamples);
      }

      // Check thresholds
      this.checkMemoryThresholds(usage);

      // Auto GC if enabled and threshold reached
      if (this.config.enableAutoGC && usage.percentage > this.config.gcThreshold) {
        this.forceGarbageCollection();
      }

      this.emit('memory_usage_updated', usage);
    }, 5000); // Every 5 seconds
  }

  private checkMemoryThresholds(usage: MemoryUsage): void {
    if (usage.percentage > this.config.memoryCriticalThreshold) {
      this.emit('memory_critical', usage);
      logger.error('Critical memory usage detected', {
        percentage: usage.percentage,
        used: usage.used,
        limit: usage.limit,
      });
    } else if (usage.percentage > this.config.memoryWarningThreshold) {
      this.emit('memory_warning', usage);
      logger.warn('High memory usage detected', {
        percentage: usage.percentage,
        used: usage.used,
        limit: usage.limit,
      });
    }
  }

  private startLeakDetection(): void {
    this.leakDetectionInterval = setInterval(() => {
      this.detectMemoryLeaks();
    }, this.config.leakDetectionInterval);
  }

  private detectMemoryLeaks(): void {
    // Check for DOM element leaks
    this.detectDOMLeaks();

    // Check for event listener leaks
    this.detectEventListenerLeaks();

    // Check for timer leaks
    this.detectTimerLeaks();

    // Check for closure leaks
    this.detectClosureLeaks();

    // Check for cache leaks
    this.detectCacheLeaks();
  }

  private detectDOMLeaks(): void {
    if (typeof document === 'undefined') return;

    const elements = document.querySelectorAll('*');
    const elementCount = elements.length;

    // Simple heuristic: too many DOM elements might indicate a leak
    if (elementCount > 10000) {
      const leak: MemoryLeak = {
        id: `dom_leak_${Date.now()}`,
        type: 'dom',
        description: `Excessive DOM elements detected: ${elementCount}`,
        size: elementCount * 100, // Rough estimate
        growth: 0, // Would calculate growth rate
        detected: Date.now(),
        source: 'DOM',
      };

      this.detectedLeaks.set(leak.id, leak);
      this.emit('memory_leak_detected', leak);
    }
  }

  private detectEventListenerLeaks(): void {
    // In a real implementation, this would track event listeners
    // For now, provide a placeholder
    logger.debug('Event listener leak detection completed');
  }

  private detectTimerLeaks(): void {
    // Check for excessive timers (would need browser API access)
    logger.debug('Timer leak detection completed');
  }

  private detectClosureLeaks(): void {
    // Check tracked objects for potential closure leaks
    let suspiciousObjects = 0;

    for (const [id, metadata] of this.retainedObjects.entries()) {
      const ref = this.objectReferences.get(id);
      if (ref && ref.deref() === undefined) {
        // Object was garbage collected, clean up tracking
        this.untrackObject(id);
      } else if (metadata.type === 'Function' && Date.now() - metadata.timestamp > 300000) {
        // Function retained for more than 5 minutes might be suspicious
        suspiciousObjects++;
      }
    }

    if (suspiciousObjects > 100) {
      const leak: MemoryLeak = {
        id: `closure_leak_${Date.now()}`,
        type: 'closure',
        description: `Suspicious long-lived functions detected: ${suspiciousObjects}`,
        size: suspiciousObjects * 1024, // Rough estimate
        growth: 0,
        detected: Date.now(),
        source: 'Closures',
      };

      this.detectedLeaks.set(leak.id, leak);
      this.emit('memory_leak_detected', leak);
    }
  }

  private detectCacheLeaks(): void {
    // Check for cache growth patterns
    const recentUsage = this.memoryHistory.slice(-10);
    if (recentUsage.length >= 10) {
      const growth = recentUsage[recentUsage.length - 1].used - recentUsage[0].used;
      const growthRate = growth / (10 * 5000); // Growth per second

      if (growthRate > 1024 * 1024) { // More than 1MB/second growth
        const leak: MemoryLeak = {
          id: `cache_leak_${Date.now()}`,
          type: 'cache',
          description: `Rapid memory growth detected: ${growthRate} bytes/second`,
          size: growth,
          growth: growthRate,
          detected: Date.now(),
          source: 'Cache',
        };

        this.detectedLeaks.set(leak.id, leak);
        this.emit('memory_leak_detected', leak);
      }
    }
  }

  private setupGCMonitoring(): void {
    // In a real implementation, this would monitor actual GC events
    // For now, simulate periodic GC events
    setInterval(() => {
      if (Math.random() > 0.9) { // 10% chance of simulated GC
        const usage = this.getCurrentMemoryUsage();
        const gcEvent: GCEvent = {
          id: `gc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          type: Math.random() > 0.7 ? 'major' : 'minor',
          duration: Math.random() * 50 + 10, // 10-60ms
          freedMemory: Math.random() * 10 * 1024 * 1024, // 0-10MB
          heapBefore: usage.used,
          heapAfter: usage.used - Math.random() * 5 * 1024 * 1024,
          reason: 'automatic',
        };

        this.gcEvents.push(gcEvent);
        
        // Limit GC event history
        if (this.gcEvents.length > 1000) {
          this.gcEvents = this.gcEvents.slice(-1000);
        }

        this.emit('gc_event', gcEvent);
      }
    }, 30000); // Check every 30 seconds
  }

  public getMemoryStats(): {
    current: MemoryUsage;
    average: number;
    peak: number;
    gcEvents: number;
    leaks: number;
    profiles: number;
  } {
    const current = this.getCurrentMemoryUsage();
    const average = this.memoryHistory.length > 0
      ? this.memoryHistory.reduce((sum, usage) => sum + usage.used, 0) / this.memoryHistory.length
      : 0;
    const peak = this.memoryHistory.length > 0
      ? Math.max(...this.memoryHistory.map(usage => usage.used))
      : 0;

    return {
      current,
      average,
      peak,
      gcEvents: this.gcEvents.length,
      leaks: this.detectedLeaks.size,
      profiles: this.memoryProfiles.size,
    };
  }

  public clearMemoryHistory(): void {
    this.memoryHistory = [];
    this.gcEvents = [];
    this.emit('memory_history_cleared');
    logger.info('Memory history cleared');
  }

  public clearDetectedLeaks(): void {
    this.detectedLeaks.clear();
    this.emit('memory_leaks_cleared');
    logger.info('Detected memory leaks cleared');
  }

  // Getters
  public getMemoryHistory(): MemoryUsage[] {
    return [...this.memoryHistory];
  }

  public getMemoryProfiles(): MemoryProfile[] {
    return Array.from(this.memoryProfiles.values());
  }

  public getDetectedLeaks(): MemoryLeak[] {
    return Array.from(this.detectedLeaks.values());
  }

  public getGCEvents(): GCEvent[] {
    return [...this.gcEvents];
  }

  public updateConfig(config: Partial<MemoryConfig>): void {
    this.config = { ...this.config, ...config };
    configManager.set('memory', this.config);
    this.emit('config_updated', this.config);
  }

  public getConfig(): MemoryConfig {
    return { ...this.config };
  }

  public destroy(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    if (this.leakDetectionInterval) {
      clearInterval(this.leakDetectionInterval);
    }
    this.removeAllListeners();
  }
}

// Export singleton instance
export const memoryManager = MemoryManager.getInstance();
