export type MetricType = 
  | 'counter'
  | 'gauge'
  | 'histogram'
  | 'summary'
  | 'custom';

export type MetricStatus = 
  | 'active'
  | 'inactive'
  | 'error'
  | 'custom';

export type MetricLabel = {
  name: string;
  value: string;
};

export type MetricUnit = 
  | 'none'
  | 'bytes'
  | 'seconds'
  | 'percent'
  | 'custom';

export interface MetricConfig {
  id: string;
  name: string;
  settings: {
    interval: number;
    retention: number;
    aggregation: boolean;
    alerting: boolean;
  };
  features: {
    realtime: boolean;
    visualization: boolean;
    export: boolean;
    api: boolean;
  };
  metadata: Record<string, any>;
}

export interface Metric {
  id: string;
  config: MetricConfig;
  name: string;
  type: MetricType;
  status: MetricStatus;
  description?: string;
  unit?: string;
  labels: Record<string, string>;
  value: number;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface MetricBucket {
  id: string;
  metric: string;
  count: number;
  sum: number;
  min: number;
  max: number;
  avg: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface MetricSummary {
  id: string;
  metric: string;
  count: number;
  sum: number;
  min: number;
  max: number;
  avg: number;
  quantiles: Record<number, number>;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface MetricAlert {
  id: string;
  metric: string;
  name: string;
  description?: string;
  condition: {
    operator: 'gt' | 'lt' | 'eq' | 'ne' | 'gte' | 'lte';
    threshold: number;
    duration: number;
  };
  actions: {
    type: 'notification' | 'webhook' | 'email' | 'custom';
    target: string;
    params?: Record<string, any>;
  }[];
  status: 'active' | 'inactive' | 'triggered';
  lastTriggered?: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface MetricStats {
  total: number;
  byType: Record<MetricType, number>;
  byStatus: Record<MetricStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface MetricService {
  createConfig: (config: Omit<MetricConfig, 'id'>) => MetricConfig;
  updateConfig: (id: string, config: Partial<MetricConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => MetricConfig | undefined;
  getConfigs: () => MetricConfig[];
  record: (metric: Omit<Metric, 'id' | 'config' | 'timestamp' | 'createdAt' | 'updatedAt'>) => Promise<Metric>;
  getMetric: (id: string) => Metric | undefined;
  getMetrics: (options?: {
    type?: MetricType[];
    status?: MetricStatus[];
    labels?: Record<string, string>;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Metric[];
  createAlert: (alert: Omit<MetricAlert, 'id'>) => Promise<MetricAlert>;
  updateAlert: (id: string, alert: Partial<MetricAlert>) => Promise<MetricAlert>;
  deleteAlert: (id: string) => Promise<void>;
  getAlert: (id: string) => MetricAlert | undefined;
  getAlerts: (options?: {
    metric?: string;
    status?: ('active' | 'inactive' | 'triggered')[];
    startDate?: Date;
    endDate?: Date;
  }) => MetricAlert[];
  query: (options: {
    metrics: string[];
    startDate: Date;
    endDate: Date;
    interval?: number;
    aggregation?: 'avg' | 'sum' | 'min' | 'max' | 'count';
  }) => Promise<Record<string, any[]>>;
  export: (options?: {
    format: 'json' | 'csv' | 'prometheus';
    metrics?: string[];
    startDate?: Date;
    endDate?: Date;
  }) => Promise<Blob>;
  clear: (options?: {
    before?: Date;
    type?: MetricType[];
    status?: MetricStatus[];
  }) => Promise<void>;
  getStats: () => MetricStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 