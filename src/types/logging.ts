import { PerformanceMetrics } from './performance';
import { SecurityEvent } from './security';

export type LogLevel = 
  | 'debug'
  | 'info'
  | 'warn'
  | 'error'
  | 'fatal';

export type LogCategory = 
  | 'system'
  | 'security'
  | 'performance'
  | 'business'
  | 'audit'
  | 'custom';

export interface LogConfig {
  id: string;
  level: LogLevel;
  category: LogCategory;
  format: string;
  destination: string;
  rotation: {
    enabled: boolean;
    maxSize: number;
    maxFiles: number;
  };
  metadata: Record<string, any>;
}

export interface LogEntry {
  id: string;
  config: LogConfig;
  level: LogLevel;
  category: LogCategory;
  message: string;
  timestamp: Date;
  context?: Record<string, any>;
  error?: Error;
  metadata?: Record<string, any>;
}

export interface LogStats {
  total: number;
  byLevel: Record<LogLevel, number>;
  byCategory: Record<LogCategory, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface LoggingService {
  createConfig: (config: Omit<LogConfig, 'id'>) => LogConfig;
  updateConfig: (id: string, config: Partial<LogConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => LogConfig | undefined;
  getConfigs: () => LogConfig[];
  log: (message: string, config: Omit<LogConfig, 'id'>, context?: Record<string, any>, error?: Error) => LogEntry;
  debug: (message: string, config: Omit<LogConfig, 'id'>, context?: Record<string, any>) => LogEntry;
  info: (message: string, config: Omit<LogConfig, 'id'>, context?: Record<string, any>) => LogEntry;
  warn: (message: string, config: Omit<LogConfig, 'id'>, context?: Record<string, any>, error?: Error) => LogEntry;
  error: (message: string, config: Omit<LogConfig, 'id'>, context?: Record<string, any>, error?: Error) => LogEntry;
  fatal: (message: string, config: Omit<LogConfig, 'id'>, context?: Record<string, any>, error?: Error) => LogEntry;
  getLogs: (config: Omit<LogConfig, 'id'>) => LogEntry[];
  clear: (config: Omit<LogConfig, 'id'>) => void;
  getStats: () => LogStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 