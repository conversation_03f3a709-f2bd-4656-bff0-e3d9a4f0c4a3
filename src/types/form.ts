export type FormFieldType = 
  | 'text'
  | 'number'
  | 'email'
  | 'password'
  | 'tel'
  | 'url'
  | 'date'
  | 'time'
  | 'datetime'
  | 'checkbox'
  | 'radio'
  | 'select'
  | 'multiselect'
  | 'textarea'
  | 'file'
  | 'custom';

export type FormValidationRule = {
  type: string;
  message: string;
  validate: (value: any) => boolean | Promise<boolean>;
  params?: Record<string, any>;
};

export interface FormField {
  id: string;
  name: string;
  type: FormFieldType;
  label: string;
  placeholder?: string;
  value: any;
  defaultValue?: any;
  required: boolean;
  disabled: boolean;
  hidden: boolean;
  validation: FormValidationRule[];
  dependencies: string[];
  options?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface FormSection {
  id: string;
  name: string;
  title: string;
  description?: string;
  fields: FormField[];
  order: number;
  metadata?: Record<string, any>;
}

export interface FormConfig {
  id: string;
  name: string;
  sections: FormSection[];
  submitButton: {
    text: string;
    icon?: string;
    loading?: boolean;
  };
  resetButton?: {
    text: string;
    icon?: string;
  };
  metadata: Record<string, any>;
}

export interface FormState {
  values: Record<string, any>;
  errors: Record<string, string[]>;
  touched: Record<string, boolean>;
  dirty: Record<string, boolean>;
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
  isSubmitted: boolean;
  metadata?: Record<string, any>;
}

export interface FormStats {
  total: number;
  byType: Record<FormFieldType, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface FormService {
  createField: (field: Omit<FormField, 'id'>) => FormField;
  updateField: (id: string, field: Partial<FormField>) => void;
  deleteField: (id: string) => void;
  getField: (id: string) => FormField | undefined;
  getFields: () => FormField[];
  createSection: (section: Omit<FormSection, 'id'>) => FormSection;
  updateSection: (id: string, section: Partial<FormSection>) => void;
  deleteSection: (id: string) => void;
  getSection: (id: string) => FormSection | undefined;
  getSections: () => FormSection[];
  createForm: (config: Omit<FormConfig, 'id'>) => FormConfig;
  updateForm: (id: string, config: Partial<FormConfig>) => void;
  deleteForm: (id: string) => void;
  getForm: (id: string) => FormConfig | undefined;
  getForms: () => FormConfig[];
  validate: (values: Record<string, any>) => Promise<Record<string, string[]>>;
  submit: (values: Record<string, any>) => Promise<void>;
  reset: () => void;
  getStats: () => FormStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 