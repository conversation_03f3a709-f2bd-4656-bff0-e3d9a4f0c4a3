export type MetricType = 'counter' | 'gauge' | 'histogram' | 'summary' | 'custom';
export type MetricStatus = 'active' | 'inactive' | 'archived';
export type MetricUnit = 'none' | 'bytes' | 'seconds' | 'percent' | 'custom';

export interface MetricConfig {
  management: {
    enabled: boolean;
    types: MetricType[];
    units: MetricUnit[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'timeseries' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    aggregation?: {
      enabled: boolean;
      functions: ('sum' | 'avg' | 'min' | 'max' | 'count' | 'custom')[];
      windows: number[];
    };
  };
  collection: {
    enabled: boolean;
    interval: number;
    timeout: number;
    retry?: {
      enabled: boolean;
      maxAttempts: number;
      delay: number;
      backoff: number;
    };
    rateLimit?: {
      enabled: boolean;
      window: number;
      max: number;
    };
  };
  processing: {
    enabled: boolean;
    filtering?: {
      enabled: boolean;
      rules: {
        field: string;
        operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'regex' | 'custom';
        value: any;
      }[];
    };
    transformation?: {
      enabled: boolean;
      rules: {
        field: string;
        type: 'map' | 'filter' | 'reduce' | 'custom';
        value: any;
      }[];
    };
    aggregation?: {
      enabled: boolean;
      rules: {
        field: string;
        type: 'sum' | 'avg' | 'min' | 'max' | 'count' | 'custom';
        window: number;
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      collection?: boolean;
      performance?: boolean;
      resource?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    visualization?: boolean;
    analytics?: boolean;
    export?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Metric {
  id: string;
  config: MetricConfig;
  type: MetricType;
  status: MetricStatus;
  name: string;
  description?: string;
  unit: MetricUnit;
  labels?: Record<string, string>;
  value: number;
  timestamp: Date;
  source: {
    type: 'system' | 'application' | 'service' | 'custom';
    id: string;
    name?: string;
  };
  collection?: {
    interval: number;
    timeout: number;
    retry: {
      maxAttempts: number;
      delay: number;
      backoff: number;
    };
    rateLimit: {
      window: number;
      max: number;
    };
  };
  processing?: {
    filtering: MetricConfig['processing']['filtering'];
    transformation: MetricConfig['processing']['transformation'];
    aggregation: MetricConfig['processing']['aggregation'];
  };
  stats?: {
    count: number;
    sum: number;
    min: number;
    max: number;
    avg: number;
  };
  metadata?: Record<string, any>;
}

export interface MetricStats {
  total: number;
  byType: Record<MetricType, number>;
  byStatus: Record<MetricStatus, number>;
  byUnit: Record<MetricUnit, number>;
  collection: {
    total: number;
    success: number;
    failure: number;
    byMetric?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageValue: number;
    byMetric?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  resources: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
    byMetric?: Record<string, {
      cpu: number;
      memory: number;
      disk: number;
      network: number;
    }>;
    byDate?: Record<string, {
      cpu: number;
      memory: number;
      disk: number;
      network: number;
    }>;
  };
}

export interface MetricService {
  createConfig: (config: Omit<MetricConfig, 'id'>) => MetricConfig;
  updateConfig: (id: string, config: Partial<MetricConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => MetricConfig | undefined;
  getConfigs: () => MetricConfig[];
  create: (config: Omit<MetricConfig, 'id'>, metric: Omit<Metric, 'id' | 'config'>) => Promise<Metric>;
  update: (id: string, metric: Partial<Metric>) => Promise<Metric>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Metric | undefined;
  getAll: (options?: {
    type?: MetricType[];
    status?: MetricStatus[];
    unit?: MetricUnit[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Metric[];
  search: (query: string, options?: {
    type?: MetricType[];
    status?: MetricStatus[];
    unit?: MetricUnit[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Metric[];
  collect: (id: string) => Promise<void>;
  aggregate: (id: string, options: {
    function: 'sum' | 'avg' | 'min' | 'max' | 'count' | 'custom';
    window: number;
    startDate?: Date;
    endDate?: Date;
  }) => Promise<number>;
  export: (options?: {
    type?: MetricType[];
    status?: MetricStatus[];
    unit?: MetricUnit[];
    startDate?: Date;
    endDate?: Date;
    format: 'csv' | 'json' | 'xml' | 'custom';
  }) => Promise<string>;
  getStats: () => MetricStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 