export type OrganizationType = 'company' | 'nonprofit' | 'government' | 'custom';
export type OrganizationStatus = 'active' | 'inactive' | 'suspended' | 'archived';
export type OrganizationPlan = 'free' | 'basic' | 'premium' | 'enterprise' | 'custom';

export interface OrganizationConfig {
  management: {
    enabled: boolean;
    types: OrganizationType[];
    plans: OrganizationPlan[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'ldap' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  billing: {
    enabled: boolean;
    plans?: {
      name: OrganizationPlan;
      price: number;
      currency: string;
      features: string[];
      limits?: Record<string, number>;
    }[];
    payment?: {
      enabled: boolean;
      providers?: {
        name: string;
        config: Record<string, any>;
      }[];
    };
  };
  collaboration: {
    enabled: boolean;
    features?: {
      teams?: boolean;
      projects?: boolean;
      documents?: boolean;
      calendar?: boolean;
    };
    notifications?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      usage?: boolean;
      performance?: boolean;
      billing?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    teams?: boolean;
    projects?: boolean;
    analytics?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Organization {
  id: string;
  config: OrganizationConfig;
  type: OrganizationType;
  status: OrganizationStatus;
  plan: OrganizationPlan;
  name: string;
  description?: string;
  contact?: {
    email?: string;
    phone?: string;
    address?: string;
    website?: string;
  };
  billing?: {
    plan: OrganizationPlan;
    price: number;
    currency: string;
    period: 'monthly' | 'yearly';
    nextBilling?: Date;
    paymentMethod?: {
      type: string;
      details: Record<string, any>;
    };
  };
  teams?: {
    id: string;
    name: string;
    members: string[];
    projects: string[];
  }[];
  projects?: {
    id: string;
    name: string;
    team: string;
    members: string[];
  }[];
  settings?: {
    features?: Record<string, boolean>;
    limits?: Record<string, number>;
    preferences?: Record<string, any>;
  };
  stats?: {
    members: number;
    teams: number;
    projects: number;
    usage: Record<string, number>;
  };
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface OrganizationLog {
  id: string;
  organization: string;
  action: 'create' | 'update' | 'delete' | 'billing' | 'team' | 'project';
  user?: string;
  details?: Record<string, any>;
  timestamp: Date;
}

export interface OrganizationStats {
  total: number;
  byType: Record<OrganizationType, number>;
  byStatus: Record<OrganizationStatus, number>;
  byPlan: Record<OrganizationPlan, number>;
  usage: {
    members: {
      total: number;
      byOrganization?: Record<string, number>;
      byDate?: Record<string, number>;
    };
    teams: {
      total: number;
      byOrganization?: Record<string, number>;
      byDate?: Record<string, number>;
    };
    projects: {
      total: number;
      byOrganization?: Record<string, number>;
      byDate?: Record<string, number>;
    };
  };
  billing: {
    revenue: {
      total: number;
      byPlan?: Record<OrganizationPlan, number>;
      byDate?: Record<string, number>;
    };
    subscriptions: {
      total: number;
      active: number;
      cancelled: number;
      byPlan?: Record<OrganizationPlan, number>;
    };
  };
  performance: {
    averageResponseTime: number;
    byOrganization?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface OrganizationService {
  createConfig: (config: Omit<OrganizationConfig, 'id'>) => OrganizationConfig;
  updateConfig: (id: string, config: Partial<OrganizationConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => OrganizationConfig | undefined;
  getConfigs: () => OrganizationConfig[];
  create: (config: Omit<OrganizationConfig, 'id'>, organization: Omit<Organization, 'id' | 'config'>) => Promise<Organization>;
  update: (id: string, organization: Partial<Organization>) => Promise<Organization>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Organization | undefined;
  getAll: (options?: {
    type?: OrganizationType[];
    status?: OrganizationStatus[];
    plan?: OrganizationPlan[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Organization[];
  search: (query: string, options?: {
    type?: OrganizationType[];
    status?: OrganizationStatus[];
    plan?: OrganizationPlan[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Organization[];
  subscribe: (id: string, options: {
    plan: OrganizationPlan;
    period: 'monthly' | 'yearly';
    paymentMethod?: {
      type: string;
      details: Record<string, any>;
    };
  }) => Promise<void>;
  unsubscribe: (id: string) => Promise<void>;
  getLogs: (options?: {
    organization?: string;
    action?: ('create' | 'update' | 'delete' | 'billing' | 'team' | 'project')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => OrganizationLog[];
  validate: (id: string) => Promise<{
    valid: boolean;
    errors?: {
      path: string;
      message: string;
    }[];
  }>;
  getStats: () => OrganizationStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 