export type PerformanceMetric = 
  | 'loadTime'
  | 'firstPaint'
  | 'firstContentfulPaint'
  | 'largestContentfulPaint'
  | 'timeToInteractive'
  | 'totalBlockingTime'
  | 'cumulativeLayoutShift'
  | 'firstInputDelay'
  | 'speedIndex'
  | 'timeToFirstByte'
  | 'domContentLoaded'
  | 'domComplete'
  | 'resourceCount'
  | 'resourceSize'
  | 'memoryUsage'
  | 'cpuUsage'
  | 'networkRequests'
  | 'cacheHits'
  | 'cacheMisses'
  | 'errorRate';

export interface PerformanceConfig {
  id: string;
  metrics: PerformanceMetric[];
  thresholds: Record<PerformanceMetric, number>;
  sampling: number;
  reporting: {
    enabled: boolean;
    interval: number;
    endpoint: string;
  };
  metadata: Record<string, any>;
}

export interface PerformanceMeasurement {
  id: string;
  metric: PerformanceMetric;
  value: number;
  timestamp: Date;
  context: {
    url: string;
    userAgent: string;
    device: string;
    connection: string;
  };
  metadata?: Record<string, any>;
}

export interface PerformanceReport {
  id: string;
  timestamp: Date;
  measurements: PerformanceMeasurement[];
  summary: {
    total: number;
    byMetric: Record<PerformanceMetric, {
      value: number;
      threshold: number;
      status: 'good' | 'needs-improvement' | 'poor';
    }>;
  };
  metadata?: Record<string, any>;
}

export interface PerformanceStats {
  total: number;
  byMetric: Record<PerformanceMetric, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface PerformanceService {
  createConfig: (config: Omit<PerformanceConfig, 'id'>) => PerformanceConfig;
  updateConfig: (id: string, config: Partial<PerformanceConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => PerformanceConfig | undefined;
  getConfigs: () => PerformanceConfig[];
  measure: (metric: PerformanceMetric, context?: Record<string, any>) => Promise<PerformanceMeasurement>;
  getMeasurement: (id: string) => PerformanceMeasurement | undefined;
  getMeasurements: () => PerformanceMeasurement[];
  getReport: () => PerformanceReport;
  getStats: () => PerformanceStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}

export interface PerformanceEntry {
  name: string;
  entryType: string;
  startTime: number;
  duration: number;
  timestamp: number;
}

export interface NavigationTiming extends PerformanceEntry {
  entryType: 'navigation';
  unloadEventStart: number;
  unloadEventEnd: number;
  redirectStart: number;
  redirectEnd: number;
  fetchStart: number;
  domainLookupStart: number;
  domainLookupEnd: number;
  connectStart: number;
  connectEnd: number;
  secureConnectionStart: number;
  requestStart: number;
  responseStart: number;
  responseEnd: number;
  domLoading: number;
  domInteractive: number;
  domContentLoadedEventStart: number;
  domContentLoadedEventEnd: number;
  domComplete: number;
  loadEventStart: number;
  loadEventEnd: number;
}

export interface ResourceTiming extends PerformanceEntry {
  entryType: 'resource';
  initiatorType: string;
  nextHopProtocol: string;
  workerStart: number;
  redirectStart: number;
  redirectEnd: number;
  fetchStart: number;
  domainLookupStart: number;
  domainLookupEnd: number;
  connectStart: number;
  connectEnd: number;
  secureConnectionStart: number;
  requestStart: number;
  responseStart: number;
  responseEnd: number;
  transferSize: number;
  encodedBodySize: number;
  decodedBodySize: number;
}

export interface PaintTiming extends PerformanceEntry {
  entryType: 'paint';
  name: 'first-paint' | 'first-contentful-paint';
}

export interface LayoutShift extends PerformanceEntry {
  entryType: 'layout-shift';
  value: number;
  sources: {
    node: Node;
    currentRect: DOMRectReadOnly;
    previousRect: DOMRectReadOnly;
  }[];
}

export interface LongTask extends PerformanceEntry {
  entryType: 'longtask';
  attribution: {
    name: string;
    entryType: string;
    startTime: number;
    duration: number;
    containerType: string;
    containerSrc: string;
    containerId: string;
    containerName: string;
  }[];
}

export interface MemoryInfo {
  jsHeapSizeLimit: number;
  totalJSHeapSize: number;
  usedJSHeapSize: number;
}

export interface NetworkInfo {
  effectiveType: 'slow-2g' | '2g' | '3g' | '4g';
  downlink: number;
  rtt: number;
  saveData: boolean;
}

export interface ErrorInfo {
  message: string;
  source: string;
  lineno: number;
  colno: number;
  error: Error;
  timestamp: number;
  stack?: string;
}

export interface PerformanceMetrics {
  navigation: NavigationTiming;
  resources: ResourceTiming[];
  paint: PaintTiming[];
  layoutShifts: LayoutShift[];
  longTasks: LongTask[];
  memory: MemoryInfo;
  network: NetworkInfo;
  errors: ErrorInfo[];
}

export interface PerformanceObserver {
  observe: (options: { entryTypes: string[] }) => void;
  disconnect: () => void;
  takeRecords: () => PerformanceEntry[];
}

export interface PerformanceMonitor {
  start: () => void;
  stop: () => void;
  getMetrics: () => PerformanceMetrics;
  reset: () => void;
  onMetricsUpdate: (callback: (metrics: PerformanceMetrics) => void) => void;
  onError: (callback: (error: ErrorInfo) => void) => void;
} 