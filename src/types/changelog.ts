export type ChangeType = 
  | 'added'
  | 'changed'
  | 'deprecated'
  | 'removed'
  | 'fixed'
  | 'security'
  | 'custom';

export type ChangeScope = 
  | 'api'
  | 'ui'
  | 'backend'
  | 'frontend'
  | 'database'
  | 'infrastructure'
  | 'custom';

export interface ChangelogConfig {
  id: string;
  name: string;
  settings: {
    format: 'markdown' | 'html' | 'json' | 'custom';
    sections: boolean;
    links: boolean;
    authors: boolean;
  };
  features: {
    templates: boolean;
    automation: boolean;
    validation: boolean;
    publishing: boolean;
  };
  metadata: Record<string, any>;
}

export interface Changelog {
  id: string;
  config: ChangelogConfig;
  version: string;
  title: string;
  description?: string;
  date: Date;
  changes: {
    type: ChangeType;
    scope: ChangeScope;
    description: string;
    breaking?: boolean;
    references?: string[];
    authors?: string[];
  }[];
  sections: {
    title: string;
    changes: string[];
  }[];
  links: {
    title: string;
    url: string;
  }[];
  authors: {
    id: string;
    name: string;
    email?: string;
    url?: string;
  }[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface ChangelogTemplate {
  id: string;
  config: ChangelogConfig;
  name: string;
  description?: string;
  sections: {
    title: string;
    types: ChangeType[];
  }[];
  format: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface ChangelogStats {
  total: number;
  byType: Record<ChangeType, number>;
  byScope: Record<ChangeScope, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface ChangelogService {
  createConfig: (config: Omit<ChangelogConfig, 'id'>) => ChangelogConfig;
  updateConfig: (id: string, config: Partial<ChangelogConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => ChangelogConfig | undefined;
  getConfigs: () => ChangelogConfig[];
  create: (config: Omit<ChangelogConfig, 'id'>, changelog: Omit<Changelog, 'id' | 'config'>) => Promise<Changelog>;
  update: (id: string, changelog: Partial<Changelog>) => Promise<Changelog>;
  delete: (id: string) => Promise<void>;
  getChangelog: (id: string) => Changelog | undefined;
  getChangelogs: (options?: {
    version?: string;
    startDate?: Date;
    endDate?: Date;
  }) => Changelog[];
  createTemplate: (config: Omit<ChangelogConfig, 'id'>, template: Omit<ChangelogTemplate, 'id' | 'config'>) => Promise<ChangelogTemplate>;
  updateTemplate: (id: string, template: Partial<ChangelogTemplate>) => Promise<ChangelogTemplate>;
  deleteTemplate: (id: string) => Promise<void>;
  getTemplate: (id: string) => ChangelogTemplate | undefined;
  getTemplates: () => ChangelogTemplate[];
  generate: (templateId: string, data: {
    version: string;
    title: string;
    description?: string;
    changes: Changelog['changes'];
    authors?: Changelog['authors'];
  }) => Promise<Changelog>;
  publish: (id: string, options?: {
    format?: ChangelogConfig['settings']['format'];
    destination?: string;
  }) => Promise<string>;
  clear: () => Promise<void>;
  getStats: () => ChangelogStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 