import { ReactNode } from 'react';

export type RouteType = 'page' | 'api' | 'component' | 'redirect' | 'custom';

export type RouteMethod = 'get' | 'post' | 'put' | 'delete' | 'patch' | 'options' | 'head';

export interface Route {
  id: string;
  type: RouteType;
  method: RouteMethod;
  path: string;
  name: string;
  component?: string;
  handler?: (params: Record<string, any>) => Promise<any>;
  params?: Record<string, {
    type: string;
    required: boolean;
    default?: any;
    validate?: (value: any) => boolean;
  }>;
  guards?: ((params: Record<string, any>) => Promise<boolean>)[];
  metadata?: Record<string, any>;
}

export interface RouteMeta {
  title?: string;
  description?: string;
  icon?: string;
  roles?: string[];
  permissions?: string[];
  layout?: string;
  breadcrumb?: string[];
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface RouteGuard {
  canActivate: (route: Route) => Promise<boolean>;
  canDeactivate: (route: Route) => Promise<boolean>;
}

export interface RouteResolver {
  resolve: (route: Route) => Promise<any>;
}

export interface RouteMatch {
  id: string;
  route: string;
  params: Record<string, any>;
  query: Record<string, any>;
  hash: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface RouteLocation {
  pathname: string;
  search: string;
  hash: string;
  state?: any;
  key?: string;
}

export interface RouteHistory {
  push: (location: RouteLocation) => void;
  replace: (location: RouteLocation) => void;
  go: (n: number) => void;
  goBack: () => void;
  goForward: () => void;
  block: (prompt?: string | boolean | ((location: RouteLocation) => string | boolean)) => () => void;
  listen: (listener: (location: RouteLocation) => void) => () => void;
}

export interface RouteConfig {
  routes: Route[];
  basename?: string;
  location?: RouteLocation;
  history?: RouteHistory;
  fallback?: React.ComponentType<any>;
  loading?: React.ComponentType<any>;
  error?: React.ComponentType<any>;
  metadata?: Record<string, any>;
}

export interface RouteService {
  navigate: (location: RouteLocation) => Promise<void>;
  replace: (location: RouteLocation) => Promise<void>;
  go: (n: number) => void;
  goBack: () => void;
  goForward: () => void;
  getCurrentLocation: () => RouteLocation;
  getCurrentMatch: () => RouteMatch;
  getCurrentRoute: () => Route;
  getCurrentParams: () => Record<string, string>;
  getCurrentQuery: () => Record<string, string>;
  getCurrentHash: () => string;
  getCurrentState: () => any;
  getCurrentMeta: () => RouteMeta;
  getCurrentBreadcrumb: () => string[];
  getCurrentTags: () => string[];
  getCurrentMetadata: () => Record<string, any>;
  isActive: (path: string) => boolean;
  isExact: (path: string) => boolean;
  isStrict: (path: string) => boolean;
  isSensitive: (path: string) => boolean;
  hasGuard: (path: string) => boolean;
  hasResolver: (path: string) => boolean;
  hasRole: (path: string, role: string) => boolean;
  hasPermission: (path: string, permission: string) => boolean;
  hasTag: (path: string, tag: string) => boolean;
  hasMetadata: (path: string, key: string) => boolean;
  getMetadata: (path: string, key: string) => any;
} 