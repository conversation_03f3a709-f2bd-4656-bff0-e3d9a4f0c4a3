export type ContentType = 'system' | 'user' | 'application' | 'custom';
export type ContentStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type ContentFormat = 'text' | 'html' | 'markdown' | 'custom';

export interface ContentConfig {
  management: {
    enabled: boolean;
    types: ContentType[];
    formats: ContentFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  content: {
    enabled: boolean;
    default: string;
    fallback: string;
    content: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: ContentFormat;
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      content?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    content?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Content {
  id: string;
  config: ContentConfig;
  type: ContentType;
  status: ContentStatus;
  format: ContentFormat;
  name: string;
  description?: string;
  content: {
    content: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: ContentFormat;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    content: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface ContentLog {
  id: string;
  content: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface ContentStats {
  total: number;
  byType: Record<ContentType, number>;
  byStatus: Record<ContentStatus, number>;
  byFormat: Record<ContentFormat, number>;
  content: {
    total: number;
    byType?: Record<string, number>;
    byContent?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byContent?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byContent?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface ContentService {
  createConfig: (config: Omit<ContentConfig, 'id'>) => ContentConfig;
  updateConfig: (id: string, config: Partial<ContentConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => ContentConfig | undefined;
  getConfigs: () => ContentConfig[];
  create: (config: Omit<ContentConfig, 'id'>, content: Omit<Content, 'id' | 'config'>) => Promise<Content>;
  update: (id: string, content: Partial<Content>) => Promise<Content>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Content | undefined;
  getAll: (options?: {
    type?: ContentType[];
    status?: ContentStatus[];
    format?: ContentFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Content[];
  search: (query: string, options?: {
    type?: ContentType[];
    status?: ContentStatus[];
    format?: ContentFormat[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Content[];
  validate: (id: string, content: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    content?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => ContentLog[];
  getStats: () => ContentStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 