export type TaskType = 'system' | 'user' | 'scheduled' | 'custom';
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'archived';
export type TaskPriority = 'low' | 'normal' | 'high' | 'urgent';

export interface TaskConfig {
  management: {
    enabled: boolean;
    types: TaskType[];
    priorities: TaskPriority[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'queue' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  execution: {
    enabled: boolean;
    concurrency?: {
      enabled: boolean;
      max: number;
      perType?: Record<TaskType, number>;
    };
    timeout?: {
      enabled: boolean;
      default: number;
      perType?: Record<TaskType, number>;
    };
    retry?: {
      enabled: boolean;
      maxAttempts: number;
      delay: number;
      backoff: number;
    };
    rateLimit?: {
      enabled: boolean;
      window: number;
      max: number;
    };
  };
  scheduling: {
    enabled: boolean;
    engine: 'cron' | 'interval' | 'custom';
    timezone: string;
    schedules: {
      name: string;
      type: TaskType;
      pattern: string;
      options?: Record<string, any>;
    }[];
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      execution?: boolean;
      performance?: boolean;
      resource?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    scheduling?: boolean;
    dependencies?: boolean;
    progress?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Task {
  id: string;
  config: TaskConfig;
  type: TaskType;
  status: TaskStatus;
  priority: TaskPriority;
  name: string;
  description?: string;
  handler: {
    type: 'function' | 'script' | 'command' | 'custom';
    value: string;
    options?: Record<string, any>;
  };
  input?: Record<string, any>;
  output?: Record<string, any>;
  schedule?: {
    pattern: string;
    timezone: string;
    startDate?: Date;
    endDate?: Date;
    options?: Record<string, any>;
  };
  dependencies?: {
    tasks: string[];
    conditions: {
      task: string;
      status: TaskStatus[];
    }[];
  };
  progress?: {
    current: number;
    total: number;
    message?: string;
    details?: Record<string, any>;
  };
  execution?: {
    attempts: number;
    lastAttempt?: Date;
    nextAttempt?: Date;
    error?: {
      code: string;
      message: string;
      details?: any;
    };
  };
  resources?: {
    cpu?: number;
    memory?: number;
    disk?: number;
    network?: number;
  };
  stats?: {
    attempts: number;
    success: number;
    failure: number;
    duration: number;
  };
  createdAt: Date;
  updatedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export interface TaskComment {
  id: string;
  task: string;
  user: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface TaskChecklist {
  id: string;
  task: string;
  title: string;
  items: {
    id: string;
    text: string;
    completed: boolean;
    completedAt?: Date;
    completedBy?: string;
  }[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface TaskQueue {
  id: string;
  name: string;
  description?: string;
  tasks: string[];
  status: TaskStatus;
  metadata?: Record<string, any>;
}

export interface TaskSchedule {
  id: string;
  task: string;
  cron: string;
  timezone: string;
  enabled: boolean;
  lastRun?: Date;
  nextRun?: Date;
  metadata?: Record<string, any>;
}

export interface TaskLog {
  id: string;
  task: string;
  action: 'create' | 'update' | 'delete' | 'start' | 'complete' | 'fail' | 'cancel' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface TaskMetric {
  id: string;
  config: TaskConfig;
  task: string;
  timestamp: Date;
  metrics: {
    execution: {
      duration: number;
      memory: number;
      cpu: number;
      attempts: number;
    };
    performance: {
      queueSize: number;
      processingTime: number;
      waitTime: number;
    };
    resources: {
      memory: {
        used: number;
        peak: number;
      };
      cpu: {
        used: number;
        peak: number;
      };
      threads: {
        active: number;
        peak: number;
      };
    };
  };
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface TaskStats {
  total: number;
  byType: Record<TaskType, number>;
  byStatus: Record<TaskStatus, number>;
  byPriority: Record<TaskPriority, number>;
  execution: {
    total: number;
    success: number;
    failure: number;
    byTask?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageDuration: number;
    byTask?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  resources: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
    byTask?: Record<string, {
      cpu: number;
      memory: number;
      disk: number;
      network: number;
    }>;
    byDate?: Record<string, {
      cpu: number;
      memory: number;
      disk: number;
      network: number;
    }>;
  };
}

export interface TaskService {
  createConfig: (config: Omit<TaskConfig, 'id'>) => TaskConfig;
  updateConfig: (id: string, config: Partial<TaskConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => TaskConfig | undefined;
  getConfigs: () => TaskConfig[];
  create: (config: Omit<TaskConfig, 'id'>, task: Omit<Task, 'id' | 'config'>) => Promise<Task>;
  update: (id: string, task: Partial<Task>) => Promise<Task>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Task | undefined;
  getAll: (options?: {
    type?: TaskType[];
    status?: TaskStatus[];
    priority?: TaskPriority[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Task[];
  search: (query: string, options?: {
    type?: TaskType[];
    status?: TaskStatus[];
    priority?: TaskPriority[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Task[];
  start: (id: string) => Promise<void>;
  stop: (id: string) => Promise<void>;
  cancel: (id: string) => Promise<void>;
  schedule: (id: string, schedule: Task['schedule']) => Promise<void>;
  updateProgress: (id: string, progress: Task['progress']) => Promise<void>;
  getLogs: (options?: {
    task?: string;
    action?: ('create' | 'update' | 'delete' | 'start' | 'complete' | 'fail' | 'cancel' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => TaskLog[];
  getStats: () => TaskStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
  addComment: (taskId: string, comment: Omit<TaskComment, 'id' | 'task'>) => Promise<TaskComment>;
  updateComment: (id: string, comment: Partial<TaskComment>) => Promise<TaskComment>;
  deleteComment: (id: string) => Promise<void>;
  getComment: (id: string) => TaskComment | undefined;
  getComments: (taskId: string) => TaskComment[];
  createChecklist: (taskId: string, checklist: Omit<TaskChecklist, 'id' | 'task'>) => Promise<TaskChecklist>;
  updateChecklist: (id: string, checklist: Partial<TaskChecklist>) => Promise<TaskChecklist>;
  deleteChecklist: (id: string) => Promise<void>;
  getChecklist: (id: string) => TaskChecklist | undefined;
  getChecklists: (taskId: string) => TaskChecklist[];
  clear: (options?: {
    before?: Date;
    type?: TaskType[];
    status?: TaskStatus[];
  }) => Promise<void>;
  createQueue: (name: string, description?: string) => string;
  deleteQueue: (id: string) => void;
  getQueue: (id: string) => TaskQueue | undefined;
  getQueues: () => TaskQueue[];
  addToQueue: (queueId: string, taskId: string) => void;
  removeFromQueue: (queueId: string, taskId: string) => void;
  getQueueTasks: (queueId: string) => Task[];
  createSchedule: (schedule: Omit<TaskSchedule, 'id' | 'lastRun' | 'nextRun'>) => string;
  deleteSchedule: (id: string) => void;
  getSchedule: (id: string) => TaskSchedule | undefined;
  getSchedules: () => TaskSchedule[];
} 