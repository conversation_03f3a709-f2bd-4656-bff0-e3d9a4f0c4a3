export type TagType = 
  | 'category'
  | 'label'
  | 'keyword'
  | 'custom';

export type TagColor = 
  | 'red'
  | 'orange'
  | 'yellow'
  | 'green'
  | 'blue'
  | 'purple'
  | 'pink'
  | 'gray'
  | 'custom';

export interface TagConfig {
  id: string;
  name: string;
  settings: {
    colors: boolean;
    icons: boolean;
    hierarchy: boolean;
    synonyms: boolean;
  };
  features: {
    suggestions: boolean;
    analytics: boolean;
    moderation: boolean;
    merging: boolean;
  };
  metadata: Record<string, any>;
}

export interface Tag {
  id: string;
  config: TagConfig;
  name: string;
  slug: string;
  type: TagType;
  color?: TagColor;
  icon?: string;
  description?: string;
  parent?: string;
  children: string[];
  synonyms: string[];
  usage: {
    total: number;
    byEntity: Record<string, number>;
  };
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface TagGroup {
  id: string;
  config: TagConfig;
  name: string;
  description?: string;
  tags: string[];
  color?: TagColor;
  icon?: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface TagReport {
  id: string;
  timestamp: Date;
  tags: Tag[];
  groups: TagGroup[];
  summary: {
    total: number;
    byType: Record<TagType, number>;
    byColor: Record<TagColor, number>;
  };
  metadata?: Record<string, any>;
}

export interface TagStats {
  total: number;
  byType: Record<TagType, number>;
  byColor: Record<TagColor, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface TagService {
  createConfig: (config: Omit<TagConfig, 'id'>) => TagConfig;
  updateConfig: (id: string, config: Partial<TagConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => TagConfig | undefined;
  getConfigs: () => TagConfig[];
  create: (config: Omit<TagConfig, 'id'>, tag: Omit<Tag, 'id' | 'config'>) => Promise<Tag>;
  update: (id: string, tag: Partial<Tag>) => Promise<Tag>;
  delete: (id: string) => Promise<void>;
  getTag: (id: string) => Tag | undefined;
  getTags: (options?: {
    type?: TagType;
    color?: TagColor;
    parent?: string;
    startDate?: Date;
    endDate?: Date;
  }) => Tag[];
  createGroup: (config: Omit<TagConfig, 'id'>, group: Omit<TagGroup, 'id' | 'config'>) => Promise<TagGroup>;
  updateGroup: (id: string, group: Partial<TagGroup>) => Promise<TagGroup>;
  deleteGroup: (id: string) => Promise<void>;
  getGroup: (id: string) => TagGroup | undefined;
  getGroups: (options?: {
    color?: TagColor;
    startDate?: Date;
    endDate?: Date;
  }) => TagGroup[];
  addToEntity: (tagId: string, entityType: string, entityId: string) => Promise<void>;
  removeFromEntity: (tagId: string, entityType: string, entityId: string) => Promise<void>;
  getEntityTags: (entityType: string, entityId: string) => Tag[];
  mergeTags: (sourceId: string, targetId: string) => Promise<Tag>;
  suggestTags: (text: string, options?: {
    type?: TagType;
    limit?: number;
  }) => Tag[];
  clear: () => Promise<void>;
  getStats: () => TagStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 