export type IntegrationType = 
  | 'api'
  | 'database'
  | 'message'
  | 'file'
  | 'custom';

export type IntegrationStatus = 
  | 'active'
  | 'inactive'
  | 'error'
  | 'custom';

export interface IntegrationConfig {
  id: string;
  name: string;
  settings: {
    connection: {
      type: 'http' | 'websocket' | 'grpc' | 'tcp' | 'custom';
      url: string;
      timeout: number;
      retry: {
        enabled: boolean;
        attempts: number;
        delay: number;
      };
      auth: {
        type: 'none' | 'basic' | 'bearer' | 'apiKey' | 'oauth2' | 'custom';
        credentials?: Record<string, any>;
      };
    };
    sync: {
      enabled: boolean;
      interval: number;
      batchSize: number;
      direction: 'push' | 'pull' | 'both';
    };
    mapping: {
      enabled: boolean;
      schema: Record<string, any>;
      transform: boolean;
      validate: boolean;
    };
    monitoring: {
      enabled: boolean;
      metrics: boolean;
      alerts: boolean;
      logs: boolean;
    };
  };
  features: {
    realtime: boolean;
    batch: boolean;
    streaming: boolean;
    caching: boolean;
  };
  metadata: Record<string, any>;
}

export interface Integration {
  id: string;
  config: IntegrationConfig;
  name: string;
  description?: string;
  type: IntegrationType;
  status: IntegrationStatus;
  provider: string;
  version: string;
  endpoints: {
    name: string;
    path: string;
    method: string;
    description?: string;
  }[];
  schemas: {
    name: string;
    type: string;
    schema: Record<string, any>;
  }[];
  handlers: {
    connect?: string[];
    disconnect?: string[];
    sync?: string[];
    transform?: string[];
    validate?: string[];
    error?: string[];
  };
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface IntegrationMapping {
  id: string;
  config: IntegrationConfig;
  integration: string;
  name: string;
  description?: string;
  source: {
    type: string;
    schema: Record<string, any>;
  };
  target: {
    type: string;
    schema: Record<string, any>;
  };
  rules: {
    field: string;
    transform?: string;
    validate?: string;
    required: boolean;
  }[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface IntegrationSync {
  id: string;
  config: IntegrationConfig;
  integration: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  direction: 'push' | 'pull' | 'both';
  data: {
    source: any;
    target: any;
  };
  stats: {
    total: number;
    processed: number;
    failed: number;
    skipped: number;
  };
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  startedAt: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface IntegrationEvent {
  id: string;
  config: IntegrationConfig;
  integration: string;
  type: 'connect' | 'disconnect' | 'sync' | 'error' | 'custom';
  status: 'success' | 'failure' | 'warning';
  data: any;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface IntegrationMetric {
  id: string;
  config: IntegrationConfig;
  integration: string;
  timestamp: Date;
  metrics: {
    connections: number;
    requests: number;
    errors: number;
    latency: {
      min: number;
      max: number;
      avg: number;
      p95: number;
      p99: number;
    };
    sync: {
      total: number;
      success: number;
      failure: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface IntegrationStats {
  total: number;
  byType: Record<IntegrationType, number>;
  byStatus: Record<IntegrationStatus, number>;
  byProvider: Record<string, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata: Record<string, any>;
}

export interface IntegrationService {
  createConfig: (config: Omit<IntegrationConfig, 'id'>) => IntegrationConfig;
  updateConfig: (id: string, config: Partial<IntegrationConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => IntegrationConfig | undefined;
  getConfigs: () => IntegrationConfig[];
  createIntegration: (config: Omit<IntegrationConfig, 'id'>, integration: Omit<Integration, 'id' | 'config'>) => Promise<Integration>;
  updateIntegration: (id: string, integration: Partial<Integration>) => Promise<Integration>;
  deleteIntegration: (id: string) => Promise<void>;
  getIntegration: (id: string) => Integration | undefined;
  getIntegrations: (options?: {
    type?: IntegrationType[];
    status?: IntegrationStatus[];
    provider?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Integration[];
  createMapping: (config: Omit<IntegrationConfig, 'id'>, mapping: Omit<IntegrationMapping, 'id' | 'config'>) => Promise<IntegrationMapping>;
  updateMapping: (id: string, mapping: Partial<IntegrationMapping>) => Promise<IntegrationMapping>;
  deleteMapping: (id: string) => Promise<void>;
  getMapping: (id: string) => IntegrationMapping | undefined;
  getMappings: (options?: {
    integration?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => IntegrationMapping[];
  startSync: (config: Omit<IntegrationConfig, 'id'>, options: {
    integration: string;
    direction: 'push' | 'pull' | 'both';
    data?: any;
  }) => Promise<IntegrationSync>;
  getSync: (id: string) => IntegrationSync | undefined;
  getSyncs: (options?: {
    integration?: string;
    status?: ('pending' | 'running' | 'completed' | 'failed')[];
    direction?: ('push' | 'pull' | 'both')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => IntegrationSync[];
  getEvents: (options?: {
    integration?: string;
    type?: ('connect' | 'disconnect' | 'sync' | 'error' | 'custom')[];
    status?: ('success' | 'failure' | 'warning')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => IntegrationEvent[];
  getMetrics: (options?: {
    integration?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => IntegrationMetric[];
  connect: (id: string) => Promise<void>;
  disconnect: (id: string) => Promise<void>;
  validate: (id: string) => Promise<{
    valid: boolean;
    errors?: {
      path: string;
      message: string;
    }[];
  }>;
  transform: (id: string, data: any) => Promise<any>;
  getStats: () => IntegrationStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 