export type ValidationType = 
  | 'required'
  | 'string'
  | 'number'
  | 'boolean'
  | 'date'
  | 'email'
  | 'url'
  | 'pattern'
  | 'min'
  | 'max'
  | 'range'
  | 'length'
  | 'custom';

export type ValidationSeverity = 
  | 'error'
  | 'warning'
  | 'info';

export interface ValidationRule {
  type: ValidationType;
  severity: ValidationSeverity;
  message: string;
  params?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface ValidationConfig {
  id: string;
  name: string;
  rules: ValidationRule[];
  stopOnFirstError: boolean;
  metadata: Record<string, any>;
}

export interface ValidationSchema {
  type: string;
  required?: boolean;
  rules?: Record<string, any>;
  properties?: Record<string, ValidationSchema>;
  items?: ValidationSchema;
  metadata?: Record<string, any>;
}

export interface ValidationContext {
  field: string;
  value: any;
  form: Record<string, any>;
  schema: ValidationSchema;
  metadata?: Record<string, any>;
}

export interface ValidationOptions {
  mode?: 'sync' | 'async';
  stopOnFirstError?: boolean;
  metadata?: Record<string, any>;
}

export interface ValidationError {
  field: string;
  rule: ValidationRule;
  value: any;
  message: string;
  metadata?: Record<string, any>;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
  value: any;
  metadata?: Record<string, any>;
}

export interface ValidationState {
  isValid: boolean;
  isDirty: boolean;
  isTouched: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  metadata?: Record<string, any>;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  metadata?: Record<string, any>;
}

export interface ValidationStats {
  total: number;
  byType: Record<ValidationType, number>;
  bySeverity: Record<ValidationSeverity, number>;
  valid: number;
  invalid: number;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface ValidationService {
  createConfig: (config: Omit<ValidationConfig, 'id'>) => ValidationConfig;
  updateConfig: (id: string, config: Partial<ValidationConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => ValidationConfig | undefined;
  getConfigs: () => ValidationConfig[];
  validate: <T>(data: T, config: Omit<ValidationConfig, 'id'>) => ValidationResult;
  addRule: (config: Omit<ValidationConfig, 'id'>, rule: ValidationRule) => void;
  removeRule: (config: Omit<ValidationConfig, 'id'>, rule: ValidationRule) => void;
  getRules: (config: Omit<ValidationConfig, 'id'>) => ValidationRule[];
  clear: (config: Omit<ValidationConfig, 'id'>) => void;
  getStats: () => ValidationStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 