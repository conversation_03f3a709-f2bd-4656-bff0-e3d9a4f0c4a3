export type ComponentType = 'system' | 'user' | 'application' | 'custom';
export type ComponentStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type ComponentFormat = 'jsx' | 'tsx' | 'vue' | 'custom';

export interface ComponentConfig {
  management: {
    enabled: boolean;
    types: ComponentType[];
    formats: ComponentFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  component: {
    enabled: boolean;
    default: string;
    fallback: string;
    component: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: ComponentFormat;
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      component?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    component?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Component {
  id: string;
  config: ComponentConfig;
  type: ComponentType;
  status: ComponentStatus;
  format: ComponentFormat;
  name: string;
  description?: string;
  component: {
    component: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: ComponentFormat;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    component: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface ComponentLog {
  id: string;
  component: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface ComponentStats {
  total: number;
  byType: Record<ComponentType, number>;
  byStatus: Record<ComponentStatus, number>;
  byFormat: Record<ComponentFormat, number>;
  component: {
    total: number;
    byType?: Record<string, number>;
    byComponent?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byComponent?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byComponent?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface ComponentService {
  createConfig: (config: Omit<ComponentConfig, 'id'>) => ComponentConfig;
  updateConfig: (id: string, config: Partial<ComponentConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => ComponentConfig | undefined;
  getConfigs: () => ComponentConfig[];
  create: (config: Omit<ComponentConfig, 'id'>, component: Omit<Component, 'id' | 'config'>) => Promise<Component>;
  update: (id: string, component: Partial<Component>) => Promise<Component>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Component | undefined;
  getAll: (options?: {
    type?: ComponentType[];
    status?: ComponentStatus[];
    format?: ComponentFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Component[];
  search: (query: string, options?: {
    type?: ComponentType[];
    status?: ComponentStatus[];
    format?: ComponentFormat[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Component[];
  validate: (id: string, component: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    component?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => ComponentLog[];
  getStats: () => ComponentStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 