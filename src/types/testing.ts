import { ReactNode } from 'react';
import { User } from './auth';
import { Notification } from './notifications';

export interface TestConfig {
  id: string;
  name: string;
  type: TestType;
  timeout: number;
  retries: number;
  dependencies: string[];
  metadata: Record<string, any>;
}

export interface TestEnvironment {
  setup: () => Promise<void>;
  teardown: () => Promise<void>;
  getGlobals: () => Record<string, any>;
  setGlobals: (globals: Record<string, any>) => void;
  resetGlobals: () => void;
  getMock: (path: string) => any;
  setMock: (path: string, mock: any) => void;
  resetMock: (path: string) => void;
  clearMock: (path: string) => void;
  restoreMock: (path: string) => void;
  getSnapshot: (name: string) => string;
  setSnapshot: (name: string, snapshot: string) => void;
  resetSnapshot: (name: string) => void;
  clearSnapshot: (name: string) => void;
  updateSnapshot: (name: string, snapshot: string) => void;
}

export type TestType = 
  | 'unit'
  | 'integration'
  | 'e2e'
  | 'performance'
  | 'security'
  | 'custom';

export type TestStatus = 
  | 'pending'
  | 'running'
  | 'passed'
  | 'failed'
  | 'skipped'
  | 'custom';

export type TestPriority = 'low' | 'medium' | 'high' | 'critical';

export interface TestCase {
  id: string;
  config: TestConfig;
  status: TestStatus;
  startedAt?: Date;
  completedAt?: Date;
  duration?: number;
  error?: string;
  result?: any;
  metadata?: Record<string, any>;
}

export interface TestSuite {
  id: string;
  name: string;
  cases: TestCase[];
  status: TestStatus;
  startedAt?: Date;
  completedAt?: Date;
  duration?: number;
  metadata?: Record<string, any>;
}

export interface TestResult {
  id: string;
  case: TestCase;
  status: TestStatus;
  startTime: Date;
  endTime: Date;
  duration: number;
  error?: Error;
  steps: {
    status: TestStatus;
    error?: Error;
  }[];
  metadata?: Record<string, any>;
}

export interface TestReport {
  id: string;
  suite: TestSuite;
  results: TestResult[];
  startTime: Date;
  endTime: Date;
  duration: number;
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
    byType: Record<TestType, number>;
    byPriority: Record<TestPriority, number>;
  };
  metadata?: Record<string, any>;
}

export interface TestStats {
  total: number;
  byType: Record<TestType, number>;
  byStatus: Record<TestStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface TestService {
  createConfig: (config: Omit<TestConfig, 'id'>) => TestConfig;
  updateConfig: (id: string, config: Partial<TestConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => TestConfig | undefined;
  getConfigs: () => TestConfig[];
  run: (config: Omit<TestConfig, 'id'>) => Promise<TestCase>;
  runSuite: (cases: Omit<TestConfig, 'id'>[]) => Promise<TestSuite>;
  getCase: (id: string) => TestCase | undefined;
  getCases: () => TestCase[];
  getSuite: (id: string) => TestSuite | undefined;
  getSuites: () => TestSuite[];
  clear: () => Promise<void>;
  getStats: () => TestStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}

export interface MockNotification extends Notification {
  id: string;
  timestamp: number;
  read: boolean;
  dismissed: boolean;
  actioned: boolean;
  metadata: Record<string, any>;
}

export interface MockNotificationGroup {
  id: string;
  name: string;
  notifications: MockNotification[];
  metadata: Record<string, any>;
}

export interface TestNotificationContext {
  notifications: MockNotification[];
  groups: MockNotificationGroup[];
  addNotification: (notification: Partial<MockNotification>) => MockNotification;
  removeNotification: (id: string) => void;
  updateNotification: (id: string, updates: Partial<MockNotification>) => void;
  addGroup: (group: Partial<MockNotificationGroup>) => MockNotificationGroup;
  removeGroup: (id: string) => void;
  updateGroup: (id: string, updates: Partial<MockNotificationGroup>) => void;
  clearNotifications: () => void;
  clearGroups: () => void;
}

export interface TestRenderer {
  render: (element: ReactNode) => {
    container: HTMLElement;
    debug: () => void;
    rerender: (element: ReactNode) => void;
    unmount: () => void;
    getByTestId: (testId: string) => HTMLElement;
    getByText: (text: string) => HTMLElement;
    getByLabelText: (text: string) => HTMLElement;
    getByPlaceholderText: (text: string) => HTMLElement;
    getByRole: (role: string) => HTMLElement;
    queryByTestId: (testId: string) => HTMLElement | null;
    queryByText: (text: string) => HTMLElement | null;
    queryByLabelText: (text: string) => HTMLElement | null;
    queryByPlaceholderText: (text: string) => HTMLElement | null;
    queryByRole: (role: string) => HTMLElement | null;
    findByTestId: (testId: string) => Promise<HTMLElement>;
    findByText: (text: string) => Promise<HTMLElement>;
    findByLabelText: (text: string) => Promise<HTMLElement>;
    findByPlaceholderText: (text: string) => Promise<HTMLElement>;
    findByRole: (role: string) => Promise<HTMLElement>;
  };
}

export interface TestUtils {
  render: TestRenderer['render'];
  fireEvent: {
    click: (element: HTMLElement) => void;
    change: (element: HTMLElement, value: any) => void;
    submit: (element: HTMLElement) => void;
    keyDown: (element: HTMLElement, key: string) => void;
    keyUp: (element: HTMLElement, key: string) => void;
    keyPress: (element: HTMLElement, key: string) => void;
    mouseDown: (element: HTMLElement) => void;
    mouseUp: (element: HTMLElement) => void;
    mouseMove: (element: HTMLElement) => void;
    mouseEnter: (element: HTMLElement) => void;
    mouseLeave: (element: HTMLElement) => void;
    focus: (element: HTMLElement) => void;
    blur: (element: HTMLElement) => void;
  };
  act: (callback: () => void) => Promise<void>;
  waitFor: (callback: () => void, options?: { timeout?: number; interval?: number }) => Promise<void>;
  waitForElementToBeRemoved: (callback: () => HTMLElement | null, options?: { timeout?: number; interval?: number }) => Promise<void>;
  within: (element: HTMLElement) => TestRenderer['render'];
  screen: {
    getByTestId: (testId: string) => HTMLElement;
    getByText: (text: string) => HTMLElement;
    getByLabelText: (text: string) => HTMLElement;
    getByPlaceholderText: (text: string) => HTMLElement;
    getByRole: (role: string) => HTMLElement;
    queryByTestId: (testId: string) => HTMLElement | null;
    queryByText: (text: string) => HTMLElement | null;
    queryByLabelText: (text: string) => HTMLElement | null;
    queryByPlaceholderText: (text: string) => HTMLElement | null;
    queryByRole: (role: string) => HTMLElement | null;
    findByTestId: (testId: string) => Promise<HTMLElement>;
    findByText: (text: string) => Promise<HTMLElement>;
    findByLabelText: (text: string) => Promise<HTMLElement>;
    findByPlaceholderText: (text: string) => Promise<HTMLElement>;
    findByRole: (role: string) => Promise<HTMLElement>;
  };
}

export interface PerformanceMetrics {
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  totalBlockingTime: number;
  speedIndex: number;
  timeToInteractive: number;
  timeToFirstByte: number;
  domContentLoaded: number;
  domComplete: number;
  load: number;
}

export interface AccessibilityMetrics {
  violations: {
    id: string;
    impact: 'minor' | 'moderate' | 'serious' | 'critical';
    description: string;
    help: string;
    helpUrl: string;
    nodes: {
      html: string;
      target: string[];
      failureSummary: string;
    }[];
  }[];
  passes: {
    id: string;
    impact: null;
    description: string;
    help: string;
    helpUrl: string;
    nodes: {
      html: string;
      target: string[];
    }[];
  }[];
  incomplete: {
    id: string;
    impact: 'minor' | 'moderate' | 'serious' | 'critical';
    description: string;
    help: string;
    helpUrl: string;
    nodes: {
      html: string;
      target: string[];
    }[];
  }[];
}

export interface TestMetrics {
  performance: PerformanceMetrics;
  accessibility: AccessibilityMetrics;
  coverage: {
    branches: number;
    functions: number;
    lines: number;
    statements: number;
  };
  memory: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    arrayBuffers: number;
  };
  timing: {
    setup: number;
    teardown: number;
    test: number;
    total: number;
  };
}

export interface TestReporter {
  onRunStart: (config: TestConfig) => void;
  onRunComplete: (results: TestResult[]) => void;
  onTestStart: (test: TestCase) => void;
  onTestComplete: (test: TestCase, result: TestResult) => void;
  onSuiteStart: (suite: TestSuite) => void;
  onSuiteComplete: (suite: TestSuite, results: TestResult[]) => void;
  onError: (error: Error) => void;
  onWarning: (warning: string) => void;
  onDebug: (message: string) => void;
  onProgress: (progress: number) => void;
  onCoverage: (coverage: TestMetrics['coverage']) => void;
  onPerformance: (metrics: PerformanceMetrics) => void;
  onAccessibility: (metrics: AccessibilityMetrics) => void;
} 