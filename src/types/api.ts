export type ApiType = 'system' | 'user' | 'application' | 'custom';
export type ApiStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type ApiMethod = 'get' | 'post' | 'put' | 'delete' | 'patch' | 'custom';

export interface ApiConfig {
  management: {
    enabled: boolean;
    types: ApiType[];
    methods: ApiMethod[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  api: {
    enabled: boolean;
    default: string;
    fallback: string;
    api: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    method: ApiMethod;
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      api?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    api?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Api {
  id: string;
  config: ApiConfig;
  type: ApiType;
  status: ApiStatus;
  method: ApiMethod;
  name: string;
  description?: string;
  api: {
    api: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    method: ApiMethod;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    api: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface ApiLog {
  id: string;
  api: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface ApiStats {
  total: number;
  byType: Record<ApiType, number>;
  byStatus: Record<ApiStatus, number>;
  byMethod: Record<ApiMethod, number>;
  api: {
    total: number;
    byType?: Record<string, number>;
    byApi?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byApi?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byApi?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface ApiService {
  createConfig: (config: Omit<ApiConfig, 'id'>) => ApiConfig;
  updateConfig: (id: string, config: Partial<ApiConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => ApiConfig | undefined;
  getConfigs: () => ApiConfig[];
  create: (config: Omit<ApiConfig, 'id'>, api: Omit<Api, 'id' | 'config'>) => Promise<Api>;
  update: (id: string, api: Partial<Api>) => Promise<Api>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Api | undefined;
  getAll: (options?: {
    type?: ApiType[];
    status?: ApiStatus[];
    method?: ApiMethod[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Api[];
  search: (query: string, options?: {
    type?: ApiType[];
    status?: ApiStatus[];
    method?: ApiMethod[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Api[];
  validate: (id: string, api: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    api?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => ApiLog[];
  getStats: () => ApiStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 