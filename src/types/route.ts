export type RouteType = 'system' | 'user' | 'application' | 'custom';
export type RouteStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type RouteMethod = 'get' | 'post' | 'put' | 'delete' | 'patch' | 'custom';

export interface RouteConfig {
  management: {
    enabled: boolean;
    types: RouteType[];
    methods: RouteMethod[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  route: {
    enabled: boolean;
    default: string;
    fallback: string;
    route: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    method: RouteMethod;
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      route?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    route?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Route {
  id: string;
  config: RouteConfig;
  type: RouteType;
  status: RouteStatus;
  method: RouteMethod;
  name: string;
  description?: string;
  route: {
    route: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    method: RouteMethod;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    route: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface RouteLog {
  id: string;
  route: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface RouteStats {
  total: number;
  byType: Record<RouteType, number>;
  byStatus: Record<RouteStatus, number>;
  byMethod: Record<RouteMethod, number>;
  route: {
    total: number;
    byType?: Record<string, number>;
    byRoute?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byRoute?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byRoute?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface RouteService {
  createConfig: (config: Omit<RouteConfig, 'id'>) => RouteConfig;
  updateConfig: (id: string, config: Partial<RouteConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => RouteConfig | undefined;
  getConfigs: () => RouteConfig[];
  create: (config: Omit<RouteConfig, 'id'>, route: Omit<Route, 'id' | 'config'>) => Promise<Route>;
  update: (id: string, route: Partial<Route>) => Promise<Route>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Route | undefined;
  getAll: (options?: {
    type?: RouteType[];
    status?: RouteStatus[];
    method?: RouteMethod[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Route[];
  search: (query: string, options?: {
    type?: RouteType[];
    status?: RouteStatus[];
    method?: RouteMethod[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Route[];
  validate: (id: string, route: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    route?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => RouteLog[];
  getStats: () => RouteStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 