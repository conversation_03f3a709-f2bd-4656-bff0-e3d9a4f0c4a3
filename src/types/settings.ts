export type SettingsType = 'system' | 'user' | 'application' | 'custom';
export type SettingsStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type SettingsFormat = 'json' | 'yaml' | 'xml' | 'custom';

export interface SettingsConfig {
  management: {
    enabled: boolean;
    types: SettingsType[];
    formats: SettingsFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  settings: {
    enabled: boolean;
    default: string;
    fallback: string;
    settings: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: SettingsFormat;
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      settings?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    settings?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Settings {
  id: string;
  config: SettingsConfig;
  type: SettingsType;
  status: SettingsStatus;
  format: SettingsFormat;
  name: string;
  description?: string;
  settings: {
    settings: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: SettingsFormat;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    settings: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface SettingsLog {
  id: string;
  settings: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface SettingsStats {
  total: number;
  byType: Record<SettingsType, number>;
  byStatus: Record<SettingsStatus, number>;
  byFormat: Record<SettingsFormat, number>;
  settings: {
    total: number;
    byType?: Record<string, number>;
    bySettings?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    bySettings?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    bySettings?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface SettingsService {
  createConfig: (config: Omit<SettingsConfig, 'id'>) => SettingsConfig;
  updateConfig: (id: string, config: Partial<SettingsConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => SettingsConfig | undefined;
  getConfigs: () => SettingsConfig[];
  create: (config: Omit<SettingsConfig, 'id'>, settings: Omit<Settings, 'id' | 'config'>) => Promise<Settings>;
  update: (id: string, settings: Partial<Settings>) => Promise<Settings>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Settings | undefined;
  getAll: (options?: {
    type?: SettingsType[];
    status?: SettingsStatus[];
    format?: SettingsFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Settings[];
  search: (query: string, options?: {
    type?: SettingsType[];
    status?: SettingsStatus[];
    format?: SettingsFormat[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Settings[];
  validate: (id: string, settings: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    settings?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => SettingsLog[];
  getStats: () => SettingsStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 