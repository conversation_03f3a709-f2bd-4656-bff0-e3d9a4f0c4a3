export type FileType = 'text' | 'image' | 'video' | 'audio' | 'archive' | 'custom';
export type FileStatus = 'uploading' | 'processing' | 'ready' | 'error' | 'deleted';
export type FileFormat = 'txt' | 'jpg' | 'png' | 'mp4' | 'mp3' | 'zip' | 'custom';

export interface FileConfig {
  management: {
    enabled: boolean;
    types: FileType[];
    formats: FileFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'local' | 's3' | 'gcs' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  processing: {
    enabled: boolean;
    engine: 'local' | 'cloud' | 'custom';
    timeout: number;
    retry?: {
      enabled: boolean;
      maxAttempts: number;
      delay: number;
      backoff: number;
    };
    rateLimit?: {
      enabled: boolean;
      window: number;
      max: number;
    };
  };
  conversion: {
    enabled: boolean;
    formats: {
      from: FileFormat;
      to: FileFormat;
      engine: 'local' | 'cloud' | 'custom';
      options?: Record<string, any>;
    }[];
  };
  compression: {
    enabled: boolean;
    algorithm: 'gzip' | 'zip' | 'custom';
    level: number;
    options?: Record<string, any>;
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      storage?: boolean;
      processing?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    versioning?: boolean;
    conversion?: boolean;
    compression?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface File {
  id: string;
  config: FileConfig;
  type: FileType;
  status: FileStatus;
  name: string;
  description?: string;
  format: FileFormat;
  content: {
    type: 'text' | 'binary' | 'url' | 'custom';
    value: string | Buffer | URL;
    size: number;
    hash: string;
  };
  metadata?: {
    author?: {
      id: string;
      name?: string;
      email?: string;
    };
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  storage?: {
    path: string;
    url?: string;
    provider?: string;
    region?: string;
    bucket?: string;
  };
  processing?: {
    attempts: number;
    lastAttempt?: Date;
    nextAttempt?: Date;
    error?: {
      code: string;
      message: string;
      details?: any;
    };
  };
  conversion?: {
    formats: {
      from: FileFormat;
      to: FileFormat;
      status: 'pending' | 'processing' | 'completed' | 'failed';
      result?: {
        path: string;
        url?: string;
        size: number;
        hash: string;
      };
      error?: {
        code: string;
        message: string;
        details?: any;
      };
    }[];
  };
  compression?: {
    status: 'pending' | 'processing' | 'completed' | 'failed';
    result?: {
      path: string;
      url?: string;
      size: number;
      ratio: number;
      hash: string;
    };
    error?: {
      code: string;
      message: string;
      details?: any;
    };
  };
  stats?: {
    size: number;
    views: number;
    downloads: number;
    conversions: number;
  };
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface FileLog {
  id: string;
  file: string;
  action: 'create' | 'update' | 'delete' | 'convert' | 'compress' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface FileStats {
  total: number;
  byType: Record<FileType, number>;
  byStatus: Record<FileStatus, number>;
  byFormat: Record<FileFormat, number>;
  storage: {
    total: number;
    byType?: Record<FileType, number>;
    byFormat?: Record<FileFormat, number>;
    byDate?: Record<string, number>;
  };
  processing: {
    total: number;
    success: number;
    failure: number;
    byFile?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageSize: number;
    averageProcessingTime: number;
    byFile?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface FileService {
  createConfig: (config: Omit<FileConfig, 'id'>) => FileConfig;
  updateConfig: (id: string, config: Partial<FileConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => FileConfig | undefined;
  getConfigs: () => FileConfig[];
  create: (config: Omit<FileConfig, 'id'>, file: Omit<File, 'id' | 'config'>) => Promise<File>;
  update: (id: string, file: Partial<File>) => Promise<File>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => File | undefined;
  getAll: (options?: {
    type?: FileType[];
    status?: FileStatus[];
    format?: FileFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => File[];
  search: (query: string, options?: {
    type?: FileType[];
    status?: FileStatus[];
    format?: FileFormat[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => File[];
  convert: (id: string, format: FileFormat) => Promise<void>;
  compress: (id: string) => Promise<void>;
  getLogs: (options?: {
    file?: string;
    action?: ('create' | 'update' | 'delete' | 'convert' | 'compress' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => FileLog[];
  getStats: () => FileStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 