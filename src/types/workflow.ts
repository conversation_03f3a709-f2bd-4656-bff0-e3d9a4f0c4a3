export type WorkflowType = 
  | 'sequential'
  | 'parallel'
  | 'conditional'
  | 'loop'
  | 'custom';

export type WorkflowStatus = 
  | 'draft'
  | 'active'
  | 'paused'
  | 'completed'
  | 'failed'
  | 'custom';

export interface WorkflowConfig {
  id: string;
  name: string;
  settings: {
    execution: {
      type: WorkflowType;
      timeout: number;
      retry: {
        enabled: boolean;
        attempts: number;
        delay: number;
      };
      concurrency: number;
    };
    validation: {
      enabled: boolean;
      schema: Record<string, any>;
      strict: boolean;
    };
    monitoring: {
      enabled: boolean;
      metrics: boolean;
      alerts: boolean;
      logs: boolean;
    };
    persistence: {
      enabled: boolean;
      storage: 'memory' | 'database' | 'file' | 'custom';
      options?: Record<string, any>;
    };
  };
  features: {
    versioning: boolean;
    branching: boolean;
    rollback: boolean;
    scheduling: boolean;
  };
  metadata: Record<string, any>;
}

export interface Workflow {
  id: string;
  config: WorkflowConfig;
  name: string;
  description?: string;
  type: WorkflowType;
  status: WorkflowStatus;
  version: string;
  steps: {
    id: string;
    name: string;
    type: string;
    action: string;
    input?: Record<string, any>;
    output?: Record<string, any>;
    conditions?: {
      field: string;
      operator: string;
      value: any;
    }[];
    next?: string[];
    error?: string[];
    timeout?: number;
    retry?: {
      enabled: boolean;
      attempts: number;
      delay: number;
    };
  }[];
  variables: {
    name: string;
    type: string;
    value: any;
    description?: string;
  }[];
  schedule?: {
    type: 'once' | 'interval' | 'cron';
    value: string;
    timezone?: string;
  };
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface WorkflowInstance {
  id: string;
  config: WorkflowConfig;
  workflow: string;
  status: WorkflowStatus;
  input: Record<string, any>;
  output?: Record<string, any>;
  variables: Record<string, any>;
  steps: {
    id: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    input?: Record<string, any>;
    output?: Record<string, any>;
    error?: {
      code: string;
      message: string;
      details?: any;
    };
    startedAt?: Date;
    completedAt?: Date;
  }[];
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  startedAt: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface WorkflowVersion {
  id: string;
  config: WorkflowConfig;
  workflow: string;
  version: string;
  changes: {
    type: 'create' | 'update' | 'delete';
    field: string;
    oldValue?: any;
    newValue?: any;
  }[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface WorkflowLog {
  id: string;
  config: WorkflowConfig;
  workflow: string;
  instance?: string;
  step?: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  data?: Record<string, any>;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface WorkflowMetric {
  id: string;
  config: WorkflowConfig;
  workflow: string;
  timestamp: Date;
  metrics: {
    instances: {
      total: number;
      active: number;
      completed: number;
      failed: number;
    };
    steps: {
      total: number;
      completed: number;
      failed: number;
    };
    performance: {
      min: number;
      max: number;
      avg: number;
      p95: number;
      p99: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface WorkflowStats {
  total: number;
  byType: Record<WorkflowType, number>;
  byStatus: Record<WorkflowStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata: Record<string, any>;
}

export interface WorkflowService {
  createConfig: (config: Omit<WorkflowConfig, 'id'>) => WorkflowConfig;
  updateConfig: (id: string, config: Partial<WorkflowConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => WorkflowConfig | undefined;
  getConfigs: () => WorkflowConfig[];
  createWorkflow: (config: Omit<WorkflowConfig, 'id'>, workflow: Omit<Workflow, 'id' | 'config'>) => Promise<Workflow>;
  updateWorkflow: (id: string, workflow: Partial<Workflow>) => Promise<Workflow>;
  deleteWorkflow: (id: string) => Promise<void>;
  getWorkflow: (id: string) => Workflow | undefined;
  getWorkflows: (options?: {
    type?: WorkflowType[];
    status?: WorkflowStatus[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Workflow[];
  startInstance: (config: Omit<WorkflowConfig, 'id'>, options: {
    workflow: string;
    input?: Record<string, any>;
    variables?: Record<string, any>;
  }) => Promise<WorkflowInstance>;
  getInstance: (id: string) => WorkflowInstance | undefined;
  getInstances: (options?: {
    workflow?: string;
    status?: WorkflowStatus[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => WorkflowInstance[];
  pauseInstance: (id: string) => Promise<void>;
  resumeInstance: (id: string) => Promise<void>;
  stopInstance: (id: string) => Promise<void>;
  getVersions: (options?: {
    workflow?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => WorkflowVersion[];
  getLogs: (options?: {
    workflow?: string;
    instance?: string;
    step?: string;
    level?: ('info' | 'warning' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => WorkflowLog[];
  getMetrics: (options?: {
    workflow?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => WorkflowMetric[];
  validate: (id: string) => Promise<{
    valid: boolean;
    errors?: {
      path: string;
      message: string;
    }[];
  }>;
  getStats: () => WorkflowStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 