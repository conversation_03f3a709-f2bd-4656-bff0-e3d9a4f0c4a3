export type ProjectType = 
  | 'software'
  | 'design'
  | 'marketing'
  | 'research'
  | 'custom';

export type ProjectStatus = 
  | 'planning'
  | 'active'
  | 'on-hold'
  | 'completed'
  | 'cancelled'
  | 'custom';

export type ProjectPriority = 
  | 'low'
  | 'medium'
  | 'high'
  | 'urgent'
  | 'custom';

export interface ProjectConfig {
  id: string;
  name: string;
  type: ProjectType;
  settings: {
    visibility: 'public' | 'private';
    access: 'open' | 'restricted';
    notifications: boolean;
  };
  features: {
    tasks: boolean;
    milestones: boolean;
    timeTracking: boolean;
    documents: boolean;
  };
  metadata: Record<string, any>;
}

export interface Project {
  id: string;
  config: ProjectConfig;
  name: string;
  slug: string;
  description?: string;
  type: ProjectType;
  status: ProjectStatus;
  priority: ProjectPriority;
  organization: string;
  team: string;
  owner: string;
  startDate: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface ProjectMember {
  id: string;
  project: string;
  user: string;
  role: string;
  joinedAt: Date;
  invitedBy: string;
  status: 'active' | 'pending' | 'inactive';
  metadata?: Record<string, any>;
}

export interface ProjectMilestone {
  id: string;
  project: string;
  title: string;
  description?: string;
  dueDate: Date;
  completedAt?: Date;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  metadata?: Record<string, any>;
}

export interface ProjectStats {
  total: number;
  byType: Record<ProjectType, number>;
  byStatus: Record<ProjectStatus, number>;
  byPriority: Record<ProjectPriority, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface ProjectService {
  createConfig: (config: Omit<ProjectConfig, 'id'>) => ProjectConfig;
  updateConfig: (id: string, config: Partial<ProjectConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => ProjectConfig | undefined;
  getConfigs: () => ProjectConfig[];
  create: (config: Omit<ProjectConfig, 'id'>, project: Omit<Project, 'id' | 'config'>) => Promise<Project>;
  update: (id: string, project: Partial<Project>) => Promise<Project>;
  delete: (id: string) => Promise<void>;
  getProject: (id: string) => Project | undefined;
  getProjects: (options?: {
    type?: ProjectType;
    status?: ProjectStatus;
    priority?: ProjectPriority;
    organization?: string;
    team?: string;
    startDate?: Date;
    endDate?: Date;
  }) => Project[];
  addMember: (projectId: string, member: Omit<ProjectMember, 'id' | 'project'>) => Promise<ProjectMember>;
  removeMember: (projectId: string, memberId: string) => Promise<void>;
  getMember: (id: string) => ProjectMember | undefined;
  getMembers: (projectId: string) => ProjectMember[];
  createMilestone: (projectId: string, milestone: Omit<ProjectMilestone, 'id' | 'project'>) => Promise<ProjectMilestone>;
  updateMilestone: (id: string, milestone: Partial<ProjectMilestone>) => Promise<ProjectMilestone>;
  deleteMilestone: (id: string) => Promise<void>;
  getMilestone: (id: string) => ProjectMilestone | undefined;
  getMilestones: (projectId: string) => ProjectMilestone[];
  clear: () => Promise<void>;
  getStats: () => ProjectStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 