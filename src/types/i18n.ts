export type Locale = string;
export type TranslationKey = string;
export type TranslationValue = string;

export type LocaleType = 
  | 'language'
  | 'region'
  | 'script'
  | 'custom';

export type LocaleStatus = 
  | 'active'
  | 'inactive'
  | 'fallback'
  | 'custom';

export interface LocaleConfig {
  id: string;
  name: string;
  settings: {
    fallback: string;
    pluralization: boolean;
    interpolation: boolean;
    formatting: boolean;
    storage: {
      type: 'local' | 'database' | 'cache' | 'custom';
      encryption: boolean;
      compression: boolean;
    };
    caching: {
      enabled: boolean;
      ttl: number;
      strategy: 'memory' | 'redis' | 'custom';
    };
  };
  features: {
    dateTime: boolean;
    number: boolean;
    currency: boolean;
    measurement: boolean;
    export: boolean;
  };
  metadata: Record<string, any>;
}

export interface Translation {
  id: string;
  config: LocaleConfig;
  key: TranslationKey;
  value: TranslationValue;
  locale: Locale;
  namespace?: string;
  type: LocaleType;
  status: LocaleStatus;
  description?: string;
  tags?: string[];
  context?: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface TranslationNamespace {
  id: string;
  config: LocaleConfig;
  name: string;
  description?: string;
  translations: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface TranslationFormat {
  id: string;
  config: LocaleConfig;
  type: 'date' | 'time' | 'number' | 'currency';
  locale: Locale;
  format: string;
  example: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface TranslationExport {
  id: string;
  config: LocaleConfig;
  format: 'json' | 'yaml' | 'xml' | 'custom';
  locale: Locale;
  namespace?: string;
  data: any;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface TranslationImport {
  id: string;
  config: LocaleConfig;
  format: 'json' | 'yaml' | 'xml' | 'custom';
  locale: Locale;
  namespace?: string;
  data: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  metadata: Record<string, any>;
}

export interface TranslationLog {
  id: string;
  config: LocaleConfig;
  translation?: string;
  namespace?: string;
  format?: string;
  export?: string;
  import?: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  data?: Record<string, any>;
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface TranslationStats {
  total: number;
  byType: Record<LocaleType, number>;
  byStatus: Record<LocaleStatus, number>;
  byLocale: Record<Locale, number>;
  byNamespace: Record<string, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata: Record<string, any>;
}

export interface I18nService {
  createConfig: (config: Omit<LocaleConfig, 'id'>) => LocaleConfig;
  updateConfig: (id: string, config: Partial<LocaleConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => LocaleConfig | undefined;
  getConfigs: () => LocaleConfig[];
  createTranslation: (config: Omit<LocaleConfig, 'id'>, translation: Omit<Translation, 'id' | 'config'>) => Promise<Translation>;
  updateTranslation: (id: string, translation: Partial<Translation>) => Promise<Translation>;
  deleteTranslation: (id: string) => Promise<void>;
  getTranslation: (id: string) => Translation | undefined;
  getTranslations: (options?: {
    type?: LocaleType[];
    status?: LocaleStatus[];
    namespace?: string;
    locale?: Locale;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Translation[];
  createNamespace: (config: Omit<LocaleConfig, 'id'>, namespace: Omit<TranslationNamespace, 'id' | 'config'>) => Promise<TranslationNamespace>;
  updateNamespace: (id: string, namespace: Partial<TranslationNamespace>) => Promise<TranslationNamespace>;
  deleteNamespace: (id: string) => Promise<void>;
  getNamespace: (id: string) => TranslationNamespace | undefined;
  getNamespaces: (options?: {
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => TranslationNamespace[];
  createFormat: (config: Omit<LocaleConfig, 'id'>, format: Omit<TranslationFormat, 'id' | 'config'>) => Promise<TranslationFormat>;
  updateFormat: (id: string, format: Partial<TranslationFormat>) => Promise<TranslationFormat>;
  deleteFormat: (id: string) => Promise<void>;
  getFormat: (id: string) => TranslationFormat | undefined;
  getFormats: (options?: {
    type?: ('date' | 'time' | 'number' | 'currency')[];
    locale?: Locale;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => TranslationFormat[];
  exportTranslations: (config: Omit<LocaleConfig, 'id'>, options: {
    format: 'json' | 'yaml' | 'xml' | 'custom';
    locale: Locale;
    namespace?: string;
  }) => Promise<TranslationExport>;
  getExport: (id: string) => TranslationExport | undefined;
  getExports: (options?: {
    format?: ('json' | 'yaml' | 'xml' | 'custom')[];
    locale?: Locale;
    namespace?: string;
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => TranslationExport[];
  importTranslations: (config: Omit<LocaleConfig, 'id'>, options: {
    format: 'json' | 'yaml' | 'xml' | 'custom';
    locale: Locale;
    namespace?: string;
    data: any;
  }) => Promise<TranslationImport>;
  getImport: (id: string) => TranslationImport | undefined;
  getImports: (options?: {
    format?: ('json' | 'yaml' | 'xml' | 'custom')[];
    locale?: Locale;
    namespace?: string;
    status?: ('pending' | 'processing' | 'completed' | 'failed')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => TranslationImport[];
  getLogs: (options?: {
    translation?: string;
    namespace?: string;
    format?: string;
    export?: string;
    import?: string;
    level?: ('info' | 'warning' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => TranslationLog[];
  translate: (key: TranslationKey, options?: {
    locale?: Locale;
    namespace?: string;
    params?: Record<string, any>;
  }) => string;
  format: (value: any, type: 'date' | 'time' | 'number' | 'currency', options?: {
    locale?: Locale;
    format?: string;
  }) => string;
  detect: (text: string) => Promise<Locale>;
  validate: (id: string) => Promise<{
    valid: boolean;
    errors?: {
      path: string;
      message: string;
    }[];
  }>;
  getStats: () => TranslationStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 