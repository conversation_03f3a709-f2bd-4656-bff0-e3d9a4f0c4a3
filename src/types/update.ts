export type UpdateType = 
  | 'major'
  | 'minor'
  | 'patch'
  | 'custom';

export type UpdateStatus = 
  | 'available'
  | 'downloading'
  | 'installing'
  | 'completed'
  | 'failed'
  | 'custom';

export type UpdateChannel = 
  | 'stable'
  | 'beta'
  | 'alpha'
  | 'nightly'
  | 'custom';

export interface UpdateConfig {
  id: string;
  name: string;
  type: UpdateType;
  version: string;
  description: string;
  changelog: string;
  size: number;
  checksum: string;
  metadata: Record<string, any>;
}

export interface Update {
  id: string;
  config: UpdateConfig;
  status: UpdateStatus;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  metadata?: Record<string, any>;
}

export interface UpdateStats {
  total: number;
  byType: Record<UpdateType, number>;
  byStatus: Record<UpdateStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface UpdateService {
  createConfig: (config: Omit<UpdateConfig, 'id'>) => UpdateConfig;
  updateConfig: (id: string, config: Partial<UpdateConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => UpdateConfig | undefined;
  getConfigs: () => UpdateConfig[];
  check: () => Promise<Update | undefined>;
  download: (config: Omit<UpdateConfig, 'id'>) => Promise<Update>;
  install: (id: string) => Promise<void>;
  rollback: (id: string) => Promise<void>;
  getUpdate: (id: string) => Update | undefined;
  getUpdates: () => Update[];
  clear: () => Promise<void>;
  getStats: () => UpdateStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 