export type DocumentationType = 'system' | 'user' | 'application' | 'custom';
export type DocumentationStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type DocumentationFormat = 'markdown' | 'html' | 'pdf' | 'custom';

export interface DocumentationConfig {
  management: {
    enabled: boolean;
    types: DocumentationType[];
    formats: DocumentationFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  documentation: {
    enabled: boolean;
    default: string;
    fallback: string;
    documentation: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: DocumentationFormat;
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      documentation?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    documentation?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Documentation {
  id: string;
  config: DocumentationConfig;
  type: DocumentationType;
  status: DocumentationStatus;
  format: DocumentationFormat;
  name: string;
  description?: string;
  documentation: {
    documentation: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: DocumentationFormat;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    documentation: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface DocumentationLog {
  id: string;
  documentation: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface DocumentationStats {
  total: number;
  byType: Record<DocumentationType, number>;
  byStatus: Record<DocumentationStatus, number>;
  byFormat: Record<DocumentationFormat, number>;
  documentation: {
    total: number;
    byType?: Record<string, number>;
    byDocumentation?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byDocumentation?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byDocumentation?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface DocumentationService {
  createConfig: (config: Omit<DocumentationConfig, 'id'>) => DocumentationConfig;
  updateConfig: (id: string, config: Partial<DocumentationConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => DocumentationConfig | undefined;
  getConfigs: () => DocumentationConfig[];
  create: (config: Omit<DocumentationConfig, 'id'>, documentation: Omit<Documentation, 'id' | 'config'>) => Promise<Documentation>;
  update: (id: string, documentation: Partial<Documentation>) => Promise<Documentation>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Documentation | undefined;
  getAll: (options?: {
    type?: DocumentationType[];
    status?: DocumentationStatus[];
    format?: DocumentationFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Documentation[];
  search: (query: string, options?: {
    type?: DocumentationType[];
    status?: DocumentationStatus[];
    format?: DocumentationFormat[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Documentation[];
  validate: (id: string, documentation: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    documentation?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => DocumentationLog[];
  getStats: () => DocumentationStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 