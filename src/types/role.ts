export type RoleType = 'system' | 'user' | 'group' | 'custom';
export type RoleStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type RoleScope = 'global' | 'user' | 'group' | 'resource' | 'custom';

export interface RoleConfig {
  management: {
    enabled: boolean;
    types: RoleType[];
    scopes: RoleScope[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  policy: {
    enabled: boolean;
    engine: 'rbac' | 'abac' | 'custom';
    rules: {
      name: string;
      effect: 'allow' | 'deny';
      actions: string[];
      resources: string[];
      conditions?: Record<string, any>;
    }[];
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  enforcement: {
    enabled: boolean;
    mode: 'strict' | 'permissive' | 'custom';
    cache?: {
      enabled: boolean;
      ttl: number;
      maxSize: number;
    };
    audit?: {
      enabled: boolean;
      level: 'none' | 'basic' | 'detailed';
      storage: 'database' | 'file' | 'custom';
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      storage?: boolean;
      policy?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    policy?: boolean;
    enforcement?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Role {
  id: string;
  config: RoleConfig;
  type: RoleType;
  status: RoleStatus;
  scope: RoleScope;
  name: string;
  description?: string;
  policy: {
    rules: {
      name: string;
      effect: 'allow' | 'deny';
      actions: string[];
      resources: string[];
      conditions?: Record<string, any>;
    }[];
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  enforcement: {
    mode: 'strict' | 'permissive' | 'custom';
    cache?: {
      hits: number;
      misses: number;
      lastUpdated: Date;
    };
    audit?: {
      logs: {
        id: string;
        action: string;
        resource: string;
        user: string;
        result: 'allow' | 'deny';
        timestamp: Date;
        details?: Record<string, any>;
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  storage?: {
    path: string;
    url?: string;
    provider?: string;
    region?: string;
    bucket?: string;
  };
  stats?: {
    assignments: number;
    grants: number;
    denials: number;
    cache: {
      hits: number;
      misses: number;
    };
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface RoleLog {
  id: string;
  role: string;
  action: 'create' | 'update' | 'delete' | 'assign' | 'revoke' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface RoleStats {
  total: number;
  byType: Record<RoleType, number>;
  byStatus: Record<RoleStatus, number>;
  byScope: Record<RoleScope, number>;
  storage: {
    total: number;
    byType?: Record<RoleType, number>;
    byScope?: Record<RoleScope, number>;
    byDate?: Record<string, number>;
  };
  policy: {
    total: number;
    allowed: number;
    denied: number;
    byRole?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byRole?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface RoleService {
  createConfig: (config: Omit<RoleConfig, 'id'>) => RoleConfig;
  updateConfig: (id: string, config: Partial<RoleConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => RoleConfig | undefined;
  getConfigs: () => RoleConfig[];
  create: (config: Omit<RoleConfig, 'id'>, role: Omit<Role, 'id' | 'config'>) => Promise<Role>;
  update: (id: string, role: Partial<Role>) => Promise<Role>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Role | undefined;
  getAll: (options?: {
    type?: RoleType[];
    status?: RoleStatus[];
    scope?: RoleScope[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Role[];
  search: (query: string, options?: {
    type?: RoleType[];
    status?: RoleStatus[];
    scope?: RoleScope[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Role[];
  assign: (id: string, user: string, resource: string) => Promise<void>;
  revoke: (id: string, user: string, resource: string) => Promise<void>;
  check: (id: string, user: string, resource: string, action: string) => Promise<boolean>;
  getLogs: (options?: {
    role?: string;
    action?: ('create' | 'update' | 'delete' | 'assign' | 'revoke' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => RoleLog[];
  getStats: () => RoleStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 