export type ThemeType = 'system' | 'user' | 'application' | 'custom';
export type ThemeStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type ThemeFormat = 'css' | 'scss' | 'less' | 'custom';

export type ThemeColor = {
  name: string;
  value: string;
  contrast: string;
  variants: Record<string, string>;
};

export type ThemeSpacing = {
  unit: number;
  scale: Record<string, number>;
};

export type ThemeTypography = {
  fontFamily: string;
  fontSize: Record<string, number>;
  fontWeight: Record<string, number>;
  lineHeight: Record<string, number>;
  letterSpacing: Record<string, number>;
};

export type ThemeBreakpoint = {
  name: string;
  value: number;
};

export interface ThemeConfig {
  management: {
    enabled: boolean;
    types: ThemeType[];
    formats: ThemeFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  theme: {
    enabled: boolean;
    default: string;
    fallback: string;
    theme: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: ThemeFormat;
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      theme?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    theme?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Theme {
  id: string;
  config: ThemeConfig;
  type: ThemeType;
  status: ThemeStatus;
  format: ThemeFormat;
  name: string;
  description?: string;
  theme: {
    theme: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: ThemeFormat;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    theme: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface ThemeVariant {
  id: string;
  theme: string;
  name: string;
  type: ThemeType;
  styles: Record<string, any>;
  assets: {
    fonts: string[];
    images: string[];
    icons: string[];
  };
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface ThemeStyle {
  id: string;
  name: string;
  config: ThemeConfig;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface ThemeLog {
  id: string;
  theme: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface ThemeStats {
  total: number;
  byType: Record<ThemeType, number>;
  byStatus: Record<ThemeStatus, number>;
  byFormat: Record<ThemeFormat, number>;
  theme: {
    total: number;
    byType?: Record<string, number>;
    byTheme?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byTheme?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byTheme?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface ThemeService {
  createConfig: (config: Omit<ThemeConfig, 'id'>) => ThemeConfig;
  updateConfig: (id: string, config: Partial<ThemeConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => ThemeConfig | undefined;
  getConfigs: () => ThemeConfig[];
  create: (config: Omit<ThemeConfig, 'id'>, theme: Omit<Theme, 'id' | 'config'>) => Promise<Theme>;
  update: (id: string, theme: Partial<Theme>) => Promise<Theme>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Theme | undefined;
  getAll: (options?: {
    type?: ThemeType[];
    status?: ThemeStatus[];
    format?: ThemeFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Theme[];
  search: (query: string, options?: {
    type?: ThemeType[];
    status?: ThemeStatus[];
    format?: ThemeFormat[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Theme[];
  validate: (id: string, theme: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    theme?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => ThemeLog[];
  getStats: () => ThemeStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 