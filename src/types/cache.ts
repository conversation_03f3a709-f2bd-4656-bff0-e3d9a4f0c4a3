export type CacheType = 'system' | 'user' | 'application' | 'custom';
export type CacheStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type CacheFormat = 'memory' | 'file' | 'redis' | 'custom';

export interface CacheConfig {
  management: {
    enabled: boolean;
    types: CacheType[];
    formats: CacheFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  cache: {
    enabled: boolean;
    default: string;
    fallback: string;
    cache: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: CacheFormat;
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      cache?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    cache?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Cache {
  id: string;
  config: CacheConfig;
  type: CacheType;
  status: CacheStatus;
  format: CacheFormat;
  name: string;
  description?: string;
  cache: {
    cache: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: CacheFormat;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    cache: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface CacheLog {
  id: string;
  cache: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface CacheStats {
  total: number;
  byType: Record<CacheType, number>;
  byStatus: Record<CacheStatus, number>;
  byFormat: Record<CacheFormat, number>;
  cache: {
    total: number;
    byType?: Record<string, number>;
    byCache?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byCache?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byCache?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface CacheService {
  createConfig: (config: Omit<CacheConfig, 'id'>) => CacheConfig;
  updateConfig: (id: string, config: Partial<CacheConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => CacheConfig | undefined;
  getConfigs: () => CacheConfig[];
  create: (config: Omit<CacheConfig, 'id'>, cache: Omit<Cache, 'id' | 'config'>) => Promise<Cache>;
  update: (id: string, cache: Partial<Cache>) => Promise<Cache>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Cache | undefined;
  getAll: (options?: {
    type?: CacheType[];
    status?: CacheStatus[];
    format?: CacheFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Cache[];
  search: (query: string, options?: {
    type?: CacheType[];
    status?: CacheStatus[];
    format?: CacheFormat[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Cache[];
  validate: (id: string, cache: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    cache?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => CacheLog[];
  getStats: () => CacheStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 