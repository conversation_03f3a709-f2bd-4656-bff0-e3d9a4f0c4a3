export type MetricType = 
  | 'counter'
  | 'gauge'
  | 'histogram'
  | 'summary'
  | 'timer';

export type AlertSeverity = 'info' | 'warning' | 'error' | 'critical';

export type AlertStatus = 'active' | 'acknowledged' | 'resolved';

export interface Metric {
  id: string;
  name: string;
  type: MetricType;
  value: number;
  labels: Record<string, string>;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface MetricConfig {
  id: string;
  name: string;
  type: MetricType;
  description: string;
  labels: string[];
  interval: number;
  retention: number;
  metadata: Record<string, any>;
}

export interface Alert {
  id: string;
  name: string;
  description: string;
  severity: AlertSeverity;
  status: AlertStatus;
  condition: {
    metric: string;
    operator: string;
    threshold: number;
    duration: number;
  };
  actions: {
    type: string;
    config: Record<string, any>;
  }[];
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface AlertConfig {
  id: string;
  name: string;
  description: string;
  severity: AlertSeverity;
  condition: {
    metric: string;
    operator: string;
    threshold: number;
    duration: number;
  };
  actions: {
    type: string;
    config: Record<string, any>;
  }[];
  metadata: Record<string, any>;
}

export interface MonitoringStats {
  total: number;
  byType: Record<MetricType, number>;
  bySeverity: Record<AlertSeverity, number>;
  byStatus: Record<AlertStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface MonitoringService {
  createMetric: (config: Omit<MetricConfig, 'id'>) => MetricConfig;
  updateMetric: (id: string, config: Partial<MetricConfig>) => void;
  deleteMetric: (id: string) => void;
  getMetric: (id: string) => MetricConfig | undefined;
  getMetrics: () => MetricConfig[];
  recordMetric: (metric: Omit<Metric, 'id' | 'timestamp'>) => void;
  getMetricValue: (id: string, labels?: Record<string, string>) => number;
  getMetricHistory: (id: string, start: Date, end: Date) => Metric[];
  createAlert: (config: Omit<AlertConfig, 'id'>) => AlertConfig;
  updateAlert: (id: string, config: Partial<AlertConfig>) => void;
  deleteAlert: (id: string) => void;
  getAlert: (id: string) => AlertConfig | undefined;
  getAlerts: () => AlertConfig[];
  getActiveAlerts: () => Alert[];
  acknowledgeAlert: (id: string) => void;
  resolveAlert: (id: string) => void;
  getStats: () => MonitoringStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}

export type MonitoringType = 
  | 'system'
  | 'application'
  | 'network'
  | 'database'
  | 'security'
  | 'custom';

export type MonitoringStatus = 
  | 'healthy'
  | 'warning'
  | 'critical'
  | 'unknown'
  | 'custom';

export interface MonitoringConfig {
  id: string;
  name: string;
  type: MonitoringType;
  interval: number;
  timeout: number;
  retries: number;
  thresholds: MonitoringThresholds;
  metadata: Record<string, any>;
}

export interface MonitoringThresholds {
  warning: number;
  critical: number;
  metadata?: Record<string, any>;
}

export interface MonitoringCheck {
  id: string;
  config: MonitoringConfig;
  status: MonitoringStatus;
  lastCheck: Date;
  nextCheck: Date;
  value: number;
  error?: string;
  metadata?: Record<string, any>;
}

export interface MonitoringAlert {
  id: string;
  check: MonitoringCheck;
  level: MonitoringStatus;
  timestamp: Date;
  message: string;
  metadata?: Record<string, any>;
}

export interface MonitoringStats {
  total: number;
  byType: Record<MonitoringType, number>;
  byStatus: Record<MonitoringStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface MonitoringService {
  createConfig: (config: Omit<MonitoringConfig, 'id'>) => MonitoringConfig;
  updateConfig: (id: string, config: Partial<MonitoringConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => MonitoringConfig | undefined;
  getConfigs: () => MonitoringConfig[];
  start: (config: Omit<MonitoringConfig, 'id'>) => Promise<MonitoringCheck>;
  stop: (id: string) => Promise<void>;
  getCheck: (id: string) => MonitoringCheck | undefined;
  getChecks: () => MonitoringCheck[];
  getAlert: (id: string) => MonitoringAlert | undefined;
  getAlerts: () => MonitoringAlert[];
  clear: () => Promise<void>;
  getStats: () => MonitoringStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 