export type EventType = 
  | 'meeting'
  | 'task'
  | 'reminder'
  | 'holiday'
  | 'custom';

export type EventStatus = 
  | 'scheduled'
  | 'in-progress'
  | 'completed'
  | 'cancelled'
  | 'custom';

export interface CalendarConfig {
  id: string;
  name: string;
  settings: {
    timezone: string;
    workingHours: {
      start: string;
      end: string;
      days: number[];
    };
    defaultDuration: number;
  };
  features: {
    recurring: boolean;
    reminders: boolean;
    availability: boolean;
    sharing: boolean;
  };
  metadata: Record<string, any>;
}

export interface CalendarEvent {
  id: string;
  config: CalendarConfig;
  title: string;
  description?: string;
  type: EventType;
  status: EventStatus;
  project?: string;
  task?: string;
  organizer: string;
  attendees: string[];
  startTime: Date;
  endTime: Date;
  allDay: boolean;
  location?: string;
  recurring?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
    interval: number;
    endDate?: Date;
    exceptions: Date[];
  };
  reminders: {
    time: number;
    type: 'email' | 'notification' | 'sms';
  }[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface CalendarAvailability {
  id: string;
  user: string;
  startTime: Date;
  endTime: Date;
  status: 'available' | 'busy' | 'tentative' | 'unavailable';
  reason?: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface CalendarStats {
  total: number;
  byType: Record<EventType, number>;
  byStatus: Record<EventStatus, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  metadata?: Record<string, any>;
}

export interface CalendarService {
  createConfig: (config: Omit<CalendarConfig, 'id'>) => CalendarConfig;
  updateConfig: (id: string, config: Partial<CalendarConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => CalendarConfig | undefined;
  getConfigs: () => CalendarConfig[];
  createEvent: (config: Omit<CalendarConfig, 'id'>, event: Omit<CalendarEvent, 'id' | 'config'>) => Promise<CalendarEvent>;
  updateEvent: (id: string, event: Partial<CalendarEvent>) => Promise<CalendarEvent>;
  deleteEvent: (id: string) => Promise<void>;
  getEvent: (id: string) => CalendarEvent | undefined;
  getEvents: (options?: {
    type?: EventType;
    status?: EventStatus;
    project?: string;
    task?: string;
    organizer?: string;
    attendee?: string;
    startDate?: Date;
    endDate?: Date;
  }) => CalendarEvent[];
  setAvailability: (availability: Omit<CalendarAvailability, 'id'>) => Promise<CalendarAvailability>;
  updateAvailability: (id: string, availability: Partial<CalendarAvailability>) => Promise<CalendarAvailability>;
  deleteAvailability: (id: string) => Promise<void>;
  getAvailability: (id: string) => CalendarAvailability | undefined;
  getAvailabilities: (options?: {
    user?: string;
    status?: CalendarAvailability['status'];
    startDate?: Date;
    endDate?: Date;
  }) => CalendarAvailability[];
  clear: () => Promise<void>;
  getStats: () => CalendarStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 