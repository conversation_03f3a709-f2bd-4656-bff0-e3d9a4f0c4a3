export type NotificationType = 'system' | 'security' | 'activity' | 'custom';
export type NotificationStatus = 'pending' | 'sent' | 'delivered' | 'failed' | 'archived';
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';
export type NotificationChannel = 'email' | 'sms' | 'push' | 'webhook' | 'custom';

export interface NotificationConfig {
  management: {
    enabled: boolean;
    types: NotificationType[];
    channels: NotificationChannel[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'queue' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  delivery: {
    enabled: boolean;
    channels?: {
      email?: {
        enabled: boolean;
        provider: string;
        config: Record<string, any>;
      };
      sms?: {
        enabled: boolean;
        provider: string;
        config: Record<string, any>;
      };
      push?: {
        enabled: boolean;
        provider: string;
        config: Record<string, any>;
      };
      webhook?: {
        enabled: boolean;
        endpoints: {
          url: string;
          method: string;
          headers?: Record<string, string>;
          timeout?: number;
          retry?: number;
        }[];
      };
    };
    retry?: {
      enabled: boolean;
      maxAttempts: number;
      delay: number;
      backoff: number;
    };
    rateLimit?: {
      enabled: boolean;
      window: number;
      max: number;
    };
  };
  templates: {
    enabled: boolean;
    engine: 'handlebars' | 'ejs' | 'custom';
    defaultLocale: string;
    fallbackLocale: string;
    templates: {
      name: string;
      type: NotificationType;
      channel: NotificationChannel;
      subject?: string;
      body: string;
      variables?: string[];
      locales?: Record<string, {
        subject?: string;
        body: string;
      }>;
    }[];
  };
  security: {
    enabled: boolean;
    features?: {
      encryption?: boolean;
      signing?: boolean;
      verification?: boolean;
    };
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    signing?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    verification?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      delivery?: boolean;
      performance?: boolean;
      security?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    templates?: boolean;
    scheduling?: boolean;
    tracking?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Notification {
  id: string;
  config: NotificationConfig;
  type: NotificationType;
  status: NotificationStatus;
  channel: NotificationChannel;
  recipient: {
    id: string;
    type: 'user' | 'group' | 'system';
    email?: string;
    phone?: string;
    device?: string;
    webhook?: string;
  };
  template: string;
  data?: Record<string, any>;
  content?: {
    subject?: string;
    body: string;
    attachments?: {
      name: string;
      type: string;
      size: number;
      url: string;
    }[];
  };
  delivery?: {
    attempts: number;
    lastAttempt?: Date;
    nextAttempt?: Date;
    error?: {
      code: string;
      message: string;
      details?: any;
    };
  };
  tracking?: {
    sent?: Date;
    delivered?: Date;
    read?: Date;
    clicked?: Date;
    opened?: Date;
  };
  stats?: {
    attempts: number;
    success: number;
    failure: number;
  };
  createdAt: Date;
  updatedAt: Date;
  scheduledFor?: Date;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export interface NotificationTemplate {
  id: string;
  config: NotificationConfig;
  name: string;
  description?: string;
  type: NotificationType;
  channels: {
    email?: {
      subject: string;
      body: string;
      html?: boolean;
    };
    push?: {
      title: string;
      body: string;
      icon?: string;
      image?: string;
      action?: string;
    };
    sms?: {
      body: string;
    };
    webhook?: {
      body: Record<string, any>;
    };
  };
  variables: string[];
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface NotificationLog {
  id: string;
  notification: string;
  action: 'create' | 'update' | 'delete' | 'send' | 'deliver' | 'read' | 'click' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface NotificationMetric {
  id: string;
  config: NotificationConfig;
  timestamp: Date;
  metrics: {
    delivery: {
      sent: number;
      delivered: number;
      failed: number;
      rate: number;
    };
    channels: {
      email: {
        sent: number;
        delivered: number;
        failed: number;
        rate: number;
      };
      push: {
        sent: number;
        delivered: number;
        failed: number;
        rate: number;
      };
      sms: {
        sent: number;
        delivered: number;
        failed: number;
        rate: number;
      };
      webhook: {
        sent: number;
        delivered: number;
        failed: number;
        rate: number;
      };
    };
    performance: {
      queueSize: number;
      processingTime: number;
      deliveryTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface NotificationStats {
  total: number;
  byType: Record<NotificationType, number>;
  byStatus: Record<NotificationStatus, number>;
  byChannel: Record<NotificationChannel, number>;
  delivery: {
    total: number;
    success: number;
    failure: number;
    byNotification?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  tracking: {
    sent: number;
    delivered: number;
    read: number;
    clicked: number;
    opened: number;
    byNotification?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageResponseTime: number;
    byNotification?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface NotificationService {
  createConfig: (config: Omit<NotificationConfig, 'id'>) => NotificationConfig;
  updateConfig: (id: string, config: Partial<NotificationConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => NotificationConfig | undefined;
  getConfigs: () => NotificationConfig[];
  create: (config: Omit<NotificationConfig, 'id'>, notification: Omit<Notification, 'id' | 'config'>) => Promise<Notification>;
  update: (id: string, notification: Partial<Notification>) => Promise<Notification>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Notification | undefined;
  getAll: (options?: {
    type?: NotificationType[];
    status?: NotificationStatus[];
    channel?: NotificationChannel[];
    recipient?: string;
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Notification[];
  search: (query: string, options?: {
    type?: NotificationType[];
    status?: NotificationStatus[];
    channel?: NotificationChannel[];
    recipient?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Notification[];
  send: (id: string) => Promise<void>;
  schedule: (id: string, date: Date) => Promise<void>;
  cancel: (id: string) => Promise<void>;
  track: (id: string, event: 'delivered' | 'read' | 'clicked' | 'opened') => Promise<void>;
  getLogs: (options?: {
    notification?: string;
    action?: ('create' | 'update' | 'delete' | 'send' | 'deliver' | 'read' | 'click' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => NotificationLog[];
  getStats: () => NotificationStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 