export type DocsType = 'system' | 'user' | 'application' | 'custom';
export type DocsStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type DocsFormat = 'markdown' | 'html' | 'pdf' | 'custom';

export interface DocsConfig {
  management: {
    enabled: boolean;
    types: DocsType[];
    formats: DocsFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  docs: {
    enabled: boolean;
    default: string;
    fallback: string;
    formats: {
      content?: {
        type: string;
        format: DocsFormat;
        custom?: Record<string, any>;
      };
      structure?: {
        type: string;
        sections: string[];
        custom?: Record<string, any>;
      };
      navigation?: {
        type: string;
        items: string[];
        custom?: Record<string, any>;
      };
      search?: {
        type: string;
        index: string;
        custom?: Record<string, any>;
      };
      custom?: Record<string, any>;
    };
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      docs?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    docs?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Docs {
  id: string;
  config: DocsConfig;
  type: DocsType;
  status: DocsStatus;
  format: DocsFormat;
  name: string;
  description?: string;
  docs: {
    content: {
      type: string;
      format: DocsFormat;
      custom?: Record<string, any>;
    };
    structure: {
      type: string;
      sections: string[];
      custom?: Record<string, any>;
    };
    navigation: {
      type: string;
      items: string[];
      custom?: Record<string, any>;
    };
    search: {
      type: string;
      index: string;
      custom?: Record<string, any>;
    };
    custom?: Record<string, any>;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    docs: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface DocsLog {
  id: string;
  docs: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface DocsStats {
  total: number;
  byType: Record<DocsType, number>;
  byStatus: Record<DocsStatus, number>;
  byFormat: Record<DocsFormat, number>;
  docs: {
    total: number;
    byType?: Record<string, number>;
    byDocs?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byDocs?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byDocs?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface DocsService {
  createConfig: (config: Omit<DocsConfig, 'id'>) => DocsConfig;
  updateConfig: (id: string, config: Partial<DocsConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => DocsConfig | undefined;
  getConfigs: () => DocsConfig[];
  create: (config: Omit<DocsConfig, 'id'>, docs: Omit<Docs, 'id' | 'config'>) => Promise<Docs>;
  update: (id: string, docs: Partial<Docs>) => Promise<Docs>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Docs | undefined;
  getAll: (options?: {
    type?: DocsType[];
    status?: DocsStatus[];
    format?: DocsFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Docs[];
  search: (query: string, options?: {
    type?: DocsType[];
    status?: DocsStatus[];
    format?: DocsFormat[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Docs[];
  validate: (id: string, docs: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    docs?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => DocsLog[];
  getStats: () => DocsStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 