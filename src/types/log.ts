export type LogType = 'system' | 'application' | 'security' | 'audit' | 'custom';
export type LogLevel = 'debug' | 'info' | 'warning' | 'error' | 'fatal';
export type LogFormat = 'text' | 'json' | 'xml' | 'custom';

export interface LogConfig {
  management: {
    enabled: boolean;
    types: LogType[];
    levels: LogLevel[];
    formats: LogFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'file' | 'database' | 'elasticsearch' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    rotation?: {
      enabled: boolean;
      size: number;
      count: number;
      compress: boolean;
    };
  };
  processing: {
    enabled: boolean;
    filtering?: {
      enabled: boolean;
      rules: {
        field: string;
        operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'regex' | 'custom';
        value: any;
      }[];
    };
    transformation?: {
      enabled: boolean;
      rules: {
        field: string;
        type: 'map' | 'filter' | 'reduce' | 'custom';
        value: any;
      }[];
    };
    aggregation?: {
      enabled: boolean;
      rules: {
        field: string;
        type: 'count' | 'sum' | 'avg' | 'min' | 'max' | 'custom';
        window: number;
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      volume?: boolean;
      performance?: boolean;
      error?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    search?: boolean;
    analytics?: boolean;
    export?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Log {
  id: string;
  config: LogConfig;
  type: LogType;
  level: LogLevel;
  format: LogFormat;
  source: {
    type: 'system' | 'application' | 'service' | 'custom';
    id: string;
    name?: string;
  };
  message: string;
  data?: Record<string, any>;
  context?: {
    request?: {
      id: string;
      method: string;
      url: string;
      headers?: Record<string, string>;
      body?: any;
    };
    response?: {
      status: number;
      headers?: Record<string, string>;
      body?: any;
    };
    user?: {
      id: string;
      name?: string;
      email?: string;
    };
    session?: {
      id: string;
      token?: string;
    };
    environment?: {
      name: string;
      version?: string;
    };
  };
  stack?: {
    frames: {
      file: string;
      line: number;
      column: number;
      function: string;
      code?: string;
    }[];
  };
  stats?: {
    size: number;
    duration: number;
  };
  createdAt: Date;
  metadata?: Record<string, any>;
}

export interface LogStats {
  total: number;
  byType: Record<LogType, number>;
  byLevel: Record<LogLevel, number>;
  byFormat: Record<LogFormat, number>;
  volume: {
    total: number;
    byType?: Record<LogType, number>;
    byLevel?: Record<LogLevel, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageSize: number;
    byType?: Record<LogType, number>;
    byLevel?: Record<LogLevel, number>;
    byDate?: Record<string, number>;
  };
  errors: {
    total: number;
    byType?: Record<LogType, number>;
    byLevel?: Record<LogLevel, number>;
    byDate?: Record<string, number>;
  };
}

export interface LogService {
  createConfig: (config: Omit<LogConfig, 'id'>) => LogConfig;
  updateConfig: (id: string, config: Partial<LogConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => LogConfig | undefined;
  getConfigs: () => LogConfig[];
  create: (config: Omit<LogConfig, 'id'>, log: Omit<Log, 'id' | 'config'>) => Promise<Log>;
  update: (id: string, log: Partial<Log>) => Promise<Log>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Log | undefined;
  getAll: (options?: {
    type?: LogType[];
    level?: LogLevel[];
    format?: LogFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Log[];
  search: (query: string, options?: {
    type?: LogType[];
    level?: LogLevel[];
    format?: LogFormat[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Log[];
  export: (options?: {
    type?: LogType[];
    level?: LogLevel[];
    format?: LogFormat[];
    startDate?: Date;
    endDate?: Date;
    format: 'csv' | 'json' | 'xml' | 'custom';
  }) => Promise<string>;
  getStats: () => LogStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 