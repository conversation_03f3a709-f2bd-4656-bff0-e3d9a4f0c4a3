export type PermissionType = 'read' | 'write' | 'delete' | 'admin' | 'custom';
export type PermissionStatus = 'active' | 'inactive' | 'revoked' | 'deleted';
export type PermissionScope = 'global' | 'user' | 'group' | 'resource' | 'custom';

export interface PermissionConfig {
  management: {
    enabled: boolean;
    types: PermissionType[];
    scopes: PermissionScope[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  policy: {
    enabled: boolean;
    engine: 'rbac' | 'abac' | 'custom';
    rules: {
      name: string;
      effect: 'allow' | 'deny';
      actions: string[];
      resources: string[];
      conditions?: Record<string, any>;
    }[];
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  enforcement: {
    enabled: boolean;
    mode: 'strict' | 'permissive' | 'custom';
    cache?: {
      enabled: boolean;
      ttl: number;
      maxSize: number;
    };
    audit?: {
      enabled: boolean;
      level: 'none' | 'basic' | 'detailed';
      storage: 'database' | 'file' | 'custom';
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      storage?: boolean;
      policy?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    policy?: boolean;
    enforcement?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Permission {
  id: string;
  config: PermissionConfig;
  type: PermissionType;
  status: PermissionStatus;
  scope: PermissionScope;
  name: string;
  description?: string;
  policy: {
    rules: {
      name: string;
      effect: 'allow' | 'deny';
      actions: string[];
      resources: string[];
      conditions?: Record<string, any>;
    }[];
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  enforcement: {
    mode: 'strict' | 'permissive' | 'custom';
    cache?: {
      hits: number;
      misses: number;
      lastUpdated: Date;
    };
    audit?: {
      logs: {
        id: string;
        action: string;
        resource: string;
        user: string;
        result: 'allow' | 'deny';
        timestamp: Date;
        details?: Record<string, any>;
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  storage?: {
    path: string;
    url?: string;
    provider?: string;
    region?: string;
    bucket?: string;
  };
  stats?: {
    grants: number;
    denials: number;
    cache: {
      hits: number;
      misses: number;
    };
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  revokedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface PermissionLog {
  id: string;
  permission: string;
  action: 'create' | 'update' | 'delete' | 'grant' | 'revoke' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface PermissionStats {
  total: number;
  byType: Record<PermissionType, number>;
  byStatus: Record<PermissionStatus, number>;
  byScope: Record<PermissionScope, number>;
  storage: {
    total: number;
    byType?: Record<PermissionType, number>;
    byScope?: Record<PermissionScope, number>;
    byDate?: Record<string, number>;
  };
  policy: {
    total: number;
    allowed: number;
    denied: number;
    byPermission?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byPermission?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface PermissionService {
  createConfig: (config: Omit<PermissionConfig, 'id'>) => PermissionConfig;
  updateConfig: (id: string, config: Partial<PermissionConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => PermissionConfig | undefined;
  getConfigs: () => PermissionConfig[];
  create: (config: Omit<PermissionConfig, 'id'>, permission: Omit<Permission, 'id' | 'config'>) => Promise<Permission>;
  update: (id: string, permission: Partial<Permission>) => Promise<Permission>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Permission | undefined;
  getAll: (options?: {
    type?: PermissionType[];
    status?: PermissionStatus[];
    scope?: PermissionScope[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Permission[];
  search: (query: string, options?: {
    type?: PermissionType[];
    status?: PermissionStatus[];
    scope?: PermissionScope[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Permission[];
  grant: (id: string, user: string, resource: string) => Promise<void>;
  revoke: (id: string, user: string, resource: string) => Promise<void>;
  check: (id: string, user: string, resource: string, action: string) => Promise<boolean>;
  getLogs: (options?: {
    permission?: string;
    action?: ('create' | 'update' | 'delete' | 'grant' | 'revoke' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => PermissionLog[];
  getStats: () => PermissionStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 