export type InfrastructureType = 
  | 'compute'
  | 'storage'
  | 'network'
  | 'database'
  | 'custom';

export type InfrastructureStatus = 
  | 'pending'
  | 'provisioning'
  | 'running'
  | 'stopped'
  | 'terminated'
  | 'custom';

export type InfrastructureProvider = 
  | 'aws'
  | 'azure'
  | 'gcp'
  | 'on-premise'
  | 'custom';

export interface InfrastructureConfig {
  id: string;
  name: string;
  settings: {
    infrastructure: {
      enabled: boolean;
      type?: InfrastructureType;
      status?: InfrastructureStatus;
      provider?: InfrastructureProvider;
      region?: string;
      zone?: string;
    };
    compute: {
      enabled: boolean;
      type?: 'vm' | 'container' | 'serverless' | 'custom';
      resources?: {
        cpu?: number;
        memory?: number;
        storage?: number;
        network?: number;
      };
      scaling?: {
        enabled: boolean;
        min?: number;
        max?: number;
        target?: number;
      };
    };
    storage: {
      enabled: boolean;
      type?: 'block' | 'object' | 'file' | 'custom';
      size?: number;
      iops?: number;
      throughput?: number;
    };
    network: {
      enabled: boolean;
      type?: 'vpc' | 'subnet' | 'security-group' | 'custom';
      cidr?: string;
      ports?: number[];
      protocols?: string[];
    };
    database: {
      enabled: boolean;
      type?: 'sql' | 'nosql' | 'cache' | 'custom';
      engine?: string;
      version?: string;
      size?: number;
      backup?: {
        enabled: boolean;
        schedule?: string;
        retention?: number;
      };
    };
    monitoring: {
      enabled: boolean;
      metrics?: boolean;
      logs?: boolean;
      traces?: boolean;
    };
    backup: {
      enabled: boolean;
      schedule?: string;
      retention?: number;
      destination?: string;
    };
  };
  features: {
    automation: boolean;
    orchestration: boolean;
    security: boolean;
    compliance: boolean;
  };
  metadata: Record<string, any>;
}

export interface Infrastructure {
  id: string;
  config: InfrastructureConfig;
  type: InfrastructureType;
  status: InfrastructureStatus;
  provider: InfrastructureProvider;
  name: string;
  description?: string;
  region: string;
  zone: string;
  compute?: {
    type: string;
    resources: {
      cpu: number;
      memory: number;
      storage: number;
      network: number;
    };
    scaling?: {
      min: number;
      max: number;
      target: number;
      current: number;
    };
    instances?: {
      id: string;
      name: string;
      status: InfrastructureStatus;
      resources: {
        cpu: number;
        memory: number;
        storage: number;
        network: number;
      };
    }[];
  };
  storage?: {
    type: string;
    size: number;
    iops: number;
    throughput: number;
    volumes?: {
      id: string;
      name: string;
      size: number;
      type: string;
      status: InfrastructureStatus;
    }[];
  };
  network?: {
    type: string;
    cidr: string;
    subnets?: {
      id: string;
      name: string;
      cidr: string;
      zone: string;
    }[];
    securityGroups?: {
      id: string;
      name: string;
      rules: {
        type: string;
        protocol: string;
        port: number;
        source: string;
      }[];
    }[];
  };
  database?: {
    type: string;
    engine: string;
    version: string;
    size: number;
    backup?: {
      schedule: string;
      retention: number;
      lastBackup?: Date;
    };
    instances?: {
      id: string;
      name: string;
      status: InfrastructureStatus;
      endpoint: string;
      port: number;
    }[];
  };
  performance?: {
    cpu: number;
    memory: number;
    storage: number;
    network: number;
  };
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface InfrastructureLog {
  id: string;
  config: InfrastructureConfig;
  infrastructure: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  details?: Record<string, any>;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface InfrastructureStats {
  total: number;
  byType: Record<InfrastructureType, number>;
  byStatus: Record<InfrastructureStatus, number>;
  byProvider: Record<InfrastructureProvider, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  performance: {
    cpu: number;
    memory: number;
    storage: number;
    network: number;
  };
  metadata: Record<string, any>;
}

export interface InfrastructureService {
  createConfig: (config: Omit<InfrastructureConfig, 'id'>) => InfrastructureConfig;
  updateConfig: (id: string, config: Partial<InfrastructureConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => InfrastructureConfig | undefined;
  getConfigs: () => InfrastructureConfig[];
  create: (config: Omit<InfrastructureConfig, 'id'>, infrastructure: Omit<Infrastructure, 'id' | 'config'>) => Promise<Infrastructure>;
  update: (id: string, infrastructure: Partial<Infrastructure>) => Promise<Infrastructure>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Infrastructure | undefined;
  getAll: (options?: {
    type?: InfrastructureType[];
    status?: InfrastructureStatus[];
    provider?: InfrastructureProvider[];
    tags?: string[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Infrastructure[];
  search: (query: string, options?: {
    type?: InfrastructureType[];
    status?: InfrastructureStatus[];
    provider?: InfrastructureProvider[];
    tags?: string[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Infrastructure[];
  provision: (id: string, options?: {
    region?: string;
    zone?: string;
    resources?: Record<string, number>;
  }) => Promise<Infrastructure>;
  terminate: (id: string, options?: {
    force?: boolean;
  }) => Promise<Infrastructure>;
  scale: (id: string, options?: {
    compute?: {
      min?: number;
      max?: number;
      target?: number;
    };
    storage?: {
      size?: number;
      iops?: number;
      throughput?: number;
    };
  }) => Promise<Infrastructure>;
  getLogs: (options?: {
    infrastructure?: string;
    level?: ('info' | 'warning' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => InfrastructureLog[];
  backup: (id: string, options?: {
    type?: 'full' | 'incremental';
    destination?: string;
    compression?: boolean;
  }) => Promise<string>;
  restore: (id: string, backup: string, options?: {
    validate?: boolean;
  }) => Promise<Infrastructure>;
  validate: (id: string) => Promise<{
    valid: boolean;
    errors?: {
      path: string;
      message: string;
    }[];
  }>;
  getStats: () => InfrastructureStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 