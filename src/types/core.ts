import { ReactNode } from 'react';

export type Theme = 'light' | 'dark' | 'system';
export type Language = 'en' | 'ru' | 'zh' | 'es' | 'fr' | 'de' | 'ja' | 'ko';
export type Platform = 'web' | 'desktop' | 'mobile';
export type Environment = 'development' | 'staging' | 'production';

export interface BaseConfig {
  theme: Theme;
  language: Language;
  platform: Platform;
  environment: Environment;
}

export interface FeatureFlags {
  [key: string]: boolean;
}

export interface UserPreferences {
  theme: Theme;
  language: Language;
  notifications: boolean;
  sound: boolean;
  vibration: boolean;
  accessibility: {
    highContrast: boolean;
    reducedMotion: boolean;
    screenReader: boolean;
    fontSize: number;
  };
  privacy: {
    analytics: boolean;
    telemetry: boolean;
    crashReports: boolean;
  };
}

export interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export interface PerformanceMetrics {
  loadTime: number;
  firstPaint: number;
  firstContentfulPaint: number;
  timeToInteractive: number;
  domSize: number;
  jsHeapSize: number;
  jsEventListeners: number;
  layoutThrashing: number;
  longTasks: number;
  memoryUsage: number;
  networkRequests: number;
  cacheHits: number;
  cacheMisses: number;
}

export interface AnalyticsEvent {
  category: string;
  action: string;
  label?: string;
  value?: number;
  nonInteraction?: boolean;
  transport?: 'beacon' | 'xhr' | 'image';
  hitCallback?: () => void;
  customDimensions?: Record<string, string>;
  customMetrics?: Record<string, number>;
}

export interface CacheConfig {
  maxSize: number;
  maxAge: number;
  strategy: 'memory' | 'localStorage' | 'indexedDB';
  version: string;
  prefix: string;
}

export interface SecurityConfig {
  encryption: {
    algorithm: string;
    keySize: number;
    iterations: number;
  };
  validation: {
    maxLength: number;
    minLength: number;
    pattern: RegExp;
  };
  rateLimit: {
    windowMs: number;
    max: number;
  };
  cors: {
    origin: string[];
    methods: string[];
    credentials: boolean;
  };
}

export interface NetworkConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
  headers: Record<string, string>;
  withCredentials: boolean;
  validateStatus: (status: number) => boolean;
}

export interface LoggerConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  format: 'json' | 'text';
  destination: 'console' | 'file' | 'remote';
  maxSize: number;
  maxFiles: number;
  compress: boolean;
}

export interface AppConfig extends BaseConfig {
  features: FeatureFlags;
  preferences: UserPreferences;
  cache: CacheConfig;
  security: SecurityConfig;
  network: NetworkConfig;
  logger: LoggerConfig;
} 