export type AuditType = 'security' | 'system' | 'user' | 'custom';
export type AuditStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type AuditLevel = 'info' | 'warning' | 'error' | 'critical';

export interface AuditConfig {
  management: {
    enabled: boolean;
    types: AuditType[];
    levels: AuditLevel[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  logging: {
    enabled: boolean;
    format: 'json' | 'text' | 'custom';
    fields: {
      timestamp: boolean;
      type: boolean;
      level: boolean;
      source: boolean;
      action: boolean;
      user: boolean;
      resource: boolean;
      details: boolean;
      custom?: string[];
    };
    rotation: {
      enabled: boolean;
      size: number;
      interval: string;
      compression: boolean;
    };
    retention: {
      enabled: boolean;
      duration: string;
      maxSize: number;
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      logging?: boolean;
      analysis?: boolean;
      alerts?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    logging?: boolean;
    analysis?: boolean;
    monitoring?: boolean;
    alerts?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Audit {
  id: string;
  config: AuditConfig;
  type: AuditType;
  status: AuditStatus;
  level: AuditLevel;
  source: {
    type: string;
    id: string;
    name?: string;
  };
  action: {
    type: string;
    name: string;
    details?: Record<string, any>;
  };
  user?: {
    id: string;
    name?: string;
    email?: string;
    role?: string;
  };
  resource?: {
    type: string;
    id: string;
    name?: string;
  };
  details?: Record<string, any>;
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    logs: number;
    analysis: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface AuditLog {
  id: string;
  audit: string;
  action: 'create' | 'update' | 'delete' | 'analyze' | 'alert' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface AuditStats {
  total: number;
  byType: Record<AuditType, number>;
  byStatus: Record<AuditStatus, number>;
  byLevel: Record<AuditLevel, number>;
  logging: {
    total: number;
    byType?: Record<string, number>;
    bySource?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  analysis: {
    total: number;
    byType?: Record<string, number>;
    bySource?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  alerts: {
    total: number;
    byType?: Record<string, number>;
    bySource?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface AuditService {
  createConfig: (config: Omit<AuditConfig, 'id'>) => AuditConfig;
  updateConfig: (id: string, config: Partial<AuditConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => AuditConfig | undefined;
  getConfigs: () => AuditConfig[];
  create: (config: Omit<AuditConfig, 'id'>, audit: Omit<Audit, 'id' | 'config'>) => Promise<Audit>;
  update: (id: string, audit: Partial<Audit>) => Promise<Audit>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Audit | undefined;
  getAll: (options?: {
    type?: AuditType[];
    status?: AuditStatus[];
    level?: AuditLevel[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Audit[];
  search: (query: string, options?: {
    type?: AuditType[];
    status?: AuditStatus[];
    level?: AuditLevel[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Audit[];
  analyze: (id: string, data: Record<string, any>) => Promise<Record<string, any>>;
  getLogs: (options?: {
    audit?: string;
    action?: ('create' | 'update' | 'delete' | 'analyze' | 'alert' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => AuditLog[];
  getStats: () => AuditStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 
} 