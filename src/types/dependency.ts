export type DependencyType = 'package' | 'library' | 'service' | 'custom';
export type DependencyStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type DependencyScope = 'global' | 'user' | 'group' | 'custom';

export interface DependencyConfig {
  management: {
    enabled: boolean;
    types: DependencyType[];
    scopes: DependencyScope[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  dependencies: {
    enabled: boolean;
    types: {
      direct?: boolean;
      indirect?: boolean;
      optional?: boolean;
      dev?: boolean;
      peer?: boolean;
      custom?: boolean;
    };
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        type: string;
        required: boolean;
        version?: string;
        range?: string;
        custom?: Record<string, any>;
      }[];
    };
    resolution?: {
      enabled: boolean;
      strategy: 'latest' | 'fixed' | 'range' | 'custom';
      rules?: {
        name: string;
        strategy: string;
        version?: string;
        range?: string;
        custom?: Record<string, any>;
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      dependencies?: boolean;
      updates?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    dependencies?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Dependency {
  id: string;
  config: DependencyConfig;
  type: DependencyType;
  status: DependencyStatus;
  scope: DependencyScope;
  name: string;
  description?: string;
  version: string;
  dependencies: {
    direct: {
      name: string;
      version: string;
      type: 'direct' | 'indirect' | 'optional' | 'dev' | 'peer' | 'custom';
      required: boolean;
      range?: string;
      custom?: Record<string, any>;
    }[];
    indirect: {
      name: string;
      version: string;
      type: 'direct' | 'indirect' | 'optional' | 'dev' | 'peer' | 'custom';
      required: boolean;
      range?: string;
      custom?: Record<string, any>;
    }[];
    optional: {
      name: string;
      version: string;
      type: 'direct' | 'indirect' | 'optional' | 'dev' | 'peer' | 'custom';
      required: boolean;
      range?: string;
      custom?: Record<string, any>;
    }[];
    dev: {
      name: string;
      version: string;
      type: 'direct' | 'indirect' | 'optional' | 'dev' | 'peer' | 'custom';
      required: boolean;
      range?: string;
      custom?: Record<string, any>;
    }[];
    peer: {
      name: string;
      version: string;
      type: 'direct' | 'indirect' | 'optional' | 'dev' | 'peer' | 'custom';
      required: boolean;
      range?: string;
      custom?: Record<string, any>;
    }[];
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    dependencies: number;
    updates: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface DependencyLog {
  id: string;
  dependency: string;
  action: 'create' | 'update' | 'delete' | 'install' | 'uninstall' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface DependencyStats {
  total: number;
  byType: Record<DependencyType, number>;
  byStatus: Record<DependencyStatus, number>;
  byScope: Record<DependencyScope, number>;
  dependencies: {
    total: number;
    byType?: Record<string, number>;
    byDependency?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  updates: {
    total: number;
    byType?: Record<string, number>;
    byDependency?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byDependency?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface DependencyService {
  createConfig: (config: Omit<DependencyConfig, 'id'>) => DependencyConfig;
  updateConfig: (id: string, config: Partial<DependencyConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => DependencyConfig | undefined;
  getConfigs: () => DependencyConfig[];
  create: (config: Omit<DependencyConfig, 'id'>, dependency: Omit<Dependency, 'id' | 'config'>) => Promise<Dependency>;
  update: (id: string, dependency: Partial<Dependency>) => Promise<Dependency>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Dependency | undefined;
  getAll: (options?: {
    type?: DependencyType[];
    status?: DependencyStatus[];
    scope?: DependencyScope[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Dependency[];
  search: (query: string, options?: {
    type?: DependencyType[];
    status?: DependencyStatus[];
    scope?: DependencyScope[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Dependency[];
  install: (id: string, dependencies: Record<string, any>) => Promise<void>;
  uninstall: (id: string, dependencies: string[]) => Promise<void>;
  getLogs: (options?: {
    dependency?: string;
    action?: ('create' | 'update' | 'delete' | 'install' | 'uninstall' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => DependencyLog[];
  getStats: () => DependencyStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 