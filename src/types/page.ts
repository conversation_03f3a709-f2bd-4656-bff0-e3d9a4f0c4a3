export type PageType = 'system' | 'user' | 'application' | 'custom';
export type PageStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type PageFormat = 'html' | 'jsx' | 'tsx' | 'custom';

export interface PageConfig {
  management: {
    enabled: boolean;
    types: PageType[];
    formats: PageFormat[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  page: {
    enabled: boolean;
    default: string;
    fallback: string;
    page: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: PageFormat;
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      page?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    page?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Page {
  id: string;
  config: PageConfig;
  type: PageType;
  status: PageStatus;
  format: PageFormat;
  name: string;
  description?: string;
  page: {
    page: {
      name: string;
      value: string;
      metadata?: Record<string, any>;
    }[];
    format: PageFormat;
    validation?: {
      rules: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    page: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface PageLog {
  id: string;
  page: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface PageStats {
  total: number;
  byType: Record<PageType, number>;
  byStatus: Record<PageStatus, number>;
  byFormat: Record<PageFormat, number>;
  page: {
    total: number;
    byType?: Record<string, number>;
    byPage?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byPage?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byPage?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface PageService {
  createConfig: (config: Omit<PageConfig, 'id'>) => PageConfig;
  updateConfig: (id: string, config: Partial<PageConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => PageConfig | undefined;
  getConfigs: () => PageConfig[];
  create: (config: Omit<PageConfig, 'id'>, page: Omit<Page, 'id' | 'config'>) => Promise<Page>;
  update: (id: string, page: Partial<Page>) => Promise<Page>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Page | undefined;
  getAll: (options?: {
    type?: PageType[];
    status?: PageStatus[];
    format?: PageFormat[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Page[];
  search: (query: string, options?: {
    type?: PageType[];
    status?: PageStatus[];
    format?: PageFormat[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Page[];
  validate: (id: string, page: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    page?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => PageLog[];
  getStats: () => PageStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 