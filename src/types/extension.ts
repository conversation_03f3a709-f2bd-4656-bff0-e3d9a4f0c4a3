export interface ExtensionAuthor {
  name: string;
  email: string;
  website?: string;
}

export interface ExtensionCompatibility {
  minVersion: string;
  maxVersion?: string;
}

export interface ExtensionIcon {
  '16': string;
  '48': string;
  '128': string;
}

export interface ExtensionAction {
  defaultIcon?: ExtensionIcon;
  defaultTitle?: string;
  defaultPopup?: string;
}

export interface ContentScript {
  matches: string[];
  excludeMatches?: string[];
  js: string[];
  css?: string[];
  runAt?: 'document_start' | 'document_end' | 'document_idle';
}

export interface ExtensionManifest {
  id: string;
  name: string;
  version: string;
  description: string;
  author: ExtensionAuthor;
  compatibility: ExtensionCompatibility;
  license: 'MIT' | 'Apache-2.0' | 'GPL-3.0' | 'BSD-3-Clause' | 'ISC' | 'UNLICENSED';
  permissions?: Array<
    | 'storage'
    | 'tabs'
    | 'bookmarks'
    | 'history'
    | 'downloads'
    | 'notifications'
    | 'webNavigation'
    | 'webRequest'
    | 'cookies'
    | 'geolocation'
    | 'clipboardRead'
    | 'clipboardWrite'
  >;
  hostPermissions?: string[];
  background?: {
    serviceWorker?: string;
    type?: 'module' | 'classic';
  };
  contentScripts?: ContentScript[];
  webAccessibleResources?: string[];
  icons: ExtensionIcon;
  action?: ExtensionAction;
  optionsPage?: string;
  minimumChromeVersion?: string;
  updateUrl?: string;
  homepageUrl?: string;
  privacyPolicyUrl?: string;
  contentSecurityPolicy?: string;
}

export interface ExtensionSettings {
  enabled: boolean;
  autoUpdate: boolean;
  notifications: boolean;
  permissions: string[];
  lastUpdated: string;
  [key: string]: any;
}

export interface ExtensionError {
  code: string;
  message: string;
  timestamp: string;
  details?: any;
}

export interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: ExtensionAuthor;
  icon: string;
  enabled: boolean;
  permissions: string[];
  settings: ExtensionSettings;
  error?: ExtensionError;
  updateAvailable: boolean;
  installedAt: string;
  updatedAt: string;
  size: number;
  source: 'store' | 'file' | 'url';
  rating?: number;
  downloads?: number;
  reviews?: number;
  compatibility: ExtensionCompatibility;
  license: string;
  homepageUrl?: string;
  privacyPolicyUrl?: string;
  contentSecurityPolicy?: string;
}

export interface ExtensionState {
  extensions: Extension[];
  loading: boolean;
  error: string | null;
  selectedExtension: string | null;
  filters: {
    enabled: boolean | null;
    source: ('store' | 'file' | 'url')[];
    permissions: string[];
    search: string;
  };
  sort: {
    field: keyof Extension;
    direction: 'asc' | 'desc';
  };
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

export interface ExtensionProcess {
  id: string;
  extensionId: string;
  type: 'background' | 'content' | 'popup' | 'options';
  status: 'running' | 'stopped' | 'error';
  startTime: string;
  memoryUsage: number;
  cpuUsage: number;
  error?: ExtensionError;
}

export interface ExtensionMetrics {
  id: string;
  extensionId: string;
  timestamp: string;
  memoryUsage: number;
  cpuUsage: number;
  networkRequests: number;
  errors: number;
  warnings: number;
  performance: {
    loadTime: number;
    responseTime: number;
    renderTime: number;
  };
}

export interface ExtensionReview {
  id: string;
  extensionId: string;
  userId: string;
  rating: number;
  comment: string;
  timestamp: string;
  helpful: number;
  reported: boolean;
  status: 'pending' | 'approved' | 'rejected';
}

export interface ExtensionReport {
  id: string;
  extensionId: string;
  userId: string;
  reason: string;
  details: string;
  timestamp: string;
  status: 'pending' | 'investigating' | 'resolved' | 'dismissed';
  resolution?: string;
}

export interface ExtensionAnalytics {
  id: string;
  extensionId: string;
  date: string;
  installs: number;
  uninstalls: number;
  activeUsers: number;
  crashes: number;
  errors: number;
  performance: {
    averageLoadTime: number;
    averageResponseTime: number;
    averageMemoryUsage: number;
    averageCpuUsage: number;
  };
  userFeedback: {
    ratings: number[];
    reviews: number;
    reports: number;
  };
}

export interface ExtensionStore {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  featured: boolean;
  promoted: boolean;
  verified: boolean;
  rating: number;
  downloads: number;
  reviewCount: number;
  lastUpdated: string;
  compatibility: ExtensionCompatibility;
  price: number;
  currency: string;
  trial: boolean;
  trialDays: number;
  subscription: boolean;
  subscriptionPeriod: 'monthly' | 'yearly';
  subscriptionPrice: number;
  developer: {
    id: string;
    name: string;
    verified: boolean;
    extensions: number;
    rating: number;
  };
  analytics: ExtensionAnalytics;
  reviewList: ExtensionReview[];
  reports: ExtensionReport[];
}

export interface ExtensionStoreState {
  extensions: ExtensionStore[];
  loading: boolean;
  error: string | null;
  filters: {
    category: string[];
    tags: string[];
    price: [number, number];
    rating: number;
    compatibility: boolean;
    verified: boolean;
    featured: boolean;
    promoted: boolean;
    search: string;
  };
  sort: {
    field: keyof ExtensionStore;
    direction: 'asc' | 'desc';
  };
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

export interface ExtensionDeveloper {
  id: string;
  name: string;
  email: string;
  website?: string;
  verified: boolean;
  extensions: string[];
  rating: number;
  joined: string;
  lastActive: string;
  analytics: {
    totalDownloads: number;
    totalRevenue: number;
    activeUsers: number;
    averageRating: number;
  };
  settings: {
    notifications: boolean;
    autoPublish: boolean;
    analytics: boolean;
    [key: string]: any;
  };
}

export interface ExtensionModerator {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'moderator' | 'reviewer';
  permissions: string[];
  assignedCategories: string[];
  statistics: {
    reviewsProcessed: number;
    reportsHandled: number;
    extensionsRemoved: number;
    averageResponseTime: number;
  };
  settings: {
    notifications: boolean;
    autoAssign: boolean;
    [key: string]: any;
  };
} 