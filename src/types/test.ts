export type TestType = 
  | 'unit'
  | 'integration'
  | 'e2e'
  | 'performance'
  | 'security'
  | 'custom';

export type TestStatus = 
  | 'pending'
  | 'running'
  | 'passed'
  | 'failed'
  | 'skipped'
  | 'custom';

export type TestPriority = 
  | 'low'
  | 'medium'
  | 'high'
  | 'critical';

export interface TestConfig {
  id: string;
  name: string;
  settings: {
    execution: {
      enabled: boolean;
      type?: TestType;
      status?: TestStatus;
      priority?: TestPriority;
      timeout?: number;
      retries?: number;
      parallel?: boolean;
    };
    environment: {
      enabled: boolean;
      type?: 'local' | 'staging' | 'production' | 'custom';
      variables?: Record<string, any>;
      setup?: Record<string, any>;
      teardown?: Record<string, any>;
    };
    coverage: {
      enabled: boolean;
      threshold?: number;
      reporters?: ('text' | 'html' | 'json' | 'lcov')[];
      exclude?: string[];
    };
    reporting: {
      enabled: boolean;
      format?: ('junit' | 'json' | 'html' | 'custom')[];
      destination?: string;
      notifications?: {
        enabled: boolean;
        channels: ('email' | 'slack' | 'webhook')[];
        template?: string;
      };
    };
    monitoring: {
      enabled: boolean;
      metrics?: boolean;
      logs?: boolean;
      traces?: boolean;
    };
  };
  features: {
    mocking: boolean;
    fixtures: boolean;
    snapshots: boolean;
    analytics: boolean;
  };
  metadata: Record<string, any>;
}

export interface Test {
  id: string;
  config: TestConfig;
  type: TestType;
  status: TestStatus;
  priority: TestPriority;
  name: string;
  description?: string;
  suite?: string;
  file?: string;
  line?: number;
  column?: number;
  steps?: {
    name: string;
    action: string;
    expected: any;
    actual?: any;
    status: TestStatus;
    duration?: number;
    error?: {
      message: string;
      stack?: string;
      details?: any;
    };
  }[];
  setup?: {
    name: string;
    action: string;
    status: TestStatus;
    duration?: number;
    error?: {
      message: string;
      stack?: string;
      details?: any;
    };
  }[];
  teardown?: {
    name: string;
    action: string;
    status: TestStatus;
    duration?: number;
    error?: {
      message: string;
      stack?: string;
      details?: any;
    };
  }[];
  performance?: {
    duration: number;
    memory: number;
    cpu: number;
    network: number;
  };
  coverage?: {
    statements: number;
    branches: number;
    functions: number;
    lines: number;
  };
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface TestLog {
  id: string;
  config: TestConfig;
  test: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  details?: Record<string, any>;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
}

export interface TestStats {
  total: number;
  byType: Record<TestType, number>;
  byStatus: Record<TestStatus, number>;
  byPriority: Record<TestPriority, number>;
  byTime: {
    lastHour: number;
    lastDay: number;
    lastWeek: number;
    lastMonth: number;
  };
  performance: {
    duration: number;
    memory: number;
    cpu: number;
    network: number;
  };
  coverage: {
    statements: number;
    branches: number;
    functions: number;
    lines: number;
  };
  metadata: Record<string, any>;
}

export interface TestService {
  createConfig: (config: Omit<TestConfig, 'id'>) => TestConfig;
  updateConfig: (id: string, config: Partial<TestConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => TestConfig | undefined;
  getConfigs: () => TestConfig[];
  create: (config: Omit<TestConfig, 'id'>, test: Omit<Test, 'id' | 'config'>) => Promise<Test>;
  update: (id: string, test: Partial<Test>) => Promise<Test>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Test | undefined;
  getAll: (options?: {
    type?: TestType[];
    status?: TestStatus[];
    priority?: TestPriority[];
    tags?: string[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Test[];
  search: (query: string, options?: {
    type?: TestType[];
    status?: TestStatus[];
    priority?: TestPriority[];
    tags?: string[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Test[];
  run: (id: string, options?: {
    environment?: string;
    data?: Record<string, any>;
    parallel?: boolean;
  }) => Promise<Test>;
  runAll: (options?: {
    type?: TestType[];
    status?: TestStatus[];
    priority?: TestPriority[];
    tags?: string[];
    environment?: string;
    data?: Record<string, any>;
    parallel?: boolean;
  }) => Promise<Test[]>;
  getLogs: (options?: {
    test?: string;
    level?: ('info' | 'warning' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => TestLog[];
  mock: (id: string, options?: {
    data?: Record<string, any>;
    delay?: number;
  }) => Promise<Test>;
  validate: (id: string) => Promise<{
    valid: boolean;
    errors?: {
      path: string;
      message: string;
    }[];
  }>;
  getStats: () => TestStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 