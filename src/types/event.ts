export type EventType = 'system' | 'user' | 'security' | 'custom';
export type EventStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'archived';
export type EventPriority = 'low' | 'normal' | 'high' | 'urgent';

export interface EventConfig {
  management: {
    enabled: boolean;
    types: EventType[];
    priorities: EventPriority[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'stream' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
  };
  processing: {
    enabled: boolean;
    concurrency?: {
      enabled: boolean;
      max: number;
      perType?: Record<EventType, number>;
    };
    timeout?: {
      enabled: boolean;
      default: number;
      perType?: Record<EventType, number>;
    };
    retry?: {
      enabled: boolean;
      maxAttempts: number;
      delay: number;
      backoff: number;
    };
    rateLimit?: {
      enabled: boolean;
      window: number;
      max: number;
    };
  };
  routing: {
    enabled: boolean;
    engine: 'direct' | 'topic' | 'fanout' | 'custom';
    options?: {
      direct?: {
        strict: boolean;
        timeout: number;
      };
      topic?: {
        strict: boolean;
        timeout: number;
        patterns?: string[];
      };
      fanout?: {
        strict: boolean;
        timeout: number;
        broadcast: boolean;
      };
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      processing?: boolean;
      performance?: boolean;
      resource?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    routing?: boolean;
    filtering?: boolean;
    transformation?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Event {
  id: string;
  config: EventConfig;
  type: EventType;
  status: EventStatus;
  priority: EventPriority;
  name: string;
  description?: string;
  source: {
    type: 'system' | 'user' | 'service' | 'custom';
    id: string;
    name?: string;
  };
  data: Record<string, any>;
  routing?: {
    engine: EventConfig['routing']['engine'];
    options?: EventConfig['routing']['options'];
    targets?: {
      type: 'queue' | 'topic' | 'service' | 'custom';
      id: string;
      options?: Record<string, any>;
    }[];
  };
  processing?: {
    concurrency: number;
    timeout: number;
    retry: {
      maxAttempts: number;
      delay: number;
      backoff: number;
    };
    rateLimit: {
      window: number;
      max: number;
    };
  };
  filtering?: {
    enabled: boolean;
    rules: {
      field: string;
      operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'regex' | 'custom';
      value: any;
    }[];
  };
  transformation?: {
    enabled: boolean;
    rules: {
      field: string;
      type: 'map' | 'filter' | 'reduce' | 'custom';
      value: any;
    }[];
  };
  resources?: {
    cpu?: number;
    memory?: number;
    disk?: number;
    network?: number;
  };
  stats?: {
    size: number;
    processed: number;
    failed: number;
    retried: number;
    duration: number;
  };
  createdAt: Date;
  updatedAt: Date;
  processedAt?: Date;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export interface EventLog {
  id: string;
  event: string;
  action: 'create' | 'update' | 'delete' | 'process' | 'complete' | 'fail' | 'retry' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface EventStats {
  total: number;
  byType: Record<EventType, number>;
  byStatus: Record<EventStatus, number>;
  byPriority: Record<EventPriority, number>;
  processing: {
    total: number;
    success: number;
    failure: number;
    byEvent?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageDuration: number;
    byEvent?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  resources: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
    byEvent?: Record<string, {
      cpu: number;
      memory: number;
      disk: number;
      network: number;
    }>;
    byDate?: Record<string, {
      cpu: number;
      memory: number;
      disk: number;
      network: number;
    }>;
  };
}

export interface EventService {
  createConfig: (config: Omit<EventConfig, 'id'>) => EventConfig;
  updateConfig: (id: string, config: Partial<EventConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => EventConfig | undefined;
  getConfigs: () => EventConfig[];
  create: (config: Omit<EventConfig, 'id'>, event: Omit<Event, 'id' | 'config'>) => Promise<Event>;
  update: (id: string, event: Partial<Event>) => Promise<Event>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Event | undefined;
  getAll: (options?: {
    type?: EventType[];
    status?: EventStatus[];
    priority?: EventPriority[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Event[];
  search: (query: string, options?: {
    type?: EventType[];
    status?: EventStatus[];
    priority?: EventPriority[];
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Event[];
  publish: (id: string) => Promise<void>;
  subscribe: (id: string, target: Event['routing']['targets'][0]) => Promise<void>;
  unsubscribe: (id: string, target: Event['routing']['targets'][0]) => Promise<void>;
  getLogs: (options?: {
    event?: string;
    action?: ('create' | 'update' | 'delete' | 'process' | 'complete' | 'fail' | 'retry' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => EventLog[];
  getStats: () => EventStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
} 