import { useEffect, useMemo, useState } from 'react';
import { ValidationError } from 'joi';
import LRUCache from 'lru-cache';

type SecurityConfig = {
  csp?: string;
  csrf?: {
    tokenHeader: string;
    cookieName: string;
  };
  cors?: {
    origins: string[];
    methods: string[];
    headers: string[];
  };
};

export const useSecurity = (config: SecurityConfig) => {
  useEffect(() => {
    // Динамическое обновление CSP
    if (config.csp) {
      const meta = document.createElement('meta');
      meta.httpEquiv = 'Content-Security-Policy';
      meta.content = config.csp;
      document.head.appendChild(meta);
    }
  }, [config.csp]);

  const csrf = useMemo(() => ({
    getToken: () => {
      const cookie = document.cookie
        .split('; ')
        .find(row => row.startsWith(`${config.csrf?.cookieName}=`));
      return cookie?.split('=')[1] || '';
    },
    validateHeader: (headers: Headers) => {
      if (!config.csrf) return true;
      const token = headers.get(config.csrf.tokenHeader);
      return token === csrf.getToken();
    }
  }), [config.csrf]);

  const cors = useMemo(() => ({
    validateOrigin: (origin: string) => 
      config.cors?.origins.includes(origin) ?? false,
    allowedMethods: config.cors?.methods.join(', ') || '',
    allowedHeaders: config.cors?.headers.join(', ') || ''
  }), [config.cors]);

  const sanitizeInput = (input: string): string => {
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
      .replace(/\//g, '&#x2F;');
  };

  return { csrf, cors, sanitizeInput };
};

// Хук для автоматического обновления CSRF-токена
export const useCSRF = (endpoint = '/api/csrf-token') => {
  const [token, setToken] = useState('');

  useEffect(() => {
    const controller = new AbortController();
    
    const fetchToken = async () => {
      try {
        const res = await fetch(endpoint, {
          signal: controller.signal,
          credentials: 'include'
        });
        const { token } = await res.json();
        setToken(token);
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('CSRF token fetch error:', error);
        }
      }
    };

    fetchToken();
    return () => controller.abort();
  }, [endpoint]);

  return token;
};

// Утилита для безопасной обработки ошибок
export const secureErrorHandler = (error: unknown): string => {
  const safeMessage = (msg: string) => msg
    .replace(/[^a-zA-Z0-9 \-_,:;.!?@]/g, '')
    .substring(0, 200);

  if (error instanceof Error) {
    return safeMessage(error.message);
  }
  return 'Произошла неизвестная ошибка';
};

export class ValidationRecovery {
  private static cache = new LRUCache<string, any>({ 
    max: 1000,
    ttl: 60 * 60 * 1000 
  });

  static async handleValidation<T>(schema: any, data: T): Promise<T> {
    try {
      return await schema.validateAsync(data);
    } catch (error) {
      if (error instanceof ValidationError) {
        const errorKey = error.details[0].type + ':' + error.details[0].context?.key;
        const cached = this.cache.get(errorKey);
        return cached ?? this.applyFallbackStrategy(error);
      }
      throw error;
    }
  }

  private static applyFallbackStrategy(error: ValidationError): any {
    console.warn('Applying validation fallback for:', error.message);
    return { __fallback: true, timestamp: Date.now() };
  }

  static cacheRule(errorType: string, handler: () => any) {
    this.cache.set(errorType, handler);
  }
}

export const { csrf, cors, sanitizeInput, useCSRF, secureErrorHandler };
export { ValidationRecovery };