import { z } from 'zod';
import { logger } from '../logging/Logger';

export const sleep = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

export const formatDate = (date: Date, format: string = 'YYYY-MM-DD'): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

export const formatNumber = (num: number, options: Intl.NumberFormatOptions = {}): string => {
  return new Intl.NumberFormat(undefined, options).format(num);
};

export const formatCurrency = (
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
};

export const generateId = (): string => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
};

export const validateEmail = (email: string): boolean => {
  const emailSchema = z.string().email();
  try {
    emailSchema.parse(email);
    return true;
  } catch (error) {
    return false;
  }
};

export const validatePassword = (password: string): boolean => {
  const passwordSchema = z
    .string()
    .min(8)
    .regex(/[A-Z]/)
    .regex(/[a-z]/)
    .regex(/[0-9]/)
    .regex(/[^A-Za-z0-9]/);
  try {
    passwordSchema.parse(password);
    return true;
  } catch (error) {
    return false;
  }
};

export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

export const mergeObjects = <T extends object>(...objects: T[]): T => {
  return objects.reduce((result, current) => {
    return { ...result, ...current };
  }, {} as T);
};

export const groupBy = <T>(
  array: T[],
  key: keyof T
): { [key: string]: T[] } => {
  return array.reduce((result, current) => {
    const groupKey = String(current[key]);
    return {
      ...result,
      [groupKey]: [...(result[groupKey] || []), current],
    };
  }, {} as { [key: string]: T[] });
};

export const chunk = <T>(array: T[], size: number): T[][] => {
  return array.reduce((result, current, index) => {
    const chunkIndex = Math.floor(index / size);
    if (!result[chunkIndex]) {
      result[chunkIndex] = [];
    }
    result[chunkIndex].push(current);
    return result;
  }, [] as T[][]);
};

export const retry = async <T>(
  fn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000
): Promise<T> => {
  try {
    return await fn();
  } catch (error) {
    if (retries === 0) {
      throw error;
    }
    await sleep(delay);
    return retry(fn, retries - 1, delay);
  }
};

export const memoize = <T extends (...args: any[]) => any>(
  fn: T
): ((...args: Parameters<T>) => ReturnType<T>) => {
  const cache = new Map();
  return (...args: Parameters<T>): ReturnType<T> => {
    const key = JSON.stringify(args);
    if (cache.has(key)) {
      return cache.get(key);
    }
    const result = fn(...args);
    cache.set(key, result);
    return result;
  };
};

export const measurePerformance = async <T>(
  fn: () => Promise<T>,
  label: string
): Promise<T> => {
  const start = performance.now();
  try {
    return await fn();
  } finally {
    const end = performance.now();
    logger.info(`${label} took ${end - start}ms`);
  }
};

export const sanitizeHtml = (html: string): string => {
  const div = document.createElement('div');
  div.textContent = html;
  return div.innerHTML;
};

export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength) + '...';
};

export const getRandomColor = (): string => {
  return '#' + Math.floor(Math.random() * 16777215).toString(16);
};

export const isMobile = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
};

export const isTouchDevice = (): boolean => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

export const getBrowserInfo = (): {
  name: string;
  version: string;
  os: string;
} => {
  const userAgent = navigator.userAgent;
  const browserInfo = {
    name: 'Unknown',
    version: 'Unknown',
    os: 'Unknown',
  };

  // Browser detection
  if (userAgent.indexOf('Chrome') > -1) {
    browserInfo.name = 'Chrome';
  } else if (userAgent.indexOf('Firefox') > -1) {
    browserInfo.name = 'Firefox';
  } else if (userAgent.indexOf('Safari') > -1) {
    browserInfo.name = 'Safari';
  } else if (userAgent.indexOf('Edge') > -1) {
    browserInfo.name = 'Edge';
  }

  // OS detection
  if (userAgent.indexOf('Windows') > -1) {
    browserInfo.os = 'Windows';
  } else if (userAgent.indexOf('Mac') > -1) {
    browserInfo.os = 'MacOS';
  } else if (userAgent.indexOf('Linux') > -1) {
    browserInfo.os = 'Linux';
  } else if (userAgent.indexOf('Android') > -1) {
    browserInfo.os = 'Android';
  } else if (userAgent.indexOf('iOS') > -1) {
    browserInfo.os = 'iOS';
  }

  return browserInfo;
}; 