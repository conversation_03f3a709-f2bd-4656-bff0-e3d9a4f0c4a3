import { app, BrowserWindow } from 'electron';
import { performance } from 'perf_hooks';
import { logger } from '../logger';
import { trackError } from '../errorTracking';

interface PerformanceMetrics {
  fps: number;
  memory: {
    used: number;
    total: number;
  };
  cpu: {
    usage: number;
    temperature: number;
  };
  network: {
    download: number;
    upload: number;
    latency: number;
  };
  render: {
    timeToFirstByte: number;
    firstContentfulPaint: number;
    largestContentfulPaint: number;
    timeToInteractive: number;
    frameTime: number;
  };
  errors: {
    count: number;
    lastError: string | null;
  };
}

interface ErrorReport {
  error: Error;
  context?: Record<string, any>;
  user?: {
    id?: string;
    email?: string;
    username?: string;
  };
  tags?: Record<string, string>;
  level?: 'fatal' | 'error' | 'warning' | 'info' | 'debug';
  breadcrumbs?: Array<{
    category: string;
    message: string;
    level: string;
    timestamp: number;
  }>;
}

interface PerformanceThresholds {
  memory: {
    heapUsed: number;
    rss: number;
  };
  cpu: {
    usage: number;
  };
  renderer: {
    fps: number;
    frameTime: number;
  };
  network: {
    latency: number;
  };
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics;
  private thresholds: PerformanceThresholds;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private window: BrowserWindow | null = null;
  private lastFrameTime: number;
  private frameCount: number;
  private lastMetricsUpdate: number;
  private errorCount: number;
  private lastError: string | null;

  private constructor() {
    this.metrics = {
      fps: 0,
      memory: {
        used: 0,
        total: 0,
      },
      cpu: {
        usage: 0,
        temperature: 0,
      },
      network: {
        download: 0,
        upload: 0,
        latency: 0,
      },
      render: {
        timeToFirstByte: 0,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0,
        timeToInteractive: 0,
        frameTime: 0,
      },
      errors: {
        count: 0,
        lastError: null,
      },
    };

    this.thresholds = {
      memory: {
        heapUsed: 0.8, // 80% of heap
        rss: 1024 * 1024 * 1024, // 1GB
      },
      cpu: {
        usage: 0.8, // 80% CPU usage
      },
      renderer: {
        fps: 30,
        frameTime: 33, // 33ms per frame
      },
      network: {
        latency: 1000, // 1 second
      },
    };

    this.lastFrameTime = performance.now();
    this.frameCount = 0;
    this.lastMetricsUpdate = performance.now();
    this.errorCount = 0;
    this.lastError = null;

    this.initializeMonitoring();
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  public setWindow(window: BrowserWindow): void {
    this.window = window;
  }

  public startMonitoring(interval: number = 5000): void {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
      this.checkThresholds();
    }, interval);

    logger.info('Performance monitoring started');
  }

  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      logger.info('Performance monitoring stopped');
    }
  }

  private initializeMonitoring(): void {
    // Monitor frame rate
    this.monitorFrameRate();

    // Monitor memory usage
    this.monitorMemory();

    // Monitor CPU usage
    this.monitorCPU();

    // Monitor network performance
    this.monitorNetwork();

    // Monitor render performance
    this.monitorRender();

    // Monitor errors
    this.monitorErrors();
  }

  private monitorFrameRate(): void {
    const updateFPS = () => {
      const now = performance.now();
      const elapsed = now - this.lastFrameTime;

      if (elapsed >= 1000) {
        this.metrics.fps = Math.round((this.frameCount * 1000) / elapsed);
        this.frameCount = 0;
        this.lastFrameTime = now;
      }

      this.frameCount++;
      requestAnimationFrame(updateFPS);
    };

    requestAnimationFrame(updateFPS);
  }

  private async monitorMemory(): Promise<void> {
    const updateMemory = async () => {
      try {
        const memoryInfo = await process.getProcessMemoryInfo();
        this.metrics.memory.used = memoryInfo.private;
        this.metrics.memory.total = memoryInfo.private + memoryInfo.shared;
      } catch (error) {
        logger.error('Failed to get memory info', { error });
        const errorObj = error instanceof Error ? error : new Error(String(error));
        trackError(errorObj, {
          context: {
            component: 'PerformanceMonitor',
            method: 'monitorMemory',
          },
        });
      }

      setTimeout(() => updateMemory(), 1000);
    };

    await updateMemory();
  }

  private monitorCPU(): void {
    const updateCPU = () => {
      try {
        const cpuUsage = process.cpuUsage();
        const totalUsage = cpuUsage.user + cpuUsage.system;
        this.metrics.cpu.usage = totalUsage / 1000000; // Convert to percentage

        // Temperature monitoring is not directly available in Electron
        // This would require native modules or system-specific APIs
        this.metrics.cpu.temperature = 0;
      } catch (error) {
        logger.error('Failed to get CPU usage', { error });
        const errorObj = error instanceof Error ? error : new Error(String(error));
        trackError(errorObj, {
          context: {
            component: 'PerformanceMonitor',
            method: 'monitorCPU',
          },
        });
      }

      setTimeout(updateCPU, 1000);
    };

    updateCPU();
  }

  private monitorNetwork(): void {
    const updateNetwork = () => {
      // Network monitoring would require additional implementation
      // This could involve tracking network requests, measuring latency, etc.
      // For now, we'll use placeholder values
      this.metrics.network.download = 0;
      this.metrics.network.upload = 0;
      this.metrics.network.latency = 0;

      setTimeout(updateNetwork, 1000);
    };

    updateNetwork();
  }

  private monitorRender(): void {
    const updateRender = () => {
      // Render performance metrics would require additional implementation
      // This could involve measuring paint times, layout thrashing, etc.
      // For now, we'll use placeholder values
      this.metrics.render.timeToFirstByte = 0;
      this.metrics.render.firstContentfulPaint = 0;
      this.metrics.render.largestContentfulPaint = 0;
      this.metrics.render.timeToInteractive = 0;
      this.metrics.render.frameTime = 0;

      setTimeout(updateRender, 1000);
    };

    updateRender();
  }

  private monitorErrors(): void {
    window.addEventListener('error', (event) => {
      this.errorCount++;
      this.lastError = event.message;
      this.metrics.errors.count = this.errorCount;
      this.metrics.errors.lastError = this.lastError;

      const error = event.error || new Error(event.message);
      trackError(error, {
        context: {
          component: 'PerformanceMonitor',
          method: 'monitorErrors',
          details: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
          },
        },
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.errorCount++;
      this.lastError = event.reason;
      this.metrics.errors.count = this.errorCount;
      this.metrics.errors.lastError = this.lastError;

      const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
      trackError(error, {
        context: {
          component: 'PerformanceMonitor',
          method: 'monitorErrors',
          details: {
            type: 'unhandledrejection',
          },
        },
      });
    });
  }

  private async collectMetrics(): Promise<void> {
    try {
      // Collect memory metrics
      const memoryUsage = process.memoryUsage();
      this.metrics.memory = {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
      };

      // Collect CPU metrics
      const cpuUsage = process.cpuUsage();
      this.metrics.cpu = {
        usage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to percentage
        temperature: 0, // Temperature monitoring is not directly available in Electron
      };

      // Collect renderer metrics if window exists
      if (this.window) {
        const rendererMetrics = await this.window.webContents.executeJavaScript(`
          {
            fps: window.performance.now(),
            frameTime: window.performance.getEntriesByType('measure')
              .filter(m => m.name === 'frame')
              .pop()?.duration || 0,
            jsHeapSize: performance.memory?.usedJSHeapSize || 0,
            jsHeapSizeLimit: performance.memory?.jsHeapSizeLimit || 0
          }
        `);
        this.metrics.fps = rendererMetrics.fps;
        this.metrics.render.frameTime = rendererMetrics.frameTime;
      }

      // Log metrics
      logger.debug('Performance metrics collected', { metrics: this.metrics });
    } catch (error) {
      trackError(error as Error, 'PerformanceMonitor.collectMetrics');
    }
  }

  private checkThresholds(): void {
    try {
      // Check memory thresholds
      if (this.metrics.memory.used / this.metrics.memory.total > this.thresholds.memory.heapUsed) {
        logger.warn('High memory usage detected', {
          used: this.metrics.memory.used,
          total: this.metrics.memory.total,
          threshold: this.thresholds.memory.heapUsed,
        });
      }

      // Check CPU thresholds
      if (this.metrics.cpu.usage > this.thresholds.cpu.usage) {
        logger.warn('High CPU usage detected', {
          usage: this.metrics.cpu.usage,
          threshold: this.thresholds.cpu.usage,
        });
      }

      // Check renderer thresholds
      if (this.metrics.fps < this.thresholds.renderer.fps) {
        logger.warn('Low FPS detected', {
          fps: this.metrics.fps,
          threshold: this.thresholds.renderer.fps,
        });
      }

      if (this.metrics.render.frameTime > this.thresholds.renderer.frameTime) {
        logger.warn('High frame time detected', {
          frameTime: this.metrics.render.frameTime,
          threshold: this.thresholds.renderer.frameTime,
        });
      }

      // Check network thresholds
      if (this.metrics.network.latency > this.thresholds.network.latency) {
        logger.warn('High network latency detected', {
          latency: this.metrics.network.latency,
          threshold: this.thresholds.network.latency,
        });
      }
    } catch (error) {
      trackError(error as Error, 'PerformanceMonitor.checkThresholds');
    }
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public setThresholds(thresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = {
      ...this.thresholds,
      ...thresholds,
    };
    logger.info('Performance thresholds updated', { thresholds: this.thresholds });
  }

  public async optimizePerformance(): Promise<void> {
    try {
      // Clear memory caches
      if (this.window) {
        await this.window.webContents.session.clearCache();
        await this.window.webContents.session.clearStorageData();
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      // Reset metrics
      this.metrics = {
        fps: 0,
        memory: {
          used: 0,
          total: 0,
        },
        cpu: {
          usage: 0,
          temperature: 0,
        },
        network: {
          download: 0,
          upload: 0,
          latency: 0,
        },
        render: {
          timeToFirstByte: 0,
          firstContentfulPaint: 0,
          largestContentfulPaint: 0,
          timeToInteractive: 0,
          frameTime: 0,
        },
        errors: {
          count: 0,
          lastError: null,
        },
      };

      this.lastFrameTime = performance.now();
      this.frameCount = 0;
      this.lastMetricsUpdate = performance.now();
      this.errorCount = 0;
      this.lastError = null;

      logger.info('Performance optimization completed');
    } catch (error) {
      trackError(error as Error, 'PerformanceMonitor.optimizePerformance');
    }
  }

  public resetMetrics(): void {
    this.metrics = {
      fps: 0,
      memory: {
        used: 0,
        total: 0,
      },
      cpu: {
        usage: 0,
        temperature: 0,
      },
      network: {
        download: 0,
        upload: 0,
        latency: 0,
      },
      render: {
        timeToFirstByte: 0,
        firstContentfulPaint: 0,
        largestContentfulPaint: 0,
        timeToInteractive: 0,
        frameTime: 0,
      },
      errors: {
        count: 0,
        lastError: null,
      },
    };

    this.lastFrameTime = performance.now();
    this.frameCount = 0;
    this.lastMetricsUpdate = performance.now();
    this.errorCount = 0;
    this.lastError = null;
  }
} 