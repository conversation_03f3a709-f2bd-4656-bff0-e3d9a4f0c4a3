import { app } from 'electron';
import * as Sentry from '@sentry/electron';
import { <PERSON><PERSON><PERSON>Window } from 'electron';
import { logger } from './logger';

export class AppError extends Error {
  constructor(
    message: string,
    public code: string,
    public status: number = 500,
    public isOperational: boolean = true
  ) {
    super(message);
    this.name = 'AppError';
    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorTypes = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  CONFLICT_ERROR: 'CONFLICT_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  FILE_SYSTEM_ERROR: 'FILE_SYSTEM_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
};

export const errorHandler = {
  handleError: (error: Error | AppError) => {
    // Log error
    logger.error({
      message: error.message,
      stack: error.stack,
      code: error instanceof AppError ? error.code : errorTypes.UNKNOWN_ERROR,
      status: error instanceof AppError ? error.status : 500,
    });

    // Report to Sentry if not operational
    if (error instanceof AppError && !error.isOperational) {
      Sentry.captureException(error);
    }

    // Show error to user
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('app-error', {
        message: error.message,
        code: error instanceof AppError ? error.code : errorTypes.UNKNOWN_ERROR,
        status: error instanceof AppError ? error.status : 500,
      });
    }

    // Handle fatal errors
    if (!(error instanceof AppError) || !error.isOperational) {
      logger.fatal('Fatal error occurred, application will restart');
      app.relaunch();
      app.exit(1);
    }
  },

  // Error middleware for Express
  errorMiddleware: (error: Error | AppError, req: any, res: any, next: any) => {
    const status = error instanceof AppError ? error.status : 500;
    const code = error instanceof AppError ? error.code : errorTypes.UNKNOWN_ERROR;
    const message = error.message;

    res.status(status).json({
      status: 'error',
      code,
      message,
      ...(process.env.NODE_ENV === 'development' && { stack: error.stack }),
    });
  },

  // Process error handlers
  processErrorHandlers: () => {
    process.on('uncaughtException', (error: Error) => {
      logger.error('Uncaught Exception:', error);
      errorHandler.handleError(error);
    });

    process.on('unhandledRejection', (reason: any) => {
      logger.error('Unhandled Rejection:', reason);
      errorHandler.handleError(reason instanceof Error ? reason : new Error(String(reason)));
    });

    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully');
      app.quit();
    });

    process.on('SIGINT', () => {
      logger.info('SIGINT received, shutting down gracefully');
      app.quit();
    });
  },

  // Validation error
  validationError: (message: string) => {
    return new AppError(message, errorTypes.VALIDATION_ERROR, 400);
  },

  // Authentication error
  authenticationError: (message: string) => {
    return new AppError(message, errorTypes.AUTHENTICATION_ERROR, 401);
  },

  // Authorization error
  authorizationError: (message: string) => {
    return new AppError(message, errorTypes.AUTHORIZATION_ERROR, 403);
  },

  // Not found error
  notFoundError: (message: string) => {
    return new AppError(message, errorTypes.NOT_FOUND_ERROR, 404);
  },

  // Conflict error
  conflictError: (message: string) => {
    return new AppError(message, errorTypes.CONFLICT_ERROR, 409);
  },

  // Rate limit error
  rateLimitError: (message: string) => {
    return new AppError(message, errorTypes.RATE_LIMIT_ERROR, 429);
  },

  // Network error
  networkError: (message: string) => {
    return new AppError(message, errorTypes.NETWORK_ERROR, 503);
  },

  // Database error
  databaseError: (message: string) => {
    return new AppError(message, errorTypes.DATABASE_ERROR, 500, false);
  },

  // File system error
  fileSystemError: (message: string) => {
    return new AppError(message, errorTypes.FILE_SYSTEM_ERROR, 500, false);
  },
}; 