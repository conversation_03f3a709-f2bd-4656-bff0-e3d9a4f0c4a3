import { renderHook, act } from '@testing-library/react-hooks';
import { useFormValidation } from '../hooks/useFormValidation';
import { ValidationErrors } from '../utils/validationUtils';

export const TEST_SCHEMA = {
  email: (v: string) => /@/.test(v) || 'Invalid email',
  password: (v: string) => v.length >= 8 || 'Too short'
};

export const ASYNC_SCHEMA = {
  username: async (v: string) => {
    await new Promise(r => setTimeout(r, 50));
    return v.length > 3 || 'Username taken';
  }
};

export const SECURITY_SCHEMA = {
  input: (v: string) => {
    const sanitized = v.replace(/<[^>]*>?/gm, '');
    return sanitized === v || 'XSS detected';
  }
};

export const renderFormHook = <T,>(
  initialValues: T,
  schema: Record<keyof T, any>
) => {
  return renderHook(() => useFormValidation(initialValues, schema));
};

export const mockAPIResponse = (valid: boolean, delay = 100) => {
  return jest.fn(
    () => new Promise(resolve => 
      setTimeout(() => resolve({ valid }), delay)
    )
  );
};

export const expectErrorSequence = async (
  result: any,
  expectedErrors: ValidationErrors<any>[]
) => {
  for (const errors of expectedErrors) {
    await act(async () => { await new Promise(r => setTimeout(r, 10)); });
    expect(result.current.errors).toMatchObject(errors);
  }
};

export const TestWrapper: React.FC = ({ children }) => (
  <div role="test-wrapper">
    <form aria-label="test-form">{children}</form>
  </div>
);

// Моки для разных фреймворков
export const mockVueComponent = {
  template: '<div/>',
  setup() {
    return { errors: { value: {} } };
  }
};

export const mockSvelteStore = {
  subscribe: jest.fn(),
  set: jest.fn()
};