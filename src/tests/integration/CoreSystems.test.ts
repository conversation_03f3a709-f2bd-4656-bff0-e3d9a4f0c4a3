import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { EnhancedLogger } from '../../core/EnhancedLogger';
import { ConfigurationManager } from '../../core/ConfigurationManager';
import { PerformanceMonitor } from '../../performance/PerformanceMonitor';
import { ErrorManager } from '../../error/ErrorManager';
import { SecurityManager } from '../../security/SecurityManager';
import { TestUtils } from '../../testing/TestFramework';

describe('Core Systems Integration', () => {
  let logger: EnhancedLogger;
  let configManager: ConfigurationManager;
  let performanceMonitor: PerformanceMonitor;
  let errorManager: ErrorManager;
  let securityManager: SecurityManager;

  beforeEach(() => {
    // Initialize systems
    logger = EnhancedLogger.getInstance({
      level: 'debug',
      enableConsole: false,
      enableStorage: false,
      enableRemote: false,
    });

    configManager = ConfigurationManager.getInstance({
      environment: 'test',
      enableValidation: true,
      enableRemoteConfig: false,
    });

    performanceMonitor = PerformanceMonitor.getInstance();
    errorManager = ErrorManager.getInstance();
    securityManager = SecurityManager.getInstance();
  });

  afterEach(() => {
    // Cleanup
    logger.clearLogs();
    configManager.reset();
    performanceMonitor.cleanup();
    errorManager.cleanup();
    securityManager.cleanup();
  });

  describe('Logger Integration', () => {
    it('integrates with configuration manager', () => {
      // Configure logger through config manager
      configManager.set('logger.level', 'warn');
      configManager.set('logger.enableConsole', true);

      // Logger should respect configuration
      logger.updateConfig({
        level: configManager.get('logger.level'),
        enableConsole: configManager.get('logger.enableConsole'),
      });

      logger.debug('Debug message');
      logger.warn('Warning message');

      const logs = logger.getLogs();
      expect(logs).toHaveLength(1);
      expect(logs[0].level).toBe('warn');
    });

    it('integrates with performance monitor', () => {
      const timer = logger.startTimer('test-operation');
      
      // Simulate some work
      const start = performance.now();
      while (performance.now() - start < 10) {
        // Busy wait for 10ms
      }
      
      timer();

      const logs = logger.getLogs('info');
      const performanceLog = logs.find(log => 
        log.message.includes('Performance: test-operation')
      );
      
      expect(performanceLog).toBeDefined();
      expect(performanceLog?.context?.performance?.duration).toBeGreaterThan(0);
    });

    it('integrates with error manager', () => {
      const testError = new Error('Test error');
      
      // Error manager should use logger
      errorManager.handleError({
        type: 'test_error',
        message: testError.message,
        stack: testError.stack,
        severity: 'high',
      });

      const logs = logger.getLogs();
      expect(logs.length).toBeGreaterThan(0);
    });
  });

  describe('Configuration Manager Integration', () => {
    it('provides configuration to all systems', () => {
      // Set configuration values
      configManager.set('performance.samplingInterval', 2000);
      configManager.set('security.sessionTimeout', 1800000);
      configManager.set('error.maxErrors', 500);

      // Systems should use these configurations
      expect(configManager.get('performance.samplingInterval')).toBe(2000);
      expect(configManager.get('security.sessionTimeout')).toBe(1800000);
      expect(configManager.get('error.maxErrors')).toBe(500);
    });

    it('validates configuration changes', () => {
      configManager.defineSchema({
        'test.value': {
          type: 'number',
          default: 100,
          validation: (value) => value > 0 && value < 1000,
        },
      });

      // Valid value should work
      expect(() => configManager.set('test.value', 500)).not.toThrow();

      // Invalid value should throw
      expect(() => configManager.set('test.value', -1)).toThrow();
      expect(() => configManager.set('test.value', 1500)).toThrow();
    });

    it('notifies systems of configuration changes', async () => {
      const changeHandler = vi.fn();
      configManager.watch('test.setting', changeHandler);

      configManager.set('test.setting', 'new-value');

      expect(changeHandler).toHaveBeenCalledWith('new-value');
    });
  });

  describe('Performance Monitor Integration', () => {
    it('monitors system performance', async () => {
      performanceMonitor.startMonitoring();

      // Simulate some operations
      performanceMonitor.recordMetric('cpu_usage', 45.5);
      performanceMonitor.recordMetric('memory_usage', 128 * 1024 * 1024);
      performanceMonitor.recordMetric('response_time', 150);

      const metrics = performanceMonitor.getMetrics('cpu_usage');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].value).toBe(45.5);

      const stats = performanceMonitor.getStatistics('cpu_usage');
      expect(stats.avg).toBe(45.5);
      expect(stats.count).toBe(1);
    });

    it('integrates with error reporting', () => {
      const errorSpy = vi.spyOn(errorManager, 'handleError');

      // Simulate performance threshold violation
      performanceMonitor.recordMetric('response_time', 5000); // Very slow response

      // Should trigger error reporting for performance issues
      expect(errorSpy).toHaveBeenCalled();
    });

    it('provides performance insights', () => {
      // Record various metrics
      for (let i = 0; i < 10; i++) {
        performanceMonitor.recordMetric('response_time', 100 + Math.random() * 50);
        performanceMonitor.recordMetric('memory_usage', 100 * 1024 * 1024 + Math.random() * 50 * 1024 * 1024);
      }

      const responseTimeStats = performanceMonitor.getStatistics('response_time');
      const memoryStats = performanceMonitor.getStatistics('memory_usage');

      expect(responseTimeStats.count).toBe(10);
      expect(responseTimeStats.avg).toBeGreaterThan(100);
      expect(responseTimeStats.avg).toBeLessThan(150);

      expect(memoryStats.count).toBe(10);
      expect(memoryStats.avg).toBeGreaterThan(100 * 1024 * 1024);
    });
  });

  describe('Error Manager Integration', () => {
    it('handles errors from all systems', () => {
      const errors = [
        { type: 'logger_error', message: 'Logger failed', severity: 'medium' as const },
        { type: 'config_error', message: 'Config validation failed', severity: 'high' as const },
        { type: 'performance_error', message: 'Performance threshold exceeded', severity: 'low' as const },
        { type: 'security_error', message: 'Security violation detected', severity: 'critical' as const },
      ];

      errors.forEach(error => errorManager.handleError(error));

      const allErrors = errorManager.getErrors();
      expect(allErrors).toHaveLength(4);

      const criticalErrors = errorManager.getErrorsByType('security_error');
      expect(criticalErrors).toHaveLength(1);
      expect(criticalErrors[0].severity).toBe('critical');
    });

    it('aggregates error statistics', () => {
      // Generate multiple errors
      for (let i = 0; i < 5; i++) {
        errorManager.handleError({
          type: 'network_error',
          message: `Network error ${i}`,
          severity: 'medium',
        });
      }

      for (let i = 0; i < 3; i++) {
        errorManager.handleError({
          type: 'validation_error',
          message: `Validation error ${i}`,
          severity: 'low',
        });
      }

      const stats = errorManager.getErrorStatistics();
      expect(stats.totalErrors).toBe(8);
      expect(stats.errorsByType.network_error).toBe(5);
      expect(stats.errorsByType.validation_error).toBe(3);
    });
  });

  describe('Security Manager Integration', () => {
    it('integrates with configuration for security settings', () => {
      configManager.set('security.sessionTimeout', 3600000); // 1 hour
      configManager.set('security.maxLoginAttempts', 5);
      configManager.set('security.blockDuration', 900000); // 15 minutes

      const config = securityManager.getConfig();
      expect(config.sessionTimeout).toBe(3600000);
      expect(config.maxLoginAttempts).toBe(5);
      expect(config.blockDuration).toBe(900000);
    });

    it('reports security events to error manager', async () => {
      const errorSpy = vi.spyOn(errorManager, 'handleError');

      // Simulate security violation
      securityManager.validateInput('<script>alert("xss")</script>');

      expect(errorSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'security_violation',
          severity: 'high',
        })
      );
    });

    it('monitors security metrics', async () => {
      const performanceSpy = vi.spyOn(performanceMonitor, 'recordMetric');

      // Simulate security operations
      await securityManager.hashPassword('test-password');
      securityManager.validateInput('safe-input');

      expect(performanceSpy).toHaveBeenCalled();
    });
  });

  describe('System Health Check', () => {
    it('performs comprehensive health check', async () => {
      const healthCheck = {
        logger: logger.getLogs().length >= 0,
        config: configManager.getAll() !== null,
        performance: performanceMonitor.getMetrics('memory_usage').length >= 0,
        errors: errorManager.getErrors().length >= 0,
        security: securityManager.getConfig() !== null,
      };

      expect(healthCheck.logger).toBe(true);
      expect(healthCheck.config).toBe(true);
      expect(healthCheck.performance).toBe(true);
      expect(healthCheck.errors).toBe(true);
      expect(healthCheck.security).toBe(true);
    });

    it('measures system startup performance', async () => {
      const startupPerformance = await TestUtils.measureComponentPerformance(() => {
        // Simulate system initialization
        logger.info('System starting');
        configManager.set('startup.time', Date.now());
        performanceMonitor.recordMetric('startup_time', performance.now());
        errorManager.handleError({
          type: 'startup_info',
          message: 'System initialized',
          severity: 'low',
        });
      }, 1);

      expect(startupPerformance.averageTime).toBeLessThan(100); // Should start quickly
    });
  });

  describe('Load Testing', () => {
    it('handles concurrent operations', async () => {
      const loadTestResult = await TestUtils.performLoadTest(
        async () => {
          logger.info('Load test operation');
          configManager.get('test.value', 'default');
          performanceMonitor.recordMetric('load_test', Math.random() * 100);
        },
        {
          concurrent: 10,
          duration: 1000, // 1 second
        }
      );

      expect(loadTestResult.successfulRequests).toBeGreaterThan(0);
      expect(loadTestResult.failedRequests).toBe(0);
      expect(loadTestResult.averageResponseTime).toBeLessThan(50);
    });
  });

  describe('Memory Management', () => {
    it('prevents memory leaks', () => {
      const memoryTracker = TestUtils.detectMemoryLeaks();

      // Perform operations that might cause memory leaks
      for (let i = 0; i < 100; i++) {
        logger.info(`Memory test ${i}`);
        configManager.set(`temp.value.${i}`, i);
        performanceMonitor.recordMetric('memory_test', i);
      }

      // Cleanup
      logger.clearLogs();
      for (let i = 0; i < 100; i++) {
        configManager.reset(`temp.value.${i}`);
      }

      const memoryIncrease = memoryTracker.check();
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // Less than 10MB increase
    });
  });
});
