import React from 'react';
import { render as rtlRender, RenderResult } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore, Store } from '@reduxjs/toolkit';
import { ThemeProvider } from 'styled-components';
import { theme } from '../../styles/theme';
import securityReducer from '../../store/slices/securitySlice';
import performanceReducer from '../../store/slices/performanceSlice';
import accessibilityReducer from '../../store/slices/accessibilitySlice';
import notificationReducer from '../../store/slices/notificationSlice';

interface CustomRenderOptions {
  preloadedState?: Record<string, any>;
  store?: Store;
}

interface CustomRenderResult extends RenderResult {
  store: Store;
}

function render(
  ui: React.ReactElement,
  {
    preloadedState = {},
    store = configureStore({
      reducer: {
        security: securityReducer,
        performance: performanceReducer,
        accessibility: accessibilityReducer,
        notification: notificationReducer,
      },
      preloadedState,
    }),
    ...renderOptions
  }: CustomRenderOptions = {}
): CustomRenderResult {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          {children}
        </ThemeProvider>
      </Provider>
    );
  }
  const rtlRenderResult = rtlRender(ui, { wrapper: Wrapper, ...renderOptions });
  return {
    ...rtlRenderResult,
    store,
  };
}

// Re-export everything
export * from '@testing-library/react';

// Override render method
export { render }; 