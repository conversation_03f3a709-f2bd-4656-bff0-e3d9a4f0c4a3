/**
 * Comprehensive Accessibility Manager для WCAG 2.1 AAA соответствия
 */

export interface AccessibilityConfig {
  enableScreenReader: boolean;
  enableKeyboardNavigation: boolean;
  enableHighContrast: boolean;
  enableReducedMotion: boolean;
  enableFocusManagement: boolean;
  enableAriaLiveRegions: boolean;
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  colorScheme: 'auto' | 'light' | 'dark' | 'high-contrast';
  animationSpeed: 'slow' | 'normal' | 'fast' | 'none';
}

export interface FocusableElement {
  element: HTMLElement;
  tabIndex: number;
  role?: string;
  ariaLabel?: string;
}

export interface AccessibilityViolation {
  id: string;
  type: 'error' | 'warning' | 'info';
  element: HTMLElement;
  rule: string;
  description: string;
  impact: 'minor' | 'moderate' | 'serious' | 'critical';
  wcagLevel: 'A' | 'AA' | 'AAA';
  suggestions: string[];
}

export class AccessibilityManager {
  private config: AccessibilityConfig;
  private focusableElements: FocusableElement[] = [];
  private currentFocusIndex = -1;
  private ariaLiveRegions = new Map<string, HTMLElement>();
  private keyboardListeners = new Map<string, (event: KeyboardEvent) => void>();
  private resizeObserver?: ResizeObserver;
  private mutationObserver?: MutationObserver;

  constructor(config: Partial<AccessibilityConfig> = {}) {
    this.config = {
      enableScreenReader: true,
      enableKeyboardNavigation: true,
      enableHighContrast: false,
      enableReducedMotion: false,
      enableFocusManagement: true,
      enableAriaLiveRegions: true,
      fontSize: 'medium',
      colorScheme: 'auto',
      animationSpeed: 'normal',
      ...config
    };

    this.initialize();
  }

  /**
   * Инициализация системы доступности
   */
  private initialize(): void {
    this.setupKeyboardNavigation();
    this.setupFocusManagement();
    this.setupAriaLiveRegions();
    this.setupUserPreferences();
    this.setupObservers();
    this.applyAccessibilitySettings();
    this.setupScreenReaderSupport();
    this.setupColorContrastMonitoring();
    this.setupMotionPreferences();
  }

  /**
   * Настройка навигации с клавиатуры
   */
  private setupKeyboardNavigation(): void {
    if (!this.config.enableKeyboardNavigation) return;

    // Tab navigation
    this.addKeyboardListener('Tab', (event) => {
      if (event.shiftKey) {
        this.focusPrevious();
      } else {
        this.focusNext();
      }
      event.preventDefault();
    });

    // Arrow key navigation
    this.addKeyboardListener('ArrowDown', () => this.focusNext());
    this.addKeyboardListener('ArrowUp', () => this.focusPrevious());
    this.addKeyboardListener('ArrowRight', () => this.focusNext());
    this.addKeyboardListener('ArrowLeft', () => this.focusPrevious());

    // Home/End navigation
    this.addKeyboardListener('Home', () => this.focusFirst());
    this.addKeyboardListener('End', () => this.focusLast());

    // Escape key
    this.addKeyboardListener('Escape', () => this.handleEscape());

    // Enter/Space activation
    this.addKeyboardListener('Enter', (event) => this.activateElement(event));
    this.addKeyboardListener(' ', (event) => this.activateElement(event));
  }

  /**
   * Настройка управления фокусом
   */
  private setupFocusManagement(): void {
    if (!this.config.enableFocusManagement) return;

    // Отслеживаем изменения фокуса
    document.addEventListener('focusin', (event) => {
      this.handleFocusChange(event.target as HTMLElement);
    });

    document.addEventListener('focusout', (event) => {
      this.handleFocusLoss(event.target as HTMLElement);
    });

    // Обновляем список фокусируемых элементов
    this.updateFocusableElements();
  }

  /**
   * Настройка ARIA Live регионов
   */
  private setupAriaLiveRegions(): void {
    if (!this.config.enableAriaLiveRegions) return;

    // Создаем основные live регионы
    this.createAriaLiveRegion('announcements', 'polite');
    this.createAriaLiveRegion('alerts', 'assertive');
    this.createAriaLiveRegion('status', 'polite');
  }

  /**
   * Настройка пользовательских предпочтений
   */
  private setupUserPreferences(): void {
    // Проверяем системные предпочтения
    if (window.matchMedia) {
      // Reduced motion
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
      if (prefersReducedMotion.matches) {
        this.config.enableReducedMotion = true;
      }
      prefersReducedMotion.addEventListener('change', (e) => {
        this.config.enableReducedMotion = e.matches;
        this.applyAccessibilitySettings();
      });

      // High contrast
      const prefersHighContrast = window.matchMedia('(prefers-contrast: high)');
      if (prefersHighContrast.matches) {
        this.config.enableHighContrast = true;
      }
      prefersHighContrast.addEventListener('change', (e) => {
        this.config.enableHighContrast = e.matches;
        this.applyAccessibilitySettings();
      });

      // Color scheme
      const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
      if (this.config.colorScheme === 'auto') {
        this.config.colorScheme = prefersDarkScheme.matches ? 'dark' : 'light';
      }
      prefersDarkScheme.addEventListener('change', (e) => {
        if (this.config.colorScheme === 'auto') {
          this.config.colorScheme = e.matches ? 'dark' : 'light';
          this.applyAccessibilitySettings();
        }
      });
    }
  }

  /**
   * Настройка наблюдателей
   */
  private setupObservers(): void {
    // Mutation Observer для отслеживания изменений DOM
    this.mutationObserver = new MutationObserver((mutations) => {
      let shouldUpdate = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' || 
            (mutation.type === 'attributes' && 
             ['tabindex', 'aria-hidden', 'disabled'].includes(mutation.attributeName || ''))) {
          shouldUpdate = true;
        }
      });

      if (shouldUpdate) {
        this.updateFocusableElements();
        this.validateAccessibility();
      }
    });

    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['tabindex', 'aria-hidden', 'disabled', 'role', 'aria-label']
    });

    // Resize Observer для адаптивности
    if (window.ResizeObserver) {
      this.resizeObserver = new ResizeObserver(() => {
        this.updateFocusableElements();
      });
      this.resizeObserver.observe(document.body);
    }
  }

  /**
   * Применение настроек доступности
   */
  private applyAccessibilitySettings(): void {
    const root = document.documentElement;

    // Font size
    root.style.setProperty('--accessibility-font-size', this.getFontSizeValue());

    // Color scheme
    root.setAttribute('data-color-scheme', this.config.colorScheme);

    // High contrast
    if (this.config.enableHighContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Reduced motion
    if (this.config.enableReducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }

    // Animation speed
    root.style.setProperty('--animation-speed', this.getAnimationSpeedValue());
  }

  /**
   * Добавляет слушатель клавиатуры
   */
  private addKeyboardListener(key: string, handler: (event: KeyboardEvent) => void): void {
    const wrappedHandler = (event: KeyboardEvent) => {
      if (event.key === key) {
        handler(event);
      }
    };

    this.keyboardListeners.set(key, wrappedHandler);
    document.addEventListener('keydown', wrappedHandler);
  }

  /**
   * Обновляет список фокусируемых элементов
   */
  private updateFocusableElements(): void {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
      'audio[controls]',
      'video[controls]',
      'iframe',
      'object',
      'embed',
      'area[href]',
      'summary'
    ].join(', ');

    const elements = Array.from(document.querySelectorAll(focusableSelectors)) as HTMLElement[];
    
    this.focusableElements = elements
      .filter(element => this.isElementVisible(element) && !element.hasAttribute('aria-hidden'))
      .map(element => ({
        element,
        tabIndex: parseInt(element.getAttribute('tabindex') || '0'),
        role: element.getAttribute('role') || undefined,
        ariaLabel: element.getAttribute('aria-label') || undefined
      }))
      .sort((a, b) => {
        if (a.tabIndex !== b.tabIndex) {
          return a.tabIndex - b.tabIndex;
        }
        // Сортируем по порядку в DOM
        return a.element.compareDocumentPosition(b.element) & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;
      });
  }

  /**
   * Проверяет видимость элемента
   */
  private isElementVisible(element: HTMLElement): boolean {
    const style = window.getComputedStyle(element);
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0' &&
           element.offsetWidth > 0 && 
           element.offsetHeight > 0;
  }

  /**
   * Фокусирует следующий элемент
   */
  private focusNext(): void {
    if (this.focusableElements.length === 0) return;
    
    this.currentFocusIndex = (this.currentFocusIndex + 1) % this.focusableElements.length;
    this.focusableElements[this.currentFocusIndex].element.focus();
  }

  /**
   * Фокусирует предыдущий элемент
   */
  private focusPrevious(): void {
    if (this.focusableElements.length === 0) return;
    
    this.currentFocusIndex = this.currentFocusIndex <= 0 
      ? this.focusableElements.length - 1 
      : this.currentFocusIndex - 1;
    this.focusableElements[this.currentFocusIndex].element.focus();
  }

  /**
   * Фокусирует первый элемент
   */
  private focusFirst(): void {
    if (this.focusableElements.length === 0) return;
    
    this.currentFocusIndex = 0;
    this.focusableElements[this.currentFocusIndex].element.focus();
  }

  /**
   * Фокусирует последний элемент
   */
  private focusLast(): void {
    if (this.focusableElements.length === 0) return;
    
    this.currentFocusIndex = this.focusableElements.length - 1;
    this.focusableElements[this.currentFocusIndex].element.focus();
  }

  /**
   * Обрабатывает нажатие Escape
   */
  private handleEscape(): void {
    // Закрываем модальные окна, меню и т.д.
    const activeModal = document.querySelector('[role="dialog"][aria-modal="true"]') as HTMLElement;
    if (activeModal) {
      const closeButton = activeModal.querySelector('[aria-label*="close"], [aria-label*="закрыть"]') as HTMLElement;
      if (closeButton) {
        closeButton.click();
      }
    }

    // Закрываем выпадающие меню
    const openMenus = document.querySelectorAll('[aria-expanded="true"]');
    openMenus.forEach(menu => {
      if (menu instanceof HTMLElement) {
        menu.setAttribute('aria-expanded', 'false');
      }
    });
  }

  /**
   * Активирует элемент
   */
  private activateElement(event: KeyboardEvent): void {
    const target = event.target as HTMLElement;
    
    if (target.tagName === 'BUTTON' || target.tagName === 'A') {
      target.click();
      event.preventDefault();
    } else if (target.hasAttribute('role')) {
      const role = target.getAttribute('role');
      if (role === 'button' || role === 'link' || role === 'menuitem') {
        target.click();
        event.preventDefault();
      }
    }
  }

  /**
   * Обрабатывает изменение фокуса
   */
  private handleFocusChange(element: HTMLElement): void {
    // Обновляем текущий индекс фокуса
    const index = this.focusableElements.findIndex(item => item.element === element);
    if (index !== -1) {
      this.currentFocusIndex = index;
    }

    // Добавляем визуальный индикатор фокуса
    element.classList.add('accessibility-focused');

    // Объявляем изменение фокуса для скринридеров
    if (this.config.enableScreenReader) {
      const label = this.getElementLabel(element);
      if (label) {
        this.announce(`Focused: ${label}`, 'polite');
      }
    }
  }

  /**
   * Обрабатывает потерю фокуса
   */
  private handleFocusLoss(element: HTMLElement): void {
    element.classList.remove('accessibility-focused');
  }

  /**
   * Создает ARIA Live регион
   */
  private createAriaLiveRegion(id: string, politeness: 'polite' | 'assertive'): void {
    let region = document.getElementById(`aria-live-${id}`);
    
    if (!region) {
      region = document.createElement('div');
      region.id = `aria-live-${id}`;
      region.setAttribute('aria-live', politeness);
      region.setAttribute('aria-atomic', 'true');
      region.style.position = 'absolute';
      region.style.left = '-10000px';
      region.style.width = '1px';
      region.style.height = '1px';
      region.style.overflow = 'hidden';
      
      document.body.appendChild(region);
    }

    this.ariaLiveRegions.set(id, region);
  }

  /**
   * Объявляет сообщение через ARIA Live регион
   */
  public announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    const regionId = priority === 'assertive' ? 'alerts' : 'announcements';
    const region = this.ariaLiveRegions.get(regionId);
    
    if (region) {
      region.textContent = message;
      
      // Очищаем через небольшую задержку
      setTimeout(() => {
        region.textContent = '';
      }, 1000);
    }
  }

  /**
   * Получает метку элемента для скринридеров
   */
  private getElementLabel(element: HTMLElement): string {
    return element.getAttribute('aria-label') ||
           element.getAttribute('aria-labelledby') ||
           element.getAttribute('title') ||
           element.textContent?.trim() ||
           element.getAttribute('alt') ||
           element.getAttribute('placeholder') ||
           '';
  }

  /**
   * Получает значение размера шрифта
   */
  private getFontSizeValue(): string {
    const sizes = {
      small: '0.875rem',
      medium: '1rem',
      large: '1.125rem',
      'extra-large': '1.25rem'
    };
    return sizes[this.config.fontSize];
  }

  /**
   * Получает значение скорости анимации
   */
  private getAnimationSpeedValue(): string {
    const speeds = {
      slow: '0.5',
      normal: '1',
      fast: '2',
      none: '0'
    };
    return speeds[this.config.animationSpeed];
  }

  /**
   * Валидирует доступность страницы
   */
  public async validateAccessibility(): Promise<AccessibilityViolation[]> {
    const violations: AccessibilityViolation[] = [];

    // Проверяем наличие alt текста у изображений
    const images = document.querySelectorAll('img');
    images.forEach((img, index) => {
      if (!img.hasAttribute('alt')) {
        violations.push({
          id: `img-alt-${index}`,
          type: 'error',
          element: img,
          rule: 'Images must have alt text',
          description: 'All images must have alternative text for screen readers',
          impact: 'serious',
          wcagLevel: 'A',
          suggestions: ['Add an alt attribute to the image', 'Use alt="" for decorative images']
        });
      }
    });

    // Проверяем контрастность цветов
    const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, a, button');
    for (const element of textElements) {
      const contrast = await this.calculateColorContrast(element as HTMLElement);
      if (contrast < 4.5) {
        violations.push({
          id: `contrast-${Math.random().toString(36).substr(2, 9)}`,
          type: 'error',
          element: element as HTMLElement,
          rule: 'Text must have sufficient color contrast',
          description: `Text contrast ratio is ${contrast.toFixed(2)}, but should be at least 4.5:1`,
          impact: 'serious',
          wcagLevel: 'AA',
          suggestions: ['Increase color contrast', 'Use darker text on light backgrounds']
        });
      }
    }

    // Проверяем наличие заголовков
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    if (headings.length === 0) {
      violations.push({
        id: 'no-headings',
        type: 'warning',
        element: document.body,
        rule: 'Page should have heading structure',
        description: 'Pages should have a logical heading structure',
        impact: 'moderate',
        wcagLevel: 'AA',
        suggestions: ['Add heading elements to structure content', 'Use h1 for main page title']
      });
    }

    return violations;
  }

  /**
   * Вычисляет контрастность цветов
   */
  private async calculateColorContrast(element: HTMLElement): Promise<number> {
    const style = window.getComputedStyle(element);
    const textColor = style.color;
    const backgroundColor = style.backgroundColor;

    // Упрощенная реализация - в реальном проекте используйте более точные вычисления
    return 4.5; // Заглушка
  }

  /**
   * Обновляет конфигурацию
   */
  public updateConfig(newConfig: Partial<AccessibilityConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.applyAccessibilitySettings();
  }

  /**
   * Получает текущую конфигурацию
   */
  public getConfig(): AccessibilityConfig {
    return { ...this.config };
  }

  /**
   * Настройка поддержки скринридеров
   */
  private setupScreenReaderSupport(): void {
    if (!this.config.enableScreenReader) return;

    // Добавляем метаданные для скринридеров
    const meta = document.createElement('meta');
    meta.name = 'screen-reader-optimized';
    meta.content = 'true';
    document.head.appendChild(meta);

    // Настраиваем объявления для скринридеров
    this.announceToScreenReader('Accessibility features enabled');
  }

  /**
   * Мониторинг цветового контраста
   */
  private setupColorContrastMonitoring(): void {
    if (!this.config.enableHighContrast) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.checkColorContrast(node as Element);
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  /**
   * Настройка предпочтений движения
   */
  private setupMotionPreferences(): void {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');

    const handleMotionChange = (e: MediaQueryListEvent) => {
      this.config.enableReducedMotion = e.matches;
      this.applyMotionSettings();
    };

    prefersReducedMotion.addEventListener('change', handleMotionChange);

    // Применяем начальные настройки
    this.config.enableReducedMotion = prefersReducedMotion.matches;
    this.applyMotionSettings();
  }

  /**
   * Применение настроек движения
   */
  private applyMotionSettings(): void {
    if (this.config.enableReducedMotion) {
      document.documentElement.style.setProperty('--animation-duration', '0.01ms');
      document.documentElement.style.setProperty('--transition-duration', '0.01ms');
    } else {
      document.documentElement.style.removeProperty('--animation-duration');
      document.documentElement.style.removeProperty('--transition-duration');
    }
  }

  /**
   * Проверка цветового контраста
   */
  private checkColorContrast(element: Element): void {
    const computedStyle = window.getComputedStyle(element);
    const backgroundColor = computedStyle.backgroundColor;
    const color = computedStyle.color;

    // Простая проверка контраста (в реальном приложении используйте более сложный алгоритм)
    if (backgroundColor && color) {
      const contrast = this.calculateContrast(backgroundColor, color);
      if (contrast < 4.5) {
        console.warn('Low color contrast detected', {
          element,
          backgroundColor,
          color,
          contrast,
        });
      }
    }
  }

  /**
   * Расчет контраста (упрощенная версия)
   */
  private calculateContrast(bg: string, fg: string): number {
    // Упрощенный расчет контраста
    // В реальном приложении используйте WCAG алгоритм
    return Math.random() * 10; // Заглушка
  }

  /**
   * Объявление для скринридера
   */
  public announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Удаляем объявление через короткое время
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }

  /**
   * Уничтожает менеджер доступности
   */
  public destroy(): void {
    // Удаляем слушатели событий
    this.keyboardListeners.forEach((handler) => {
      document.removeEventListener('keydown', handler);
    });

    // Отключаем наблюдатели
    this.mutationObserver?.disconnect();
    this.resizeObserver?.disconnect();

    // Удаляем ARIA Live регионы
    this.ariaLiveRegions.forEach((region) => {
      region.remove();
    });

    // Очищаем коллекции
    this.focusableElements = [];
    this.ariaLiveRegions.clear();
    this.keyboardListeners.clear();
  }
}

// Глобальный экземпляр
export const accessibilityManager = new AccessibilityManager();
