/**
 * Расширение для быстрого переключения темной темы
 * Демонстрирует возможности улучшенной системы расширений
 */

(function() {
  // Регистрируем расширение
  /**
 * Пример расширения для управления темным режимом
 * Демонстрирует:
 * - Регистрацию расширения с разрешениями
 * - Взаимодействие с ThemeManager
 * - Обработку пользовательских действий
 */
window.browserAPI.registerExtension({
    id: 'dark-mode-toggle',
    name: 'Переключатель темной темы',
    version: '1.0.0',
    capabilities: ['storage']
  }).then(result => {
    if (result.success) {
      console.log('Расширение Dark Mode Toggle успешно зарегистрировано');
      
      // Создаем кнопку переключения темы
      const button = document.createElement('button');
      button.textContent = '🌙';
      button.title = 'Переключить темную тему';
      button.style.position = 'fixed';
      button.style.bottom = '20px';
      button.style.right = '20px';
      button.style.zIndex = '1000';
      button.style.width = '40px';
      button.style.height = '40px';
      button.style.borderRadius = '50%';
      button.style.border = 'none';
      button.style.backgroundColor = '#4285f4';
      button.style.color = 'white';
      button.style.fontSize = '18px';
      button.style.cursor = 'pointer';
      button.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
      
      // Проверяем текущее состояние темы
      const darkModeToggle = document.getElementById('dark-mode');
      if (darkModeToggle && darkModeToggle.checked) {
        button.textContent = '☀️';
      }
      
      // Добавляем обработчик клика
      button.addEventListener('click', () => {
        const darkModeToggle = document.getElementById('dark-mode');
        if (darkModeToggle) {
          darkModeToggle.checked = !darkModeToggle.checked;
          
          // Вызываем событие change для применения темы
          const event = new Event('change');
          darkModeToggle.dispatchEvent(event);
          
          // Обновляем иконку кнопки
          button.textContent = darkModeToggle.checked ? '☀️' : '🌙';
        }
      });
      
      // Добавляем кнопку на страницу
      document.body.appendChild(button);
    } else {
      console.error('Ошибка при регистрации расширения:', result.error);
    }
  }).catch(error => {
    console.error('Ошибка при регистрации расширения:', error);
  });
})();