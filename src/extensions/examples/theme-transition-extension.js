/**
 * Расширение для A11 Browser: Плавный переход между темами
 * 
 * Это расширение добавляет плавные переходы между светлой и тёмной темами,
 * улучшая визуальный опыт пользователя при смене режимов.
 */

// Регистрация расширения
window.browserAPI.registerExtension({
  id: 'theme-transition',
  name: 'Плавные переходы тем',
  version: '1.0.0',
  capabilities: ['storage']
}).then(result => {
  if (result.success) {
    initThemeTransitionExtension();
  } else {
    console.error('Не удалось зарегистрировать расширение:', result.error);
  }
});

// Инициализация расширения
function initThemeTransitionExtension() {
  // Добавляем стили для плавного перехода
  addTransitionStyles();
  
  // Добавляем обработчик события изменения настроек
  window.addEventListener('settings-changed', handleSettingsChange);
  
  // Добавляем настройки в интерфейс
  addTransitionSettings();
  
  console.log('Расширение "Плавные переходы тем" активировано');
}

// Добавление стилей для плавного перехода
function addTransitionStyles() {
  try {
    // Проверяем доступность document
    if (typeof document === 'undefined') {
      console.warn('DOM недоступен, невозможно добавить стили переходов');
      return;
    }
    
    // Создаем элемент стиля
    const styleElement = document.createElement('style');
    styleElement.id = 'theme-transition-styles';
    
    // Определяем стили для плавного перехода
    styleElement.textContent = `
      /* Плавный переход для всех элементов при смене темы */
      body, .browser-container, .tabs-container, .browser-toolbar, .tab,
      .panel, input, button, .browser-content, .settings-panel,
      .extensions-panel, .bookmarks-panel, .history-panel,
      .notifications-panel, .autocomplete-list, .notification-popup {
        transition: background-color 0.5s ease, color 0.5s ease, border-color 0.5s ease, box-shadow 0.5s ease;
      }
      
      /* Плавный переход для иконок и SVG */
      svg, img, .icon {
        transition: filter 0.5s ease;
      }
    `;
    
    // Добавляем стили в head
    document.head.appendChild(styleElement);
  } catch (error) {
    console.error('Ошибка при добавлении стилей переходов:', error);
  }
}

// Обработчик изменения настроек
function handleSettingsChange(event) {
  try {
    const settings = event.detail;
    
    // Проверяем, изменился ли режим темы
    if (settings && settings.darkMode !== undefined) {
      // Применяем анимацию перехода
      applyThemeTransition(settings.darkMode);
    }
  } catch (error) {
    console.error('Ошибка при обработке изменения настроек:', error);
  }
}

// Применение анимации перехода при смене темы
function applyThemeTransition(isDarkMode) {
  try {
    // Получаем текущие настройки переходов
    window.browserAPI.getSettings().then(settings => {
      // Проверяем, включены ли переходы
      const transitionEnabled = settings?.themeTransitionSettings?.enabled !== false;
      
      if (!transitionEnabled) {
        return; // Переходы отключены в настройках
      }
      
      // Получаем длительность перехода из настроек или используем значение по умолчанию
      const duration = settings?.themeTransitionSettings?.duration || 500;
      
      // Обновляем стили с новой длительностью и добавляем плавность переходов
      const styleElement = document.getElementById('theme-transition-styles');
      if (styleElement) {
        styleElement.textContent = `
          /* Плавный переход для всех элементов при смене темы */
          body, .browser-container, .tabs-container, .browser-toolbar, .tab,
          .panel, input, button, .browser-content, .settings-panel,
          .extensions-panel, .bookmarks-panel, .history-panel,
          .notifications-panel, .autocomplete-list, .notification-popup {
            transition: background-color ${duration}ms cubic-bezier(0.4, 0, 0.2, 1), 
                        color ${duration}ms cubic-bezier(0.4, 0, 0.2, 1), 
                        border-color ${duration}ms cubic-bezier(0.4, 0, 0.2, 1), 
                        box-shadow ${duration}ms cubic-bezier(0.4, 0, 0.2, 1);
          }
          
          /* Плавный переход для иконок и SVG */
          svg, img, .icon {
            transition: filter ${duration}ms cubic-bezier(0.4, 0, 0.2, 1);
          }
          
          /* Дополнительные стили для плавности */
          .theme-transitioning {
            animation: theme-pulse ${duration * 0.5}ms ease-in-out;
          }
          
          @keyframes theme-pulse {
            0% { opacity: 1; }
            50% { opacity: 0.95; }
            100% { opacity: 1; }
          }
        `;
      }
      
      // Добавляем класс анимации
      document.body.classList.add('theme-transitioning');
      
      // Показываем уведомление о смене темы
      const themeMessage = isDarkMode ? 'Переключение на тёмную тему' : 'Переключение на светлую тему';
      showThemeChangeNotification(themeMessage, duration);
      
      // Удаляем класс анимации после завершения перехода
      setTimeout(() => {
        document.body.classList.remove('theme-transitioning');
      }, duration + 50);
      
    }).catch(error => {
      console.error('Ошибка при получении настроек для анимации перехода:', error);
    });
  } catch (error) {
    console.error('Ошибка при применении анимации перехода:', error);
  }
}

// Показать уведомление о смене темы
function showThemeChangeNotification(message, duration) {
  try {
    // Проверяем, существует ли уже уведомление
    let notification = document.getElementById('theme-change-notification');
    
    // Если уведомление уже существует, удаляем его
    if (notification) {
      document.body.removeChild(notification);
    }
    
    // Создаем новое уведомление
    notification = document.createElement('div');
    notification.id = 'theme-change-notification';
    notification.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 20px;
      background-color: var(--notification-bg, rgba(50, 50, 50, 0.8));
      color: var(--notification-text, white);
      padding: 10px 15px;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      z-index: 9999;
      font-size: 14px;
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 300ms ease, transform 300ms ease;
    `;
    
    // Добавляем индикатор прогресса
    const progressBar = document.createElement('div');
    progressBar.style.cssText = `
      height: 3px;
      background-color: #4285f4;
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
      border-radius: 0 0 4px 4px;
      transform-origin: left;
    `;
    
    // Добавляем содержимое уведомления
    notification.textContent = message;
    notification.appendChild(progressBar);
    document.body.appendChild(notification);
    
    // Анимируем появление уведомления
    setTimeout(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateY(0)';
      
      // Анимируем индикатор прогресса
      progressBar.style.transition = `width ${duration}ms linear`;
      progressBar.style.width = '0';
    }, 10);
    
    // Удаляем уведомление после завершения перехода
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateY(20px)';
      
      // Удаляем элемент после завершения анимации
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, duration);
  } catch (error) {
    console.error('Ошибка при отображении уведомления о смене темы:', error);
  }
}

// Добавление настроек переходов в интерфейс
function addTransitionSettings() {
  try {
    // Проверяем доступность document
    if (typeof document === 'undefined') {
      console.warn('DOM недоступен, невозможно добавить настройки переходов');
      return;
    }
    
    // Находим контейнер настроек
    const settingsContainer = document.querySelector('.settings-container') || 
                             document.querySelector('.settings-panel') || 
                             document.querySelector('.settings');
    
    if (!settingsContainer) {
      console.warn('Контейнер настроек не найден, невозможно добавить настройки переходов');
      return;
    }
    
    // Создаем контейнер для настроек переходов, если его еще нет
    let transitionSettingsContainer = document.getElementById('theme-transition-settings');
    
    if (!transitionSettingsContainer) {
      transitionSettingsContainer = document.createElement('div');
      transitionSettingsContainer.id = 'theme-transition-settings';
      transitionSettingsContainer.className = 'settings-section';
      transitionSettingsContainer.innerHTML = '<h3>Настройки переходов тем</h3>';
      
      // Получаем текущие настройки
      window.browserAPI.getSettings().then(settings => {
        const transitionEnabled = settings?.themeTransitionSettings?.enabled !== false;
        const transitionDuration = settings?.themeTransitionSettings?.duration || 500;
        
        // Создаем элементы управления для настройки переходов
        const transitionSettingsHTML = `
          <div class="setting-item">
            <label>
              <input type="checkbox" id="theme-transition-enabled" ${transitionEnabled ? 'checked' : ''}>
              Включить плавные переходы между темами
            </label>
          </div>
          <div class="setting-item">
            <label for="theme-transition-duration">Длительность перехода (мс):</label>
            <input type="range" id="theme-transition-duration" min="100" max="1000" step="50" value="${transitionDuration}">
            <span id="transition-duration-value">${transitionDuration}</span>
          </div>
          <div class="setting-item">
            <button id="save-transition-settings">Сохранить настройки переходов</button>
          </div>
        `;
        
        transitionSettingsContainer.innerHTML += transitionSettingsHTML;
        settingsContainer.appendChild(transitionSettingsContainer);
        
        // Добавляем обработчики для элементов управления
        const durationSlider = document.getElementById('theme-transition-duration');
        const durationValue = document.getElementById('transition-duration-value');
        
        if (durationSlider && durationValue) {
          durationSlider.addEventListener('input', function() {
            durationValue.textContent = this.value;
          });
        }
        
        // Добавляем обработчик для сохранения настроек
        const saveButton = document.getElementById('save-transition-settings');
        if (saveButton) {
          saveButton.addEventListener('click', saveTransitionSettings);
        }
      }).catch(error => {
        console.error('Ошибка при получении настроек для интерфейса переходов:', error);
      });
    }
  } catch (error) {
    console.error('Ошибка при добавлении настроек переходов:', error);
  }
}

// Сохранение настроек переходов
async function saveTransitionSettings() {
  try {
    const enabledCheckbox = document.getElementById('theme-transition-enabled');
    const durationSlider = document.getElementById('theme-transition-duration');
    
    if (!enabledCheckbox || !durationSlider) {
      console.warn('Элементы управления настройками переходов не найдены');
      return;
    }
    
    const enabled = enabledCheckbox.checked;
    const duration = parseInt(durationSlider.value, 10);
    
    // Создаем индикатор сохранения
    const saveIndicator = createSaveIndicator();
    
    try {
      // Получаем текущие настройки
      const settings = await window.browserAPI.getSettings() || {};
      
      // Проверяем, изменились ли настройки
      const currentSettings = settings.themeTransitionSettings || {};
      const settingsChanged = currentSettings.enabled !== enabled || currentSettings.duration !== duration;
      
      if (!settingsChanged) {
        // Если настройки не изменились, просто показываем уведомление
        updateSaveIndicator(saveIndicator, 'success', 'Настройки не изменились');
        return;
      }
      
      // Обновляем только настройки переходов, не затрагивая остальные
      settings.themeTransitionSettings = {
        enabled: enabled,
        duration: duration
      };
      
      // Сохраняем настройки
      await window.browserAPI.saveSettings(settings);
      
      console.log('Настройки переходов успешно сохранены:', settings.themeTransitionSettings);
      
      // Обновляем стили с новыми настройками
      if (enabled) {
        updateTransitionStyles(duration);
      }
      
      // Обновляем индикатор сохранения
      updateSaveIndicator(saveIndicator, 'success', 'Настройки сохранены');
      
      // Показываем уведомление
      if (window.sendBrowserNotification) {
        window.sendBrowserNotification({
          title: 'Настройки переходов обновлены',
          message: enabled ? `Плавные переходы включены (${duration} мс)` : 'Плавные переходы отключены',
          type: 'success'
        });
      }
    } catch (error) {
      console.error('Ошибка при сохранении настроек переходов:', error);
      
      // Обновляем индикатор сохранения
      updateSaveIndicator(saveIndicator, 'error', 'Ошибка сохранения');
      
      // Показываем уведомление об ошибке
      if (window.sendBrowserNotification) {
        window.sendBrowserNotification({
          title: 'Ошибка',
          message: 'Не удалось сохранить настройки переходов',
          type: 'error'
        });
      }
    }
  } catch (error) {
    console.error('Ошибка при сохранении настроек переходов:', error);
  }
}

// Создание индикатора сохранения
function createSaveIndicator() {
  // Проверяем, существует ли уже индикатор
  let indicator = document.getElementById('save-indicator');
  
  if (indicator) {
    return indicator;
  }
  
  // Создаем новый индикатор
  indicator = document.createElement('span');
  indicator.id = 'save-indicator';
  indicator.style.cssText = `
    margin-left: 10px;
    font-size: 14px;
    opacity: 0;
    transition: opacity 300ms ease;
  `;
  
  // Добавляем индикатор рядом с кнопкой сохранения
  const saveButton = document.getElementById('save-transition-settings');
  if (saveButton && saveButton.parentNode) {
    saveButton.parentNode.appendChild(indicator);
  }
  
  return indicator;
}

// Обновление индикатора сохранения
function updateSaveIndicator(indicator, status, message) {
  if (!indicator) return;
  
  // Устанавливаем цвет в зависимости от статуса
  let color = '#4285f4'; // Синий по умолчанию
  
  if (status === 'success') {
    color = '#0f9d58'; // Зеленый для успеха
  } else if (status === 'error') {
    color = '#db4437'; // Красный для ошибки
  }
  
  // Обновляем стиль и содержимое
  indicator.style.color = color;
  indicator.textContent = message;
  indicator.style.opacity = '1';
  
  // Скрываем индикатор через 3 секунды
  setTimeout(() => {
    indicator.style.opacity = '0';
  }, 3000);
}

// Обновление стилей переходов
function updateTransitionStyles(duration) {
  const styleElement = document.getElementById('theme-transition-styles');
  if (!styleElement) return;
  
  styleElement.textContent = `
    /* Плавный переход для всех элементов при смене темы */
    body, .browser-container, .tabs-container, .browser-toolbar, .tab,
    .panel, input, button, .browser-content, .settings-panel,
    .extensions-panel, .bookmarks-panel, .history-panel,
    .notifications-panel, .autocomplete-list, .notification-popup {
      transition: background-color ${duration}ms cubic-bezier(0.4, 0, 0.2, 1), 
                  color ${duration}ms cubic-bezier(0.4, 0, 0.2, 1), 
                  border-color ${duration}ms cubic-bezier(0.4, 0, 0.2, 1), 
                  box-shadow ${duration}ms cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    /* Плавный переход для иконок и SVG */
    svg, img, .icon {
      transition: filter ${duration}ms cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    /* Дополнительные стили для плавности */
    .theme-transitioning {
      animation: theme-pulse ${duration * 0.5}ms ease-in-out;
    }
    
    @keyframes theme-pulse {
      0% { opacity: 1; }
      50% { opacity: 0.95; }
      100% { opacity: 1; }
    }
  `;
}