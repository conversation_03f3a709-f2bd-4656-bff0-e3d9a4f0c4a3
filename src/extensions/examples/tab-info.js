/**
 * Расширение для отображения информации о текущей вкладке
 * Демонстрирует возможности улучшенного API для работы с вкладками
 */

(function() {
  // Регистрируем расширение
  window.browserAPI.registerExtension({
    id: 'tab-info',
    name: 'Информация о вкладке',
    version: '1.0.0',
    capabilities: ['tabs', 'storage']
  }).then(result => {
    if (result.success) {
      console.log('Расширение Tab Info успешно зарегистрировано');
      
      // Создаем панель информации
      const infoPanel = document.createElement('div');
      infoPanel.style.position = 'fixed';
      infoPanel.style.top = '10px';
      infoPanel.style.left = '10px';
      infoPanel.style.zIndex = '1000';
      infoPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      infoPanel.style.color = 'white';
      infoPanel.style.padding = '10px';
      infoPanel.style.borderRadius = '5px';
      infoPanel.style.fontSize = '12px';
      infoPanel.style.maxWidth = '300px';
      infoPanel.style.display = 'none';
      infoPanel.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.3)';
      
      // Создаем кнопку для отображения информации
      const button = document.createElement('button');
      button.textContent = 'ℹ️';
      button.title = 'Показать информацию о вкладке';
      button.style.position = 'fixed';
      button.style.bottom = '70px';
      button.style.right = '20px';
      button.style.zIndex = '1000';
      button.style.width = '40px';
      button.style.height = '40px';
      button.style.borderRadius = '50%';
      button.style.border = 'none';
      button.style.backgroundColor = '#4285f4';
      button.style.color = 'white';
      button.style.fontSize = '18px';
      button.style.cursor = 'pointer';
      button.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
      
      // Функция для обновления информации о вкладке
      function updateTabInfo() {
        const activeWebview = document.querySelector('webview.active');
        if (activeWebview) {
          const url = activeWebview.getURL();
          const title = activeWebview.getTitle() || 'Без названия';
          const canGoBack = activeWebview.canGoBack();
          const canGoForward = activeWebview.canGoForward();
          
          infoPanel.innerHTML = `
            <h3 style="margin: 0 0 5px 0;">Информация о вкладке</h3>
            <p style="margin: 0 0 3px 0;"><strong>Заголовок:</strong> ${title}</p>
            <p style="margin: 0 0 3px 0;"><strong>URL:</strong> ${url}</p>
            <p style="margin: 0 0 3px 0;"><strong>Навигация:</strong> 
              ${canGoBack ? '⬅️ Можно назад' : '⬅️ Нельзя назад'} | 
              ${canGoForward ? '➡️ Можно вперед' : '➡️ Нельзя вперед'}
            </p>
          `;
        } else {
          infoPanel.innerHTML = '<p>Нет активной вкладки</p>';
        }
      }
      
      // Добавляем обработчик клика по кнопке
      let isPanelVisible = false;
      button.addEventListener('click', () => {
        isPanelVisible = !isPanelVisible;
        infoPanel.style.display = isPanelVisible ? 'block' : 'none';
        
        if (isPanelVisible) {
          updateTabInfo();
        }
      });
      
      // Обработчик события загрузки страницы
      document.addEventListener('webview-did-stop-loading', () => {
        if (isPanelVisible) {
          updateTabInfo();
        }
      });
      
      // Обработчик события переключения вкладок
      document.addEventListener('tab-activated', () => {
        if (isPanelVisible) {
          updateTabInfo();
        }
      });
      
      // Добавляем элементы на страницу
      document.body.appendChild(infoPanel);
      document.body.appendChild(button);
    } else {
      console.error('Ошибка при регистрации расширения:', result.error);
    }
  }).catch(error => {
    console.error('Ошибка при регистрации расширения:', error);
  });
})();