/**
 * Расширение для A11 Browser: Система уведомлений
 * 
 * Это расширение добавляет централизованную систему уведомлений для браузера,
 * позволяя другим расширениям отправлять и управлять уведомлениями.
 */

// Регистрация расширения
/**
 * Пример расширения для работы с системными уведомлениями
 * Демонстрирует:
 * - Регистрацию обработчика уведомлений
 * - Использование permissions: 'notifications'
 * - Взаимодействие с нативным API браузера
 */
window.browserAPI.registerExtension({
  id: 'notifications-system',
  name: 'Система уведомлений',
  version: '1.0.0',
  capabilities: ['storage']
}).then(result => {
  if (result.success) {
    initNotificationsSystem();
  } else {
    console.error('Не удалось зарегистрировать расширение:', result.error);
  }
});

// Глобальные переменные для хранения уведомлений
let notifications = [];
let notificationCounter = 0;

// Инициализация системы уведомлений
function initNotificationsSystem() {
  // Создаем контейнер для уведомлений
  createNotificationsContainer();
  
  // Добавляем стили для уведомлений
  addNotificationStyles();
  
  // Загружаем сохраненные уведомления
  loadNotifications();
  
  // Добавляем глобальный метод для отправки уведомлений
  window.sendBrowserNotification = sendNotification;
  
  // Добавляем кнопку уведомлений в панель инструментов
  addNotificationButton();
  
  // Настраиваем обработчики событий
  setupEventListeners();
  
  console.log('Расширение "Система уведомлений" активировано');
}

// Создание контейнера для уведомлений
function createNotificationsContainer() {
  const container = document.createElement('div');
  container.id = 'notifications-container';
  document.body.appendChild(container);
  
  // Создаем панель уведомлений
  const panel = document.createElement('div');
  panel.id = 'notifications-panel';
  panel.className = 'panel hidden';
  panel.innerHTML = `
    <h2>Уведомления</h2>
    <div id="notifications-list"></div>
    <div class="notifications-actions">
      <button id="clear-notifications">Очистить все</button>
      <button id="close-notifications">Закрыть</button>
    </div>
  `;
  
  document.querySelector('.browser-container').appendChild(panel);
}

// Добавление кнопки уведомлений в панель инструментов
function addNotificationButton() {
  const toolbarActions = document.querySelector('.toolbar-actions');
  if (!toolbarActions) return;
  
  const notificationButton = document.createElement('button');
  notificationButton.id = 'notifications-button';
  notificationButton.title = 'Уведомления';
  notificationButton.innerHTML = '🔔';
  notificationButton.addEventListener('click', toggleNotificationsPanel);
  
  // Добавляем счетчик уведомлений
  const counter = document.createElement('span');
  counter.id = 'notifications-counter';
  counter.className = 'hidden';
  counter.textContent = '0';
  notificationButton.appendChild(counter);
  
  // Вставляем кнопку перед кнопкой настроек
  const settingsButton = document.getElementById('settings-button');
  if (settingsButton) {
    toolbarActions.insertBefore(notificationButton, settingsButton);
  } else {
    toolbarActions.appendChild(notificationButton);
  }
}

// Загрузка сохраненных уведомлений
async function loadNotifications() {
  try {
    const settings = await window.browserAPI.getSettings();
    notifications = settings.notifications || [];
    
    // Обновляем счетчик и список уведомлений
    updateNotificationCounter();
    updateNotificationsList();
  } catch (error) {
    console.error('Ошибка при загрузке уведомлений:', error);
    notifications = [];
  }
}

// Сохранение уведомлений
async function saveNotifications() {
  try {
    const settings = await window.browserAPI.getSettings();
    settings.notifications = notifications;
    await window.browserAPI.saveSettings(settings);
  } catch (error) {
    console.error('Ошибка при сохранении уведомлений:', error);
  }
}

// Отправка нового уведомления
function sendNotification(options) {
  // Проверяем параметры
  if (!options || !options.title) {
    console.error('Для уведомления требуется заголовок');
    return null;
  }
  
  // Создаем новое уведомление
  const notification = {
    id: `notification-${Date.now()}-${notificationCounter++}`,
    title: options.title,
    message: options.message || '',
    type: options.type || 'info', // info, success, warning, error
    source: options.source || 'system',
    timestamp: Date.now(),
    read: false,
    actions: options.actions || []
  };
  
  // Добавляем уведомление в список
  notifications.unshift(notification);
  
  // Ограничиваем количество уведомлений
  if (notifications.length > 50) {
    notifications = notifications.slice(0, 50);
  }
  
  // Обновляем счетчик и список
  updateNotificationCounter();
  updateNotificationsList();
  
  // Сохраняем уведомления
  saveNotifications();
  
  // Показываем всплывающее уведомление
  showPopupNotification(notification);
  
  return notification.id;
}

// Показать всплывающее уведомление
function showPopupNotification(notification) {
  // Создаем элемент уведомления
  const popup = document.createElement('div');
  popup.className = `notification-popup ${notification.type}`;
  popup.dataset.id = notification.id;
  
  // Добавляем содержимое
  popup.innerHTML = `
    <div class="notification-header">
      <span class="notification-title">${notification.title}</span>
      <button class="notification-close">×</button>
    </div>
    ${notification.message ? `<div class="notification-message">${notification.message}</div>` : ''}
    ${notification.actions.length > 0 ? createActionButtons(notification) : ''}
  `;
  
  // Добавляем в контейнер
  const container = document.getElementById('notifications-container');
  container.appendChild(popup);
  
  // Добавляем обработчики
  popup.querySelector('.notification-close').addEventListener('click', () => {
    popup.classList.add('closing');
    setTimeout(() => popup.remove(), 300);
  });
  
  // Автоматически скрываем через 5 секунд
  setTimeout(() => {
    if (popup.parentNode) {
      popup.classList.add('closing');
      setTimeout(() => popup.remove(), 300);
    }
  }, 5000);
  
  // Анимация появления
  setTimeout(() => popup.classList.add('show'), 10);
}

// Создание кнопок действий для уведомления
function createActionButtons(notification) {
  if (!notification.actions || notification.actions.length === 0) {
    return '';
  }
  
  let buttonsHtml = '<div class="notification-actions">';
  
  notification.actions.forEach(action => {
    buttonsHtml += `<button class="notification-action" data-action-id="${action.id}">${action.title}</button>`;
  });
  
  buttonsHtml += '</div>';
  return buttonsHtml;
}

// Обновление счетчика уведомлений
function updateNotificationCounter() {
  const counter = document.getElementById('notifications-counter');
  if (!counter) return;
  
  // Подсчитываем непрочитанные уведомления
  const unreadCount = notifications.filter(n => !n.read).length;
  
  if (unreadCount > 0) {
    counter.textContent = unreadCount > 99 ? '99+' : unreadCount;
    counter.classList.remove('hidden');
  } else {
    counter.classList.add('hidden');
  }
}

// Обновление списка уведомлений
function updateNotificationsList() {
  const list = document.getElementById('notifications-list');
  if (!list) return;
  
  // Очищаем список
  list.innerHTML = '';
  
  if (notifications.length === 0) {
    list.innerHTML = '<div class="no-notifications">Нет уведомлений</div>';
    return;
  }
  
  // Добавляем уведомления в список
  notifications.forEach(notification => {
    const item = document.createElement('div');
    item.className = `notification-item ${notification.read ? 'read' : 'unread'}`;
    item.dataset.id = notification.id;
    
    // Форматируем дату
    const date = new Date(notification.timestamp);
    const formattedDate = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
    
    // Добавляем содержимое
    item.innerHTML = `
      <div class="notification-item-header">
        <span class="notification-item-title">${notification.title}</span>
        <span class="notification-item-time">${formattedDate}</span>
      </div>
      ${notification.message ? `<div class="notification-item-message">${notification.message}</div>` : ''}
      <div class="notification-item-footer">
        <span class="notification-item-source">${notification.source}</span>
        <button class="notification-item-delete">Удалить</button>
      </div>
    `;
    
    // Добавляем обработчики
    item.addEventListener('click', () => markNotificationAsRead(notification.id));
    item.querySelector('.notification-item-delete').addEventListener('click', (e) => {
      e.stopPropagation();
      deleteNotification(notification.id);
    });
    
    list.appendChild(item);
  });
}

// Отметить уведомление как прочитанное
function markNotificationAsRead(id) {
  const notification = notifications.find(n => n.id === id);
  if (notification && !notification.read) {
    notification.read = true;
    
    // Обновляем UI
    updateNotificationCounter();
    const item = document.querySelector(`.notification-item[data-id="${id}"]`);
    if (item) {
      item.classList.remove('unread');
      item.classList.add('read');
    }
    
    // Сохраняем изменения
    saveNotifications();
  }
}

// Удаление уведомления
function deleteNotification(id) {
  const index = notifications.findIndex(n => n.id === id);
  if (index !== -1) {
    notifications.splice(index, 1);
    
    // Обновляем UI
    updateNotificationCounter();
    updateNotificationsList();
    
    // Сохраняем изменения
    saveNotifications();
  }
}

// Очистка всех уведомлений
function clearAllNotifications() {
  notifications = [];
  
  // Обновляем UI
  updateNotificationCounter();
  updateNotificationsList();
  
  // Сохраняем изменения
  saveNotifications();
}

// Переключение панели уведомлений
function toggleNotificationsPanel() {
  const panel = document.getElementById('notifications-panel');
  if (!panel) return;
  
  // Закрываем другие панели
  document.querySelectorAll('.panel').forEach(p => {
    if (p.id !== 'notifications-panel') {
      p.classList.add('hidden');
    }
  });
  
  // Переключаем видимость панели уведомлений
  panel.classList.toggle('hidden');
  
  // Если панель открыта, отмечаем все уведомления как прочитанные
  if (!panel.classList.contains('hidden')) {
    notifications.forEach(notification => {
      if (!notification.read) {
        notification.read = true;
      }
    });
    
    // Обновляем UI
    updateNotificationCounter();
    updateNotificationsList();
    
    // Сохраняем изменения
    saveNotifications();
  }
}

// Настройка обработчиков событий
function setupEventListeners() {
  // Обработчик для кнопки закрытия панели уведомлений
  const closeButton = document.getElementById('close-notifications');
  if (closeButton) {
    closeButton.addEventListener('click', () => {
      const panel = document.getElementById('notifications-panel');
      if (panel) {
        panel.classList.add('hidden');
      }
    });
  }
  
  // Обработчик для кнопки очистки уведомлений
  const clearButton = document.getElementById('clear-notifications');
  if (clearButton) {
    clearButton.addEventListener('click', clearAllNotifications);
  }
  
  // Обработчик для действий в уведомлениях
  document.addEventListener('click', (e) => {
    if (e.target.classList.contains('notification-action')) {
      const actionId = e.target.dataset.actionId;
      const notificationId = e.target.closest('.notification-popup')?.dataset.id;
      
      if (actionId && notificationId) {
        const notification = notifications.find(n => n.id === notificationId);
        if (notification) {
          const action = notification.actions.find(a => a.id === actionId);
          if (action && action.callback && typeof action.callback === 'function') {
            action.callback();
          }
        }
        
        // Закрываем всплывающее уведомление
        const popup = e.target.closest('.notification-popup');
        if (popup) {
          popup.classList.add('closing');
          setTimeout(() => popup.remove(), 300);
        }
      }
    }
  });
  
  // Обработчик для событий от других расширений
  window.addEventListener('extension-notification', (event) => {
    if (event.detail && event.detail.notification) {
      sendNotification(event.detail.notification);
    }
  });
}

// Добавление стилей для уведомлений
function addNotificationStyles() {
  const style = document.createElement('style');
  style.textContent = `
    /* Контейнер для всплывающих уведомлений */
    #notifications-container {
      position: fixed;
      top: 10px;
      right: 10px;
      width: 300px;
      max-width: 100%;
      z-index: 9999;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    
    /* Стили для всплывающих уведомлений */
    .notification-popup {
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 10px;
      margin-bottom: 10px;
      opacity: 0;
      transform: translateX(50px);
      transition: opacity 0.3s, transform 0.3s;
      border-left: 4px solid #ccc;
    }
    
    .notification-popup.show {
      opacity: 1;
      transform: translateX(0);
    }
    
    .notification-popup.closing {
      opacity: 0;
      transform: translateX(50px);
    }
    
    /* Типы уведомлений */
    .notification-popup.info {
      border-left-color: #2196F3;
    }
    
    .notification-popup.success {
      border-left-color: #4CAF50;
    }
    
    .notification-popup.warning {
      border-left-color: #FF9800;
    }
    
    .notification-popup.error {
      border-left-color: #F44336;
    }
    
    /* Заголовок уведомления */
    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
    }
    
    .notification-title {
      font-weight: bold;
    }
    
    .notification-close {
      background: transparent;
      border: none;
      font-size: 16px;
      cursor: pointer;
      opacity: 0.7;
    }
    
    .notification-close:hover {
      opacity: 1;
    }
    
    /* Сообщение уведомления */
    .notification-message {
      margin-bottom: 10px;
    }
    
    /* Кнопки действий */
    .notification-actions {
      display: flex;
      gap: 5px;
      justify-content: flex-end;
    }
    
    .notification-action {
      background-color: #f0f0f0;
      border: none;
      border-radius: 3px;
      padding: 5px 10px;
      cursor: pointer;
      font-size: 12px;
    }
    
    .notification-action:hover {
      background-color: #e0e0e0;
    }
    
    /* Кнопка уведомлений в панели инструментов */
    #notifications-button {
      position: relative;
    }
    
    #notifications-counter {
      position: absolute;
      top: -5px;
      right: -5px;
      background-color: #F44336;
      color: white;
      border-radius: 50%;
      width: 16px;
      height: 16px;
      font-size: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    #notifications-counter.hidden {
      display: none;
    }
    
    /* Панель уведомлений */
    #notifications-panel {
      width: 350px;
      max-height: 500px;
      overflow-y: auto;
    }
    
    /* Список уведомлений */
    #notifications-list {
      max-height: 400px;
      overflow-y: auto;
    }
    
    .no-notifications {
      padding: 20px;
      text-align: center;
      color: #999;
    }
    
    /* Элемент уведомления в списке */
    .notification-item {
      padding: 10px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
    }
    
    .notification-item:hover {
      background-color: #f9f9f9;
    }
    
    .notification-item.unread {
      background-color: #f0f7ff;
    }
    
    .notification-item-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
    
    .notification-item-title {
      font-weight: bold;
    }
    
    .notification-item-time {
      font-size: 12px;
      color: #999;
    }
    
    .notification-item-message {
      margin-bottom: 5px;
    }
    
    .notification-item-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
    }
    
    .notification-item-source {
      color: #666;
    }
    
    .notification-item-delete {
      background: transparent;
      border: none;
      color: #F44336;
      cursor: pointer;
      padding: 2px 5px;
    }
    
    .notification-item-delete:hover {
      text-decoration: underline;
    }
    
    .notifications-actions {
      display: flex;
      justify-content: flex-end;
      padding: 10px;
      gap: 10px;
    }
    
    /* Стили для темного режима */
    body.dark-mode .notification-popup {
      background-color: #333;
      color: #eee;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }
    
    body.dark-mode .notification-action {
      background-color: #444;
      color: #eee;
    }
    
    body.dark-mode .notification-action:hover {
      background-color: #555;
    }
    
    body.dark-mode .notification-item {
      border-bottom-color: #444;
    }
    
    body.dark-mode .notification-item:hover {
      background-color: #383838;
    }
    
    body.dark-mode .notification-item.unread {
      background-color: #2c3e50;
    }
    
    body.dark-mode .notification-item-time,
    body.dark-mode .notification-item-source {
      color: #aaa;
    }
  `;
  
  document.head.appendChild(style);
}