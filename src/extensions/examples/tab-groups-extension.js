/**
 * Расширение для A11 Browser: Группировка вкладок и оптимизация производительности
 * 
 * Это расширение добавляет возможность группировать вкладки и оптимизирует
 * производительность браузера при работе с большим количеством вкладок.
 */

// Регистрация расширения
window.browserAPI.registerExtension({
  id: 'tab-groups',
  name: 'Группировка вкладок',
  version: '1.0.0',
  capabilities: ['tabs', 'storage']
}).then(result => {
  if (result.success) {
    initTabGroupsExtension();
  } else {
    console.error('Не удалось зарегистрировать расширение:', result.error);
  }
});

// Глобальные переменные для хранения групп и их состояния
let tabGroups = [];
let activeGroupId = null;

// Инициализация расширения
function initTabGroupsExtension() {
  // Загружаем сохраненные группы
  loadTabGroups();
  
  // Добавляем элементы интерфейса для управления группами
  createTabGroupsUI();
  
  // Настраиваем оптимизацию производительности
  setupPerformanceOptimization();
  
  // Добавляем обработчики событий
  setupEventListeners();
  
  console.log('Расширение "Группировка вкладок" активировано');
}

// Загрузка сохраненных групп вкладок
async function loadTabGroups() {
  try {
    const settings = await window.browserAPI.getSettings();
    tabGroups = settings.tabGroups || [];
    activeGroupId = settings.activeGroupId || null;
    
    // Если групп нет, создаем группу по умолчанию
    if (tabGroups.length === 0) {
      const defaultGroup = {
        id: generateId(),
        name: 'Основная',
        color: '#4285f4',
        tabs: []
      };
      
      tabGroups.push(defaultGroup);
      activeGroupId = defaultGroup.id;
      
      // Сохраняем изменения
      await saveTabGroups();
    }
  } catch (error) {
    console.error('Ошибка при загрузке групп вкладок:', error);
    
    // Создаем группу по умолчанию в случае ошибки
    tabGroups = [{
      id: generateId(),
      name: 'Основная',
      color: '#4285f4',
      tabs: []
    }];
    
    activeGroupId = tabGroups[0].id;
  }
}

// Сохранение групп вкладок
async function saveTabGroups() {
  try {
    const settings = await window.browserAPI.getSettings();
    settings.tabGroups = tabGroups;
    settings.activeGroupId = activeGroupId;
    await window.browserAPI.saveSettings(settings);
    
    // Отправляем событие об изменении настроек
    window.dispatchEvent(new CustomEvent('settings-changed', { 
      detail: settings 
    }));
  } catch (error) {
    console.error('Ошибка при сохранении групп вкладок:', error);
  }
}

// Создание элементов интерфейса для управления группами
function createTabGroupsUI() {
  // Создаем контейнер для групп вкладок
  const tabsContainer = document.querySelector('.tabs-container');
  if (!tabsContainer) return;
  
  // Создаем панель групп
  const groupsPanel = document.createElement('div');
  groupsPanel.className = 'tab-groups-panel';
  tabsContainer.prepend(groupsPanel);
  
  // Добавляем стили для групп вкладок
  addTabGroupStyles();
  
  // Обновляем панель групп
  updateGroupsPanel();
  
  // Добавляем кнопку создания новой группы
  const newGroupButton = document.createElement('button');
  newGroupButton.id = 'new-group-button';
  newGroupButton.title = 'Новая группа';
  newGroupButton.textContent = '+ Группа';
  newGroupButton.addEventListener('click', createNewGroup);
  groupsPanel.appendChild(newGroupButton);
}

// Обновление панели групп
function updateGroupsPanel() {
  const groupsPanel = document.querySelector('.tab-groups-panel');
  if (!groupsPanel) return;
  
  // Удаляем существующие группы
  const existingGroups = groupsPanel.querySelectorAll('.tab-group');
  existingGroups.forEach(group => group.remove());
  
  // Добавляем группы
  tabGroups.forEach(group => {
    const groupElement = document.createElement('div');
    groupElement.className = 'tab-group';
    groupElement.dataset.groupId = group.id;
    if (group.id === activeGroupId) {
      groupElement.classList.add('active');
    }
    
    // Добавляем цветовой индикатор
    const colorIndicator = document.createElement('span');
    colorIndicator.className = 'group-color';
    colorIndicator.style.backgroundColor = group.color;
    groupElement.appendChild(colorIndicator);
    
    // Добавляем название группы
    const groupName = document.createElement('span');
    groupName.className = 'group-name';
    groupName.textContent = group.name;
    groupElement.appendChild(groupName);
    
    // Добавляем счетчик вкладок
    const tabCount = document.createElement('span');
    tabCount.className = 'group-tab-count';
    tabCount.textContent = group.tabs.length;
    groupElement.appendChild(tabCount);
    
    // Добавляем обработчик клика
    groupElement.addEventListener('click', () => switchTabGroup(group.id));
    
    // Добавляем контекстное меню
    groupElement.addEventListener('contextmenu', (e) => showGroupContextMenu(e, group.id));
    
    // Добавляем группу в панель
    const newGroupButton = document.getElementById('new-group-button');
    groupsPanel.insertBefore(groupElement, newGroupButton);
  });
  
  // Обновляем видимость вкладок
  updateTabsVisibility();
}

// Обновление видимости вкладок в зависимости от активной группы
function updateTabsVisibility() {
  // Получаем все вкладки
  const allTabs = document.querySelectorAll('.tab');
  const activeGroup = tabGroups.find(group => group.id === activeGroupId);
  
  if (!activeGroup) return;
  
  // Скрываем все вкладки
  allTabs.forEach(tab => {
    tab.style.display = 'none';
  });
  
  // Показываем вкладки активной группы
  activeGroup.tabs.forEach(tabId => {
    const tab = document.querySelector(`.tab[data-tab-id="${tabId}"]`);
    if (tab) {
      tab.style.display = 'flex';
    }
  });
  
  // Обновляем активную вкладку
  updateActiveTab();
}

// Обновление активной вкладки
function updateActiveTab() {
  const activeGroup = tabGroups.find(group => group.id === activeGroupId);
  if (!activeGroup || activeGroup.tabs.length === 0) return;
  
  // Проверяем, есть ли активная вкладка в текущей группе
  const activeTab = document.querySelector('.tab.active');
  if (activeTab && activeTab.style.display !== 'none') return;
  
  // Активируем первую вкладку в группе
  const firstTabId = activeGroup.tabs[0];
  const firstTab = document.querySelector(`.tab[data-tab-id="${firstTabId}"]`);
  if (firstTab) {
    // Эмулируем клик по вкладке
    firstTab.click();
  }
}

// Переключение на другую группу вкладок
function switchTabGroup(groupId) {
  activeGroupId = groupId;
  
  // Обновляем UI
  updateGroupsPanel();
  
  // Сохраняем изменения
  saveTabGroups();
}

// Создание новой группы вкладок
function createNewGroup() {
  // Запрашиваем имя группы
  const groupName = prompt('Введите название новой группы:', 'Новая группа');
  if (!groupName) return;
  
  // Генерируем случайный цвет
  const colors = ['#4285f4', '#ea4335', '#fbbc05', '#34a853', '#673ab7', '#ff6d00', '#795548'];
  const randomColor = colors[Math.floor(Math.random() * colors.length)];
  
  // Создаем новую группу
  const newGroup = {
    id: generateId(),
    name: groupName,
    color: randomColor,
    tabs: []
  };
  
  // Добавляем группу
  tabGroups.push(newGroup);
  activeGroupId = newGroup.id;
  
  // Обновляем UI
  updateGroupsPanel();
  
  // Сохраняем изменения
  saveTabGroups();
}

// Показать контекстное меню группы
function showGroupContextMenu(event, groupId) {
  event.preventDefault();
  
  // Удаляем существующее меню, если оно есть
  const existingMenu = document.querySelector('.group-context-menu');
  if (existingMenu) {
    existingMenu.remove();
  }
  
  // Создаем контекстное меню
  const contextMenu = document.createElement('div');
  contextMenu.className = 'group-context-menu';
  contextMenu.style.position = 'absolute';
  contextMenu.style.left = `${event.pageX}px`;
  contextMenu.style.top = `${event.pageY}px`;
  
  // Добавляем пункты меню
  const menuItems = [
    { text: 'Переименовать', action: () => renameGroup(groupId) },
    { text: 'Изменить цвет', action: () => changeGroupColor(groupId) },
    { text: 'Удалить', action: () => deleteGroup(groupId) }
  ];
  
  menuItems.forEach(item => {
    const menuItem = document.createElement('div');
    menuItem.className = 'menu-item';
    menuItem.textContent = item.text;
    menuItem.addEventListener('click', () => {
      item.action();
      contextMenu.remove();
    });
    contextMenu.appendChild(menuItem);
  });
  
  // Добавляем меню в DOM
  document.body.appendChild(contextMenu);
  
  // Закрываем меню при клике вне его
  document.addEventListener('click', function closeMenu(e) {
    if (!contextMenu.contains(e.target)) {
      contextMenu.remove();
      document.removeEventListener('click', closeMenu);
    }
  });
}

// Переименование группы
function renameGroup(groupId) {
  const group = tabGroups.find(g => g.id === groupId);
  if (!group) return;
  
  const newName = prompt('Введите новое название группы:', group.name);
  if (!newName) return;
  
  group.name = newName;
  
  // Обновляем UI
  updateGroupsPanel();
  
  // Сохраняем изменения
  saveTabGroups();
}

// Изменение цвета группы
function changeGroupColor(groupId) {
  const group = tabGroups.find(g => g.id === groupId);
  if (!group) return;
  
  const colors = [
    { name: 'Синий', value: '#4285f4' },
    { name: 'Красный', value: '#ea4335' },
    { name: 'Желтый', value: '#fbbc05' },
    { name: 'Зеленый', value: '#34a853' },
    { name: 'Фиолетовый', value: '#673ab7' },
    { name: 'Оранжевый', value: '#ff6d00' },
    { name: 'Коричневый', value: '#795548' }
  ];
  
  let colorOptions = '';
  colors.forEach(color => {
    colorOptions += `<option value="${color.value}" ${group.color === color.value ? 'selected' : ''}>${color.name}</option>`;
  });
  
  const colorSelector = document.createElement('div');
  colorSelector.innerHTML = `
    <div style="background: white; padding: 10px; border: 1px solid #ccc; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
      <h3 style="margin-top: 0;">Выберите цвет группы</h3>
      <select id="group-color-select" style="width: 100%; padding: 5px;">
        ${colorOptions}
      </select>
      <div style="display: flex; justify-content: flex-end; margin-top: 10px;">
        <button id="color-cancel">Отмена</button>
        <button id="color-save" style="margin-left: 5px;">Сохранить</button>
      </div>
    </div>
  `;
  
  // Стилизуем и добавляем в DOM
  colorSelector.style.position = 'fixed';
  colorSelector.style.top = '50%';
  colorSelector.style.left = '50%';
  colorSelector.style.transform = 'translate(-50%, -50%)';
  colorSelector.style.zIndex = '1000';
  
  document.body.appendChild(colorSelector);
  
  // Добавляем обработчики
  document.getElementById('color-cancel').addEventListener('click', () => {
    colorSelector.remove();
  });
  
  document.getElementById('color-save').addEventListener('click', () => {
    const select = document.getElementById('group-color-select');
    group.color = select.value;
    
    // Обновляем UI
    updateGroupsPanel();
    
    // Сохраняем изменения
    saveTabGroups();
    
    colorSelector.remove();
  });
}

// Удаление группы
function deleteGroup(groupId) {
  // Проверяем, что это не последняя группа
  if (tabGroups.length <= 1) {
    alert('Нельзя удалить последнюю группу!');
    return;
  }
  
  if (!confirm('Вы уверены, что хотите удалить эту группу? Все вкладки в ней будут перемещены в первую группу.')) {
    return;
  }
  
  const groupIndex = tabGroups.findIndex(g => g.id === groupId);
  if (groupIndex === -1) return;
  
  const group = tabGroups[groupIndex];
  
  // Перемещаем вкладки в первую группу
  if (group.tabs.length > 0 && tabGroups.length > 1) {
    const firstGroup = tabGroups[0].id === groupId ? tabGroups[1] : tabGroups[0];
    firstGroup.tabs = [...firstGroup.tabs, ...group.tabs];
  }
  
  // Удаляем группу
  tabGroups.splice(groupIndex, 1);
  
  // Если удаляем активную группу, активируем первую
  if (activeGroupId === groupId) {
    activeGroupId = tabGroups[0].id;
  }
  
  // Обновляем UI
  updateGroupsPanel();
  
  // Сохраняем изменения
  saveTabGroups();
}

// Добавление вкладки в текущую группу
function addTabToCurrentGroup(tabId) {
  const activeGroup = tabGroups.find(group => group.id === activeGroupId);
  if (!activeGroup) return;
  
  // Проверяем, есть ли уже эта вкладка в группе
  if (!activeGroup.tabs.includes(tabId)) {
    activeGroup.tabs.push(tabId);
    
    // Сохраняем изменения
    saveTabGroups();
  }
}

// Удаление вкладки из группы
function removeTabFromGroup(tabId) {
  // Находим группу, содержащую вкладку
  tabGroups.forEach(group => {
    const tabIndex = group.tabs.indexOf(tabId);
    if (tabIndex !== -1) {
      group.tabs.splice(tabIndex, 1);
    }
  });
  
  // Сохраняем изменения
  saveTabGroups();
}

// Настройка оптимизации производительности
function setupPerformanceOptimization() {
  // Оптимизация для большого количества вкладок
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // Проверяем, добавлены ли новые вкладки
        mutation.addedNodes.forEach(node => {
          if (node.classList && node.classList.contains('tab')) {
            // Добавляем новую вкладку в текущую группу
            const tabId = node.dataset.tabId;
            if (tabId) {
              addTabToCurrentGroup(tabId);
            }
            
            // Применяем ленивую загрузку для webview
            applyLazyLoading(tabId);
          }
        });
      }
    });
  });
  
  // Наблюдаем за изменениями в контейнере вкладок
  const tabsBar = document.getElementById('tabs-bar');
  if (tabsBar) {
    observer.observe(tabsBar, { childList: true });
  }
}

// Применение ленивой загрузки для webview
function applyLazyLoading(tabId) {
  const webview = document.querySelector(`webview[data-tab-id="${tabId}"]`);
  if (!webview) return;
  
  // Устанавливаем атрибут для отложенной загрузки
  webview.setAttribute('preload', 'js/webview-preload.js');
  
  // Оптимизируем использование памяти
  webview.addEventListener('dom-ready', () => {
    // Проверяем, видима ли вкладка
    const tab = document.querySelector(`.tab[data-tab-id="${tabId}"]`);
    const isVisible = tab && tab.style.display !== 'none';
    
    if (!isVisible) {
      // Если вкладка не видна, приостанавливаем выполнение скриптов
      webview.executeJavaScript(`
        if (window.suspendScripts === undefined) {
          window.suspendScripts = function() {
            // Сохраняем оригинальный setTimeout
            if (!window._originalSetTimeout) {
              window._originalSetTimeout = window.setTimeout;
              window._originalSetInterval = window.setInterval;
              window._suspendedTimeouts = [];
              window._suspendedIntervals = [];
              
              // Переопределяем setTimeout
              window.setTimeout = function(callback, delay, ...args) {
                // Для критических таймаутов используем оригинальный setTimeout
                if (delay < 100) {
                  return window._originalSetTimeout(callback, delay, ...args);
                }
                
                // Сохраняем информацию о таймауте
                const timeoutInfo = { callback, delay, args };
                window._suspendedTimeouts.push(timeoutInfo);
                return window._suspendedTimeouts.length;
              };
              
              // Переопределяем setInterval
              window.setInterval = function(callback, delay, ...args) {
                // Для критических интервалов используем оригинальный setInterval
                if (delay < 100) {
                  return window._originalSetInterval(callback, delay, ...args);
                }
                
                // Сохраняем информацию об интервале
                const intervalInfo = { callback, delay, args };
                window._suspendedIntervals.push(intervalInfo);
                return window._suspendedIntervals.length;
              };
            }
          };
          
          window.resumeScripts = function() {
            // Восстанавливаем оригинальные функции
            if (window._originalSetTimeout) {
              window.setTimeout = window._originalSetTimeout;
              window.setInterval = window._originalSetInterval;
              
              // Запускаем отложенные таймауты
              window._suspendedTimeouts.forEach(info => {
                window.setTimeout(info.callback, info.delay, ...info.args);
              });
              
              // Запускаем отложенные интервалы
              window._suspendedIntervals.forEach(info => {
                window.setInterval(info.callback, info.delay, ...info.args);
              });
              
              // Очищаем сохраненные данные
              window._suspendedTimeouts = [];
              window._suspendedIntervals = [];
              window._originalSetTimeout = null;
              window._originalSetInterval = null;
            }
          };
        }
        
        // Приостанавливаем скрипты
        window.suspendScripts();
      `);
    } else {
      // Если вкладка видна, возобновляем выполнение скриптов
      webview.executeJavaScript(`
        if (window.resumeScripts) {
          window.resumeScripts();
        }
      `);
    }
  });
}

// Настройка обработчиков событий
function setupEventListeners() {
  // Обработчик создания новой вкладки
  const newTabButton = document.getElementById('new-tab-button');
  if (newTabButton) {
    const originalClickHandler = newTabButton.onclick;
    newTabButton.onclick = function(event) {
      // Вызываем оригинальный обработчик
      if (originalClickHandler) {
        originalClickHandler.call(this, event);
      }
      
      // Добавляем новую вкладку в текущую группу
      setTimeout(() => {
        const newTab = document.querySelector('.tab:last-child');
        if (newTab) {
          const tabId = newTab.dataset.tabId;
          if (tabId) {
            addTabToCurrentGroup(tabId);
          }
        }
      }, 100);
    };
  }
  
  // Обработчик закрытия вкладки
  document.addEventListener('click', event => {
    if (event.target.classList.contains('tab-close')) {
      const tab = event.target.closest('.tab');
      if (tab) {
        const tabId = tab.dataset.tabId;
        if (tabId) {
          removeTabFromGroup(tabId);
        }
      }
    }
  });
  
  // Обработчик переключения вкладок для оптимизации производительности
  document.addEventListener('click', event => {
    const tab = event.target.closest('.tab');
    if (tab && !event.target.classList.contains('tab-close')) {
      const tabId = tab.dataset.tabId;
      if (tabId) {
        const webview = document.querySelector(`webview[data-tab-id="${tabId}"]`);
        if (webview) {
          // Возобновляем выполнение скриптов в активной вкладке
          webview.executeJavaScript(`
            if (window.resumeScripts) {
              window.resumeScripts();
            }
          `);
          
          // Приостанавливаем скрипты в неактивных вкладках
          const otherWebviews = document.querySelectorAll(`webview:not([data-tab-id="${tabId}"])`);
          otherWebviews.forEach(otherWebview => {
            otherWebview.executeJavaScript(`
              if (window.suspendScripts) {
                window.suspendScripts();
              }
            `);
          });
        }
      }
    }
  });
}

// Добавление стилей для групп вкладок
function addTabGroupStyles() {
  const style = document.createElement('style');
  style.textContent = `
    .tab-groups-panel {
      display: flex;
      background-color: #d0d0d0;
      padding: 5px;
      overflow-x: auto;
      white-space: nowrap;
      border-bottom: 1px solid #ccc;
    }
    
    .tab-group {
      display: flex;
      align-items: center;
      padding: 5px 10px;
      margin-right: 5px;
      background-color: #e0e0e0;
      border-radius: 15px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .tab-group.active {
      background-color: #f0f0f0;
      font-weight: 500;
    }
    
    .group-color {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 5px;
    }
    
    .group-name {
      margin-right: 5px;
    }
    
    .group-tab-count {
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 10px;
      padding: 2px 6px;
      font-size: 0.8em;
    }
    
    #new-group-button {
      background-color: transparent;
      border: 1px dashed #999;
      border-radius: 15px;
      padding: 5px 10px;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    #new-group-button:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    .group-context-menu {
      background-color: white;
      border: 1px solid #ccc;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      z-index: 1000;
    }
    
    .menu-item {
      padding: 8px 12px;
      cursor: pointer;
    }
    
    .menu-item:hover {
      background-color: #f5f5f5;
    }
    
    /* Стили для темного режима */
    body.dark-mode .tab-groups-panel {
      background-color: #333;
      border-bottom-color: #444;
    }
    
    body.dark-mode .tab-group {
      background-color: #444;
    }
    
    body.dark-mode .tab-group.active {
      background-color: #555;
    }
    
    body.dark-mode #new-group-button {
      border-color: #666;
    }
    
    body.dark-mode .group-context-menu {
      background-color: #333;
      border-color: #444;
    }
    
    body.dark-mode .menu-item:hover {
      background-color: #444;
    }
  `;
  
  document.head.appendChild(style);
}

// Генерация уникального ID
function generateId() {
  return Math.random().toString(36).substr(2, 9);
}