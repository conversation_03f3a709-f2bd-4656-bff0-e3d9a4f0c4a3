/**
 * Preload file for the extension sandbox
 * Provides a secure API for isolated extensions
 */

const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Extension information, will be set during initialization
let extensionInfo = null;

/**
 * Secure wrapper for IPC communication with the main process
 * @param {string} action - Type of action
 * @param {any} data - Data to transmit
 * @returns {Promise<any>} - Result of the request execution
 */
async function sendSandboxRequest(action, data) {
  if (!extensionInfo) {
    throw new Error('Extension not initialized');
  }
  
  return await ipcRenderer.invoke('extension-sandbox-request', {
    extensionId: extensionInfo.id,
    token: extensionInfo.token,
    action,
    data
  });
}

/**
 * Checks if the extension has a permission
 * @param {string} permission - The permission to check
 * @returns {Promise<boolean>} - The result of the check
 */
async function checkPermission(permission) {
  const response = await sendSandboxRequest('checkPermission', { permission });
  return response.success && response.hasPermission;
}

/**
 * Creates a secure API for the extension based on available permissions
 * @returns {Object} - The extension API
 */
async function createExtensionAPI() {
  const api = {};
  
  // Basic API, available to all extensions
  api.runtime = {
    getManifest: () => ({ ...extensionInfo.manifest }),
    getURL: (path) => `extension://${extensionInfo.id}/${path}`,
    id: extensionInfo.id
  };
  
  // API for working with tabs (if permission is granted)
  if (await checkPermission('tabs')) {
    api.tabs = {
      query: async (queryInfo) => {
        const response = await sendSandboxRequest('executeAPI', {
          apiNamespace: 'tabs',
          method: 'query',
          args: [queryInfo]
        });
        return response.success ? response.result : [];
      },
      
      get: async (tabId) => {
        const response = await sendSandboxRequest('executeAPI', {
          apiNamespace: 'tabs',
          method: 'get',
          args: [tabId]
        });
        return response.success ? response.result : null;
      },
      
      create: async (createProperties) => {
        const response = await sendSandboxRequest('executeAPI', {
          apiNamespace: 'tabs',
          method: 'create',
          args: [createProperties]
        });
        return response.success ? response.result : null;
      },
      
      update: async (tabId, updateProperties) => {
        const response = await sendSandboxRequest('executeAPI', {
          apiNamespace: 'tabs',
          method: 'update',
          args: [tabId, updateProperties]
        });
        return response.success ? response.result : null;
      },
      
      remove: async (tabIds) => {
        const response = await sendSandboxRequest('executeAPI', {
          apiNamespace: 'tabs',
          method: 'remove',
          args: [tabIds]
        });
        return response.success ? response.result : false;
      }
    };
  }
  
  // API for working with storage (if permission is granted)
  if (await checkPermission('storage')) {
    api.storage = {
      local: {
        get: async (keys) => {
          const response = await sendSandboxRequest('executeAPI', {
            apiNamespace: 'storage',
            method: 'local.get',
            args: [keys]
          });
          return response.success ? response.result : {};
        },
        
        set: async (items) => {
          const response = await sendSandboxRequest('executeAPI', {
            apiNamespace: 'storage',
            method: 'local.set',
            args: [items]
          });
          return response.success;
        },
        
        remove: async (keys) => {
          const response = await sendSandboxRequest('executeAPI', {
            apiNamespace: 'storage',
            method: 'local.remove',
            args: [keys]
          });
          return response.success;
        },
        
        clear: async () => {
          const response = await sendSandboxRequest('executeAPI', {
            apiNamespace: 'storage',
            method: 'local.clear',
            args: []
          });
          return response.success;
        }
      },
      
      // Synchronized storage (if synchronization is supported)
      sync: {
        get: async (keys) => {
          const response = await sendSandboxRequest('executeAPI', {
            apiNamespace: 'storage',
            method: 'sync.get',
            args: [keys]
          });
          return response.success ? response.result : {};
        },
        
        set: async (items) => {
          const response = await sendSandboxRequest('executeAPI', {
            apiNamespace: 'storage',
            method: 'sync.set',
            args: [items]
          });
          return response.success;
        },
        
        remove: async (keys) => {
          const response = await sendSandboxRequest('executeAPI', {
            apiNamespace: 'storage',
            method: 'sync.remove',
            args: [keys]
          });
          return response.success;
        },
        
        clear: async () => {
          const response = await sendSandboxRequest('executeAPI', {
            apiNamespace: 'storage',
            method: 'sync.clear',
            args: []
          });
          return response.success;
        }
      }
    };
  }
  
  // API for working with cookies (if permission is granted)
  if (await checkPermission('cookies')) {
    api.cookies = {
      get: async (details) => {
        const response = await sendSandboxRequest('executeAPI', {
          apiNamespace: 'cookies',
          method: 'get',
          args: [details]
        });
        return response.success ? response.result : null;
      },
      
      getAll: async (details) => {
        const response = await sendSandboxRequest('executeAPI', {
          apiNamespace: 'cookies',
          method: 'getAll',
          args: [details]
        });
        return response.success ? response.result : [];
      },
      
      set: async (details) => {
        const response = await sendSandboxRequest('executeAPI', {
          apiNamespace: 'cookies',
          method: 'set',
          args: [details]
        });
        return response.success ? response.result : null;
      },
      
      remove: async (details) => {
        const response = await sendSandboxRequest('executeAPI', {
          apiNamespace: 'cookies',
          method: 'remove',
          args: [details]
        });
        return response.success ? response.result : false;
      }
    };
  }
  
  // Другие API могут быть добавлены аналогичным образом
  
  return api;
}

// Экспортируем API для инициализации расширения
contextBridge.exposeInMainWorld('extensionInit', async (info) => {
  try {
    extensionInfo = {
      id: info.id,
      token: info.token,
      path: info.path,
      manifest: info.manifest || {}
    };
    
    // Создаем и экспортируем API расширения
    const extensionAPI = await createExtensionAPI();
    contextBridge.exposeInMainWorld('browser', extensionAPI);
    
    // Для совместимости с расширениями Chrome
    contextBridge.exposeInMainWorld('chrome', extensionAPI);
    
    return true;
  } catch (error) {
    global.logger.error('Error initializing extension sandbox:', error);
    return false;
  }
});