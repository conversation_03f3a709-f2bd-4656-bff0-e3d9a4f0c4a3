# Extension API Documentation

## Overview
A11 Browser provides a secure extension system with sandboxed execution. Extensions can enhance browser functionality while maintaining security.

## Extension Registration
```javascript
window.electronAPI.registerExtension({
  name: 'ExtensionName',
  version: '1.0.0',
  permissions: ['tabs', 'storage'],
  main: 'extension-main.js'
});
```

## Available APIs
- **tabs**: Manage browser tabs
- **storage**: Persistent data storage
- **notifications**: Display system notifications
- **network**: Intercept network requests

## Example Extensions
See implementations in `src/extensions/examples/`:
- Dark Mode Toggle
- Tab Groups
- AdBlocker Integration

## Best Practices
1. Use limited capabilities
2. Validate all user input
3. Handle errors gracefully
4. Test in sandbox mode

## Security Considerations
- All extensions run in isolated context
- Limited system access via capability model
- Automatic updates from trusted sources

## Contribution Guidelines
- Follow JavaScript Standard Style
- Include unit tests
- Document public API methods