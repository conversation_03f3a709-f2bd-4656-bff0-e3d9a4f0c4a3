<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Extension Sandbox</title>
  <style>
    /* Стили для контейнера, если необходимо */
    body {
      margin: 0;
      padding: 0;
      /* Можно добавить стили для отладки, например, рамку */
      /* border: 1px solid red; */
    }
  </style>
</head>
<body>
  <script>
    let extensionId = null;
    let extensionAPIProxy = {};
    let callIdCounter = 0;
    const pendingApiCalls = new Map();

    // Функция для отправки вызовов API в ExtensionManager
    function callHostAPI(namespace, method, ...args) {
      return new Promise((resolve, reject) => {
        const callId = callIdCounter++;
        pendingApiCalls.set(callId, { resolve, reject });
        window.parent.postMessage({
          type: 'apiCall',
          extensionId: extensionId,
          callDetails: {
            callId: callId,
            apiNamespace: namespace,
            methodName: method,
            args: args
          }
        }, '*'); // Отправляем в родительское окно (ExtensionManager)
      });
    }

    // Создание прокси для API расширения
    function createAPIProxy(grantedPermissions = []) {
      const proxy = {};
      
      proxy.runtime = {
        getManifest: () => callHostAPI('runtime', 'getManifest'),
        onMessage: {
          addListener: (callback) => {
            // TODO: Реализовать систему слушателей сообщений
            global.logger.warn('[Sandbox] runtime.onMessage.addListener не полностью реализован');
          }
        }
        // другие методы runtime API
      };

      // Пример для 'tabs'
      if (grantedPermissions.includes('tabs') || grantedPermissions.includes('activeTab')) {
        proxy.tabs = {
          query: (queryInfo) => callHostAPI('tabs', 'query', queryInfo),
          create: (createProperties) => callHostAPI('tabs', 'create', createProperties),
          update: (tabId, updateProperties) => callHostAPI('tabs', 'update', tabId, updateProperties),
          // ... другие методы tabs API
        };
      }

      // Пример для 'storage'
      if (grantedPermissions.includes('storage')) {
        proxy.storage = {
          local: {
            get: (keys) => callHostAPI('storage', 'get', keys),
            set: (items) => callHostAPI('storage', 'set', items),
            remove: (keys) => callHostAPI('storage', 'remove', keys),
            clear: () => callHostAPI('storage', 'clear'),
          }
          // sync, managed storage и т.д.
        };
      }
      
      // API для разрешений
      proxy.permissions = {
        onAdded: {
            _listeners: [],
            addListener: function(callback) { this._listeners.push(callback); },
            dispatch: function(event) { this._listeners.forEach(l => l(event)); }
        },
        onRemoved: {
            _listeners: [],
            addListener: function(callback) { this._listeners.push(callback); },
            dispatch: function(event) { this._listeners.forEach(l => l(event)); }
        },
        contains: (permissionsToCheck) => callHostAPI('permissions', 'contains', permissionsToCheck),
        request: (permissionsToRequest) => callHostAPI('permissions', 'request', permissionsToRequest),
      };

      return proxy;
    }

    window.addEventListener('message', async (event) => {
      const { data, source } = event;

      if (source !== window.parent) {
        return;
      }

      if (data && data.type === 'initialize') {
        extensionId = data.extensionId;
        global.logger.info(`[Sandbox ${extensionId}] Инициализация...`);
        
        // data.grantedPermissions должен передаваться из ExtensionManager
        const grantedPermissions = data.grantedPermissions || []; 
        window.chrome = window.browser = createAPIProxy(grantedPermissions);

        if (data.scriptPath) {
          try {
            global.logger.info(`[Sandbox ${extensionId}] Загрузка скрипта: ${data.scriptPath}`);
            const scriptElement = document.createElement('script');
            // ВАЖНО: Для безопасности, ExtensionManager должен предоставлять путь к скрипту
            // как blob URL или data URL, созданный из содержимого файла, а не прямой путь к файловой системе.
            // Это предотвращает несанкционированный доступ к файловой системе из песочницы.
            // Пример: scriptElement.src = URL.createObjectURL(new Blob([scriptContent], {type: 'text/javascript'}));
            scriptElement.src = data.scriptPath; 
            scriptElement.type = 'module'; // Если это ES модуль, иначе удалить
            scriptElement.onload = () => global.logger.info(`[Sandbox ${extensionId}] Скрипт загружен и выполнен.`);
            scriptElement.onerror = (e) => {
                global.logger.error(`[Sandbox ${extensionId}] Ошибка загрузки или выполнения скрипта:`, e);
                window.parent.postMessage({ type: 'scriptError', extensionId, error: `Ошибка загрузки скрипта: ${data.scriptPath}` }, '*');
            };
            document.body.appendChild(scriptElement);

          } catch (e) {
            global.logger.error(`[Sandbox ${extensionId}] Ошибка при настройке загрузки скрипта расширения:`, e);
            window.parent.postMessage({ type: 'scriptError', extensionId, error: e.message }, '*');
          }
        }
      } else if (data && data.type === 'apiResponse') {
        const { callId, result, error } = data;
        if (pendingApiCalls.has(callId)) {
          const { resolve, reject } = pendingApiCalls.get(callId);
          if (error) {
            reject(new Error(error));
          } else {
            resolve(result);
          }
          pendingApiCalls.delete(callId);
        }
      } else if (data && data.type === 'permissionGranted') {
        global.logger.info(`[Sandbox ${extensionId}] Получено уведомление о предоставлении разрешения: ${data.permission}`);
        if (window.chrome && window.chrome.permissions && window.chrome.permissions.onAdded) {
            window.chrome.permissions.onAdded.dispatch({permissions: [data.permission]});
        }
      } else if (data && data.type === 'permissionRevoked') { // Обработка отзыва разрешений
        global.logger.info(`[Sandbox ${extensionId}] Получено уведомление об отзыве разрешения: ${data.permission}`);
        if (window.chrome && window.chrome.permissions && window.chrome.permissions.onRemoved) {
            window.chrome.permissions.onRemoved.dispatch({permissions: [data.permission]});
        }
      }
    });

    // Сообщаем родительскому окну, что песочница готова к инициализации
    if (window.parent && window.parent !== window) {
        window.parent.postMessage({ type: 'sandboxReady', extensionId: 'pending' }, '*'); // extensionId будет установлен позже
    }

    global.logger.info('[Sandbox] Контейнер песочницы загружен и готов к инициализации.');
  </script>
</body>
</html>