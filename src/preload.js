const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld(
  'api', {
    // Window management
    minimize: () => ipcRenderer.send('window-minimize'),
    maximize: () => ipcRenderer.send('window-maximize'),
    close: () => ipcRenderer.send('window-close'),
    
    // Navigation
    navigate: (url) => ipcRenderer.send('navigate', url),
    goBack: () => ipcRenderer.send('go-back'),
    goForward: () => ipcRenderer.send('go-forward'),
    reload: () => ipcRenderer.send('reload'),
    
    // Tab management
    createTab: (url) => ipcRenderer.send('create-tab', url),
    closeTab: (tabId) => ipcRenderer.send('close-tab', tabId),
    switchTab: (tabId) => ipc<PERSON>enderer.send('switch-tab', tabId),
    
    // Settings
    getSettings: () => ipcRenderer.invoke('get-settings'),
    updateSettings: (settings) => ipcRenderer.send('update-settings', settings),
    
    // Security
    checkSecurity: (url) => ipcRenderer.invoke('check-security', url),
    getSecurityStatus: () => ipcRenderer.invoke('get-security-status'),
    
    // Bookmarks
    addBookmark: (bookmark) => ipcRenderer.send('add-bookmark', bookmark),
    removeBookmark: (bookmarkId) => ipcRenderer.send('remove-bookmark', bookmarkId),
    getBookmarks: () => ipcRenderer.invoke('get-bookmarks'),
    
    // History
    addHistory: (entry) => ipcRenderer.send('add-history', entry),
    getHistory: () => ipcRenderer.invoke('get-history'),
    clearHistory: () => ipcRenderer.send('clear-history'),
    
    // Downloads
    download: (url) => ipcRenderer.send('download', url),
    getDownloads: () => ipcRenderer.invoke('get-downloads'),
    
    // Extensions
    getExtensions: () => ipcRenderer.invoke('get-extensions'),
    installExtension: (extensionId) => ipcRenderer.send('install-extension', extensionId),
    uninstallExtension: (extensionId) => ipcRenderer.send('uninstall-extension', extensionId),
    
    // Events
    onWindowStateChange: (callback) => {
      ipcRenderer.on('window-state-changed', (_, state) => callback(state));
    },
    onTabUpdate: (callback) => {
      ipcRenderer.on('tab-updated', (_, tab) => callback(tab));
    },
    onSecurityUpdate: (callback) => {
      ipcRenderer.on('security-updated', (_, status) => callback(status));
    },
    onDownloadProgress: (callback) => {
      ipcRenderer.on('download-progress', (_, progress) => callback(progress));
    }
  }
);