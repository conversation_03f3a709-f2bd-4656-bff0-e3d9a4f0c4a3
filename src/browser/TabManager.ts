import { EventEmitter } from 'events';
import { BrowserWindow, webContents } from 'electron';
import { logger } from '../core/EnhancedLogger';
import { configManager } from '../core/ConfigurationManager';
import { browserEngine } from './BrowserEngine';

export interface Tab {
  id: string;
  title: string;
  url: string;
  favicon?: string;
  isActive: boolean;
  isLoading: boolean;
  isPinned: boolean;
  isMuted: boolean;
  isPrivate: boolean;
  webContentsId: number;
  createdAt: number;
  lastActivated: number;
  navigationHistory: string[];
  currentHistoryIndex: number;
  zoomLevel: number;
  userAgent?: string;
  permissions: TabPermissions;
  performance: TabPerformance;
}

export interface TabPermissions {
  camera: boolean;
  microphone: boolean;
  location: boolean;
  notifications: boolean;
  fullscreen: boolean;
  autoplay: boolean;
}

export interface TabPerformance {
  memoryUsage: number;
  cpuUsage: number;
  networkActivity: number;
  lastUpdated: number;
}

export interface TabGroup {
  id: string;
  name: string;
  color: string;
  tabIds: string[];
  collapsed: boolean;
  createdAt: number;
}

export interface TabManagerConfig {
  maxTabs: number;
  enableTabGroups: boolean;
  enableTabSuspension: boolean;
  suspensionTimeout: number;
  enableTabPreview: boolean;
  enableTabSync: boolean;
  defaultZoomLevel: number;
  enableTabAudio: boolean;
}

export class TabManager extends EventEmitter {
  private static instance: TabManager;
  private tabs: Map<string, Tab> = new Map();
  private tabGroups: Map<string, TabGroup> = new Map();
  private activeTabId: string | null = null;
  private config: TabManagerConfig;
  private suspensionTimer: Map<string, NodeJS.Timeout> = new Map();

  private constructor() {
    super();
    this.config = {
      maxTabs: 100,
      enableTabGroups: true,
      enableTabSuspension: true,
      suspensionTimeout: 30 * 60 * 1000, // 30 minutes
      enableTabPreview: true,
      enableTabSync: false,
      defaultZoomLevel: 1.0,
      enableTabAudio: true,
    };

    this.initializeTabManager();
  }

  public static getInstance(): TabManager {
    if (!TabManager.instance) {
      TabManager.instance = new TabManager();
    }
    return TabManager.instance;
  }

  private async initializeTabManager(): Promise<void> {
    // Load configuration
    const tabConfig = configManager.get('tabs', {});
    this.config = { ...this.config, ...tabConfig };

    // Setup performance monitoring
    this.setupPerformanceMonitoring();

    // Setup suspension monitoring
    if (this.config.enableTabSuspension) {
      this.setupSuspensionMonitoring();
    }

    logger.info('Tab manager initialized', {
      maxTabs: this.config.maxTabs,
      enableTabGroups: this.config.enableTabGroups,
      enableTabSuspension: this.config.enableTabSuspension,
    });
  }

  public async createTab(url: string = 'about:blank', options: {
    isPrivate?: boolean;
    isPinned?: boolean;
    groupId?: string;
    userAgent?: string;
    activate?: boolean;
  } = {}): Promise<Tab> {
    // Check tab limit
    if (this.tabs.size >= this.config.maxTabs) {
      throw new Error(`Maximum number of tabs (${this.config.maxTabs}) reached`);
    }

    const tabId = `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const tab: Tab = {
      id: tabId,
      title: 'New Tab',
      url,
      isActive: false,
      isLoading: false,
      isPinned: options.isPinned || false,
      isMuted: false,
      isPrivate: options.isPrivate || false,
      webContentsId: 0, // Will be set when webContents is created
      createdAt: Date.now(),
      lastActivated: Date.now(),
      navigationHistory: [url],
      currentHistoryIndex: 0,
      zoomLevel: this.config.defaultZoomLevel,
      userAgent: options.userAgent,
      permissions: {
        camera: false,
        microphone: false,
        location: false,
        notifications: false,
        fullscreen: false,
        autoplay: false,
      },
      performance: {
        memoryUsage: 0,
        cpuUsage: 0,
        networkActivity: 0,
        lastUpdated: Date.now(),
      },
    };

    // Create webContents for the tab
    await this.createWebContents(tab);

    this.tabs.set(tabId, tab);

    // Add to group if specified
    if (options.groupId && this.tabGroups.has(options.groupId)) {
      this.addTabToGroup(tabId, options.groupId);
    }

    // Activate if requested
    if (options.activate !== false) {
      await this.activateTab(tabId);
    }

    this.emit('tab_created', tab);
    logger.info('Tab created', {
      tabId,
      url,
      isPrivate: tab.isPrivate,
      isPinned: tab.isPinned,
    });

    return tab;
  }

  private async createWebContents(tab: Tab): Promise<void> {
    // In a real implementation, this would create actual webContents
    // For now, simulate webContents creation
    tab.webContentsId = Math.floor(Math.random() * 10000);
    
    // Setup webContents event listeners
    this.setupWebContentsListeners(tab);
  }

  private setupWebContentsListeners(tab: Tab): void {
    // Simulate webContents events
    setTimeout(() => {
      if (tab.url !== 'about:blank') {
        this.handleNavigationStart(tab.id, tab.url);
      }
    }, 100);
  }

  public async closeTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`);
    }

    // Clear suspension timer
    const timer = this.suspensionTimer.get(tabId);
    if (timer) {
      clearTimeout(timer);
      this.suspensionTimer.delete(tabId);
    }

    // Remove from group
    const group = this.getTabGroup(tabId);
    if (group) {
      this.removeTabFromGroup(tabId, group.id);
    }

    // If this was the active tab, activate another tab
    if (this.activeTabId === tabId) {
      const remainingTabs = Array.from(this.tabs.values()).filter(t => t.id !== tabId);
      if (remainingTabs.length > 0) {
        await this.activateTab(remainingTabs[remainingTabs.length - 1].id);
      } else {
        this.activeTabId = null;
      }
    }

    this.tabs.delete(tabId);

    this.emit('tab_closed', { tabId, tab });
    logger.info('Tab closed', { tabId, url: tab.url });
  }

  public async activateTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`);
    }

    // Deactivate current active tab
    if (this.activeTabId) {
      const currentTab = this.tabs.get(this.activeTabId);
      if (currentTab) {
        currentTab.isActive = false;
        this.startSuspensionTimer(this.activeTabId);
      }
    }

    // Activate new tab
    tab.isActive = true;
    tab.lastActivated = Date.now();
    this.activeTabId = tabId;

    // Cancel suspension timer
    this.cancelSuspensionTimer(tabId);

    this.emit('tab_activated', tab);
    logger.debug('Tab activated', { tabId, url: tab.url });
  }

  public async navigateTab(tabId: string, url: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`);
    }

    this.handleNavigationStart(tabId, url);

    try {
      // Use browser engine to navigate
      const result = await browserEngine.navigate({
        url,
        method: 'GET',
        userGesture: true,
        timestamp: Date.now(),
      });

      if (result.success) {
        this.handleNavigationComplete(tabId, url, result);
      } else {
        this.handleNavigationError(tabId, url, result.error || 'Navigation failed');
      }
    } catch (error) {
      this.handleNavigationError(tabId, url, error instanceof Error ? error.message : String(error));
    }
  }

  private handleNavigationStart(tabId: string, url: string): void {
    const tab = this.tabs.get(tabId);
    if (!tab) return;

    tab.isLoading = true;
    tab.url = url;
    tab.title = 'Loading...';

    this.emit('tab_loading_started', { tabId, url });
  }

  private handleNavigationComplete(tabId: string, url: string, result: any): void {
    const tab = this.tabs.get(tabId);
    if (!tab) return;

    tab.isLoading = false;
    tab.url = url;
    tab.title = this.extractTitleFromUrl(url);

    // Update navigation history
    if (tab.navigationHistory[tab.currentHistoryIndex] !== url) {
      tab.navigationHistory = tab.navigationHistory.slice(0, tab.currentHistoryIndex + 1);
      tab.navigationHistory.push(url);
      tab.currentHistoryIndex = tab.navigationHistory.length - 1;
    }

    this.emit('tab_loading_completed', { tabId, url, result });
    logger.debug('Tab navigation completed', { tabId, url });
  }

  private handleNavigationError(tabId: string, url: string, error: string): void {
    const tab = this.tabs.get(tabId);
    if (!tab) return;

    tab.isLoading = false;
    tab.title = 'Error loading page';

    this.emit('tab_loading_error', { tabId, url, error });
    logger.warn('Tab navigation error', { tabId, url, error });
  }

  private extractTitleFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname || url;
    } catch {
      return url;
    }
  }

  public async goBack(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab || tab.currentHistoryIndex <= 0) {
      return;
    }

    tab.currentHistoryIndex--;
    const url = tab.navigationHistory[tab.currentHistoryIndex];
    await this.navigateTab(tabId, url);
  }

  public async goForward(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab || tab.currentHistoryIndex >= tab.navigationHistory.length - 1) {
      return;
    }

    tab.currentHistoryIndex++;
    const url = tab.navigationHistory[tab.currentHistoryIndex];
    await this.navigateTab(tabId, url);
  }

  public async reload(tabId: string, ignoreCache: boolean = false): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`);
    }

    await this.navigateTab(tabId, tab.url);
  }

  public async setZoomLevel(tabId: string, zoomLevel: number): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`);
    }

    tab.zoomLevel = Math.max(0.25, Math.min(5.0, zoomLevel));
    this.emit('tab_zoom_changed', { tabId, zoomLevel: tab.zoomLevel });
  }

  public async toggleMute(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`);
    }

    tab.isMuted = !tab.isMuted;
    this.emit('tab_mute_changed', { tabId, isMuted: tab.isMuted });
  }

  public async pinTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`);
    }

    tab.isPinned = true;
    this.emit('tab_pinned', { tabId });
  }

  public async unpinTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`);
    }

    tab.isPinned = false;
    this.emit('tab_unpinned', { tabId });
  }

  // Tab Groups
  public createTabGroup(name: string, color: string = '#007acc'): TabGroup {
    const groupId = `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const group: TabGroup = {
      id: groupId,
      name,
      color,
      tabIds: [],
      collapsed: false,
      createdAt: Date.now(),
    };

    this.tabGroups.set(groupId, group);
    this.emit('tab_group_created', group);
    
    return group;
  }

  public addTabToGroup(tabId: string, groupId: string): void {
    const group = this.tabGroups.get(groupId);
    if (!group) {
      throw new Error(`Tab group ${groupId} not found`);
    }

    if (!group.tabIds.includes(tabId)) {
      group.tabIds.push(tabId);
      this.emit('tab_added_to_group', { tabId, groupId });
    }
  }

  public removeTabFromGroup(tabId: string, groupId: string): void {
    const group = this.tabGroups.get(groupId);
    if (!group) return;

    const index = group.tabIds.indexOf(tabId);
    if (index > -1) {
      group.tabIds.splice(index, 1);
      this.emit('tab_removed_from_group', { tabId, groupId });

      // Delete group if empty
      if (group.tabIds.length === 0) {
        this.tabGroups.delete(groupId);
        this.emit('tab_group_deleted', { groupId });
      }
    }
  }

  private getTabGroup(tabId: string): TabGroup | null {
    for (const group of this.tabGroups.values()) {
      if (group.tabIds.includes(tabId)) {
        return group;
      }
    }
    return null;
  }

  // Performance and Suspension
  private setupPerformanceMonitoring(): void {
    setInterval(() => {
      this.updateTabPerformance();
    }, 5000); // Update every 5 seconds
  }

  private updateTabPerformance(): void {
    for (const tab of this.tabs.values()) {
      // Simulate performance data
      tab.performance = {
        memoryUsage: Math.floor(Math.random() * 100 * 1024 * 1024), // 0-100MB
        cpuUsage: Math.random() * 100, // 0-100%
        networkActivity: Math.random() * 1024 * 1024, // 0-1MB/s
        lastUpdated: Date.now(),
      };
    }
  }

  private setupSuspensionMonitoring(): void {
    this.on('tab_activated', (tab: Tab) => {
      this.cancelSuspensionTimer(tab.id);
    });
  }

  private startSuspensionTimer(tabId: string): void {
    if (!this.config.enableTabSuspension) return;

    const tab = this.tabs.get(tabId);
    if (!tab || tab.isPinned) return;

    this.cancelSuspensionTimer(tabId);

    const timer = setTimeout(() => {
      this.suspendTab(tabId);
    }, this.config.suspensionTimeout);

    this.suspensionTimer.set(tabId, timer);
  }

  private cancelSuspensionTimer(tabId: string): void {
    const timer = this.suspensionTimer.get(tabId);
    if (timer) {
      clearTimeout(timer);
      this.suspensionTimer.delete(tabId);
    }
  }

  private async suspendTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab || tab.isActive || tab.isPinned) return;

    // Suspend tab (in real implementation, would unload webContents)
    logger.debug('Tab suspended', { tabId, url: tab.url });
    this.emit('tab_suspended', { tabId });
  }

  // Getters
  public getTabs(): Tab[] {
    return Array.from(this.tabs.values());
  }

  public getTab(tabId: string): Tab | null {
    return this.tabs.get(tabId) || null;
  }

  public getActiveTab(): Tab | null {
    return this.activeTabId ? this.tabs.get(this.activeTabId) || null : null;
  }

  public getTabGroups(): TabGroup[] {
    return Array.from(this.tabGroups.values());
  }

  public getTabCount(): number {
    return this.tabs.size;
  }

  public updateConfig(config: Partial<TabManagerConfig>): void {
    this.config = { ...this.config, ...config };
    configManager.set('tabs', this.config);
    this.emit('config_updated', this.config);
  }

  public getConfig(): TabManagerConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const tabManager = TabManager.getInstance();
