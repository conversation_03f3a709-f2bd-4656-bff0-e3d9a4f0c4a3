import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';
import { configManager } from '../core/ConfigurationManager';

export interface HistoryEntry {
  id: string;
  url: string;
  title: string;
  visitTime: number;
  visitCount: number;
  lastVisitTime: number;
  favicon?: string;
  isPrivate: boolean;
  referrer?: string;
  transition: HistoryTransition;
  duration?: number;
  scrollPosition?: number;
  formData?: Record<string, any>;
}

export type HistoryTransition = 
  | 'link'           // User clicked a link
  | 'typed'          // User typed URL in address bar
  | 'auto_bookmark'  // User clicked a bookmark
  | 'auto_subframe'  // Subframe navigation
  | 'manual_subframe' // Manual subframe navigation
  | 'generated'      // Generated by browser
  | 'start_page'     // Start page
  | 'form_submit'    // Form submission
  | 'reload'         // Page reload
  | 'keyword'        // Keyword search
  | 'keyword_generated'; // Generated from keyword

export interface HistorySearchOptions {
  query?: string;
  startTime?: number;
  endTime?: number;
  maxResults?: number;
  includePrivate?: boolean;
  sortBy?: 'visitTime' | 'title' | 'url' | 'visitCount';
  sortOrder?: 'asc' | 'desc';
}

export interface HistoryStats {
  totalEntries: number;
  totalVisits: number;
  uniqueUrls: number;
  averageVisitsPerDay: number;
  topDomains: Array<{ domain: string; visits: number }>;
  topPages: Array<{ url: string; title: string; visits: number }>;
  visitsByHour: number[];
  visitsByDay: number[];
}

export interface HistoryManagerConfig {
  maxEntries: number;
  retentionDays: number;
  enablePrivateHistory: boolean;
  enableFormDataSaving: boolean;
  enableScrollPositionSaving: boolean;
  autoCleanupInterval: number;
  indexingEnabled: boolean;
}

export class HistoryManager extends EventEmitter {
  private static instance: HistoryManager;
  private history: Map<string, HistoryEntry> = new Map();
  private urlIndex: Map<string, string[]> = new Map(); // URL -> entry IDs
  private domainIndex: Map<string, string[]> = new Map(); // domain -> entry IDs
  private timeIndex: Map<string, string[]> = new Map(); // date -> entry IDs
  private searchIndex: Map<string, Set<string>> = new Map(); // word -> entry IDs
  private config: HistoryManagerConfig;
  private cleanupTimer: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    this.config = {
      maxEntries: 100000,
      retentionDays: 90,
      enablePrivateHistory: false,
      enableFormDataSaving: false,
      enableScrollPositionSaving: true,
      autoCleanupInterval: 24 * 60 * 60 * 1000, // 24 hours
      indexingEnabled: true,
    };

    this.initializeHistoryManager();
  }

  public static getInstance(): HistoryManager {
    if (!HistoryManager.instance) {
      HistoryManager.instance = new HistoryManager();
    }
    return HistoryManager.instance;
  }

  private async initializeHistoryManager(): Promise<void> {
    // Load configuration
    const historyConfig = configManager.get('history', {});
    this.config = { ...this.config, ...historyConfig };

    // Load history from storage
    await this.loadHistory();

    // Build search indices
    if (this.config.indexingEnabled) {
      this.buildSearchIndices();
    }

    // Setup automatic cleanup
    this.setupAutoCleanup();

    logger.info('History manager initialized', {
      entryCount: this.history.size,
      retentionDays: this.config.retentionDays,
      maxEntries: this.config.maxEntries,
    });
  }

  public async addHistoryEntry(entryData: {
    url: string;
    title: string;
    transition: HistoryTransition;
    isPrivate?: boolean;
    referrer?: string;
    duration?: number;
    scrollPosition?: number;
    formData?: Record<string, any>;
  }): Promise<HistoryEntry> {
    // Skip private browsing if disabled
    if (entryData.isPrivate && !this.config.enablePrivateHistory) {
      logger.debug('Skipping private history entry', { url: entryData.url });
      return this.createTemporaryEntry(entryData);
    }

    const now = Date.now();
    const url = entryData.url;

    // Check if URL already exists
    const existingIds = this.urlIndex.get(url) || [];
    let existingEntry: HistoryEntry | null = null;

    // Find the most recent entry for this URL
    if (existingIds.length > 0) {
      const sortedIds = existingIds
        .map(id => this.history.get(id))
        .filter(entry => entry !== undefined)
        .sort((a, b) => b!.lastVisitTime - a!.lastVisitTime);
      
      existingEntry = sortedIds[0] || null;
    }

    let historyEntry: HistoryEntry;

    if (existingEntry && now - existingEntry.lastVisitTime < 30000) {
      // Update existing entry if visited within 30 seconds
      existingEntry.visitCount++;
      existingEntry.lastVisitTime = now;
      existingEntry.title = entryData.title; // Update title in case it changed
      
      if (entryData.duration !== undefined) {
        existingEntry.duration = entryData.duration;
      }
      
      if (entryData.scrollPosition !== undefined && this.config.enableScrollPositionSaving) {
        existingEntry.scrollPosition = entryData.scrollPosition;
      }

      historyEntry = existingEntry;
    } else {
      // Create new entry
      const entryId = `history_${now}_${Math.random().toString(36).substr(2, 9)}`;
      
      historyEntry = {
        id: entryId,
        url: entryData.url,
        title: entryData.title,
        visitTime: now,
        visitCount: 1,
        lastVisitTime: now,
        isPrivate: entryData.isPrivate || false,
        referrer: entryData.referrer,
        transition: entryData.transition,
        duration: entryData.duration,
        scrollPosition: this.config.enableScrollPositionSaving ? entryData.scrollPosition : undefined,
        formData: this.config.enableFormDataSaving ? entryData.formData : undefined,
      };

      this.history.set(entryId, historyEntry);

      // Update indices
      this.updateIndices(historyEntry);
    }

    // Check if we need to cleanup old entries
    if (this.history.size > this.config.maxEntries) {
      await this.cleanupOldEntries();
    }

    // Save to storage
    await this.saveHistory();

    this.emit('history_entry_added', historyEntry);
    logger.debug('History entry added', {
      entryId: historyEntry.id,
      url: historyEntry.url,
      title: historyEntry.title,
      visitCount: historyEntry.visitCount,
    });

    return historyEntry;
  }

  private createTemporaryEntry(entryData: any): HistoryEntry {
    // Create a temporary entry that won't be persisted
    return {
      id: `temp_${Date.now()}`,
      url: entryData.url,
      title: entryData.title,
      visitTime: Date.now(),
      visitCount: 1,
      lastVisitTime: Date.now(),
      isPrivate: true,
      transition: entryData.transition,
    };
  }

  public async deleteHistoryEntry(entryId: string): Promise<void> {
    const entry = this.history.get(entryId);
    if (!entry) {
      throw new Error(`History entry ${entryId} not found`);
    }

    // Remove from indices
    this.removeFromIndices(entry);

    // Remove from history
    this.history.delete(entryId);

    // Save to storage
    await this.saveHistory();

    this.emit('history_entry_deleted', { entryId, entry });
    logger.debug('History entry deleted', { entryId, url: entry.url });
  }

  public async deleteHistoryByUrl(url: string): Promise<number> {
    const entryIds = this.urlIndex.get(url) || [];
    let deletedCount = 0;

    for (const entryId of entryIds) {
      try {
        await this.deleteHistoryEntry(entryId);
        deletedCount++;
      } catch (error) {
        logger.warn('Failed to delete history entry', { entryId, error });
      }
    }

    logger.info('History entries deleted by URL', { url, deletedCount });
    return deletedCount;
  }

  public async deleteHistoryByTimeRange(startTime: number, endTime: number): Promise<number> {
    const entries = Array.from(this.history.values())
      .filter(entry => entry.visitTime >= startTime && entry.visitTime <= endTime);

    let deletedCount = 0;

    for (const entry of entries) {
      try {
        await this.deleteHistoryEntry(entry.id);
        deletedCount++;
      } catch (error) {
        logger.warn('Failed to delete history entry', { entryId: entry.id, error });
      }
    }

    logger.info('History entries deleted by time range', {
      startTime,
      endTime,
      deletedCount,
    });

    return deletedCount;
  }

  public async clearAllHistory(): Promise<void> {
    const entryCount = this.history.size;

    this.history.clear();
    this.urlIndex.clear();
    this.domainIndex.clear();
    this.timeIndex.clear();
    this.searchIndex.clear();

    // Save to storage
    await this.saveHistory();

    this.emit('history_cleared', { entryCount });
    logger.info('All history cleared', { entryCount });
  }

  public searchHistory(options: HistorySearchOptions): HistoryEntry[] {
    let results = Array.from(this.history.values());

    // Filter by privacy
    if (!options.includePrivate) {
      results = results.filter(entry => !entry.isPrivate);
    }

    // Filter by time range
    if (options.startTime !== undefined) {
      results = results.filter(entry => entry.visitTime >= options.startTime!);
    }

    if (options.endTime !== undefined) {
      results = results.filter(entry => entry.visitTime <= options.endTime!);
    }

    // Text search
    if (options.query) {
      const query = options.query.toLowerCase();
      results = results.filter(entry =>
        entry.title.toLowerCase().includes(query) ||
        entry.url.toLowerCase().includes(query)
      );
    }

    // Sort results
    const sortBy = options.sortBy || 'visitTime';
    const sortOrder = options.sortOrder || 'desc';

    results.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'visitTime':
          aValue = a.lastVisitTime;
          bValue = b.lastVisitTime;
          break;
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'url':
          aValue = a.url.toLowerCase();
          bValue = b.url.toLowerCase();
          break;
        case 'visitCount':
          aValue = a.visitCount;
          bValue = b.visitCount;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === 'desc' ? 1 : -1;
      if (aValue > bValue) return sortOrder === 'desc' ? -1 : 1;
      return 0;
    });

    // Limit results
    if (options.maxResults) {
      results = results.slice(0, options.maxResults);
    }

    return results;
  }

  public getHistoryStats(): HistoryStats {
    const entries = Array.from(this.history.values());
    const totalEntries = entries.length;
    const totalVisits = entries.reduce((sum, entry) => sum + entry.visitCount, 0);
    const uniqueUrls = new Set(entries.map(entry => entry.url)).size;

    // Calculate average visits per day
    const oldestEntry = entries.reduce((oldest, entry) => 
      entry.visitTime < oldest.visitTime ? entry : oldest, 
      entries[0] || { visitTime: Date.now() }
    );
    const daysSinceOldest = Math.max(1, (Date.now() - oldestEntry.visitTime) / (24 * 60 * 60 * 1000));
    const averageVisitsPerDay = totalVisits / daysSinceOldest;

    // Top domains
    const domainCounts = new Map<string, number>();
    entries.forEach(entry => {
      try {
        const domain = new URL(entry.url).hostname;
        domainCounts.set(domain, (domainCounts.get(domain) || 0) + entry.visitCount);
      } catch {
        // Invalid URL, skip
      }
    });

    const topDomains = Array.from(domainCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([domain, visits]) => ({ domain, visits }));

    // Top pages
    const topPages = entries
      .sort((a, b) => b.visitCount - a.visitCount)
      .slice(0, 10)
      .map(entry => ({
        url: entry.url,
        title: entry.title,
        visits: entry.visitCount,
      }));

    // Visits by hour (0-23)
    const visitsByHour = new Array(24).fill(0);
    entries.forEach(entry => {
      const hour = new Date(entry.lastVisitTime).getHours();
      visitsByHour[hour] += entry.visitCount;
    });

    // Visits by day of week (0-6, Sunday-Saturday)
    const visitsByDay = new Array(7).fill(0);
    entries.forEach(entry => {
      const day = new Date(entry.lastVisitTime).getDay();
      visitsByDay[day] += entry.visitCount;
    });

    return {
      totalEntries,
      totalVisits,
      uniqueUrls,
      averageVisitsPerDay,
      topDomains,
      topPages,
      visitsByHour,
      visitsByDay,
    };
  }

  public getMostVisitedUrls(limit: number = 10): Array<{ url: string; title: string; visitCount: number }> {
    return Array.from(this.history.values())
      .sort((a, b) => b.visitCount - a.visitCount)
      .slice(0, limit)
      .map(entry => ({
        url: entry.url,
        title: entry.title,
        visitCount: entry.visitCount,
      }));
  }

  public getRecentlyVisited(limit: number = 10): HistoryEntry[] {
    return Array.from(this.history.values())
      .sort((a, b) => b.lastVisitTime - a.lastVisitTime)
      .slice(0, limit);
  }

  private updateIndices(entry: HistoryEntry): void {
    // URL index
    if (!this.urlIndex.has(entry.url)) {
      this.urlIndex.set(entry.url, []);
    }
    this.urlIndex.get(entry.url)!.push(entry.id);

    // Domain index
    try {
      const domain = new URL(entry.url).hostname;
      if (!this.domainIndex.has(domain)) {
        this.domainIndex.set(domain, []);
      }
      this.domainIndex.get(domain)!.push(entry.id);
    } catch {
      // Invalid URL, skip domain indexing
    }

    // Time index (by date)
    const dateKey = new Date(entry.visitTime).toISOString().split('T')[0];
    if (!this.timeIndex.has(dateKey)) {
      this.timeIndex.set(dateKey, []);
    }
    this.timeIndex.get(dateKey)!.push(entry.id);

    // Search index
    if (this.config.indexingEnabled) {
      this.updateSearchIndex(entry);
    }
  }

  private removeFromIndices(entry: HistoryEntry): void {
    // URL index
    const urlEntries = this.urlIndex.get(entry.url);
    if (urlEntries) {
      const index = urlEntries.indexOf(entry.id);
      if (index > -1) {
        urlEntries.splice(index, 1);
        if (urlEntries.length === 0) {
          this.urlIndex.delete(entry.url);
        }
      }
    }

    // Domain index
    try {
      const domain = new URL(entry.url).hostname;
      const domainEntries = this.domainIndex.get(domain);
      if (domainEntries) {
        const index = domainEntries.indexOf(entry.id);
        if (index > -1) {
          domainEntries.splice(index, 1);
          if (domainEntries.length === 0) {
            this.domainIndex.delete(domain);
          }
        }
      }
    } catch {
      // Invalid URL, skip
    }

    // Time index
    const dateKey = new Date(entry.visitTime).toISOString().split('T')[0];
    const timeEntries = this.timeIndex.get(dateKey);
    if (timeEntries) {
      const index = timeEntries.indexOf(entry.id);
      if (index > -1) {
        timeEntries.splice(index, 1);
        if (timeEntries.length === 0) {
          this.timeIndex.delete(dateKey);
        }
      }
    }

    // Search index
    this.removeFromSearchIndex(entry);
  }

  private buildSearchIndices(): void {
    this.searchIndex.clear();
    for (const entry of this.history.values()) {
      this.updateSearchIndex(entry);
    }
  }

  private updateSearchIndex(entry: HistoryEntry): void {
    const words = [
      ...entry.title.toLowerCase().split(/\s+/),
      ...entry.url.toLowerCase().split(/[\/\.\-_]/),
    ];

    for (const word of words) {
      if (word.length > 2) {
        if (!this.searchIndex.has(word)) {
          this.searchIndex.set(word, new Set());
        }
        this.searchIndex.get(word)!.add(entry.id);
      }
    }
  }

  private removeFromSearchIndex(entry: HistoryEntry): void {
    for (const wordSet of this.searchIndex.values()) {
      wordSet.delete(entry.id);
    }
  }

  private setupAutoCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupOldEntries();
    }, this.config.autoCleanupInterval);
  }

  private async cleanupOldEntries(): Promise<void> {
    const cutoffTime = Date.now() - (this.config.retentionDays * 24 * 60 * 60 * 1000);
    const entriesToDelete = Array.from(this.history.values())
      .filter(entry => entry.visitTime < cutoffTime);

    let deletedCount = 0;
    for (const entry of entriesToDelete) {
      try {
        await this.deleteHistoryEntry(entry.id);
        deletedCount++;
      } catch (error) {
        logger.warn('Failed to cleanup history entry', { entryId: entry.id, error });
      }
    }

    if (deletedCount > 0) {
      logger.info('History cleanup completed', { deletedCount, cutoffTime });
    }
  }

  private async loadHistory(): Promise<void> {
    try {
      // In a real implementation, load from persistent storage
      logger.debug('History loaded from storage');
    } catch (error) {
      logger.warn('Failed to load history', { error });
    }
  }

  private async saveHistory(): Promise<void> {
    try {
      // In a real implementation, save to persistent storage
      logger.debug('History saved to storage');
    } catch (error) {
      logger.warn('Failed to save history', { error });
    }
  }

  // Getters
  public getHistory(): HistoryEntry[] {
    return Array.from(this.history.values());
  }

  public getHistoryEntry(entryId: string): HistoryEntry | null {
    return this.history.get(entryId) || null;
  }

  public getHistoryCount(): number {
    return this.history.size;
  }

  public updateConfig(config: Partial<HistoryManagerConfig>): void {
    this.config = { ...this.config, ...config };
    configManager.set('history', this.config);
    this.emit('config_updated', this.config);
  }

  public getConfig(): HistoryManagerConfig {
    return { ...this.config };
  }

  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.removeAllListeners();
  }
}

// Export singleton instance
export const historyManager = HistoryManager.getInstance();
