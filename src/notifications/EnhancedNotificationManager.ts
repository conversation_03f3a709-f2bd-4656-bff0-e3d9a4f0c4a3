import { EventEmitter } from 'events';
import { logger } from '../core/EnhancedLogger';
import { configManager } from '../core/ConfigurationManager';

export interface NotificationConfig {
  enableNotifications: boolean;
  enableSystemNotifications: boolean;
  enableInAppNotifications: boolean;
  enableSoundNotifications: boolean;
  enableVibrationNotifications: boolean;
  notificationTimeout: number;
  maxNotifications: number;
  enableDoNotDisturb: boolean;
  doNotDisturbStart: string; // HH:MM format
  doNotDisturbEnd: string; // HH:MM format
  enablePriorityFiltering: boolean;
  enableGrouping: boolean;
  enableBadgeCount: boolean;
  soundVolume: number; // 0-100
}

export interface EnhancedNotification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  category: NotificationCategory;
  icon?: string;
  image?: string;
  actions?: NotificationAction[];
  data?: Record<string, any>;
  timestamp: number;
  expiresAt?: number;
  persistent: boolean;
  silent: boolean;
  requireInteraction: boolean;
  tag?: string;
  group?: string;
  status: 'pending' | 'shown' | 'clicked' | 'dismissed' | 'expired';
  source: string;
  url?: string;
  read: boolean;
}

export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'security' | 'update' | 'download' | 'sync';

export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

export type NotificationCategory = 'system' | 'security' | 'download' | 'sync' | 'extension' | 'update' | 'user';

export interface NotificationAction {
  id: string;
  title: string;
  icon?: string;
  type: 'button' | 'input';
  placeholder?: string;
  action?: () => void;
}

export interface NotificationGroup {
  id: string;
  title: string;
  notifications: EnhancedNotification[];
  collapsed: boolean;
  priority: NotificationPriority;
  lastUpdated: number;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  category: NotificationCategory;
  icon?: string;
  actions?: NotificationAction[];
  variables: string[];
}

export interface NotificationStats {
  totalNotifications: number;
  shownNotifications: number;
  clickedNotifications: number;
  dismissedNotifications: number;
  clickThroughRate: number;
  averageDisplayTime: number;
  notificationsByType: Record<NotificationType, number>;
  notificationsByPriority: Record<NotificationPriority, number>;
}

export class EnhancedNotificationManager extends EventEmitter {
  private static instance: EnhancedNotificationManager;
  private config: NotificationConfig;
  private notifications: Map<string, EnhancedNotification> = new Map();
  private groups: Map<string, NotificationGroup> = new Map();
  private templates: Map<string, NotificationTemplate> = new Map();
  private activeNotifications: Set<string> = new Set();
  private notificationQueue: EnhancedNotification[] = [];
  private cleanupTimer: NodeJS.Timeout | null = null;
  private badgeCount = 0;

  private constructor() {
    super();
    this.config = {
      enableNotifications: true,
      enableSystemNotifications: true,
      enableInAppNotifications: true,
      enableSoundNotifications: true,
      enableVibrationNotifications: false,
      notificationTimeout: 5000, // 5 seconds
      maxNotifications: 10,
      enableDoNotDisturb: false,
      doNotDisturbStart: '22:00',
      doNotDisturbEnd: '08:00',
      enablePriorityFiltering: true,
      enableGrouping: true,
      enableBadgeCount: true,
      soundVolume: 50,
    };

    this.initializeNotificationManager();
  }

  public static getInstance(): EnhancedNotificationManager {
    if (!EnhancedNotificationManager.instance) {
      EnhancedNotificationManager.instance = new EnhancedNotificationManager();
    }
    return EnhancedNotificationManager.instance;
  }

  private async initializeNotificationManager(): Promise<void> {
    // Загрузка конфигурации
    const notificationConfig = configManager.get('notifications', {});
    this.config = { ...this.config, ...notificationConfig };

    // Запрос разрешений на уведомления
    await this.requestNotificationPermission();

    // Загрузка шаблонов уведомлений
    this.loadNotificationTemplates();

    // Настройка автоочистки
    this.setupAutoCleanup();

    // Восстановление состояния
    await this.restoreNotificationState();

    logger.info('Enhanced notification manager initialized', {
      enableNotifications: this.config.enableNotifications,
      enableSystemNotifications: this.config.enableSystemNotifications,
      maxNotifications: this.config.maxNotifications,
    });
  }

  public async showNotification(notificationData: {
    title: string;
    message: string;
    type?: NotificationType;
    priority?: NotificationPriority;
    category?: NotificationCategory;
    icon?: string;
    image?: string;
    actions?: NotificationAction[];
    data?: Record<string, any>;
    persistent?: boolean;
    silent?: boolean;
    requireInteraction?: boolean;
    tag?: string;
    group?: string;
    source?: string;
    url?: string;
    expiresIn?: number;
  }): Promise<EnhancedNotification> {
    if (!this.config.enableNotifications) {
      throw new Error('Notifications are disabled');
    }

    // Проверка режима "Не беспокоить"
    if (this.isDoNotDisturbActive() && notificationData.priority !== 'urgent') {
      logger.debug('Notification blocked by Do Not Disturb mode', {
        title: notificationData.title,
        priority: notificationData.priority,
      });
      throw new Error('Notifications blocked by Do Not Disturb mode');
    }

    const notificationId = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = Date.now();

    const notification: EnhancedNotification = {
      id: notificationId,
      title: notificationData.title,
      message: notificationData.message,
      type: notificationData.type || 'info',
      priority: notificationData.priority || 'normal',
      category: notificationData.category || 'system',
      icon: notificationData.icon,
      image: notificationData.image,
      actions: notificationData.actions,
      data: notificationData.data,
      timestamp: now,
      expiresAt: notificationData.expiresIn ? now + notificationData.expiresIn : undefined,
      persistent: notificationData.persistent || false,
      silent: notificationData.silent || false,
      requireInteraction: notificationData.requireInteraction || false,
      tag: notificationData.tag,
      group: notificationData.group,
      status: 'pending',
      source: notificationData.source || 'system',
      url: notificationData.url,
      read: false,
    };

    // Проверка дублирования по тегу
    if (notification.tag) {
      const existingNotification = Array.from(this.notifications.values())
        .find(n => n.tag === notification.tag && n.status !== 'dismissed');
      
      if (existingNotification) {
        // Обновление существующего уведомления
        return this.updateNotification(existingNotification.id, notificationData);
      }
    }

    this.notifications.set(notificationId, notification);

    // Добавление в группу
    if (notification.group && this.config.enableGrouping) {
      this.addToGroup(notification);
    }

    // Проверка лимита активных уведомлений
    if (this.activeNotifications.size >= this.config.maxNotifications) {
      this.notificationQueue.push(notification);
    } else {
      await this.displayNotification(notification);
    }

    this.emit('notification_created', notification);
    logger.debug('Enhanced notification created', {
      id: notificationId,
      title: notification.title,
      type: notification.type,
      priority: notification.priority,
    });

    return notification;
  }

  public async updateNotification(notificationId: string, updates: Partial<EnhancedNotification>): Promise<EnhancedNotification> {
    const notification = this.notifications.get(notificationId);
    if (!notification) {
      throw new Error(`Notification ${notificationId} not found`);
    }

    // Обновление полей
    Object.assign(notification, updates);
    notification.timestamp = Date.now();

    // Повторное отображение если уведомление активно
    if (this.activeNotifications.has(notificationId)) {
      await this.displayNotification(notification);
    }

    this.emit('notification_updated', notification);
    logger.debug('Notification updated', { id: notificationId });

    return notification;
  }

  public async dismissNotification(notificationId: string): Promise<void> {
    const notification = this.notifications.get(notificationId);
    if (!notification) {
      return;
    }

    notification.status = 'dismissed';
    this.activeNotifications.delete(notificationId);

    // Обновление счетчика бейджа
    this.updateBadgeCount();

    // Скрытие системного уведомления
    if (this.config.enableSystemNotifications) {
      await this.hideSystemNotification(notificationId);
    }

    // Обработка очереди
    this.processNotificationQueue();

    this.emit('notification_dismissed', notification);
    logger.debug('Notification dismissed', { id: notificationId });
  }

  public markAsRead(id: string): void {
    const notification = this.notifications.get(id);
    if (notification) {
      notification.read = true;
      this.emit('notification_read', notification);
      logger.debug('Notification marked as read', { id });
    }
  }

  public async dismissAllNotifications(): Promise<void> {
    const activeNotifications = Array.from(this.activeNotifications);
    
    for (const notificationId of activeNotifications) {
      await this.dismissNotification(notificationId);
    }

    this.emit('all_notifications_dismissed');
    logger.info('All notifications dismissed');
  }

  public clearAll(): void {
    this.notifications.clear();
    this.activeNotifications.clear();
    this.groups.clear();
    this.notificationQueue = [];
    this.badgeCount = 0;
    this.updateBadgeCount();
    this.emit('notifications_cleared');
    logger.info('All notifications cleared');
  }

  private async displayNotification(notification: EnhancedNotification): Promise<void> {
    notification.status = 'shown';
    this.activeNotifications.add(notification.id);

    // Обновление счетчика бейджа
    this.updateBadgeCount();

    // Отображение системного уведомления
    if (this.config.enableSystemNotifications && this.hasSystemNotificationPermission()) {
      await this.showSystemNotification(notification);
    }

    // Отображение внутреннего уведомления
    if (this.config.enableInAppNotifications) {
      this.showInAppNotification(notification);
    }

    // Воспроизведение звука
    if (this.config.enableSoundNotifications && !notification.silent) {
      this.playNotificationSound(notification);
    }

    // Вибрация
    if (this.config.enableVibrationNotifications && !notification.silent) {
      this.triggerVibration(notification);
    }

    // Автоматическое скрытие
    if (!notification.persistent && !notification.requireInteraction) {
      setTimeout(() => {
        if (notification.status === 'shown') {
          this.dismissNotification(notification.id);
        }
      }, this.config.notificationTimeout);
    }

    this.emit('notification_displayed', notification);
    logger.debug('Notification displayed', { id: notification.id });
  }

  private async showSystemNotification(notification: EnhancedNotification): Promise<void> {
    if (!('Notification' in window)) {
      return;
    }

    try {
      const systemNotification = new Notification(notification.title, {
        body: notification.message,
        icon: notification.icon,
        tag: notification.tag,
        silent: notification.silent,
        requireInteraction: notification.requireInteraction,
        data: { id: notification.id },
      });

      systemNotification.onclick = () => {
        this.clickNotification(notification.id);
      };

      systemNotification.onclose = () => {
        this.dismissNotification(notification.id);
      };

    } catch (error) {
      logger.error('Failed to show system notification', error, { id: notification.id });
    }
  }

  private async hideSystemNotification(notificationId: string): Promise<void> {
    // В реальной реализации здесь будет скрытие системного уведомления
    logger.debug('System notification hidden', { id: notificationId });
  }

  private showInAppNotification(notification: EnhancedNotification): void {
    // Отображение внутреннего уведомления в интерфейсе браузера
    this.emit('show_in_app_notification', notification);
  }

  private playNotificationSound(notification: EnhancedNotification): void {
    if (!this.config.enableSoundNotifications || notification.silent) {
      return;
    }

    try {
      // Воспроизведение звука уведомления
      const audio = new Audio('/sounds/notification.mp3');
      audio.volume = this.config.soundVolume / 100;
      audio.play().catch(error => {
        logger.warn('Failed to play notification sound', error);
      });
    } catch (error) {
      logger.warn('Failed to create notification audio', error);
    }
  }

  private triggerVibration(notification: EnhancedNotification): void {
    if (!this.config.enableVibrationNotifications || notification.silent) {
      return;
    }

    if ('vibrate' in navigator) {
      // Паттерн вибрации в зависимости от приоритета
      const vibrationPatterns = {
        low: [100],
        normal: [200],
        high: [100, 100, 200],
        urgent: [200, 100, 200, 100, 200],
      };

      navigator.vibrate(vibrationPatterns[notification.priority]);
    }
  }

  private async clickNotification(notificationId: string, actionId?: string): Promise<void> {
    const notification = this.notifications.get(notificationId);
    if (!notification) {
      return;
    }

    notification.status = 'clicked';

    // Обработка действия
    if (actionId && notification.actions) {
      const action = notification.actions.find(a => a.id === actionId);
      if (action) {
        if (action.action) {
          action.action();
        }
        this.emit('notification_action_clicked', { notification, action });
      }
    } else {
      // Обычный клик по уведомлению
      if (notification.url) {
        // Открытие URL
        this.emit('notification_url_requested', { notification, url: notification.url });
      }
    }

    // Автоматическое скрытие после клика (если не persistent)
    if (!notification.persistent) {
      await this.dismissNotification(notificationId);
    }

    this.emit('notification_clicked', { notification, actionId });
    logger.debug('Notification clicked', { id: notificationId, actionId });
  }

  private addToGroup(notification: EnhancedNotification): void {
    if (!notification.group) {
      return;
    }

    let group = this.groups.get(notification.group);
    if (!group) {
      group = {
        id: notification.group,
        title: notification.group,
        notifications: [],
        collapsed: false,
        priority: notification.priority,
        lastUpdated: Date.now(),
      };
      this.groups.set(notification.group, group);
    }

    group.notifications.push(notification);
    group.lastUpdated = Date.now();
    
    // Обновление приоритета группы
    if (this.getPriorityLevel(notification.priority) > this.getPriorityLevel(group.priority)) {
      group.priority = notification.priority;
    }

    this.emit('notification_group_updated', group);
  }

  private processNotificationQueue(): void {
    if (this.notificationQueue.length === 0) {
      return;
    }

    if (this.activeNotifications.size < this.config.maxNotifications) {
      const nextNotification = this.notificationQueue.shift();
      if (nextNotification) {
        this.displayNotification(nextNotification);
      }
    }
  }

  private updateBadgeCount(): void {
    if (!this.config.enableBadgeCount) {
      return;
    }

    this.badgeCount = this.activeNotifications.size;

    // Обновление бейджа в интерфейсе
    this.emit('badge_count_updated', this.badgeCount);

    // Обновление бейджа в системе (если поддерживается)
    if ('setAppBadge' in navigator) {
      (navigator as any).setAppBadge(this.badgeCount);
    }
  }

  private isDoNotDisturbActive(): boolean {
    if (!this.config.enableDoNotDisturb) {
      return false;
    }

    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const start = this.config.doNotDisturbStart;
    const end = this.config.doNotDisturbEnd;

    // Проверка времени "Не беспокоить"
    if (start <= end) {
      return currentTime >= start && currentTime <= end;
    } else {
      // Период через полночь
      return currentTime >= start || currentTime <= end;
    }
  }

  private getPriorityLevel(priority: NotificationPriority): number {
    const levels = { low: 1, normal: 2, high: 3, urgent: 4 };
    return levels[priority] || 2;
  }

  private async requestNotificationPermission(): Promise<void> {
    if (!('Notification' in window)) {
      logger.warn('Browser does not support notifications');
      return;
    }

    if (Notification.permission === 'default') {
      try {
        const permission = await Notification.requestPermission();
        logger.info('Notification permission requested', { permission });
      } catch (error) {
        logger.error('Failed to request notification permission', error);
      }
    }
  }

  private hasSystemNotificationPermission(): boolean {
    return 'Notification' in window && Notification.permission === 'granted';
  }

  private loadNotificationTemplates(): void {
    // Загрузка предустановленных шаблонов
    const defaultTemplates: NotificationTemplate[] = [
      {
        id: 'download_completed',
        name: 'Download Completed',
        title: 'Download Completed',
        message: 'File "{{filename}}" has been downloaded successfully',
        type: 'success',
        priority: 'normal',
        category: 'download',
        icon: '/icons/download.png',
        variables: ['filename'],
      },
      {
        id: 'security_threat',
        name: 'Security Threat',
        title: 'Security Alert',
        message: 'Security threat detected: {{threat}}',
        type: 'security',
        priority: 'urgent',
        category: 'security',
        icon: '/icons/security.png',
        variables: ['threat'],
      },
      {
        id: 'sync_completed',
        name: 'Sync Completed',
        title: 'Sync Completed',
        message: 'Data synchronization completed successfully',
        type: 'success',
        priority: 'low',
        category: 'sync',
        icon: '/icons/sync.png',
        variables: [],
      },
    ];

    defaultTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  private setupAutoCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredNotifications();
    }, 60000); // Каждую минуту
  }

  private cleanupExpiredNotifications(): void {
    const now = Date.now();
    const expiredNotifications = Array.from(this.notifications.values())
      .filter(notification => 
        notification.expiresAt && 
        notification.expiresAt < now &&
        notification.status !== 'dismissed'
      );

    expiredNotifications.forEach(notification => {
      notification.status = 'expired';
      this.activeNotifications.delete(notification.id);
    });

    if (expiredNotifications.length > 0) {
      this.updateBadgeCount();
      this.processNotificationQueue();
      
      logger.debug('Expired notifications cleaned up', { count: expiredNotifications.length });
    }
  }

  private async restoreNotificationState(): Promise<void> {
    // Восстановление состояния уведомлений после перезапуска
    logger.debug('Notification state restored');
  }

  // Геттеры
  public getNotifications(): EnhancedNotification[] {
    return Array.from(this.notifications.values());
  }

  public getActiveNotifications(): EnhancedNotification[] {
    return Array.from(this.notifications.values())
      .filter(notification => this.activeNotifications.has(notification.id));
  }

  public getUnreadCount(): number {
    return Array.from(this.notifications.values()).filter(n => !n.read).length;
  }

  public getNotificationGroups(): NotificationGroup[] {
    return Array.from(this.groups.values());
  }

  public getNotificationTemplates(): NotificationTemplate[] {
    return Array.from(this.templates.values());
  }

  public getBadgeCount(): number {
    return this.badgeCount;
  }

  public getNotificationStats(): NotificationStats {
    const notifications = Array.from(this.notifications.values());
    const shownNotifications = notifications.filter(n => n.status === 'shown' || n.status === 'clicked');
    const clickedNotifications = notifications.filter(n => n.status === 'clicked');

    const notificationsByType = notifications.reduce((acc, notification) => {
      acc[notification.type] = (acc[notification.type] || 0) + 1;
      return acc;
    }, {} as Record<NotificationType, number>);

    const notificationsByPriority = notifications.reduce((acc, notification) => {
      acc[notification.priority] = (acc[notification.priority] || 0) + 1;
      return acc;
    }, {} as Record<NotificationPriority, number>);

    return {
      totalNotifications: notifications.length,
      shownNotifications: shownNotifications.length,
      clickedNotifications: clickedNotifications.length,
      dismissedNotifications: notifications.filter(n => n.status === 'dismissed').length,
      clickThroughRate: shownNotifications.length > 0 ? (clickedNotifications.length / shownNotifications.length) * 100 : 0,
      averageDisplayTime: 0, // Будет рассчитано на основе реальных данных
      notificationsByType,
      notificationsByPriority,
    };
  }

  public updateConfig(config: Partial<NotificationConfig>): void {
    this.config = { ...this.config, ...config };
    configManager.set('notifications', this.config);
    this.emit('config_updated', this.config);
  }

  public getConfig(): NotificationConfig {
    return { ...this.config };
  }

  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.removeAllListeners();
  }
}

// Экспорт синглтона
export const enhancedNotificationManager = EnhancedNotificationManager.getInstance();
