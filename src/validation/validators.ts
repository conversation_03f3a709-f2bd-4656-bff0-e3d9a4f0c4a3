import { z } from 'zod';
import { ValidationResult } from '../types';

export class Validator {
  private static instance: Validator;
  private validators: Map<string, z.ZodType<any>>;

  private constructor() {
    this.validators = new Map();
    this.initializeValidators();
  }

  public static getInstance(): Validator {
    if (!Validator.instance) {
      Validator.instance = new Validator();
    }
    return Validator.instance;
  }

  private initializeValidators(): void {
    // User validators
    this.validators.set('user', z.object({
      id: z.string().uuid(),
      email: z.string().email(),
      username: z.string().min(3).max(50),
      role: z.enum(['admin', 'user', 'guest']),
      permissions: z.array(z.string()),
      settings: z.record(z.unknown()),
      createdAt: z.date(),
      updatedAt: z.date(),
    }));

    // Security validators
    this.validators.set('security', z.object({
      encryption: z.object({
        algorithm: z.string(),
        keySize: z.number().min(128).max(512),
        iterations: z.number().min(1000),
      }),
      jwt: z.object({
        secret: z.string().min(32),
        expiresIn: z.string(),
        refreshExpiresIn: z.string(),
      }),
    }));

    // Performance validators
    this.validators.set('performance', z.object({
      cache: z.object({
        enabled: z.boolean(),
        ttl: z.number().min(0),
        maxSize: z.number().min(1),
      }),
      compression: z.object({
        enabled: z.boolean(),
        level: z.number().min(0).max(9),
      }),
    }));

    // Analytics validators
    this.validators.set('analytics', z.object({
      enabled: z.boolean(),
      providers: z.array(z.string()),
      events: z.array(z.string()),
    }));

    // Accessibility validators
    this.validators.set('accessibility', z.object({
      enabled: z.boolean(),
      features: z.array(z.string()),
      compliance: z.array(z.string()),
    }));
  }

  public validate<T>(type: string, data: unknown): ValidationResult {
    const validator = this.validators.get(type);
    if (!validator) {
      return {
        isValid: false,
        errors: [`No validator found for type: ${type}`],
        warnings: [],
      };
    }

    try {
      const result = validator.safeParse(data);
      if (result.success) {
        return {
          isValid: true,
          errors: [],
          warnings: [],
        };
      } else {
        return {
          isValid: false,
          errors: result.error.errors.map(err => err.message),
          warnings: [],
        };
      }
    } catch (error) {
      return {
        isValid: false,
        errors: [error instanceof Error ? error.message : 'Unknown validation error'],
        warnings: [],
      };
    }
  }

  public addValidator(type: string, schema: z.ZodType<any>): void {
    this.validators.set(type, schema);
  }

  public removeValidator(type: string): void {
    this.validators.delete(type);
  }

  public getValidator(type: string): z.ZodType<any> | undefined {
    return this.validators.get(type);
  }
}

export const validator = Validator.getInstance(); 