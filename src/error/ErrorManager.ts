import { EventEmitter } from 'events';

export interface ErrorEvent {
  id: string;
  type: string;
  message: string;
  stack?: string;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
  user?: {
    id?: string;
    sessionId?: string;
    userAgent?: string;
  };
}

export interface ErrorConfig {
  maxErrors: number;
  errorRetentionPeriod: number;
  autoReport: boolean;
  reportEndpoint?: string;
  logToConsole: boolean;
  severityThresholds: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
}

export class ErrorManager extends EventEmitter {
  private static instance: ErrorManager;
  private config: ErrorConfig;
  private errors: ErrorEvent[];
  private errorCounts: Map<string, number>;
  private isReporting: boolean;

  private constructor() {
    super();
    this.config = {
      maxErrors: 1000,
      errorRetentionPeriod: 7 * 24 * 60 * 60 * 1000, // 7 days
      autoReport: true,
      logToConsole: true,
      severityThresholds: {
        low: 100,
        medium: 50,
        high: 10,
        critical: 1,
      },
    };
    this.errors = [];
    this.errorCounts = new Map();
    this.isReporting = false;
  }

  public static getInstance(): ErrorManager {
    if (!ErrorManager.instance) {
      ErrorManager.instance = new ErrorManager();
    }
    return ErrorManager.instance;
  }

  public initialize(): void {
    this.setupGlobalErrorHandlers();
    this.startCleanupInterval();
    this.emit('initialized');
  }

  private setupGlobalErrorHandlers(): void {
    // Handle uncaught errors
    window.addEventListener('error', (event) => {
      this.handleError({
        type: 'uncaught',
        message: event.message,
        stack: event.error?.stack,
        severity: 'high',
        context: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        },
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: 'unhandledRejection',
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        severity: 'high',
        context: {
          reason: event.reason,
        },
      });
    });
  }

  private startCleanupInterval(): void {
    setInterval(() => {
      this.cleanupOldErrors();
    }, 60 * 60 * 1000); // Clean up every hour
  }

  public handleError(error: Partial<ErrorEvent>): void {
    const errorEvent: ErrorEvent = {
      id: this.generateErrorId(),
      type: error.type || 'unknown',
      message: error.message || 'Unknown error occurred',
      stack: error.stack,
      timestamp: Date.now(),
      severity: error.severity || 'medium',
      context: {
        ...error.context,
        url: typeof window !== 'undefined' ? window.location.href : 'unknown',
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
        timestamp: new Date().toISOString(),
        buildVersion: process.env.REACT_APP_VERSION || 'unknown',
        environment: process.env.NODE_ENV || 'unknown',
        correlationId: this.generateCorrelationId(),
      },
      user: this.getUserContext(),
    };

    // Add to errors array
    this.errors.push(errorEvent);

    // Update error counts
    const count = (this.errorCounts.get(errorEvent.type) || 0) + 1;
    this.errorCounts.set(errorEvent.type, count);

    // Check severity thresholds
    this.checkSeverityThresholds(errorEvent);

    // Log to console if enabled
    if (this.config.logToConsole) {
      this.logToConsole(errorEvent);
    }

    // Auto-report if enabled
    if (this.config.autoReport) {
      this.reportError(errorEvent);
    }

    // Emit error event
    this.emit('error', errorEvent);

    // Store error in local storage for persistence
    this.persistError(errorEvent);

    // Update error metrics
    this.updateErrorMetrics(errorEvent);

    // Trim errors array if needed
    if (this.errors.length > this.config.maxErrors) {
      this.errors = this.errors.slice(-this.config.maxErrors);
    }
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCorrelationId(): string {
    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private persistError(error: ErrorEvent): void {
    try {
      const persistedErrors = this.getPersistedErrors();
      persistedErrors.push(error);

      // Keep only last 100 errors in storage
      const trimmedErrors = persistedErrors.slice(-100);

      localStorage.setItem('browser_errors', JSON.stringify(trimmedErrors));
    } catch (e) {
      console.warn('Failed to persist error to localStorage:', e);
    }
  }

  private getPersistedErrors(): ErrorEvent[] {
    try {
      const stored = localStorage.getItem('browser_errors');
      return stored ? JSON.parse(stored) : [];
    } catch (e) {
      console.warn('Failed to retrieve persisted errors:', e);
      return [];
    }
  }

  private updateErrorMetrics(error: ErrorEvent): void {
    // Update error rate metrics
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    // Count errors in the last hour
    const recentErrors = this.errors.filter(e => e.timestamp > oneHourAgo);
    const errorRate = recentErrors.length;

    // Emit metrics update
    this.emit('metrics', {
      totalErrors: this.errors.length,
      errorRate,
      errorsByType: Object.fromEntries(this.errorCounts),
      lastError: error,
    });
  }

  private getUserContext(): ErrorEvent['user'] {
    return {
      userAgent: navigator.userAgent,
      sessionId: this.getSessionId(),
    };
  }

  private getSessionId(): string {
    // In a real implementation, you would get this from your session management system
    return 'session_' + Math.random().toString(36).substr(2, 9);
  }

  private checkSeverityThresholds(error: ErrorEvent): void {
    const count = this.errorCounts.get(error.type) || 0;
    const threshold = this.config.severityThresholds[error.severity];

    if (count >= threshold) {
      this.emit('thresholdExceeded', {
        type: error.type,
        severity: error.severity,
        count,
        threshold,
      });
    }
  }

  private logToConsole(error: ErrorEvent): void {
    const style = this.getConsoleStyle(error.severity);
    console.group(`%c${error.type}`, style);
    console.error('Message:', error.message);
    if (error.stack) console.error('Stack:', error.stack);
    if (error.context) console.error('Context:', error.context);
    console.groupEnd();
  }

  private getConsoleStyle(severity: ErrorEvent['severity']): string {
    const styles = {
      low: 'color: #666;',
      medium: 'color: #f90;',
      high: 'color: #f00;',
      critical: 'color: #f00; font-weight: bold;',
    };
    return styles[severity];
  }

  private async reportError(error: ErrorEvent): Promise<void> {
    if (!this.config.reportEndpoint || this.isReporting) return;

    this.isReporting = true;
    try {
      const response = await fetch(this.config.reportEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(error),
      });

      if (!response.ok) {
        throw new Error('Failed to report error');
      }

      this.emit('errorReported', error);
    } catch (error) {
      this.emit('reportError', error);
    } finally {
      this.isReporting = false;
    }
  }

  private cleanupOldErrors(): void {
    const cutoffTime = Date.now() - this.config.errorRetentionPeriod;
    this.errors = this.errors.filter((error) => error.timestamp > cutoffTime);
    this.emit('errorsCleaned', { count: this.errors.length });
  }

  public getErrors(): ErrorEvent[] {
    return [...this.errors];
  }

  public getErrorCounts(): Map<string, number> {
    return new Map(this.errorCounts);
  }

  public getErrorById(id: string): ErrorEvent | undefined {
    return this.errors.find((error) => error.id === id);
  }

  public getErrorsByType(type: string): ErrorEvent[] {
    return this.errors.filter((error) => error.type === type);
  }

  public getErrorsBySeverity(severity: ErrorEvent['severity']): ErrorEvent[] {
    return this.errors.filter((error) => error.severity === severity);
  }

  public updateConfig(config: Partial<ErrorConfig>): void {
    this.config = { ...this.config, ...config };
    this.emit('configUpdated', this.config);
  }

  public getConfig(): ErrorConfig {
    return { ...this.config };
  }

  public cleanup(): void {
    this.errors = [];
    this.errorCounts.clear();
    this.removeAllListeners();
  }
} 