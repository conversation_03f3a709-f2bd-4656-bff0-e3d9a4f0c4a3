import { ErrorManager } from '../ErrorManager';

describe('ErrorManager', () => {
  let errorManager: ErrorManager;

  beforeEach(() => {
    // Mock fetch
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({}),
    });

    // Mock console methods
    console.error = jest.fn();
    console.group = jest.fn();
    console.groupEnd = jest.fn();

    errorManager = ErrorManager.getInstance();
    errorManager.cleanup();
  });

  describe('Initialization', () => {
    it('should create a singleton instance', () => {
      const instance1 = ErrorManager.getInstance();
      const instance2 = ErrorManager.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should initialize with default configuration', () => {
      const config = errorManager.getConfig();
      expect(config.maxErrors).toBe(1000);
      expect(config.errorRetentionPeriod).toBe(7 * 24 * 60 * 60 * 1000);
      expect(config.autoReport).toBe(true);
      expect(config.logToConsole).toBe(true);
    });

    it('should set up global error handlers', () => {
      const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
      errorManager.initialize();
      expect(addEventListenerSpy).toHaveBeenCalledWith('error', expect.any(Function));
      expect(addEventListenerSpy).toHaveBeenCalledWith('unhandledrejection', expect.any(Function));
    });
  });

  describe('Error Handling', () => {
    it('should handle errors with default severity', () => {
      const emitSpy = jest.spyOn(errorManager, 'emit');
      errorManager.handleError({
        type: 'test',
        message: 'Test error',
      });
      expect(emitSpy).toHaveBeenCalledWith('error', expect.objectContaining({
        type: 'test',
        message: 'Test error',
        severity: 'medium',
      }));
    });

    it('should handle errors with custom severity', () => {
      const emitSpy = jest.spyOn(errorManager, 'emit');
      errorManager.handleError({
        type: 'test',
        message: 'Test error',
        severity: 'high',
      });
      expect(emitSpy).toHaveBeenCalledWith('error', expect.objectContaining({
        severity: 'high',
      }));
    });

    it('should include stack trace when available', () => {
      const error = new Error('Test error');
      errorManager.handleError({
        type: 'test',
        message: error.message,
        stack: error.stack,
      });
      const errors = errorManager.getErrors();
      expect(errors[0].stack).toBe(error.stack);
    });

    it('should include user context', () => {
      errorManager.handleError({
        type: 'test',
        message: 'Test error',
      });
      const errors = errorManager.getErrors();
      expect(errors[0].user).toBeDefined();
      expect(errors[0].user?.userAgent).toBe(navigator.userAgent);
    });
  });

  describe('Error Reporting', () => {
    it('should report errors when auto-report is enabled', async () => {
      errorManager.updateConfig({
        autoReport: true,
        reportEndpoint: 'https://api.example.com/errors',
      });
      await errorManager.handleError({
        type: 'test',
        message: 'Test error',
      });
      expect(fetch).toHaveBeenCalledWith(
        'https://api.example.com/errors',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        })
      );
    });

    it('should not report errors when auto-report is disabled', async () => {
      errorManager.updateConfig({
        autoReport: false,
      });
      await errorManager.handleError({
        type: 'test',
        message: 'Test error',
      });
      expect(fetch).not.toHaveBeenCalled();
    });

    it('should handle reporting errors', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));
      const emitSpy = jest.spyOn(errorManager, 'emit');
      errorManager.updateConfig({
        autoReport: true,
        reportEndpoint: 'https://api.example.com/errors',
      });
      await errorManager.handleError({
        type: 'test',
        message: 'Test error',
      });
      expect(emitSpy).toHaveBeenCalledWith('reportError', expect.any(Error));
    });
  });

  describe('Error Management', () => {
    it('should maintain error counts', () => {
      errorManager.handleError({ type: 'test1', message: 'Error 1' });
      errorManager.handleError({ type: 'test1', message: 'Error 2' });
      errorManager.handleError({ type: 'test2', message: 'Error 3' });
      const counts = errorManager.getErrorCounts();
      expect(counts.get('test1')).toBe(2);
      expect(counts.get('test2')).toBe(1);
    });

    it('should limit the number of stored errors', () => {
      errorManager.updateConfig({ maxErrors: 2 });
      errorManager.handleError({ type: 'test1', message: 'Error 1' });
      errorManager.handleError({ type: 'test2', message: 'Error 2' });
      errorManager.handleError({ type: 'test3', message: 'Error 3' });
      expect(errorManager.getErrors()).toHaveLength(2);
    });

    it('should clean up old errors', () => {
      const oldError = {
        type: 'test',
        message: 'Old error',
        timestamp: Date.now() - (8 * 24 * 60 * 60 * 1000), // 8 days old
      };
      errorManager['errors'].push(oldError as any);
      errorManager['cleanupOldErrors']();
      expect(errorManager.getErrors()).not.toContainEqual(oldError);
    });
  });

  describe('Error Retrieval', () => {
    it('should get error by ID', () => {
      errorManager.handleError({
        type: 'test',
        message: 'Test error',
      });
      const errors = errorManager.getErrors();
      const error = errorManager.getErrorById(errors[0].id);
      expect(error).toBeDefined();
      expect(error?.id).toBe(errors[0].id);
    });

    it('should get errors by type', () => {
      errorManager.handleError({ type: 'test1', message: 'Error 1' });
      errorManager.handleError({ type: 'test1', message: 'Error 2' });
      errorManager.handleError({ type: 'test2', message: 'Error 3' });
      const errors = errorManager.getErrorsByType('test1');
      expect(errors).toHaveLength(2);
      expect(errors.every(e => e.type === 'test1')).toBe(true);
    });

    it('should get errors by severity', () => {
      errorManager.handleError({ type: 'test1', message: 'Error 1', severity: 'high' });
      errorManager.handleError({ type: 'test2', message: 'Error 2', severity: 'high' });
      errorManager.handleError({ type: 'test3', message: 'Error 3', severity: 'low' });
      const errors = errorManager.getErrorsBySeverity('high');
      expect(errors).toHaveLength(2);
      expect(errors.every(e => e.severity === 'high')).toBe(true);
    });
  });

  describe('Configuration', () => {
    it('should update configuration', () => {
      const newConfig = {
        maxErrors: 500,
        autoReport: false,
        logToConsole: false,
      };
      errorManager.updateConfig(newConfig);
      const config = errorManager.getConfig();
      expect(config.maxErrors).toBe(500);
      expect(config.autoReport).toBe(false);
      expect(config.logToConsole).toBe(false);
    });

    it('should emit config update event', () => {
      const emitSpy = jest.spyOn(errorManager, 'emit');
      errorManager.updateConfig({ maxErrors: 500 });
      expect(emitSpy).toHaveBeenCalledWith('configUpdated', expect.any(Object));
    });
  });

  describe('Cleanup', () => {
    it('should clean up resources', () => {
      errorManager.handleError({ type: 'test', message: 'Test error' });
      errorManager.cleanup();
      expect(errorManager.getErrors()).toHaveLength(0);
      expect(errorManager.getErrorCounts().size).toBe(0);
    });
  });
}); 