.app {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--background-color);
  color: var(--text-color);
}

.app * {
  box-sizing: border-box;
}

:root {
  --background-color: #ffffff;
  --text-color: #000000;
}

[data-theme='dark'] {
  --background-color: #121212;
  --text-color: #ffffff;
}

.header {
  padding: 1rem;
  background-color: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.main {
  flex: 1;
  padding: 1rem;
  background-color: var(--background-color);
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .header {
    background-color: var(--dark-background-color);
    border-bottom-color: var(--dark-border-color);
  }

  .main {
    background-color: var(--dark-background-color);
  }
} 