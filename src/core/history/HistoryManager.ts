import { app } from 'electron';
import { promises as fs } from 'fs';
import * as path from 'path';
import { createHash } from 'crypto';

interface HistoryEntry {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  visitCount: number;
  lastVisitTime: number;
  firstVisitTime: number;
  typedCount: number;
  transition: string;
  referrer?: string;
  visitDuration?: number;
}

interface HistorySettings {
  maxEntries: number;
  retentionPeriod: number;
  autoCleanup: boolean;
  cleanupInterval: number;
  privacyMode: boolean;
}

export class HistoryManager {
  private static instance: HistoryManager;
  private history: Map<string, HistoryEntry>;
  private settings: HistorySettings;
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.history = new Map();
    this.settings = {
      maxEntries: 10000,
      retentionPeriod: 90 * 24 * 60 * 60 * 1000, // 90 days
      autoCleanup: true,
      cleanupInterval: 24 * 60 * 60 * 1000, // 24 hours
      privacyMode: false
    };
  }

  public static getInstance(): HistoryManager {
    if (!HistoryManager.instance) {
      HistoryManager.instance = new HistoryManager();
    }
    return HistoryManager.instance;
  }

  public async initialize(): Promise<void> {
    await this.loadHistory();
    await this.loadSettings();
    if (this.settings.autoCleanup) {
      this.startCleanupInterval();
    }
  }

  private async loadHistory(): Promise<void> {
    try {
      const historyPath = path.join(app.getPath('userData'), 'history.json');
      const data = await fs.readFile(historyPath, 'utf-8');
      const entries = JSON.parse(data);
      
      for (const entry of entries) {
        this.history.set(entry.id, entry);
      }
    } catch (error) {
      // If history doesn't exist, start with empty history
      await this.saveHistory();
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'history-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      // If settings don't exist, use defaults
      await this.saveSettings();
    }
  }

  private async saveHistory(): Promise<void> {
    const historyPath = path.join(app.getPath('userData'), 'history.json');
    const entries = Array.from(this.history.values());
    await fs.writeFile(historyPath, JSON.stringify(entries, null, 2));
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'history-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, this.settings.cleanupInterval);
  }

  private async cleanup(): Promise<void> {
    const now = Date.now();
    const cutoffTime = now - this.settings.retentionPeriod;

    // Remove old entries
    for (const [id, entry] of this.history.entries()) {
      if (entry.lastVisitTime < cutoffTime) {
        this.history.delete(id);
      }
    }

    // If still over max entries, remove least visited entries
    if (this.history.size > this.settings.maxEntries) {
      const entries = Array.from(this.history.entries())
        .sort((a, b) => a[1].visitCount - b[1].visitCount);

      for (const [id, _] of entries) {
        if (this.history.size <= this.settings.maxEntries) break;
        this.history.delete(id);
      }
    }

    await this.saveHistory();
  }

  public async addEntry(url: string, options: Partial<HistoryEntry> = {}): Promise<HistoryEntry> {
    if (this.settings.privacyMode) return null;

    const id = this.generateId(url);
    const now = Date.now();
    const existingEntry = this.history.get(id);

    const entry: HistoryEntry = existingEntry ? {
      ...existingEntry,
      visitCount: existingEntry.visitCount + 1,
      lastVisitTime: now,
      ...options
    } : {
      id,
      url,
      title: options.title || url,
      favicon: options.favicon,
      visitCount: 1,
      lastVisitTime: now,
      firstVisitTime: now,
      typedCount: options.typedCount || 0,
      transition: options.transition || 'link',
      referrer: options.referrer,
      visitDuration: options.visitDuration
    };

    this.history.set(id, entry);
    await this.saveHistory();
    return entry;
  }

  private generateId(url: string): string {
    return createHash('sha256').update(url).digest('hex');
  }

  public async updateEntry(id: string, updates: Partial<HistoryEntry>): Promise<HistoryEntry | undefined> {
    const entry = this.history.get(id);
    if (!entry) return undefined;

    const updatedEntry = {
      ...entry,
      ...updates,
      lastVisitTime: Date.now()
    };

    this.history.set(id, updatedEntry);
    await this.saveHistory();
    return updatedEntry;
  }

  public async deleteEntry(id: string): Promise<void> {
    this.history.delete(id);
    await this.saveHistory();
  }

  public async clearHistory(): Promise<void> {
    this.history.clear();
    await this.saveHistory();
  }

  public async clearHistoryRange(startTime: number, endTime: number): Promise<void> {
    for (const [id, entry] of this.history.entries()) {
      if (entry.lastVisitTime >= startTime && entry.lastVisitTime <= endTime) {
        this.history.delete(id);
      }
    }
    await this.saveHistory();
  }

  public getEntry(id: string): HistoryEntry | undefined {
    return this.history.get(id);
  }

  public getAllEntries(): HistoryEntry[] {
    return Array.from(this.history.values());
  }

  public getEntriesByTimeRange(startTime: number, endTime: number): HistoryEntry[] {
    return Array.from(this.history.values())
      .filter(entry => entry.lastVisitTime >= startTime && entry.lastVisitTime <= endTime)
      .sort((a, b) => b.lastVisitTime - a.lastVisitTime);
  }

  public getMostVisitedSites(limit: number = 10): HistoryEntry[] {
    return Array.from(this.history.values())
      .sort((a, b) => b.visitCount - a.visitCount)
      .slice(0, limit);
  }

  public getRecentlyVisitedSites(limit: number = 10): HistoryEntry[] {
    return Array.from(this.history.values())
      .sort((a, b) => b.lastVisitTime - a.lastVisitTime)
      .slice(0, limit);
  }

  public searchHistory(query: string): HistoryEntry[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.history.values())
      .filter(entry => 
        entry.title.toLowerCase().includes(lowerQuery) ||
        entry.url.toLowerCase().includes(lowerQuery)
      )
      .sort((a, b) => b.lastVisitTime - a.lastVisitTime);
  }

  public getSettings(): HistorySettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<HistorySettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();

    if (this.settings.autoCleanup) {
      this.startCleanupInterval();
    } else if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }

  public cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }
} 