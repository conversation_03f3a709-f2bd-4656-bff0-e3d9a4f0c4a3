import { promises as fs } from 'fs';
import path from 'path';
import { EventEmitter } from 'events';
import { Bookmark, BookmarkConfig } from '../BookmarkManager';

export class BackupManager extends EventEmitter {
  private backupPath: string;
  private lastBackup: number;
  private config: BookmarkConfig;

  constructor(backupPath: string, config: BookmarkConfig) {
    super();
    this.backupPath = backupPath;
    this.lastBackup = 0;
    this.config = config;
  }

  public async backup(bookmarks: Map<string, Bookmark>): Promise<void> {
    try {
      const backupDir = path.join(this.backupPath, new Date().toISOString());
      await fs.mkdir(backupDir, { recursive: true });

      const bookmarksPath = path.join(backupDir, 'bookmarks.json');
      const configPath = path.join(backupDir, 'config.json');

      await fs.writeFile(bookmarksPath, JSON.stringify(Object.fromEntries(bookmarks), null, 2));
      await fs.writeFile(configPath, JSON.stringify(this.config, null, 2));

      this.lastBackup = Date.now();
      this.emit('backup-created', backupDir);
    } catch (error) {
      console.error('Failed to create backup:', error);
      this.emit('backup-error', error);
    }
  }

  public async restore(backupDir: string): Promise<{bookmarks: Map<string, Bookmark>, config: BookmarkConfig} | null> {
    try {
      const bookmarksPath = path.join(backupDir, 'bookmarks.json');
      const configPath = path.join(backupDir, 'config.json');

      const bookmarksData = await fs.readFile(bookmarksPath, 'utf-8');
      const configData = await fs.readFile(configPath, 'utf-8');

      const bookmarks = new Map<string, Bookmark>(Object.entries(JSON.parse(bookmarksData)));
      const config = { ...this.config, ...JSON.parse(configData) };

      this.emit('restore-completed', backupDir);
      return { bookmarks, config };
    } catch (error) {
      console.error('Failed to restore backup:', error);
      this.emit('restore-error', error);
      return null;
    }
  }
}
