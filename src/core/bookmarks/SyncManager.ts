import { promises as fs } from 'fs';
import path from 'path';
import { EventEmitter } from 'events';
import { Bookmark, BookmarkConfig } from '../BookmarkManager';

export class SyncManager extends EventEmitter {
  private syncPath: string;
  private lastSync: number;
  private config: BookmarkConfig;

  constructor(syncPath: string, config: BookmarkConfig) {
    super();
    this.syncPath = syncPath;
    this.lastSync = 0;
    this.config = config;
  }

  public async sync(bookmarks: Map<string, Bookmark>): Promise<void> {
    try {
      const syncDir = path.join(this.syncPath, new Date().toISOString());
      await fs.mkdir(syncDir, { recursive: true });

      const bookmarksPath = path.join(syncDir, 'bookmarks.json');
      const configPath = path.join(syncDir, 'config.json');

      await fs.writeFile(bookmarksPath, JSON.stringify(Object.fromEntries(bookmarks), null, 2));
      await fs.writeFile(configPath, JSON.stringify(this.config, null, 2));

      this.lastSync = Date.now();
      this.emit('sync-completed', syncDir);
    } catch (error) {
      console.error('Failed to sync:', error);
      this.emit('sync-error', error);
    }
  }
}
