import { app } from 'electron';
import { promises as fs } from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

interface Bookmark {
  id: string;
  title: string;
  url: string;
  favicon?: string;
  description?: string;
  tags: string[];
  createdAt: number;
  updatedAt: number;
  parentId?: string;
  children?: Bookmark[];
  isFolder: boolean;
  order: number;
}

interface BookmarkFolder extends Bookmark {
  isFolder: true;
  children: Bookmark[];
}

interface BookmarkSettings {
  autoBackup: boolean;
  backupInterval: number;
  maxBackups: number;
  syncEnabled: boolean;
  defaultFolder: string;
}

export class BookmarkManager {
  private static instance: BookmarkManager;
  private bookmarks: Map<string, Bookmark>;
  private settings: BookmarkSettings;
  private backupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.bookmarks = new Map();
    this.settings = {
      autoBackup: true,
      backupInterval: 24 * 60 * 60 * 1000, // 24 hours
      maxBackups: 10,
      syncEnabled: false,
      defaultFolder: 'root'
    };
  }

  public static getInstance(): BookmarkManager {
    if (!BookmarkManager.instance) {
      BookmarkManager.instance = new BookmarkManager();
    }
    return BookmarkManager.instance;
  }

  public async initialize(): Promise<void> {
    await this.loadBookmarks();
    await this.loadSettings();
    if (this.settings.autoBackup) {
      this.startBackupInterval();
    }
  }

  private async loadBookmarks(): Promise<void> {
    try {
      const bookmarksPath = path.join(app.getPath('userData'), 'bookmarks.json');
      const data = await fs.readFile(bookmarksPath, 'utf-8');
      const bookmarks = JSON.parse(data);
      
      for (const bookmark of bookmarks) {
        this.bookmarks.set(bookmark.id, bookmark);
      }
    } catch (error) {
      // If bookmarks don't exist, create root folder
      const rootFolder: BookmarkFolder = {
        id: 'root',
        title: 'Bookmarks',
        url: '',
        tags: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isFolder: true,
        children: [],
        order: 0
      };
      this.bookmarks.set('root', rootFolder);
      await this.saveBookmarks();
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'bookmark-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      // If settings don't exist, use defaults
      await this.saveSettings();
    }
  }

  private async saveBookmarks(): Promise<void> {
    const bookmarksPath = path.join(app.getPath('userData'), 'bookmarks.json');
    const bookmarks = Array.from(this.bookmarks.values());
    await fs.writeFile(bookmarksPath, JSON.stringify(bookmarks, null, 2));
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'bookmark-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private startBackupInterval(): void {
    this.backupInterval = setInterval(() => {
      this.createBackup();
    }, this.settings.backupInterval);
  }

  private async createBackup(): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(app.getPath('userData'), 'bookmarks-backup', `bookmarks-${timestamp}.json`);
    
    await fs.mkdir(path.dirname(backupPath), { recursive: true });
    await fs.writeFile(backupPath, JSON.stringify(Array.from(this.bookmarks.values()), null, 2));

    // Clean up old backups
    await this.cleanupOldBackups();
  }

  private async cleanupOldBackups(): Promise<void> {
    const backupDir = path.join(app.getPath('userData'), 'bookmarks-backup');
    const files = await fs.readdir(backupDir);
    
    if (files.length > this.settings.maxBackups) {
      const sortedFiles = files
        .map(file => ({ name: file, time: fs.stat(path.join(backupDir, file)).then(stat => stat.mtime.getTime()) }))
        .sort((a, b) => b.time - a.time);

      for (let i = this.settings.maxBackups; i < sortedFiles.length; i++) {
        await fs.unlink(path.join(backupDir, sortedFiles[i].name));
      }
    }
  }

  public async createBookmark(options: Partial<Bookmark>): Promise<Bookmark> {
    const bookmark: Bookmark = {
      id: uuidv4(),
      title: options.title || 'New Bookmark',
      url: options.url || '',
      favicon: options.favicon,
      description: options.description,
      tags: options.tags || [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      parentId: options.parentId || this.settings.defaultFolder,
      isFolder: options.isFolder || false,
      order: this.getNextOrder(options.parentId || this.settings.defaultFolder),
      ...(options.isFolder ? { children: [] } : {})
    };

    this.bookmarks.set(bookmark.id, bookmark);
    await this.saveBookmarks();
    return bookmark;
  }

  private getNextOrder(parentId: string): number {
    const parent = this.bookmarks.get(parentId) as BookmarkFolder;
    if (!parent || !parent.children) return 0;
    return parent.children.length;
  }

  public async updateBookmark(id: string, updates: Partial<Bookmark>): Promise<Bookmark | undefined> {
    const bookmark = this.bookmarks.get(id);
    if (!bookmark) return undefined;

    const updatedBookmark = {
      ...bookmark,
      ...updates,
      updatedAt: Date.now()
    };

    this.bookmarks.set(id, updatedBookmark);
    await this.saveBookmarks();
    return updatedBookmark;
  }

  public async deleteBookmark(id: string): Promise<void> {
    const bookmark = this.bookmarks.get(id);
    if (!bookmark) return;

    // If it's a folder, delete all children
    if (bookmark.isFolder) {
      const folder = bookmark as BookmarkFolder;
      for (const child of folder.children || []) {
        await this.deleteBookmark(child.id);
      }
    }

    this.bookmarks.delete(id);
    await this.saveBookmarks();
  }

  public async moveBookmark(id: string, newParentId: string, newOrder?: number): Promise<void> {
    const bookmark = this.bookmarks.get(id);
    const newParent = this.bookmarks.get(newParentId) as BookmarkFolder;
    
    if (!bookmark || !newParent || !newParent.isFolder) return;

    bookmark.parentId = newParentId;
    bookmark.order = newOrder ?? this.getNextOrder(newParentId);
    bookmark.updatedAt = Date.now();

    await this.saveBookmarks();
  }

  public async addTag(id: string, tag: string): Promise<void> {
    const bookmark = this.bookmarks.get(id);
    if (!bookmark) return;

    if (!bookmark.tags.includes(tag)) {
      bookmark.tags.push(tag);
      bookmark.updatedAt = Date.now();
      await this.saveBookmarks();
    }
  }

  public async removeTag(id: string, tag: string): Promise<void> {
    const bookmark = this.bookmarks.get(id);
    if (!bookmark) return;

    bookmark.tags = bookmark.tags.filter(t => t !== tag);
    bookmark.updatedAt = Date.now();
    await this.saveBookmarks();
  }

  public getBookmark(id: string): Bookmark | undefined {
    return this.bookmarks.get(id);
  }

  public getAllBookmarks(): Bookmark[] {
    return Array.from(this.bookmarks.values());
  }

  public getBookmarksByTag(tag: string): Bookmark[] {
    return Array.from(this.bookmarks.values())
      .filter(bookmark => bookmark.tags.includes(tag));
  }

  public getBookmarksByFolder(folderId: string): Bookmark[] {
    return Array.from(this.bookmarks.values())
      .filter(bookmark => bookmark.parentId === folderId)
      .sort((a, b) => a.order - b.order);
  }

  public getSettings(): BookmarkSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<BookmarkSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();

    if (this.settings.autoBackup) {
      this.startBackupInterval();
    } else if (this.backupInterval) {
      clearInterval(this.backupInterval);
    }
  }

  public cleanup(): void {
    if (this.backupInterval) {
      clearInterval(this.backupInterval);
    }
  }
} 