import { app } from 'electron';
import { promises as fs } from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import { Worker } from 'worker_threads';
import { spawn } from 'child_process';
import { exec } from 'child_process';
import { promisify } from 'util';
import { createServer } from 'net';
import { createServer as createHttpServer } from 'http';
import { createServer as createHttpsServer } from 'https';
import { createServer as createWebSocketServer } from 'ws';
import { createServer as createGrpcServer } from '@grpc/grpc-js';

interface PerformanceSettings {
  enabled: boolean;
  profiling: {
    enabled: boolean;
    interval: number;
    retention: number;
  };
  optimization: {
    enabled: boolean;
    autoOptimize: boolean;
    optimizationLevel: 'low' | 'medium' | 'high';
  };
  monitoring: {
    enabled: boolean;
    metrics: boolean;
    alerts: boolean;
    thresholds: {
      cpu: number;
      memory: number;
      disk: number;
      network: number;
    };
  };
  storage: {
    enabled: boolean;
    path: string;
  };
}

interface PerformanceMetrics {
  cpu: {
    usage: number;
    temperature: number;
    frequency: number;
    cores: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    swap: {
      total: number;
      used: number;
      free: number;
    };
  };
  disk: {
    total: number;
    used: number;
    free: number;
    iops: number;
    latency: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    connections: number;
    latency: number;
  };
  process: {
    uptime: number;
    threads: number;
    handles: number;
    heap: {
      total: number;
      used: number;
      free: number;
    };
  };
}

interface PerformanceProfile {
  id: string;
  name: string;
  startTime: number;
  endTime: number;
  duration: number;
  metrics: PerformanceMetrics;
  metadata: {
    type: 'cpu' | 'memory' | 'disk' | 'network' | 'process' | 'all';
    operation?: string;
    status?: string;
    error?: string;
  };
}

interface PerformanceAlert {
  id: string;
  type: 'cpu' | 'memory' | 'disk' | 'network' | 'process';
  level: 'warning' | 'error' | 'critical';
  message: string;
  timestamp: number;
  metrics: PerformanceMetrics;
  metadata: {
    threshold: number;
    current: number;
    operation?: string;
  };
}

export class PerformanceManager extends EventEmitter {
  private static instance: PerformanceManager;
  private settings: PerformanceSettings;
  private metrics: PerformanceMetrics[];
  private profiles: PerformanceProfile[];
  private alerts: PerformanceAlert[];
  private isInitialized: boolean = false;
  private metricsInterval: NodeJS.Timeout | null = null;
  private optimizationInterval: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    this.settings = {
      enabled: true,
      profiling: {
        enabled: true,
        interval: 60000,
        retention: 1000
      },
      optimization: {
        enabled: true,
        autoOptimize: true,
        optimizationLevel: 'medium'
      },
      monitoring: {
        enabled: true,
        metrics: true,
        alerts: true,
        thresholds: {
          cpu: 80,
          memory: 80,
          disk: 80,
          network: 80
        }
      },
      storage: {
        enabled: true,
        path: 'performance'
      }
    };
    this.metrics = [];
    this.profiles = [];
    this.alerts = [];
  }

  public static getInstance(): PerformanceManager {
    if (!PerformanceManager.instance) {
      PerformanceManager.instance = new PerformanceManager();
    }
    return PerformanceManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadSettings();
      await this.setupStorage();
      await this.setupProfiling();
      await this.setupOptimization();
      await this.setupMonitoring();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize PerformanceManager:', error);
      throw error;
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'performance-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'performance-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private async setupStorage(): Promise<void> {
    if (!this.settings.storage.enabled) return;

    const storagePath = path.join(app.getPath('userData'), this.settings.storage.path);
    await fs.mkdir(storagePath, { recursive: true });
  }

  private async setupProfiling(): Promise<void> {
    if (!this.settings.profiling.enabled) return;

    try {
      const profilesPath = path.join(app.getPath('userData'), this.settings.storage.path, 'profiles.json');
      const data = await fs.readFile(profilesPath, 'utf-8');
      this.profiles = JSON.parse(data);
    } catch (error) {
      await this.saveProfiles();
    }

    this.metricsInterval = setInterval(async () => {
      try {
        const metrics = await this.collectMetrics();
        this.metrics.push(metrics);
        if (this.metrics.length > this.settings.profiling.retention) {
          this.metrics = this.metrics.slice(-this.settings.profiling.retention);
        }
        await this.saveMetrics();
        this.emit('metrics-collected', metrics);
      } catch (error) {
        console.error('Failed to collect metrics:', error);
      }
    }, this.settings.profiling.interval);

    process.on('exit', () => {
      if (this.metricsInterval) {
        clearInterval(this.metricsInterval);
      }
    });
  }

  private async saveMetrics(): Promise<void> {
    if (!this.settings.storage.enabled) return;

    const metricsPath = path.join(app.getPath('userData'), this.settings.storage.path, 'metrics.json');
    await fs.writeFile(metricsPath, JSON.stringify(this.metrics, null, 2));
  }

  private async collectMetrics(): Promise<PerformanceMetrics> {
    const os = require('os');
    const process = require('process');

    return {
      cpu: {
        usage: os.loadavg()[0],
        temperature: await this.getCpuTemperature(),
        frequency: await this.getCpuFrequency(),
        cores: os.cpus().length
      },
      memory: {
        total: os.totalmem(),
        used: os.totalmem() - os.freemem(),
        free: os.freemem(),
        swap: await this.getSwapMetrics()
      },
      disk: await this.getDiskMetrics(),
      network: await this.getNetworkMetrics(),
      process: {
        uptime: process.uptime(),
        threads: process.getActiveResourcesInfo().length,
        handles: process.getActiveHandles().length,
        heap: process.memoryUsage()
      }
    };
  }

  private async getCpuTemperature(): Promise<number> {
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync('sensors');
      const match = stdout.match(/Core 0:\s+\+(\d+\.\d+)°C/);
      return match ? parseFloat(match[1]) : 0;
    } catch (error) {
      return 0;
    }
  }

  private async getCpuFrequency(): Promise<number> {
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync('cat /proc/cpuinfo | grep "cpu MHz"');
      const match = stdout.match(/cpu MHz\s+:\s+(\d+\.\d+)/);
      return match ? parseFloat(match[1]) : 0;
    } catch (error) {
      return 0;
    }
  }

  private async getSwapMetrics(): Promise<{ total: number; used: number; free: number }> {
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync('free -b');
      const lines = stdout.split('\n');
      const swapLine = lines.find((line: string) => line.startsWith('Swap:'));
      if (swapLine) {
        const values = swapLine.split(/\s+/);
        return {
          total: parseInt(values[1]),
          used: parseInt(values[2]),
          free: parseInt(values[3])
        };
      }
      return { total: 0, used: 0, free: 0 };
    } catch (error) {
      return { total: 0, used: 0, free: 0 };
    }
  }

  private async getDiskMetrics(): Promise<{ total: number; used: number; free: number; iops: number; latency: number }> {
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync('df -k /');
      const lines = stdout.split('\n');
      const values = lines[1].split(/\s+/);

      const { stdout: iostat } = await execAsync('iostat -d -k 1 1');
      const iostatLines = iostat.split('\n');
      const iostatValues = iostatLines[3].split(/\s+/);

      return {
        total: parseInt(values[1]) * 1024,
        used: parseInt(values[2]) * 1024,
        free: parseInt(values[3]) * 1024,
        iops: parseFloat(iostatValues[3]),
        latency: parseFloat(iostatValues[4])
      };
    } catch (error) {
      return { total: 0, used: 0, free: 0, iops: 0, latency: 0 };
    }
  }

  private async getNetworkMetrics(): Promise<{ bytesIn: number; bytesOut: number; connections: number; latency: number }> {
    try {
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync('netstat -i');
      const lines = stdout.split('\n');
      const values = lines[2].split(/\s+/);

      const { stdout: ping } = await execAsync('ping -c 1 8.8.8.8');
      const match = ping.match(/time=(\d+\.\d+) ms/);
      const latency = match ? parseFloat(match[1]) : 0;

      return {
        bytesIn: parseInt(values[3]),
        bytesOut: parseInt(values[7]),
        connections: parseInt(values[8]),
        latency
      };
    } catch (error) {
      return { bytesIn: 0, bytesOut: 0, connections: 0, latency: 0 };
    }
  }

  private async setupOptimization(): Promise<void> {
    if (!this.settings.optimization.enabled) return;

    this.optimizationInterval = setInterval(async () => {
      try {
        if (this.settings.optimization.autoOptimize) {
          await this.optimize();
        }
      } catch (error) {
        console.error('Failed to optimize:', error);
      }
    }, 300000); // Run optimization every 5 minutes

    process.on('exit', () => {
      if (this.optimizationInterval) {
        clearInterval(this.optimizationInterval);
      }
    });
  }

  private async optimize(): Promise<void> {
    const metrics = await this.collectMetrics();

    // Optimize CPU
    if (metrics.cpu.usage > this.settings.monitoring.thresholds.cpu) {
      await this.optimizeCpu();
    }

    // Optimize Memory
    if (metrics.memory.used / metrics.memory.total * 100 > this.settings.monitoring.thresholds.memory) {
      await this.optimizeMemory();
    }

    // Optimize Disk
    if (metrics.disk.used / metrics.disk.total * 100 > this.settings.monitoring.thresholds.disk) {
      await this.optimizeDisk();
    }

    // Optimize Network
    if (metrics.network.latency > this.settings.monitoring.thresholds.network) {
      await this.optimizeNetwork();
    }
  }

  private async optimizeCpu(): Promise<void> {
    // Implement CPU optimization strategies
    // For example, adjust process priority, limit background tasks, etc.
  }

  private async optimizeMemory(): Promise<void> {
    // Implement memory optimization strategies
    // For example, clear caches, run garbage collection, etc.
  }

  private async optimizeDisk(): Promise<void> {
    // Implement disk optimization strategies
    // For example, defragmentation, cleanup temporary files, etc.
  }

  private async optimizeNetwork(): Promise<void> {
    // Implement network optimization strategies
    // For example, adjust connection pool size, optimize DNS resolution, etc.
  }

  private async setupMonitoring(): Promise<void> {
    if (!this.settings.monitoring.enabled) return;

    try {
      const alertsPath = path.join(app.getPath('userData'), this.settings.storage.path, 'alerts.json');
      const data = await fs.readFile(alertsPath, 'utf-8');
      this.alerts = JSON.parse(data);
    } catch (error) {
      await this.saveAlerts();
    }

    this.on('metrics-collected', async (metrics: PerformanceMetrics) => {
      await this.checkThresholds(metrics);
    });
  }

  private async checkThresholds(metrics: PerformanceMetrics): Promise<void> {
    if (!this.settings.monitoring.alerts) return;

    // Check CPU threshold
    if (metrics.cpu.usage > this.settings.monitoring.thresholds.cpu) {
      await this.createAlert('cpu', 'warning', `High CPU usage: ${metrics.cpu.usage}%`, metrics);
    }

    // Check Memory threshold
    const memoryUsage = (metrics.memory.used / metrics.memory.total) * 100;
    if (memoryUsage > this.settings.monitoring.thresholds.memory) {
      await this.createAlert('memory', 'warning', `High memory usage: ${memoryUsage.toFixed(2)}%`, metrics);
    }

    // Check Disk threshold
    const diskUsage = (metrics.disk.used / metrics.disk.total) * 100;
    if (diskUsage > this.settings.monitoring.thresholds.disk) {
      await this.createAlert('disk', 'warning', `High disk usage: ${diskUsage.toFixed(2)}%`, metrics);
    }

    // Check Network threshold
    if (metrics.network.latency > this.settings.monitoring.thresholds.network) {
      await this.createAlert('network', 'warning', `High network latency: ${metrics.network.latency}ms`, metrics);
    }
  }

  private async createAlert(
    type: 'cpu' | 'memory' | 'disk' | 'network' | 'process',
    level: 'warning' | 'error' | 'critical',
    message: string,
    metrics: PerformanceMetrics
  ): Promise<void> {
    const alert: PerformanceAlert = {
      id: Math.random().toString(36).substr(2, 9),
      type,
      level,
      message,
      timestamp: Date.now(),
      metrics,
      metadata: {
        threshold: type === 'process' ? 80 : this.settings.monitoring.thresholds[type],
        current: this.getCurrentValue(type, metrics)
      }
    };

    this.alerts.push(alert);
    await this.saveAlerts();
    this.emit('alert', alert);
  }

  private getCurrentValue(type: string, metrics: PerformanceMetrics): number {
    switch (type) {
      case 'cpu':
        return metrics.cpu.usage;
      case 'memory':
        return (metrics.memory.used / metrics.memory.total) * 100;
      case 'disk':
        return (metrics.disk.used / metrics.disk.total) * 100;
      case 'network':
        return metrics.network.latency;
      case 'process':
        return metrics.process.heap.used / metrics.process.heap.total * 100;
      default:
        return 0;
    }
  }

  private async saveAlerts(): Promise<void> {
    if (!this.settings.storage.enabled) return;

    const alertsPath = path.join(app.getPath('userData'), this.settings.storage.path, 'alerts.json');
    await fs.writeFile(alertsPath, JSON.stringify(this.alerts, null, 2));
  }

  public async startProfiling(name: string, type: 'cpu' | 'memory' | 'disk' | 'network' | 'process' | 'all'): Promise<string> {
    const profile: PerformanceProfile = {
      id: Math.random().toString(36).substr(2, 9),
      name,
      startTime: Date.now(),
      endTime: 0,
      duration: 0,
      metrics: await this.collectMetrics(),
      metadata: {
        type,
        status: 'running'
      }
    };

    this.profiles.push(profile);
    await this.saveProfiles();
    this.emit('profile-started', profile);

    return profile.id;
  }

  public async stopProfiling(id: string): Promise<PerformanceProfile> {
    const profile = this.profiles.find(p => p.id === id);
    if (!profile) {
      throw new Error(`Profile not found: ${id}`);
    }

    profile.endTime = Date.now();
    profile.duration = profile.endTime - profile.startTime;
    profile.metrics = await this.collectMetrics();
    profile.metadata.status = 'completed';

    await this.saveProfiles();
    this.emit('profile-completed', profile);

    return profile;
  }

  private async saveProfiles(): Promise<void> {
    if (!this.settings.storage.enabled) return;

    const profilesPath = path.join(app.getPath('userData'), this.settings.storage.path, 'profiles.json');
    await fs.writeFile(profilesPath, JSON.stringify(this.profiles, null, 2));
  }

  public getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  public getProfiles(): PerformanceProfile[] {
    return [...this.profiles];
  }

  public getAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }

  public getSettings(): PerformanceSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<PerformanceSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();
    this.emit('settings-updated', this.settings);
  }
} 