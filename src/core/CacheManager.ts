import { EventEmitter } from 'events';
import { logger } from './EnhancedLogger';
import { configManager } from './ConfigurationManager';

export interface CacheEntry<T = any> {
  key: string;
  value: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  size: number;
  tags: string[];
  metadata?: Record<string, any>;
}

export interface CacheConfig {
  maxSize: number; // Maximum cache size in bytes
  maxEntries: number; // Maximum number of entries
  defaultTTL: number; // Default time to live in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
  enableCompression: boolean;
  enableEncryption: boolean;
  enablePersistence: boolean;
  persistenceKey: string;
  evictionPolicy: 'LRU' | 'LFU' | 'FIFO' | 'TTL';
  enableMetrics: boolean;
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  totalSize: number;
  entryCount: number;
  hitRate: number;
  averageAccessTime: number;
}

export interface CacheStats {
  metrics: CacheMetrics;
  topKeys: Array<{ key: string; accessCount: number; size: number }>;
  memoryUsage: {
    used: number;
    available: number;
    percentage: number;
  };
  performance: {
    averageGetTime: number;
    averageSetTime: number;
    averageDeleteTime: number;
  };
}

export class CacheManager extends EventEmitter {
  private static instance: CacheManager;
  private cache: Map<string, CacheEntry> = new Map();
  private config: CacheConfig;
  private metrics: CacheMetrics;
  private cleanupTimer: NodeJS.Timeout | null = null;
  private accessTimes: number[] = [];

  private constructor() {
    super();
    this.config = {
      maxSize: 100 * 1024 * 1024, // 100MB
      maxEntries: 10000,
      defaultTTL: 60 * 60 * 1000, // 1 hour
      cleanupInterval: 5 * 60 * 1000, // 5 minutes
      enableCompression: true,
      enableEncryption: false,
      enablePersistence: true,
      persistenceKey: 'browser_cache',
      evictionPolicy: 'LRU',
      enableMetrics: true,
    };

    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      totalSize: 0,
      entryCount: 0,
      hitRate: 0,
      averageAccessTime: 0,
    };

    this.initialize();
  }

  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  private async initialize(): Promise<void> {
    // Load configuration
    const cacheConfig = configManager.get('cache', {});
    this.config = { ...this.config, ...cacheConfig };

    // Load persisted cache
    if (this.config.enablePersistence) {
      await this.loadPersistedCache();
    }

    // Start cleanup timer
    this.startCleanupTimer();

    logger.info('Cache manager initialized', {
      maxSize: this.config.maxSize,
      maxEntries: this.config.maxEntries,
      evictionPolicy: this.config.evictionPolicy,
    });
  }

  public async get<T = any>(key: string): Promise<T | null> {
    const startTime = performance.now();

    try {
      const entry = this.cache.get(key);

      if (!entry) {
        this.metrics.misses++;
        this.updateMetrics();
        return null;
      }

      // Check if entry has expired
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        this.metrics.misses++;
        this.metrics.evictions++;
        this.updateMetrics();
        return null;
      }

      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = Date.now();

      this.metrics.hits++;
      this.updateMetrics();

      const value = this.config.enableCompression ? this.decompress(entry.value) : entry.value;
      const decryptedValue = this.config.enableEncryption ? this.decrypt(value) : value;

      this.emit('cache_hit', { key, value: decryptedValue });
      return decryptedValue;
    } finally {
      const accessTime = performance.now() - startTime;
      this.accessTimes.push(accessTime);
      if (this.accessTimes.length > 1000) {
        this.accessTimes = this.accessTimes.slice(-1000);
      }
    }
  }

  public async set<T = any>(
    key: string,
    value: T,
    options: {
      ttl?: number;
      tags?: string[];
      metadata?: Record<string, any>;
    } = {}
  ): Promise<void> {
    const startTime = performance.now();

    try {
      const ttl = options.ttl || this.config.defaultTTL;
      const tags = options.tags || [];
      const metadata = options.metadata || {};

      // Encrypt and compress value if enabled
      let processedValue = value;
      if (this.config.enableEncryption) {
        processedValue = this.encrypt(processedValue);
      }
      if (this.config.enableCompression) {
        processedValue = this.compress(processedValue);
      }

      const size = this.calculateSize(processedValue);
      const entry: CacheEntry<T> = {
        key,
        value: processedValue,
        timestamp: Date.now(),
        ttl,
        accessCount: 0,
        lastAccessed: Date.now(),
        size,
        tags,
        metadata,
      };

      // Check if we need to evict entries
      await this.ensureCapacity(size);

      this.cache.set(key, entry);
      this.metrics.sets++;
      this.metrics.totalSize += size;
      this.metrics.entryCount++;
      this.updateMetrics();

      // Persist cache if enabled
      if (this.config.enablePersistence) {
        await this.persistCache();
      }

      this.emit('cache_set', { key, value, size, ttl });
      logger.debug('Cache entry set', { key, size, ttl, tags });
    } finally {
      const setTime = performance.now() - startTime;
      this.accessTimes.push(setTime);
    }
  }

  public async delete(key: string): Promise<boolean> {
    const startTime = performance.now();

    try {
      const entry = this.cache.get(key);
      if (!entry) {
        return false;
      }

      this.cache.delete(key);
      this.metrics.deletes++;
      this.metrics.totalSize -= entry.size;
      this.metrics.entryCount--;
      this.updateMetrics();

      this.emit('cache_delete', { key });
      logger.debug('Cache entry deleted', { key });
      return true;
    } finally {
      const deleteTime = performance.now() - startTime;
      this.accessTimes.push(deleteTime);
    }
  }

  public async clear(): Promise<void> {
    const entryCount = this.cache.size;
    this.cache.clear();
    this.metrics.totalSize = 0;
    this.metrics.entryCount = 0;
    this.updateMetrics();

    if (this.config.enablePersistence) {
      localStorage.removeItem(this.config.persistenceKey);
    }

    this.emit('cache_clear', { entryCount });
    logger.info('Cache cleared', { entryCount });
  }

  public async invalidateByTag(tag: string): Promise<number> {
    let invalidatedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.includes(tag)) {
        await this.delete(key);
        invalidatedCount++;
      }
    }

    this.emit('cache_invalidate_tag', { tag, count: invalidatedCount });
    logger.info('Cache entries invalidated by tag', { tag, count: invalidatedCount });
    return invalidatedCount;
  }

  public has(key: string): boolean {
    const entry = this.cache.get(key);
    return entry !== undefined && !this.isExpired(entry);
  }

  public keys(): string[] {
    return Array.from(this.cache.keys()).filter(key => {
      const entry = this.cache.get(key);
      return entry && !this.isExpired(entry);
    });
  }

  public getStats(): CacheStats {
    const topKeys = Array.from(this.cache.entries())
      .map(([key, entry]) => ({
        key,
        accessCount: entry.accessCount,
        size: entry.size,
      }))
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, 10);

    const averageAccessTime = this.accessTimes.length > 0
      ? this.accessTimes.reduce((sum, time) => sum + time, 0) / this.accessTimes.length
      : 0;

    return {
      metrics: { ...this.metrics },
      topKeys,
      memoryUsage: {
        used: this.metrics.totalSize,
        available: this.config.maxSize - this.metrics.totalSize,
        percentage: (this.metrics.totalSize / this.config.maxSize) * 100,
      },
      performance: {
        averageGetTime: averageAccessTime,
        averageSetTime: averageAccessTime,
        averageDeleteTime: averageAccessTime,
      },
    };
  }

  private async ensureCapacity(newEntrySize: number): Promise<void> {
    // Check size limit
    while (this.metrics.totalSize + newEntrySize > this.config.maxSize) {
      await this.evictEntry();
    }

    // Check entry count limit
    while (this.cache.size >= this.config.maxEntries) {
      await this.evictEntry();
    }
  }

  private async evictEntry(): Promise<void> {
    let keyToEvict: string | null = null;

    switch (this.config.evictionPolicy) {
      case 'LRU':
        keyToEvict = this.findLRUKey();
        break;
      case 'LFU':
        keyToEvict = this.findLFUKey();
        break;
      case 'FIFO':
        keyToEvict = this.findFIFOKey();
        break;
      case 'TTL':
        keyToEvict = this.findExpiredKey();
        break;
    }

    if (keyToEvict) {
      await this.delete(keyToEvict);
      this.metrics.evictions++;
    }
  }

  private findLRUKey(): string | null {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  private findLFUKey(): string | null {
    let leastUsedKey: string | null = null;
    let leastCount = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.accessCount < leastCount) {
        leastCount = entry.accessCount;
        leastUsedKey = key;
      }
    }

    return leastUsedKey;
  }

  private findFIFOKey(): string | null {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  private findExpiredKey(): string | null {
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        return key;
      }
    }
    return null;
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private calculateSize(value: any): number {
    return JSON.stringify(value).length * 2; // Rough estimate (UTF-16)
  }

  private compress(value: any): any {
    // Simple compression simulation
    return this.config.enableCompression ? value : value;
  }

  private decompress(value: any): any {
    // Simple decompression simulation
    return this.config.enableCompression ? value : value;
  }

  private encrypt(value: any): any {
    // Simple encryption simulation
    return this.config.enableEncryption ? value : value;
  }

  private decrypt(value: any): any {
    // Simple decryption simulation
    return this.config.enableEncryption ? value : value;
  }

  private updateMetrics(): void {
    const total = this.metrics.hits + this.metrics.misses;
    this.metrics.hitRate = total > 0 ? (this.metrics.hits / total) * 100 : 0;
    this.metrics.averageAccessTime = this.accessTimes.length > 0
      ? this.accessTimes.reduce((sum, time) => sum + time, 0) / this.accessTimes.length
      : 0;
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  private async cleanup(): Promise<void> {
    let cleanedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        await this.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.debug('Cache cleanup completed', { cleanedCount });
      this.emit('cache_cleanup', { cleanedCount });
    }
  }

  private async loadPersistedCache(): Promise<void> {
    try {
      const stored = localStorage.getItem(this.config.persistenceKey);
      if (stored) {
        const data = JSON.parse(stored);
        for (const [key, entry] of Object.entries(data)) {
          if (!this.isExpired(entry as CacheEntry)) {
            this.cache.set(key, entry as CacheEntry);
          }
        }
        logger.info('Persisted cache loaded', { entryCount: this.cache.size });
      }
    } catch (error) {
      logger.warn('Failed to load persisted cache', { error });
    }
  }

  private async persistCache(): Promise<void> {
    try {
      const data = Object.fromEntries(this.cache.entries());
      localStorage.setItem(this.config.persistenceKey, JSON.stringify(data));
    } catch (error) {
      logger.warn('Failed to persist cache', { error });
    }
  }

  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.cache.clear();
    this.removeAllListeners();
  }
}

// Export singleton instance
export const cacheManager = CacheManager.getInstance();
