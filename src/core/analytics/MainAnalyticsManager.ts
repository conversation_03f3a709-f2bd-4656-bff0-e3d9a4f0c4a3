import { app } from 'electron';
import { promises as fs } from 'fs';
import * as https from 'https';
import * as path from 'path';
import { URL } from 'url';
import { AnalyticsCore, AnalyticsConfig, AnalyticsEvent } from './AnalyticsCore';

/**
 * Analytics manager for the main process.
 * Extends AnalyticsCore with Node.js/Electron-specific functionality.
 */
export class MainAnalyticsManager extends AnalyticsCore {
  private static instance: MainAnalyticsManager;
  private deviceId: string;
  private isInitialized = false;

  private constructor(config: Partial<AnalyticsConfig> = {}) {
    super(config);
    this.deviceId = ''; // Will be loaded or created in initialize
  }

  /**
   * Gets the singleton instance of the MainAnalyticsManager.
   */
  public static getInstance(config?: Partial<AnalyticsConfig>): MainAnalyticsManager {
    if (!MainAnalyticsManager.instance) {
      MainAnalyticsManager.instance = new MainAnalyticsManager(config);
    }
    return MainAnalyticsManager.instance;
  }

  /**
   * Initializes the manager: loads config, sets up device ID, and starts listeners.
   */
  public async initialize(): Promise<void> {
    await this.loadConfigAndDeviceId();
    super.initialize(); // This will start the flush timer
    if (this.config.enabled) {
      this.setupAppEventListeners();
      this.isInitialized = true;
      this.log('MainAnalyticsManager initialized.');
    }
  }

  /**
   * Implements the event sending logic using Node.js's https module.
   */
  protected sendEvents(events: AnalyticsEvent[]): Promise<void> {
    return new Promise((resolve, reject) => {
      const endpointUrl = new URL(this.config.endpoint);
      const payload = JSON.stringify({
        events,
        sentAt: new Date().toISOString(),
      });

      const options = {
        hostname: endpointUrl.hostname,
        port: endpointUrl.port || 443,
        path: endpointUrl.pathname,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(payload),
        },
      };

      const req = https.request(options, (res) => {
        if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
          this.log(`${events.length} events sent successfully.`);
          resolve();
        } else {
          this.log(`HTTP Error: ${res.statusCode} ${res.statusMessage}`);
          reject(new Error(`HTTP Error: ${res.statusCode}`));
        }
      });

      req.on('error', (error) => {
        this.log('Request error while sending events.', error);
        reject(error);
      });

      req.write(payload);
      req.end();
    });
  }

  /**
   * Gathers common properties from the Electron main process environment.
   */
  protected getCommonProperties(): Record<string, any> {
    return {
      platform: process.platform,
      appVersion: app.getVersion(),
      locale: app.getLocale(),
      electronVersion: process.versions.electron,
      chromeVersion: process.versions.chrome,
      nodeVersion: process.versions.node,
      deviceId: this.deviceId,
    };
  }

  /**
   * Loads the analytics configuration and device ID from the user data directory.
   * If they don't exist, creates them.
   */
  private async loadConfigAndDeviceId(): Promise<void> {
    const userDataPath = app.getPath('userData');
    const configPath = path.join(userDataPath, 'analytics.config.json');
    const deviceIdPath = path.join(userDataPath, 'device.id');

    try {
      // Load config
      const configData = await fs.readFile(configPath, 'utf-8');
      const savedConfig = JSON.parse(configData);
      this.config = { ...this.config, ...savedConfig };
      this.log('Loaded config from disk.', this.config);
    } catch (error) {
      this.log('No saved config found. Using defaults and saving.');
      await this.saveConfig(); // Save default config if none exists
    }

    try {
      // Load device ID
      this.deviceId = await fs.readFile(deviceIdPath, 'utf-8');
      this.log(`Loaded device ID: ${this.deviceId}`);
    } catch (error) {
      this.deviceId = this.generateDeviceId();
      this.log(`Generated new device ID: ${this.deviceId}`);
      await fs.writeFile(deviceIdPath, this.deviceId, 'utf-8');
    }
  }

  /**
   * Saves the current configuration to disk.
   */
  private async saveConfig(): Promise<void> {
    const configPath = path.join(app.getPath('userData'), 'analytics.config.json');
    try {
      await fs.writeFile(configPath, JSON.stringify(this.config, null, 2), 'utf-8');
      this.log('Saved config to disk.');
    } catch (error) {
      this.log('Error saving config to disk.', error);
    }
  }

  /**
   * Sets up listeners for Electron app lifecycle events.
   */
  private setupAppEventListeners(): void {
    app.on('before-quit', async () => {
      this.track('app_quit', {});
      await this.flush();
    });

    // More advanced tracking can be added here, e.g., for crashes
    process.on('uncaughtException', (error) => {
        this.track('error', {
            type: 'uncaughtException',
            message: error.message,
            stack: error.stack,
        });
        this.flush();
        // It's important to re-throw or exit after this, but for analytics, we just track.
    });
  }

  private generateDeviceId(): string {
    // A more robust unique ID generation could be used here
    return `device-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;
  }

  public getDeviceId(): string {
    return this.deviceId;
  }
}