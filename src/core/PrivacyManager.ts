import { app, session, BrowserWindow } from 'electron';
import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';
import { createHash } from 'crypto';

export interface PrivacyConfig {
  enableDoNotTrack: boolean;
  enableTrackingProtection: boolean;
  enableFingerprintingProtection: boolean;
  enableWebRTCIPHandling: boolean;
  enableCookieProtection: boolean;
  enableLocalStorageProtection: boolean;
  enableSessionStorageProtection: boolean;
  enableIndexedDBProtection: boolean;
  enableWebSQLProtection: boolean;
  enableCacheProtection: boolean;
  enableHistoryProtection: boolean;
  enableDownloadHistoryProtection: boolean;
  enableFormDataProtection: boolean;
  enablePasswordProtection: boolean;
  enableAutofillProtection: boolean;
  enableExtensionsProtection: boolean;
  enablePluginsProtection: boolean;
  enableNotificationsProtection: boolean;
  enableGeolocationProtection: boolean;
  enableMicrophoneProtection: boolean;
  enableCameraProtection: boolean;
  enableScreenCaptureProtection: boolean;
  enableClipboardProtection: boolean;
  enableDragAndDropProtection: boolean;
  enablePrintingProtection: boolean;
  enableSpellcheckProtection: boolean;
  enableTranslationProtection: boolean;
  enableSpeechRecognitionProtection: boolean;
  enableSpeechSynthesisProtection: boolean;
  enableMIDIAccessProtection: boolean;
  enableUSBProtection: boolean;
  enableBluetoothProtection: boolean;
  enableSerialProtection: boolean;
  enableHIDProtection: boolean;
  enableGamepadProtection: boolean;
  enableVRProtection: boolean;
  enableARProtection: boolean;
  enableSensorsProtection: boolean;
  enablePaymentRequestProtection: boolean;
  enableCredentialManagementProtection: boolean;
  enableWebAuthenticationProtection: boolean;
  enableWebOTPProtection: boolean;
  enableWebShareProtection: boolean;
  enableWebPushProtection: boolean;
  enableBackgroundSyncProtection: boolean;
  enablePeriodicBackgroundSyncProtection: boolean;
  enableQuotaManagementProtection: boolean;
  enableStorageAccessProtection: boolean;
  enableStorageEstimateProtection: boolean;
  enableStorageManagerProtection: boolean;
  enableStorageQuotaProtection: boolean;
  enableStorageUsageProtection: boolean;
}

export class PrivacyManager extends EventEmitter {
  private static instance: PrivacyManager;
  private config: PrivacyConfig;
  private privacyState: Map<string, boolean>;
  private protectedData: Map<string, any>;

  private constructor() {
    super();
    this.config = this.getDefaultConfig();
    this.privacyState = new Map();
    this.protectedData = new Map();
    this.initializePrivacy();
  }

  public static getInstance(): PrivacyManager {
    if (!PrivacyManager.instance) {
      PrivacyManager.instance = new PrivacyManager();
    }
    return PrivacyManager.instance;
  }

  private getDefaultConfig(): PrivacyConfig {
    return {
      enableDoNotTrack: true,
      enableTrackingProtection: true,
      enableFingerprintingProtection: true,
      enableWebRTCIPHandling: true,
      enableCookieProtection: true,
      enableLocalStorageProtection: true,
      enableSessionStorageProtection: true,
      enableIndexedDBProtection: true,
      enableWebSQLProtection: true,
      enableCacheProtection: true,
      enableHistoryProtection: true,
      enableDownloadHistoryProtection: true,
      enableFormDataProtection: true,
      enablePasswordProtection: true,
      enableAutofillProtection: true,
      enableExtensionsProtection: true,
      enablePluginsProtection: true,
      enableNotificationsProtection: true,
      enableGeolocationProtection: true,
      enableMicrophoneProtection: true,
      enableCameraProtection: true,
      enableScreenCaptureProtection: true,
      enableClipboardProtection: true,
      enableDragAndDropProtection: true,
      enablePrintingProtection: true,
      enableSpellcheckProtection: true,
      enableTranslationProtection: true,
      enableSpeechRecognitionProtection: true,
      enableSpeechSynthesisProtection: true,
      enableMIDIAccessProtection: true,
      enableUSBProtection: true,
      enableBluetoothProtection: true,
      enableSerialProtection: true,
      enableHIDProtection: true,
      enableGamepadProtection: true,
      enableVRProtection: true,
      enableARProtection: true,
      enableSensorsProtection: true,
      enablePaymentRequestProtection: true,
      enableCredentialManagementProtection: true,
      enableWebAuthenticationProtection: true,
      enableWebOTPProtection: true,
      enableWebShareProtection: true,
      enableWebPushProtection: true,
      enableBackgroundSyncProtection: true,
      enablePeriodicBackgroundSyncProtection: true,
      enableQuotaManagementProtection: true,
      enableStorageAccessProtection: true,
      enableStorageEstimateProtection: true,
      enableStorageManagerProtection: true,
      enableStorageQuotaProtection: true,
      enableStorageUsageProtection: true,
    };
  }

  private async initializePrivacy(): Promise<void> {
    try {
      await this.setupPrivacyProtection();
      await this.setupDataProtection();
      await this.setupTrackingProtection();
      this.setupPrivacyEvents();
    } catch (error) {
      console.error('Privacy initialization failed:', error);
      this.emit('privacy-error', error);
    }
  }

  private async setupPrivacyProtection(): Promise<void> {
    if (this.config.enableDoNotTrack) {
      app.commandLine.appendSwitch('enable-features', 'DoNotTrack');
    }

    if (this.config.enableTrackingProtection) {
      app.commandLine.appendSwitch('enable-features', 'TrackingProtection');
    }

    if (this.config.enableFingerprintingProtection) {
      app.commandLine.appendSwitch('enable-features', 'FingerprintingProtection');
    }

    if (this.config.enableWebRTCIPHandling) {
      app.commandLine.appendSwitch('enable-features', 'WebRTCIPHandling');
    }
  }

  private async setupDataProtection(): Promise<void> {
    session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
      const permissionMap: Record<string, boolean> = {
        'notifications': this.config.enableNotificationsProtection,
        'geolocation': this.config.enableGeolocationProtection,
        'media': this.config.enableMicrophoneProtection || this.config.enableCameraProtection,
        'midi': this.config.enableMIDIAccessProtection,
        'usb': this.config.enableUSBProtection,
        'bluetooth': this.config.enableBluetoothProtection,
        'serial': this.config.enableSerialProtection,
        'hid': this.config.enableHIDProtection,
        'gamepad': this.config.enableGamepadProtection,
        'vr': this.config.enableVRProtection,
        'ar': this.config.enableARProtection,
        'sensors': this.config.enableSensorsProtection,
        'payment': this.config.enablePaymentRequestProtection,
        'credentials': this.config.enableCredentialManagementProtection,
        'webauthn': this.config.enableWebAuthenticationProtection,
        'otp': this.config.enableWebOTPProtection,
        'share': this.config.enableWebShareProtection,
        'push': this.config.enableWebPushProtection,
        'background-sync': this.config.enableBackgroundSyncProtection,
        'periodic-background-sync': this.config.enablePeriodicBackgroundSyncProtection,
      };

      callback(permissionMap[permission] ?? false);
    });
  }

  private async setupTrackingProtection(): Promise<void> {
    session.defaultSession.webRequest.onBeforeRequest(
      { urls: ['*://*/*'] },
      (details, callback) => {
        if (this.isTrackingRequest(details.url)) {
          callback({ cancel: true });
        } else {
          callback({ cancel: false });
        }
      }
    );
  }

  private setupPrivacyEvents(): void {
    app.on('browser-window-created', (event, window) => {
      this.monitorWindowPrivacy(window);
    });
  }

  private monitorWindowPrivacy(window: BrowserWindow): void {
    window.webContents.on('will-navigate', (event, url) => {
      if (this.isPrivacyViolation(url)) {
        event.preventDefault();
        this.emit('privacy-violation', { url, type: 'navigation' });
      }
    });

    window.webContents.on('will-redirect', (event, url) => {
      if (this.isPrivacyViolation(url)) {
        event.preventDefault();
        this.emit('privacy-violation', { url, type: 'redirect' });
      }
    });
  }

  private isTrackingRequest(url: string): boolean {
    const trackingPatterns = [
      /analytics/i,
      /tracking/i,
      /pixel/i,
      /beacon/i,
      /spy/i,
      /monitor/i,
    ];

    return trackingPatterns.some(pattern => pattern.test(url));
  }

  private isPrivacyViolation(url: string): boolean {
    const privacyViolationPatterns = [
      /data-collection/i,
      /user-tracking/i,
      /behavioral-tracking/i,
      /surveillance/i,
    ];

    return privacyViolationPatterns.some(pattern => pattern.test(url));
  }

  public getPrivacyConfig(): PrivacyConfig {
    return { ...this.config };
  }

  public updatePrivacyConfig(newConfig: Partial<PrivacyConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config-updated', this.config);
  }

  public getPrivacyState(): Map<string, boolean> {
    return new Map(this.privacyState);
  }

  public async protectData(data: any, type: string): Promise<void> {
    const hash = createHash('sha256').update(JSON.stringify(data)).digest('hex');
    this.protectedData.set(hash, { data, type });
    this.emit('data-protected', { hash, type });
  }

  public async unprotectData(hash: string): Promise<any> {
    const protectedItem = this.protectedData.get(hash);
    if (protectedItem) {
      this.protectedData.delete(hash);
      this.emit('data-unprotected', { hash, type: protectedItem.type });
      return protectedItem.data;
    }
    return null;
  }

  public async clearProtectedData(): Promise<void> {
    this.protectedData.clear();
    this.emit('protected-data-cleared');
  }

  public async exportPrivacySettings(): Promise<void> {
    const settings = {
      config: this.config,
      state: Object.fromEntries(this.privacyState),
    };

    await fs.writeFile(
      path.join(app.getPath('userData'), 'privacy-settings.json'),
      JSON.stringify(settings, null, 2)
    );

    this.emit('settings-exported');
  }

  public async importPrivacySettings(): Promise<void> {
    try {
      const data = await fs.readFile(
        path.join(app.getPath('userData'), 'privacy-settings.json'),
        'utf-8'
      );
      const settings = JSON.parse(data);
      this.config = settings.config;
      this.privacyState = new Map(Object.entries(settings.state));
      this.emit('settings-imported');
    } catch (error) {
      console.error('Failed to import privacy settings:', error);
      this.emit('import-error', error);
    }
  }
} 