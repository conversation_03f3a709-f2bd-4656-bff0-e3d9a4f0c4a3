import { app } from 'electron';
import { promises as fs } from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

interface SyncData {
  bookmarks: any[];
  history: any[];
  settings: any;
  extensions: any[];
  lastSync: number;
}

export class SyncManager {
  private static instance: SyncManager;
  private syncData: SyncData;
  private deviceId: string;
  private syncInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.syncData = {
      bookmarks: [],
      history: [],
      settings: {},
      extensions: [],
      lastSync: 0
    };
    this.deviceId = uuidv4();
  }

  public static getInstance(): SyncManager {
    if (!SyncManager.instance) {
      SyncManager.instance = new SyncManager();
    }
    return SyncManager.instance;
  }

  public async initialize(): Promise<void> {
    await this.loadLocalData();
    this.startSyncInterval();
  }

  private async loadLocalData(): Promise<void> {
    try {
      const syncPath = path.join(app.getPath('userData'), 'sync.json');
      const data = await fs.readFile(syncPath, 'utf-8');
      this.syncData = JSON.parse(data);
    } catch (error) {
      // If file doesn't exist or is corrupted, start with empty data
      this.syncData = {
        bookmarks: [],
        history: [],
        settings: {},
        extensions: [],
        lastSync: Date.now()
      };
    }
  }

  private async saveLocalData(): Promise<void> {
    const syncPath = path.join(app.getPath('userData'), 'sync.json');
    await fs.writeFile(syncPath, JSON.stringify(this.syncData, null, 2));
  }

  private startSyncInterval(): void {
    // Sync every 5 minutes
    this.syncInterval = setInterval(() => {
      this.sync();
    }, 5 * 60 * 1000);
  }

  public async sync(): Promise<void> {
    try {
      // Here you would implement the actual sync logic with your backend
      // For example, using WebSocket or REST API
      
      // Update last sync time
      this.syncData.lastSync = Date.now();
      await this.saveLocalData();
    } catch (error) {
      console.error('Sync failed:', error);
    }
  }

  public async updateBookmarks(bookmarks: any[]): Promise<void> {
    this.syncData.bookmarks = bookmarks;
    await this.saveLocalData();
    await this.sync();
  }

  public async updateHistory(history: any[]): Promise<void> {
    this.syncData.history = history;
    await this.saveLocalData();
    await this.sync();
  }

  public async updateSettings(settings: any): Promise<void> {
    this.syncData.settings = settings;
    await this.saveLocalData();
    await this.sync();
  }

  public async updateExtensions(extensions: any[]): Promise<void> {
    this.syncData.extensions = extensions;
    await this.saveLocalData();
    await this.sync();
  }

  public getDeviceId(): string {
    return this.deviceId;
  }

  public getLastSyncTime(): number {
    return this.syncData.lastSync;
  }

  public cleanup(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
  }
} 