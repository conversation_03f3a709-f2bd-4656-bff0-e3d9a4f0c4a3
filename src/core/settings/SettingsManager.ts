import { app } from 'electron';
import { promises as fs } from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';

interface SettingValue {
  value: any;
  type: string;
  description: string;
  category: string;
  isDefault: boolean;
  lastModified: number;
  validation?: (value: any) => boolean;
}

interface SettingsCategory {
  name: string;
  description: string;
  icon?: string;
  order: number;
}

interface SettingsConfig {
  autoSave: boolean;
  saveInterval: number;
  maxHistory: number;
  categories: SettingsCategory[];
}

export class SettingsManager extends EventEmitter {
  private static instance: SettingsManager;
  private settings: Map<string, SettingValue>;
  private config: SettingsConfig;
  private saveInterval: NodeJS.Timeout | null = null;
  private settingsHistory: Map<string, any[]>;

  private constructor() {
    super();
    this.settings = new Map();
    this.settingsHistory = new Map();
    this.config = {
      autoSave: true,
      saveInterval: 5 * 60 * 1000, // 5 minutes
      maxHistory: 10,
      categories: [
        {
          name: 'general',
          description: 'General Settings',
          order: 0
        },
        {
          name: 'appearance',
          description: 'Appearance Settings',
          order: 1
        },
        {
          name: 'privacy',
          description: 'Privacy Settings',
          order: 2
        },
        {
          name: 'performance',
          description: 'Performance Settings',
          order: 3
        },
        {
          name: 'security',
          description: 'Security Settings',
          order: 4
        }
      ]
    };
  }

  public static getInstance(): SettingsManager {
    if (!SettingsManager.instance) {
      SettingsManager.instance = new SettingsManager();
    }
    return SettingsManager.instance;
  }

  public async initialize(): Promise<void> {
    await this.loadSettings();
    if (this.config.autoSave) {
      this.startSaveInterval();
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      const settings = JSON.parse(data);
      
      for (const [key, value] of Object.entries(settings)) {
        this.settings.set(key, value as SettingValue);
      }
    } catch (error) {
      // If settings don't exist, initialize with defaults
      await this.initializeDefaultSettings();
    }
  }

  private async initializeDefaultSettings(): Promise<void> {
    // General settings
    this.setSetting('language', {
      value: 'en',
      type: 'string',
      description: 'Application language',
      category: 'general',
      isDefault: true,
      lastModified: Date.now()
    });

    this.setSetting('theme', {
      value: 'system',
      type: 'string',
      description: 'Application theme',
      category: 'appearance',
      isDefault: true,
      lastModified: Date.now()
    });

    // Privacy settings
    this.setSetting('doNotTrack', {
      value: true,
      type: 'boolean',
      description: 'Enable Do Not Track',
      category: 'privacy',
      isDefault: true,
      lastModified: Date.now()
    });

    // Performance settings
    this.setSetting('hardwareAcceleration', {
      value: true,
      type: 'boolean',
      description: 'Enable hardware acceleration',
      category: 'performance',
      isDefault: true,
      lastModified: Date.now()
    });

    // Security settings
    this.setSetting('safeBrowsing', {
      value: true,
      type: 'boolean',
      description: 'Enable safe browsing',
      category: 'security',
      isDefault: true,
      lastModified: Date.now()
    });

    await this.saveSettings();
  }

  private startSaveInterval(): void {
    this.saveInterval = setInterval(() => {
      this.saveSettings();
    }, this.config.saveInterval);
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'settings.json');
    const settings = Object.fromEntries(this.settings);
    await fs.writeFile(settingsPath, JSON.stringify(settings, null, 2));
  }

  public setSetting(key: string, value: SettingValue): void {
    const oldValue = this.settings.get(key);
    
    // Validate the new value if validation function exists
    if (value.validation && !value.validation(value.value)) {
      throw new Error(`Invalid value for setting ${key}`);
    }

    // Add to history
    if (oldValue) {
      const history = this.settingsHistory.get(key) || [];
      history.unshift(oldValue.value);
      if (history.length > this.config.maxHistory) {
        history.pop();
      }
      this.settingsHistory.set(key, history);
    }

    this.settings.set(key, value);
    this.emit('setting-changed', { key, oldValue, newValue: value });

    if (this.config.autoSave) {
      this.saveSettings();
    }
  }

  public getSetting(key: string): SettingValue | undefined {
    return this.settings.get(key);
  }

  public getSettingValue(key: string): any {
    return this.settings.get(key)?.value;
  }

  public getSettingsByCategory(category: string): Map<string, SettingValue> {
    return new Map(
      Array.from(this.settings.entries())
        .filter(([_, value]) => value.category === category)
    );
  }

  public getSettingHistory(key: string): any[] {
    return this.settingsHistory.get(key) || [];
  }

  public async resetSetting(key: string): Promise<void> {
    const setting = this.settings.get(key);
    if (setting) {
      const defaultSetting = await this.getDefaultSetting(key);
      if (defaultSetting) {
        this.setSetting(key, defaultSetting);
      }
    }
  }

  private async getDefaultSetting(key: string): Promise<SettingValue | undefined> {
    // Implement logic to get default setting value
    return undefined;
  }

  public async resetAllSettings(): Promise<void> {
    this.settings.clear();
    this.settingsHistory.clear();
    await this.initializeDefaultSettings();
  }

  public getCategories(): SettingsCategory[] {
    return [...this.config.categories].sort((a, b) => a.order - b.order);
  }

  public getConfig(): SettingsConfig {
    return { ...this.config };
  }

  public async updateConfig(config: Partial<SettingsConfig>): Promise<void> {
    this.config = { ...this.config, ...config };
    
    if (this.config.autoSave) {
      this.startSaveInterval();
    } else if (this.saveInterval) {
      clearInterval(this.saveInterval);
    }
  }

  public cleanup(): void {
    if (this.saveInterval) {
      clearInterval(this.saveInterval);
    }
  }
} 