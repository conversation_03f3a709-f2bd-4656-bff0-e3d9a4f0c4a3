import { app } from 'electron';
import { promises as fs } from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

interface AccessibilityProfile {
  id: string;
  name: string;
  description: string;
  settings: {
    fontSize: number;
    lineHeight: number;
    letterSpacing: number;
    fontFamily: string;
    highContrast: boolean;
    reducedMotion: boolean;
    colorBlindness: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
    screenReader: boolean;
    keyboardNavigation: boolean;
    focusHighlight: boolean;
    textToSpeech: boolean;
    speechRate: number;
    speechVolume: number;
    speechVoice: string;
  };
}

interface AccessibilitySettings {
  enabled: boolean;
  activeProfile: string;
  autoDetect: boolean;
  keyboardShortcuts: {
    toggleScreenReader: string;
    increaseFontSize: string;
    decreaseFontSize: string;
    toggleHighContrast: string;
    toggleReducedMotion: string;
    speakSelectedText: string;
  };
  notifications: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
  };
  wcag: {
    level: 'A' | 'AA' | 'AAA';
    autoCheck: boolean;
    reportIssues: boolean;
  };
}

interface AccessibilityIssue {
  id: string;
  type: 'error' | 'warning' | 'info';
  element: string;
  description: string;
  wcagCriteria: string;
  recommendation: string;
  timestamp: number;
}

export class AccessibilityManager extends EventEmitter {
  private static instance: AccessibilityManager;
  private profiles: Map<string, AccessibilityProfile>;
  private settings: AccessibilitySettings;
  private issues: AccessibilityIssue[];
  private isInitialized: boolean = false;
  private screenReader: any; // Will be initialized based on OS
  private speechSynthesis: any; // Will be initialized based on OS

  private constructor() {
    super();
    this.profiles = new Map();
    this.settings = {
      enabled: true,
      activeProfile: 'default',
      autoDetect: true,
      keyboardShortcuts: {
        toggleScreenReader: 'Alt+Shift+S',
        increaseFontSize: 'Ctrl+Plus',
        decreaseFontSize: 'Ctrl+Minus',
        toggleHighContrast: 'Alt+Shift+C',
        toggleReducedMotion: 'Alt+Shift+M',
        speakSelectedText: 'Alt+Shift+T'
      },
      notifications: {
        enabled: true,
        sound: true,
        vibration: false
      },
      wcag: {
        level: 'AA',
        autoCheck: true,
        reportIssues: true
      }
    };
    this.issues = [];
  }

  public static getInstance(): AccessibilityManager {
    if (!AccessibilityManager.instance) {
      AccessibilityManager.instance = new AccessibilityManager();
    }
    return AccessibilityManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadSettings();
      await this.loadProfiles();
      await this.setupScreenReader();
      await this.setupSpeechSynthesis();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize AccessibilityManager:', error);
      throw error;
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'accessibility-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'accessibility-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private async loadProfiles(): Promise<void> {
    try {
      const profilesPath = path.join(app.getPath('userData'), 'accessibility-profiles.json');
      const data = await fs.readFile(profilesPath, 'utf-8');
      const profiles = JSON.parse(data);
      
      for (const profile of profiles) {
        this.profiles.set(profile.id, profile);
      }

      // Create default profile if none exists
      if (!this.profiles.has('default')) {
        await this.createDefaultProfile();
      }
    } catch (error) {
      await this.saveProfiles();
    }
  }

  private async saveProfiles(): Promise<void> {
    const profilesPath = path.join(app.getPath('userData'), 'accessibility-profiles.json');
    await fs.writeFile(
      profilesPath,
      JSON.stringify(Array.from(this.profiles.values()), null, 2)
    );
  }

  private async createDefaultProfile(): Promise<void> {
    const defaultProfile: AccessibilityProfile = {
      id: 'default',
      name: 'Default',
      description: 'Default accessibility settings',
      settings: {
        fontSize: 16,
        lineHeight: 1.5,
        letterSpacing: 0,
        fontFamily: 'system-ui',
        highContrast: false,
        reducedMotion: false,
        colorBlindness: 'none',
        screenReader: false,
        keyboardNavigation: true,
        focusHighlight: true,
        textToSpeech: false,
        speechRate: 1,
        speechVolume: 1,
        speechVoice: 'default'
      }
    };

    this.profiles.set('default', defaultProfile);
    await this.saveProfiles();
  }

  private async setupScreenReader(): Promise<void> {
    // Initialize screen reader based on OS
    if (process.platform === 'darwin') {
      // macOS VoiceOver
      this.screenReader = {
        isEnabled: async () => {
          const { stdout } = await execAsync('defaults read com.apple.universalaccess VoiceOverEnabled');
          return stdout.trim() === '1';
        },
        enable: async () => {
          await execAsync('defaults write com.apple.universalaccess VoiceOverEnabled -bool true');
        },
        disable: async () => {
          await execAsync('defaults write com.apple.universalaccess VoiceOverEnabled -bool false');
        }
      };
    } else if (process.platform === 'win32') {
      // Windows Narrator
      this.screenReader = {
        isEnabled: async () => {
          const { stdout } = await execAsync('reg query "HKEY_CURRENT_USER\\Software\\Microsoft\\Narrator" /v "NarratorEnabled"');
          return stdout.includes('0x1');
        },
        enable: async () => {
          await execAsync('reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Narrator" /v "NarratorEnabled" /t REG_DWORD /d 1 /f');
        },
        disable: async () => {
          await execAsync('reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Narrator" /v "NarratorEnabled" /t REG_DWORD /d 0 /f');
        }
      };
    }
  }

  private async setupSpeechSynthesis(): Promise<void> {
    // Initialize speech synthesis based on OS
    if (process.platform === 'darwin') {
      // macOS speech synthesis
      this.speechSynthesis = {
        speak: async (text: string, options: any) => {
          await execAsync(`say "${text}" -v ${options.voice || 'default'} -r ${options.rate || 1}`);
        },
        getVoices: async () => {
          const { stdout } = await execAsync('say -v "?"');
          return stdout.split('\n').map(line => {
            const [name, ...rest] = line.split(' ');
            return { name, description: rest.join(' ') };
          });
        }
      };
    } else if (process.platform === 'win32') {
      // Windows speech synthesis
      this.speechSynthesis = {
        speak: async (text: string, options: any) => {
          await execAsync(`powershell -Command "Add-Type -AssemblyName System.Speech; (New-Object System.Speech.Synthesis.SpeechSynthesizer).Speak('${text}')"`);
        },
        getVoices: async () => {
          const { stdout } = await execAsync('powershell -Command "Add-Type -AssemblyName System.Speech; (New-Object System.Speech.Synthesis.SpeechSynthesizer).GetInstalledVoices() | ForEach-Object { $_.VoiceInfo.Name }"');
          return stdout.split('\n').map(name => ({ name, description: '' }));
        }
      };
    }
  }

  public async createProfile(profile: Omit<AccessibilityProfile, 'id'>): Promise<AccessibilityProfile> {
    const newProfile: AccessibilityProfile = {
      ...profile,
      id: Math.random().toString(36).substr(2, 9)
    };

    this.profiles.set(newProfile.id, newProfile);
    await this.saveProfiles();
    this.emit('profile-created', newProfile);

    return newProfile;
  }

  public async updateProfile(id: string, updates: Partial<AccessibilityProfile>): Promise<AccessibilityProfile> {
    const profile = this.profiles.get(id);
    if (!profile) {
      throw new Error(`Profile not found: ${id}`);
    }

    const updatedProfile = { ...profile, ...updates };
    this.profiles.set(id, updatedProfile);
    await this.saveProfiles();
    this.emit('profile-updated', updatedProfile);

    return updatedProfile;
  }

  public async deleteProfile(id: string): Promise<void> {
    if (id === 'default') {
      throw new Error('Cannot delete default profile');
    }

    const profile = this.profiles.get(id);
    if (!profile) {
      throw new Error(`Profile not found: ${id}`);
    }

    this.profiles.delete(id);
    await this.saveProfiles();
    this.emit('profile-deleted', profile);
  }

  public async setActiveProfile(id: string): Promise<void> {
    const profile = this.profiles.get(id);
    if (!profile) {
      throw new Error(`Profile not found: ${id}`);
    }

    this.settings.activeProfile = id;
    await this.saveSettings();
    await this.applyProfile(profile);
    this.emit('profile-activated', profile);
  }

  private async applyProfile(profile: AccessibilityProfile): Promise<void> {
    // Apply font settings
    document.documentElement.style.fontSize = `${profile.settings.fontSize}px`;
    document.documentElement.style.lineHeight = profile.settings.lineHeight.toString();
    document.documentElement.style.letterSpacing = `${profile.settings.letterSpacing}px`;
    document.documentElement.style.fontFamily = profile.settings.fontFamily;

    // Apply high contrast
    if (profile.settings.highContrast) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }

    // Apply reduced motion
    if (profile.settings.reducedMotion) {
      document.documentElement.classList.add('reduced-motion');
    } else {
      document.documentElement.classList.remove('reduced-motion');
    }

    // Apply color blindness simulation
    document.documentElement.classList.remove('protanopia', 'deuteranopia', 'tritanopia');
    if (profile.settings.colorBlindness !== 'none') {
      document.documentElement.classList.add(profile.settings.colorBlindness);
    }

    // Apply screen reader
    if (profile.settings.screenReader) {
      await this.screenReader?.enable();
    } else {
      await this.screenReader?.disable();
    }

    // Apply keyboard navigation
    if (profile.settings.keyboardNavigation) {
      document.documentElement.classList.add('keyboard-navigation');
    } else {
      document.documentElement.classList.remove('keyboard-navigation');
    }

    // Apply focus highlight
    if (profile.settings.focusHighlight) {
      document.documentElement.classList.add('focus-highlight');
    } else {
      document.documentElement.classList.remove('focus-highlight');
    }
  }

  public async checkAccessibility(element: HTMLElement): Promise<AccessibilityIssue[]> {
    const issues: AccessibilityIssue[] = [];

    // Check for alt text on images
    const images = element.getElementsByTagName('img');
    for (const img of images) {
      if (!img.hasAttribute('alt')) {
        issues.push({
          id: Math.random().toString(36).substr(2, 9),
          type: 'error',
          element: img.outerHTML,
          description: 'Image missing alt text',
          wcagCriteria: '1.1.1',
          recommendation: 'Add descriptive alt text to the image',
          timestamp: Date.now()
        });
      }
    }

    // Check for proper heading structure
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    for (const heading of headings) {
      const level = parseInt(heading.tagName[1]);
      if (level - previousLevel > 1) {
        issues.push({
          id: Math.random().toString(36).substr(2, 9),
          type: 'warning',
          element: heading.outerHTML,
          description: 'Skipped heading level',
          wcagCriteria: '1.3.1',
          recommendation: 'Maintain proper heading hierarchy',
          timestamp: Date.now()
        });
      }
      previousLevel = level;
    }

    // Check for sufficient color contrast
    const elements = element.getElementsByTagName('*');
    for (const el of elements) {
      const style = window.getComputedStyle(el);
      const backgroundColor = style.backgroundColor;
      const color = style.color;
      // TODO: Implement color contrast calculation
    }

    // Check for keyboard accessibility
    const interactiveElements = element.querySelectorAll('button, [role="button"], a, input, select, textarea');
    for (const el of interactiveElements) {
      if (!el.hasAttribute('tabindex') && !el.hasAttribute('role')) {
        issues.push({
          id: Math.random().toString(36).substr(2, 9),
          type: 'warning',
          element: el.outerHTML,
          description: 'Interactive element may not be keyboard accessible',
          wcagCriteria: '2.1.1',
          recommendation: 'Ensure element is keyboard accessible',
          timestamp: Date.now()
        });
      }
    }

    this.issues = [...this.issues, ...issues];
    this.emit('accessibility-checked', issues);

    return issues;
  }

  public async speakText(text: string, options: any = {}): Promise<void> {
    if (!this.speechSynthesis) {
      throw new Error('Speech synthesis not available');
    }

    await this.speechSynthesis.speak(text, {
      voice: options.voice || 'default',
      rate: options.rate || 1,
      volume: options.volume || 1
    });
  }

  public async getAvailableVoices(): Promise<{ name: string; description: string }[]> {
    if (!this.speechSynthesis) {
      throw new Error('Speech synthesis not available');
    }

    return await this.speechSynthesis.getVoices();
  }

  public getSettings(): AccessibilitySettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<AccessibilitySettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();
    this.emit('settings-updated', this.settings);
  }

  public getIssues(): AccessibilityIssue[] {
    return [...this.issues];
  }

  public cleanup(): void {
    // Cleanup any active screen reader or speech synthesis
    if (this.screenReader) {
      this.screenReader.disable();
    }
  }
} 