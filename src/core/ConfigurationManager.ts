import { EventEmitter } from 'events';
import { logger } from './EnhancedLogger';

export interface ConfigSchema {
  [key: string]: {
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    default: any;
    required?: boolean;
    validation?: (value: any) => boolean;
    description?: string;
    sensitive?: boolean;
  };
}

export interface ConfigurationOptions {
  environment?: string;
  enableValidation?: boolean;
  enableEncryption?: boolean;
  enableRemoteConfig?: boolean;
  remoteConfigUrl?: string;
  refreshInterval?: number;
  enableHotReload?: boolean;
}

export class ConfigurationManager extends EventEmitter {
  private static instance: ConfigurationManager;
  private config: Record<string, any> = {};
  private schema: ConfigSchema = {};
  private options: ConfigurationOptions;
  private watchers: Map<string, ((value: any) => void)[]> = new Map();
  private refreshTimer: NodeJS.Timeout | null = null;

  private constructor(options: ConfigurationOptions = {}) {
    super();
    this.options = {
      environment: process.env.NODE_ENV || 'development',
      enableValidation: true,
      enableEncryption: false,
      enableRemoteConfig: false,
      refreshInterval: 60000, // 1 minute
      enableHotReload: true,
      ...options,
    };
    
    this.initialize();
  }

  public static getInstance(options?: ConfigurationOptions): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager(options);
    }
    return ConfigurationManager.instance;
  }

  private async initialize(): Promise<void> {
    try {
      // Load configuration from various sources
      await this.loadEnvironmentConfig();
      await this.loadFileConfig();
      await this.loadLocalStorageConfig();
      
      if (this.options.enableRemoteConfig) {
        await this.loadRemoteConfig();
        this.startRefreshTimer();
      }
      
      logger.info('Configuration manager initialized', {
        environment: this.options.environment,
        configKeys: Object.keys(this.config),
      });
      
      this.emit('initialized');
    } catch (error) {
      logger.error('Failed to initialize configuration manager', error);
      throw error;
    }
  }

  public defineSchema(schema: ConfigSchema): void {
    this.schema = { ...this.schema, ...schema };
    
    if (this.options.enableValidation) {
      this.validateConfiguration();
    }
  }

  public get<T = any>(key: string, defaultValue?: T): T {
    const value = this.getNestedValue(this.config, key);
    return value !== undefined ? value : defaultValue;
  }

  public set(key: string, value: any): void {
    const oldValue = this.get(key);
    
    if (this.options.enableValidation && this.schema[key]) {
      this.validateValue(key, value);
    }
    
    this.setNestedValue(this.config, key, value);
    
    // Persist to localStorage
    this.saveToLocalStorage(key, value);
    
    // Notify watchers
    this.notifyWatchers(key, value, oldValue);
    
    // Emit change event
    this.emit('change', { key, value, oldValue });
    
    logger.debug('Configuration value changed', {
      key,
      newValue: this.schema[key]?.sensitive ? '[REDACTED]' : value,
      oldValue: this.schema[key]?.sensitive ? '[REDACTED]' : oldValue,
    });
  }

  public watch(key: string, callback: (value: any) => void): () => void {
    if (!this.watchers.has(key)) {
      this.watchers.set(key, []);
    }
    
    this.watchers.get(key)!.push(callback);
    
    // Return unwatch function
    return () => {
      const callbacks = this.watchers.get(key);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  public getAll(): Record<string, any> {
    return { ...this.config };
  }

  public reset(key?: string): void {
    if (key) {
      const schemaEntry = this.schema[key];
      if (schemaEntry) {
        this.set(key, schemaEntry.default);
      } else {
        this.setNestedValue(this.config, key, undefined);
      }
    } else {
      // Reset all to defaults
      Object.keys(this.schema).forEach(k => {
        this.set(k, this.schema[k].default);
      });
    }
  }

  public export(includeDefaults = false): Record<string, any> {
    const exported: Record<string, any> = {};
    
    Object.keys(this.config).forEach(key => {
      const value = this.config[key];
      const schemaEntry = this.schema[key];
      
      if (includeDefaults || (schemaEntry && value !== schemaEntry.default)) {
        exported[key] = schemaEntry?.sensitive ? '[REDACTED]' : value;
      }
    });
    
    return exported;
  }

  public import(config: Record<string, any>): void {
    Object.keys(config).forEach(key => {
      this.set(key, config[key]);
    });
  }

  private async loadEnvironmentConfig(): Promise<void> {
    // Load from process.env
    Object.keys(process.env).forEach(key => {
      if (key.startsWith('REACT_APP_') || key.startsWith('BROWSER_')) {
        const configKey = key.toLowerCase().replace(/^(react_app_|browser_)/, '');
        const value = this.parseEnvironmentValue(process.env[key]!);
        this.config[configKey] = value;
      }
    });
  }

  private async loadFileConfig(): Promise<void> {
    try {
      // Try to load config file based on environment
      const configFile = `config.${this.options.environment}.json`;
      const response = await fetch(`/config/${configFile}`);
      
      if (response.ok) {
        const fileConfig = await response.json();
        this.config = { ...this.config, ...fileConfig };
      }
    } catch (error) {
      logger.warn('Failed to load file configuration', { error });
    }
  }

  private loadLocalStorageConfig(): void {
    try {
      const stored = localStorage.getItem('browser_config');
      if (stored) {
        const localConfig = JSON.parse(stored);
        this.config = { ...this.config, ...localConfig };
      }
    } catch (error) {
      logger.warn('Failed to load localStorage configuration', { error });
    }
  }

  private async loadRemoteConfig(): Promise<void> {
    if (!this.options.remoteConfigUrl) return;
    
    try {
      const response = await fetch(this.options.remoteConfigUrl);
      if (response.ok) {
        const remoteConfig = await response.json();
        this.config = { ...this.config, ...remoteConfig };
        logger.info('Remote configuration loaded successfully');
      }
    } catch (error) {
      logger.warn('Failed to load remote configuration', { error });
    }
  }

  private validateConfiguration(): void {
    Object.keys(this.schema).forEach(key => {
      const value = this.get(key);
      const schemaEntry = this.schema[key];
      
      if (schemaEntry.required && value === undefined) {
        throw new Error(`Required configuration key '${key}' is missing`);
      }
      
      if (value !== undefined) {
        this.validateValue(key, value);
      }
    });
  }

  private validateValue(key: string, value: any): void {
    const schemaEntry = this.schema[key];
    if (!schemaEntry) return;
    
    // Type validation
    const expectedType = schemaEntry.type;
    const actualType = Array.isArray(value) ? 'array' : typeof value;
    
    if (actualType !== expectedType) {
      throw new Error(`Configuration key '${key}' expected type '${expectedType}' but got '${actualType}'`);
    }
    
    // Custom validation
    if (schemaEntry.validation && !schemaEntry.validation(value)) {
      throw new Error(`Configuration key '${key}' failed custom validation`);
    }
  }

  private parseEnvironmentValue(value: string): any {
    // Try to parse as JSON first
    try {
      return JSON.parse(value);
    } catch {
      // If not JSON, return as string
      return value;
    }
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!(key in current)) {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    target[lastKey] = value;
  }

  private saveToLocalStorage(key: string, value: any): void {
    try {
      const stored = localStorage.getItem('browser_config') || '{}';
      const config = JSON.parse(stored);
      this.setNestedValue(config, key, value);
      localStorage.setItem('browser_config', JSON.stringify(config));
    } catch (error) {
      logger.warn('Failed to save configuration to localStorage', { error });
    }
  }

  private notifyWatchers(key: string, value: any, oldValue: any): void {
    const callbacks = this.watchers.get(key);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(value);
        } catch (error) {
          logger.error('Error in configuration watcher callback', error);
        }
      });
    }
  }

  private startRefreshTimer(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    
    this.refreshTimer = setInterval(async () => {
      await this.loadRemoteConfig();
    }, this.options.refreshInterval);
  }

  public destroy(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    this.watchers.clear();
    this.removeAllListeners();
  }
}

// Export singleton instance
export const configManager = ConfigurationManager.getInstance();
