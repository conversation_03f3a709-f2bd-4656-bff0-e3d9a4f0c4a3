/**
 * Система управления Web Workers для выполнения тяжелых вычислений
 * в фоновых потоках без блокировки основного UI потока
 */

export interface WorkerTask<T = any, R = any> {
  id: string;
  type: string;
  data: T;
  priority?: number;
  timeout?: number;
  retries?: number;
  onProgress?: (progress: number) => void;
  onComplete?: (result: R) => void;
  onError?: (error: Error) => void;
}

export interface WorkerMessage<T = any> {
  id: string;
  type: 'task' | 'result' | 'error' | 'progress';
  data?: T;
  error?: string;
  progress?: number;
}

export interface WorkerPool {
  workers: Worker[];
  available: boolean[];
  taskQueue: WorkerTask[];
  activeTasks: Map<string, WorkerTask>;
}

export class WorkerManager {
  private pools = new Map<string, WorkerPool>();
  private taskCounter = 0;
  private defaultPoolSize = navigator.hardwareConcurrency || 4;

  /**
   * Создает пул воркеров для определенного типа задач
   */
  createPool(
    poolName: string,
    workerScript: string,
    poolSize: number = this.defaultPoolSize
  ): void {
    if (this.pools.has(poolName)) {
      throw new Error(`Worker pool '${poolName}' already exists`);
    }

    const workers: Worker[] = [];
    const available: boolean[] = [];

    for (let i = 0; i < poolSize; i++) {
      const worker = new Worker(workerScript);
      worker.onmessage = (event) => this.handleWorkerMessage(poolName, i, event);
      worker.onerror = (error) => this.handleWorkerError(poolName, i, error);

      workers.push(worker);
      available.push(true);
    }

    this.pools.set(poolName, {
      workers,
      available,
      taskQueue: [],
      activeTasks: new Map()
    });
  }

  /**
   * Выполняет задачу в воркере
   */
  async executeTask<T, R>(
    poolName: string,
    taskType: string,
    data: T,
    options: Partial<WorkerTask<T, R>> = {}
  ): Promise<R> {
    const pool = this.pools.get(poolName);
    if (!pool) {
      throw new Error(`Worker pool '${poolName}' not found`);
    }

    const task: WorkerTask<T, R> = {
      id: this.generateTaskId(),
      type: taskType,
      data,
      priority: 0,
      timeout: 30000,
      retries: 3,
      ...options
    };

    return new Promise<R>((resolve, reject) => {
      task.onComplete = resolve;
      task.onError = reject;

      this.scheduleTask(poolName, task);
    });
  }

  /**
   * Выполняет задачу с прогрессом
   */
  executeTaskWithProgress<T, R>(
    poolName: string,
    taskType: string,
    data: T,
    onProgress: (progress: number) => void,
    options: Partial<WorkerTask<T, R>> = {}
  ): Promise<R> {
    return this.executeTask(poolName, taskType, data, {
      ...options,
      onProgress
    });
  }

  /**
   * Выполняет несколько задач параллельно
   */
  async executeParallel<T, R>(
    poolName: string,
    tasks: Array<{ type: string; data: T; options?: Partial<WorkerTask<T, R>> }>
  ): Promise<R[]> {
    const promises = tasks.map(({ type, data, options }) =>
      this.executeTask<T, R>(poolName, type, data, options)
    );

    return Promise.all(promises);
  }

  /**
   * Выполняет задачи последовательно
   */
  async executeSequential<T, R>(
    poolName: string,
    tasks: Array<{ type: string; data: T; options?: Partial<WorkerTask<T, R>> }>
  ): Promise<R[]> {
    const results: R[] = [];

    for (const { type, data, options } of tasks) {
      const result = await this.executeTask<T, R>(poolName, type, data, options);
      results.push(result);
    }

    return results;
  }

  /**
   * Отменяет задачу
   */
  cancelTask(poolName: string, taskId: string): boolean {
    const pool = this.pools.get(poolName);
    if (!pool) return false;

    // Удаляем из очереди
    const queueIndex = pool.taskQueue.findIndex(task => task.id === taskId);
    if (queueIndex !== -1) {
      pool.taskQueue.splice(queueIndex, 1);
      return true;
    }

    return false;
  }

  private generateTaskId(): string {
    return `task_${++this.taskCounter}_${Date.now()}`;
  }

  private scheduleTask<T, R>(poolName: string, task: WorkerTask<T, R>): void {
    const pool = this.pools.get(poolName)!;

    // Добавляем в очередь с учетом приоритета
    const insertIndex = pool.taskQueue.findIndex(
      queuedTask => (queuedTask.priority || 0) < (task.priority || 0)
    );

    if (insertIndex === -1) {
      pool.taskQueue.push(task);
    } else {
      pool.taskQueue.splice(insertIndex, 0, task);
    }

    this.processQueue(poolName);
  }

  private processQueue(poolName: string): void {
    const pool = this.pools.get(poolName)!;

    while (pool.taskQueue.length > 0) {
      const availableWorkerIndex = pool.available.findIndex(Boolean);
      if (availableWorkerIndex === -1) break;

      const task = pool.taskQueue.shift()!;
      const worker = pool.workers[availableWorkerIndex];

      pool.available[availableWorkerIndex] = false;
      pool.activeTasks.set(task.id, task);

      // Устанавливаем таймаут для задачи
      if (task.timeout) {
        setTimeout(() => {
          if (pool.activeTasks.has(task.id)) {
            this.cancelTask(poolName, task.id);
            task.onError?.(new Error('Task timeout'));
          }
        }, task.timeout);
      }

      // Отправляем задачу воркеру
      const message: WorkerMessage = {
        id: task.id,
        type: 'task',
        data: {
          type: task.type,
          data: task.data
        }
      };

      worker.postMessage(message);
    }
  }

  private handleWorkerMessage(poolName: string, workerIndex: number, event: MessageEvent): void {
    const pool = this.pools.get(poolName)!;
    const message: WorkerMessage = event.data;
    const task = pool.activeTasks.get(message.id);

    if (!task) return;

    switch (message.type) {
      case 'result':
        pool.available[workerIndex] = true;
        pool.activeTasks.delete(message.id);
        task.onComplete?.(message.data);
        this.processQueue(poolName);
        break;

      case 'error':
        pool.available[workerIndex] = true;
        pool.activeTasks.delete(message.id);

        if (task.retries && task.retries > 0) {
          task.retries--;
          this.scheduleTask(poolName, task);
        } else {
          task.onError?.(new Error(message.error || 'Worker error'));
        }

        this.processQueue(poolName);
        break;

      case 'progress':
        if (message.progress !== undefined) {
          task.onProgress?.(message.progress);
        }
        break;
    }
  }

  private handleWorkerError(poolName: string, workerIndex: number, error: ErrorEvent): void {
    console.error(`Worker error in pool ${poolName}:`, error);
  }
}

// Глобальный менеджер воркеров
export const workerManager = new WorkerManager();