import { app } from 'electron';
import { promises as fs } from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import { Worker } from 'worker_threads';
import { spawn } from 'child_process';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as crypto from 'crypto';
import * as zlib from 'zlib';
import { createReadStream, createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';
import * as archiver from 'archiver';

interface ExtensionSettings {
  enabled: boolean;
  autoUpdate: boolean;
  permissions: {
    [key: string]: boolean;
    storage: boolean;
    network: boolean;
    tabs: boolean;
    bookmarks: boolean;
    history: boolean;
    downloads: boolean;
    notifications: boolean;
    clipboard: boolean;
    geolocation: boolean;
    camera: boolean;
    microphone: boolean;
  };
  security: {
    sandbox: boolean;
    contentSecurityPolicy: boolean;
    crossOriginIsolation: boolean;
    trustedTypes: boolean;
  };
  storage: {
    enabled: boolean;
    path: string;
  };
}

interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  homepage: string;
  permissions: string[];
  manifest: {
    name: string;
    version: string;
    description: string;
    author: string;
    homepage: string;
    permissions: string[];
    content_scripts?: {
      matches: string[];
      js: string[];
      css: string[];
    }[];
    background?: {
      service_worker: string;
    };
    action?: {
      default_popup: string;
      default_icon: string;
    };
  };
  status: 'enabled' | 'disabled' | 'updating' | 'error';
  installDate: number;
  updateDate: number;
  size: number;
  hash: string;
}

interface ExtensionUpdate {
  id: string;
  version: string;
  releaseNotes: string;
  size: number;
  hash: string;
  url: string;
  date: number;
}

export class ExtensionManager extends EventEmitter {
  private static instance: ExtensionManager;
  private settings: ExtensionSettings;
  private extensions: Map<string, Extension>;
  private updates: Map<string, ExtensionUpdate>;
  private isInitialized: boolean = false;
  private updateInterval: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    this.settings = {
      enabled: true,
      autoUpdate: true,
      permissions: {
        storage: true,
        network: true,
        tabs: true,
        bookmarks: true,
        history: true,
        downloads: true,
        notifications: true,
        clipboard: true,
        geolocation: true,
        camera: true,
        microphone: true
      },
      security: {
        sandbox: true,
        contentSecurityPolicy: true,
        crossOriginIsolation: true,
        trustedTypes: true
      },
      storage: {
        enabled: true,
        path: 'extensions'
      }
    };
    this.extensions = new Map();
    this.updates = new Map();
  }

  public static getInstance(): ExtensionManager {
    if (!ExtensionManager.instance) {
      ExtensionManager.instance = new ExtensionManager();
    }
    return ExtensionManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadSettings();
      await this.setupStorage();
      await this.loadExtensions();
      await this.setupUpdates();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize ExtensionManager:', error);
      throw error;
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'extension-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'extension-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private async setupStorage(): Promise<void> {
    if (!this.settings.storage.enabled) return;

    const storagePath = path.join(app.getPath('userData'), this.settings.storage.path);
    await fs.mkdir(storagePath, { recursive: true });
  }

  private async loadExtensions(): Promise<void> {
    try {
      const extensionsPath = path.join(app.getPath('userData'), this.settings.storage.path, 'extensions.json');
      const data = await fs.readFile(extensionsPath, 'utf-8');
      const extensions = JSON.parse(data);
      this.extensions = new Map(Object.entries(extensions));
    } catch (error) {
      await this.saveExtensions();
    }
  }

  private async saveExtensions(): Promise<void> {
    if (!this.settings.storage.enabled) return;

    const extensionsPath = path.join(app.getPath('userData'), this.settings.storage.path, 'extensions.json');
    await fs.writeFile(extensionsPath, JSON.stringify(Object.fromEntries(this.extensions), null, 2));
  }

  private async setupUpdates(): Promise<void> {
    if (!this.settings.autoUpdate) return;

    this.updateInterval = setInterval(async () => {
      try {
        await this.checkForUpdates();
      } catch (error) {
        console.error('Failed to check for updates:', error);
      }
    }, 3600000); // Check for updates every hour

    process.on('exit', () => {
      if (this.updateInterval) {
        clearInterval(this.updateInterval);
      }
    });
  }

  public async installExtension(filePath: string): Promise<Extension> {
    try {
      const manifest = await this.extractManifest(filePath);
      const extension: Extension = {
        id: crypto.randomBytes(16).toString('hex'),
        name: manifest.name,
        version: manifest.version,
        description: manifest.description,
        author: manifest.author,
        homepage: manifest.homepage,
        permissions: manifest.permissions,
        manifest,
        status: 'enabled',
        installDate: Date.now(),
        updateDate: Date.now(),
        size: (await fs.stat(filePath)).size,
        hash: await this.calculateHash(filePath)
      };

      await this.validateExtension(extension);
      await this.installFiles(filePath, extension);
      this.extensions.set(extension.id, extension);
      await this.saveExtensions();
      this.emit('extension-installed', extension);

      return extension;
    } catch (error) {
      console.error('Failed to install extension:', error);
      throw error;
    }
  }

  private async extractManifest(filePath: string): Promise<any> {
    const extractPath = path.join(app.getPath('temp'), 'extension-extract');
    await fs.mkdir(extractPath, { recursive: true });

    const archive = archiver('zip', { zlib: { level: 9 } });
    const extractStream = createWriteStream(path.join(extractPath, 'extension.zip'));
    await pipeline(createReadStream(filePath), archive, extractStream);

    const manifestPath = path.join(extractPath, 'manifest.json');
    const manifestData = await fs.readFile(manifestPath, 'utf-8');
    return JSON.parse(manifestData);
  }

  private async validateExtension(extension: Extension): Promise<void> {
    // Validate manifest
    if (!extension.manifest.name || !extension.manifest.version) {
      throw new Error('Invalid manifest: missing required fields');
    }

    // Validate permissions
    for (const permission of extension.permissions) {
      if (!this.settings.permissions[permission]) {
        throw new Error(`Permission not allowed: ${permission}`);
      }
    }

    // Validate security
    if (this.settings.security.sandbox) {
      // Add sandbox validation
    }

    if (this.settings.security.contentSecurityPolicy) {
      // Add CSP validation
    }
  }

  private async installFiles(filePath: string, extension: Extension): Promise<void> {
    const extensionPath = path.join(app.getPath('userData'), this.settings.storage.path, extension.id);
    await fs.mkdir(extensionPath, { recursive: true });

    const archive = archiver('zip', { zlib: { level: 9 } });
    const extractStream = createWriteStream(path.join(extensionPath, 'extension.zip'));
    await pipeline(createReadStream(filePath), archive, extractStream);
  }

  private async calculateHash(filePath: string): Promise<string> {
    const hash = crypto.createHash('sha256');
    const stream = createReadStream(filePath);
    for await (const chunk of stream) {
      hash.update(chunk);
    }
    return hash.digest('hex');
  }

  public async uninstallExtension(id: string): Promise<void> {
    const extension = this.extensions.get(id);
    if (!extension) {
      throw new Error(`Extension not found: ${id}`);
    }

    try {
      const extensionPath = path.join(app.getPath('userData'), this.settings.storage.path, id);
      await fs.rm(extensionPath, { recursive: true });
      this.extensions.delete(id);
      await this.saveExtensions();
      this.emit('extension-uninstalled', extension);
    } catch (error) {
      console.error('Failed to uninstall extension:', error);
      throw error;
    }
  }

  public async updateExtension(id: string): Promise<Extension> {
    const extension = this.extensions.get(id);
    if (!extension) {
      throw new Error(`Extension not found: ${id}`);
    }

    try {
      extension.status = 'updating';
      await this.saveExtensions();
      this.emit('extension-updating', extension);

      const update = this.updates.get(id);
      if (!update) {
        throw new Error(`No update available for extension: ${id}`);
      }

      const response = await fetch(update.url);
      const buffer = await response.arrayBuffer();
      const filePath = path.join(app.getPath('temp'), `extension-update-${id}.zip`);
      await fs.writeFile(filePath, Buffer.from(buffer));

      const updatedExtension = await this.installExtension(filePath);
      updatedExtension.id = id;
      this.extensions.set(id, updatedExtension);
      await this.saveExtensions();
      this.emit('extension-updated', updatedExtension);

      return updatedExtension;
    } catch (error) {
      extension.status = 'error';
      await this.saveExtensions();
      this.emit('extension-update-failed', extension, error);
      throw error;
    }
  }

  private async checkForUpdates(): Promise<void> {
    for (const [id, extension] of this.extensions) {
      try {
        const update = await this.fetchUpdate(extension);
        if (update) {
          this.updates.set(id, update);
          this.emit('update-available', extension, update);
        }
      } catch (error) {
        console.error(`Failed to check for updates for extension ${id}:`, error);
      }
    }
  }

  private async fetchUpdate(extension: Extension): Promise<ExtensionUpdate | null> {
    // Implement update checking logic here
    // This could involve checking a registry, API, or other update source
    return null;
  }

  public async enableExtension(id: string): Promise<void> {
    const extension = this.extensions.get(id);
    if (!extension) {
      throw new Error(`Extension not found: ${id}`);
    }

    extension.status = 'enabled';
    await this.saveExtensions();
    this.emit('extension-enabled', extension);
  }

  public async disableExtension(id: string): Promise<void> {
    const extension = this.extensions.get(id);
    if (!extension) {
      throw new Error(`Extension not found: ${id}`);
    }

    extension.status = 'disabled';
    await this.saveExtensions();
    this.emit('extension-disabled', extension);
  }

  public getExtensions(): Extension[] {
    return Array.from(this.extensions.values());
  }

  public getExtension(id: string): Extension | undefined {
    return this.extensions.get(id);
  }

  public getUpdates(): ExtensionUpdate[] {
    return Array.from(this.updates.values());
  }

  public getSettings(): ExtensionSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<ExtensionSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();
    this.emit('settings-updated', this.settings);
  }
} 