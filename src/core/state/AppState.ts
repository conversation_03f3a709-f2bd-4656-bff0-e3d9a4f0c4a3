export interface AppState {
  // Настройки приложения
  settings: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    fontSize: number;
    notifications: boolean;
    sound: boolean;
  };

  // Состояние браузера
  browser: {
    tabs: Array<{
      id: string;
      url: string;
      title: string;
      favicon: string;
      isActive: boolean;
      isLoading: boolean;
    }>;
    activeTabId: string | null;
    history: Array<{
      url: string;
      title: string;
      timestamp: number;
    }>;
    bookmarks: Array<{
      url: string;
      title: string;
      folder: string;
      timestamp: number;
    }>;
  };

  // Состояние безопасности
  security: {
    isIncognito: boolean;
    blockedSites: string[];
    allowedSites: string[];
    privacyLevel: 'low' | 'medium' | 'high';
  };

  // Состояние производительности
  performance: {
    memoryUsage: number;
    cpuUsage: number;
    networkStatus: 'online' | 'offline';
    lastOptimization: number;
  };

  // Состояние кэша
  cache: {
    size: number;
    lastCleanup: number;
    maxSize: number;
  };

  // Состояние уведомлений
  notifications: {
    unread: number;
    lastCheck: number;
    settings: {
      enabled: boolean;
      sound: boolean;
      desktop: boolean;
    };
  };
}

export const initialAppState: AppState = {
  settings: {
    theme: 'system',
    language: 'en',
    fontSize: 16,
    notifications: true,
    sound: true
  },
  browser: {
    tabs: [],
    activeTabId: null,
    history: [],
    bookmarks: []
  },
  security: {
    isIncognito: false,
    blockedSites: [],
    allowedSites: [],
    privacyLevel: 'medium'
  },
  performance: {
    memoryUsage: 0,
    cpuUsage: 0,
    networkStatus: 'online',
    lastOptimization: Date.now()
  },
  cache: {
    size: 0,
    lastCleanup: Date.now(),
    maxSize: 100 * 1024 * 1024 // 100MB
  },
  notifications: {
    unread: 0,
    lastCheck: Date.now(),
    settings: {
      enabled: true,
      sound: true,
      desktop: true
    }
  }
}; 