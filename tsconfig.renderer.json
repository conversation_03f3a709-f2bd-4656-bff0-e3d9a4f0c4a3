{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["DOM", "DOM.Iterable", "ES2020"], "jsx": "react-jsx", "outDir": "./dist/renderer", "rootDir": "./src/renderer", "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo-renderer"}, "include": ["src/renderer/**/*", "src/shared/**/*", "src/types/**/*", "src/components/**/*", "src/hooks/**/*", "src/providers/**/*", "src/store/**/*", "src/utils/**/*"], "exclude": ["node_modules", "dist", "src/main", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}