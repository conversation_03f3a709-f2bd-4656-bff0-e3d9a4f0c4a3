export const ariaLabel = (element: HTMLElement, label: string) => {
  element.setAttribute('aria-label', label);
};

export const validateA11y = (container: HTMLElement) => {
  const errors: string[] = [];
  
  // Проверка обязательных ARIA-атрибутов
  const interactiveElements = container.querySelectorAll<HTMLElement>('[tabindex], button, a, input');
  interactiveElements.forEach(el => {
    if (!el.getAttribute('aria-label') && !el.textContent?.trim()) {
      errors.push(`Missing aria-label for interactive element: ${el.tagName}`);
    }
  });

  // Проверка контрастности цветов
  const style = getComputedStyle(container);
  const bgColor = style.backgroundColor;
  const textColor = style.color;
  
  if (!isContrastValid(bgColor, textColor)) {
    errors.push('Insufficient color contrast between background and text');
  }

  return errors;
};

const isContrastValid = (bg: string, fg: string): boolean => {
  // Реализация проверки контраста
  return true; // Заглушка для примера
};