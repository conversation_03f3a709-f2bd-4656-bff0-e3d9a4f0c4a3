const fs = require('fs');
const path = require('path');

const LOCALES_DIR = path.join(__dirname, '../src/i18n/locales');
const DEFAULT_LOCALE = 'en';

// Read all translation files
const readTranslationFiles = () => {
  const files = fs.readdirSync(LOCALES_DIR);
  const translations = {};

  files.forEach(file => {
    if (file.endsWith('.json')) {
      const locale = file.replace('.json', '');
      const content = fs.readFileSync(path.join(LOCALES_DIR, file), 'utf8');
      translations[locale] = JSON.parse(content);
    }
  });

  return translations;
};

// Get all keys from the default locale
const getAllKeys = (obj, prefix = '') => {
  return Object.entries(obj).reduce((keys, [key, value]) => {
    const newKey = prefix ? `${prefix}.${key}` : key;
    if (typeof value === 'object' && value !== null) {
      return [...keys, ...getAllKeys(value, newKey)];
    }
    return [...keys, newKey];
  }, []);
};

// Update missing translations
const updateTranslations = () => {
  const translations = readTranslationFiles();
  const defaultTranslations = translations[DEFAULT_LOCALE];
  const defaultKeys = getAllKeys(defaultTranslations);

  Object.entries(translations).forEach(([locale, translation]) => {
    if (locale === DEFAULT_LOCALE) return;

    const missingKeys = defaultKeys.filter(key => {
      const parts = key.split('.');
      let current = translation;
      for (const part of parts) {
        if (!current[part]) return true;
        current = current[part];
      }
      return false;
    });

    if (missingKeys.length > 0) {
      console.log(`\nMissing translations in ${locale}:`);
      missingKeys.forEach(key => {
        console.log(`  - ${key}`);
        const parts = key.split('.');
        let current = translation;
        for (let i = 0; i < parts.length - 1; i++) {
          if (!current[parts[i]]) {
            current[parts[i]] = {};
          }
          current = current[parts[i]];
        }
        current[parts[parts.length - 1]] = key;
      });

      fs.writeFileSync(
        path.join(LOCALES_DIR, `${locale}.json`),
        JSON.stringify(translation, null, 2),
        'utf8'
      );
    }
  });
};

// Validate translations
const validateTranslations = () => {
  const translations = readTranslationFiles();
  const defaultTranslations = translations[DEFAULT_LOCALE];
  const defaultKeys = getAllKeys(defaultTranslations);

  Object.entries(translations).forEach(([locale, translation]) => {
    if (locale === DEFAULT_LOCALE) return;

    const missingKeys = defaultKeys.filter(key => {
      const parts = key.split('.');
      let current = translation;
      for (const part of parts) {
        if (!current[part]) return true;
        current = current[part];
      }
      return false;
    });

    if (missingKeys.length > 0) {
      console.log(`\nMissing translations in ${locale}:`);
      missingKeys.forEach(key => console.log(`  - ${key}`));
    }
  });
};

// Main function
const main = () => {
  const command = process.argv[2];

  if (!command || !['update', 'validate'].includes(command)) {
    console.error('Usage: node update-translations.js [update|validate]');
    process.exit(1);
  }

  try {
    if (command === 'update') {
      updateTranslations();
    } else {
      validateTranslations();
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
};

main(); 