describe('Authentication', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('should login successfully with valid credentials', () => {
    cy.login('<EMAIL>', 'password123');
    cy.url().should('include', '/dashboard');
    cy.get('[data-testid=user-menu]').should('be.visible');
  });

  it('should show error message with invalid credentials', () => {
    cy.login('<EMAIL>', 'wrongpassword');
    cy.get('[data-testid=error-message]')
      .should('be.visible')
      .and('contain', 'Invalid credentials');
  });

  it('should logout successfully', () => {
    cy.login('<EMAIL>', 'password123');
    cy.logout();
    cy.url().should('include', '/login');
  });

  it('should remember user session', () => {
    cy.login('<EMAIL>', 'password123');
    cy.get('[data-testid=remember-me]').check();
    cy.reload();
    cy.url().should('include', '/dashboard');
  });

  it('should redirect to login page when accessing protected route', () => {
    cy.visit('/dashboard');
    cy.url().should('include', '/login');
  });
}); 