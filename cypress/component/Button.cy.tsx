import React from 'react';
import { mount } from 'cypress/react18';
import Button from '../../src/shared/components/Button';

describe('Button Component', () => {
  it('renders correctly', () => {
    mount(<Button>Click me</Button>);
    cy.get('button').should('contain', 'Click me');
  });

  it('handles click events', () => {
    const onClickSpy = cy.spy().as('onClickSpy');
    mount(<Button onClick={onClickSpy}>Click me</Button>);
    cy.get('button').click();
    cy.get('@onClickSpy').should('have.been.calledOnce');
  });

  it('applies variant styles', () => {
    mount(<Button variant="primary">Primary Button</Button>);
    cy.get('button').should('have.class', 'primary');
  });

  it('applies size styles', () => {
    mount(<Button size="large">Large Button</Button>);
    cy.get('button').should('have.class', 'large');
  });

  it('disables button when disabled prop is true', () => {
    mount(<Button disabled>Disabled Button</Button>);
    cy.get('button').should('be.disabled');
  });

  it('shows loading state', () => {
    mount(<Button loading>Loading Button</Button>);
    cy.get('button').should('have.class', 'loading');
    cy.get('button').should('be.disabled');
  });

  it('applies custom className', () => {
    mount(<Button className="custom-class">Custom Button</Button>);
    cy.get('button').should('have.class', 'custom-class');
  });
}); 