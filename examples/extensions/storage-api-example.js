// Пример расширения для работы с API хранилища
import { serializer } from '../../src/serialization/protobuf-wrapper';
import { applySecurityPolicy } from '../../src/security/csp-config';

export default class StorageExtension {
  constructor() {
    applySecurityPolicy('development');
    this.storage = new Map();
  }

  async setItem(key, value) {
    const data = await serializer.serialize({
      id: 'storage-ext',
      payload: JSON.stringify({ key, value }),
      metadata: {
        version: '1.0',
        source: 'storage-extension'
      }
    });
    this.storage.set(key, data);
    return this._notifyCore('storage_update', data);
  }

  async getItem(key) {
    const data = this.storage.get(key);
    return data ? serializer.deserialize(data) : null;
  }

  _notifyCore(event, data) {
    return chrome.runtime.sendMessage({
      type: 'CORE_COMMUNICATION',
      event,
      payload: data,
      timestamp: Date.now()
    });
  }
}