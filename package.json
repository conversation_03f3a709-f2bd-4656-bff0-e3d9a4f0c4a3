{"name": "a14-browser", "version": "1.0.0", "description": "Advanced web browser with enhanced security and performance", "main": "dist/main.js", "scripts": {"start": "electron .", "dev": "vite", "build": "tsc && vite build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "prepare": "husky install", "commit": "git-cz", "release": "standard-version", "docs": "typedoc --out docs src", "analyze": "source-map-explorer 'dist/assets/*.js'", "security-audit": "npm audit", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "postbuild": "npm run analyze"}, "dependencies": {"@reduxjs/toolkit": "^2.0.0", "axios": "^1.6.0", "electron": "^28.0.0", "electron-store": "^8.1.0", "i18next": "^23.7.0", "jwt-decode": "^4.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.0", "react-router-dom": "^6.20.0", "winston": "^3.11.0", "zod": "^3.22.0"}, "devDependencies": {"@commitlint/cli": "^18.0.0", "@commitlint/config-conventional": "^18.0.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.1.0", "@types/archiver": "^6.0.3", "@types/jest": "^29.5.0", "@types/node": "^20.10.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.0", "commitizen": "^4.3.0", "cypress": "^13.5.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "postcss": "^8.4.0", "prettier": "^3.1.0", "rimraf": "^5.0.0", "source-map-explorer": "^2.5.0", "standard-version": "^9.5.0", "tailwindcss": "^3.3.0", "ts-jest": "^29.1.0", "typedoc": "^0.25.0", "typescript": "^5.3.0", "vite": "^5.0.0", "vite-plugin-electron": "^0.15.0", "lru-cache": "^7.14.1", "@opentelemetry/api": "^1.4.1", "joi": "^17.9.2"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/a14-browser.git"}, "keywords": ["browser", "electron", "security", "performance", "accessibility"], "author": "Your Name", "license": "MIT", "bugs": {"url": "https://github.com/yourusername/a14-browser/issues"}, "homepage": "https://github.com/yourusername/a14-browser#readme"}