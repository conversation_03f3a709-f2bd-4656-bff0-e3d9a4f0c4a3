# Contributing to <PERSON><PERSON><PERSON><PERSON>

Thank you for your interest in contributing to NovaBrowser! This document provides guidelines and instructions for contributing.

## Code of Conduct

By participating in this project, you agree to abide by our [Code of Conduct](CODE_OF_CONDUCT.md).

## How Can I Contribute?

### Reporting Bugs

Before creating bug reports, please check the issue list as you might find out that you don't need to create one. When you are creating a bug report, please include as many details as possible:

- Use a clear and descriptive title
- Describe the exact steps to reproduce the problem
- Provide specific examples to demonstrate the steps
- Describe the behavior you observed after following the steps
- Explain which behavior you expected to see instead and why
- Include screenshots if possible
- Include the version of NovaBrowser you're using
- Include your operating system and version

### Suggesting Enhancements

If you have a suggestion for a new feature or enhancement, please:

- Use a clear and descriptive title
- Provide a detailed description of the proposed functionality
- Explain why this enhancement would be useful
- List any similar features in other browsers
- Include mockups or screenshots if applicable

### Pull Requests

1. Fork the repo and create your branch from `main`
2. If you've added code that should be tested, add tests
3. If you've changed APIs, update the documentation
4. Ensure the test suite passes
5. Make sure your code lints
6. Issue that pull request!

### Development Process

1. Clone the repository:
```bash
git clone https://github.com/novabrowser/novabrowser.git
cd novabrowser
```

2. Install dependencies:
```bash
npm install
```

3. Create a new branch:
```bash
git checkout -b feature/your-feature-name
```

4. Make your changes

5. Run tests:
```bash
npm test
```

6. Run linter:
```bash
npm run lint
```

7. Commit your changes:
```bash
git commit -m "feat: add your feature"
```

8. Push to your fork:
```bash
git push origin feature/your-feature-name
```

9. Create a Pull Request

### Commit Message Guidelines

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

- `feat:` A new feature
- `fix:` A bug fix
- `docs:` Documentation only changes
- `style:` Changes that do not affect the meaning of the code
- `refactor:` A code change that neither fixes a bug nor adds a feature
- `perf:` A code change that improves performance
- `test:` Adding missing tests or correcting existing tests
- `chore:` Changes to the build process or auxiliary tools

### Coding Standards

- Follow the [TypeScript Style Guide](https://google.github.io/styleguide/tsguide.html)
- Follow the [React Style Guide](https://reactjs.org/docs/code-splitting.html)
- Use ESLint and Prettier for code formatting
- Write meaningful comments
- Follow the DRY (Don't Repeat Yourself) principle
- Write unit tests for new features
- Update documentation as needed

### Testing

- Write unit tests for new features
- Write integration tests for complex features
- Ensure all tests pass before submitting a PR
- Maintain or improve test coverage

### Documentation

- Update README.md if needed
- Add JSDoc comments to new functions
- Update API documentation
- Add inline comments for complex logic
- Update changelog

### Review Process

1. All PRs require at least one review
2. All tests must pass
3. Code must be properly formatted
4. Documentation must be updated
5. Changes must be properly tested

### Getting Help

- Check the [documentation](docs/README.md)
- Join our [Discord community](https://discord.gg/novabrowser)
- Open an issue for questions
- Contact the maintainers

## Development Setup

### Prerequisites

- Node.js (v18 or higher)
- npm (v9 or higher)
- Git
- A code editor (VS Code recommended)

### Environment Setup

1. Install dependencies:
```bash
npm install
```

2. Create environment file:
```bash
cp .env.example .env
```

3. Start development server:
```bash
npm run dev
```

### Available Scripts

- `npm start` - Start the development server
- `npm run build` - Build for production
- `npm test` - Run tests
- `npm run lint` - Run linter
- `npm run format` - Format code
- `npm run type-check` - Run TypeScript type checking
- `npm run storybook` - Start Storybook
- `npm run cypress` - Run Cypress tests

## Project Structure

```
src/
├── core/           # Core browser functionality
├── features/       # Feature modules
├── shared/         # Shared components and utilities
├── utils/          # Utility functions
├── services/       # Service layer
├── types/          # TypeScript type definitions
├── constants/      # Constants and configuration
├── hooks/          # Custom React hooks
├── store/          # State management
├── styles/         # Global styles and themes
├── assets/         # Static assets
└── config/         # Configuration files
```

## Style Guide

### TypeScript

- Use TypeScript for all new code
- Use strict type checking
- Avoid using `any` type
- Use interfaces for object types
- Use enums for constants
- Use type guards when necessary

### React

- Use functional components
- Use hooks for state management
- Use proper prop types
- Use proper event handling
- Use proper error boundaries
- Use proper loading states

### CSS

- Use CSS modules
- Use BEM naming convention
- Use CSS variables for theming
- Use responsive design
- Use proper accessibility

## Release Process

1. Update version in package.json
2. Update CHANGELOG.md
3. Create release branch
4. Run tests
5. Build for production
6. Create release tag
7. Deploy to production

## License

By contributing to NovaBrowser, you agree that your contributions will be licensed under the project's [MIT License](LICENSE.md).